# ================================================= 
# 					  Properties 				  #
#				    Spoil Configs                 #
# ================================================= 
# Base chance of success Spoil-a, as a percentage.
# Official : 75
BasePercentChanceOfSpoilSuccess = 85

# Minimum success rate Spoil-a, as a percentage.
MinimumPercentChanceOfSpoilSuccess = 55

# Read Spoil the standard formula passing skill.
AltFormula = False

# ======================
# Manor Config - core  #
# ======================

# Allow Manor
AllowManor = True

# Manor Refresh Time in Military hours Default 8pm (20)
AltManorRefreshTime = 20

# Manor Refresh Time for Min's, Default 00 so at the start of the hour.
AltManorRefreshMin = 00

# Manor Next Period Approve Time in Military hours Default 6am
AltManorApproveTime = 6

# Manor Next Period Approve Time for Min's, Default 00 so at the start of the hour.
AltManorApproveMin = 00

# Manor Maintenance time, Default 6 minutes.
AltManorMaintenancePeriod = 360000

# ========================
# Manor Config - seeds   #
# ========================

# Base chance of success for Sowing, in percent.
BasePercentChanceOfSowingSuccess = 100

# Base chance of success for Sowing, using viola seeds, percent.
BasePercentChanceOfSowingAltSuccess = 10

# Base chance of success for Harvesting, in percent.
BasePercentChanceOfHarvestingSuccess = 90

# The minimum level difference between the player and the mob, in which there is no penalty.
MinDiffPlayerMob = 5

# The penalty for each level difference between the player and the mob, as a percentage.
DiffPlayerMobPenalty = 5

# The minimum level difference between the seed and the mob, in which there is no penalty.
MinDiffSeedMob = 5

# The penalty for each level difference between the seed and the mob, as a percentage.
DiffSeedMobPenalty = 5