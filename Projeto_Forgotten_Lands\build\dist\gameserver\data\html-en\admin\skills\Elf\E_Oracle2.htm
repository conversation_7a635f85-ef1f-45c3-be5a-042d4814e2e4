<title>Choose skill to add</title>
<center>
[admin_add_skill 1016 1|Resurrection 1 (Lv 20)]%%
[admin_add_skill 1016 2|Resurrection 2 (Lv 30)]%%
[admin_add_skill 1027 4|Group Heal 4 (Lv 20)]%%
[admin_add_skill 1027 5|Group Heal 5 (Lv 20)]%%
[admin_add_skill 1027 6|Group Heal 6 (Lv 20)]%%
[admin_add_skill 1027 7|Group Heal 7 (Lv 25)]%%
[admin_add_skill 1027 8|Group Heal 8 (Lv 25)]%%
[admin_add_skill 1027 9|Group Heal 9 (Lv 25)]%%
[admin_add_skill 1027 10|Group Heal 10 (Lv 30)]%%
[admin_add_skill 1027 11|Group Heal 11 (Lv 30)]%%
[admin_add_skill 1027 12|Group Heal 12 (Lv 30)]%%
[admin_add_skill 1027 13|Group Heal 13 (Lv 35)]%%
[admin_add_skill 1027 14|Group Heal 14 (Lv 35)]%%
[admin_add_skill 1027 15|Group Heal 15 (Lv 35)]%%
[admin_add_skill 1031 1|Disrupt Undead 1 (Lv 20)]%%
[admin_add_skill 1031 2|Disrupt Undead 2 (Lv 20)]%%
[admin_add_skill 1031 3|Disrupt Undead 3 (Lv 25)]%%
[admin_add_skill 1031 4|Disrupt Undead 4 (Lv 25)]%%
[admin_add_skill 1031 5|Disrupt Undead 5 (Lv 30)]%%
[admin_add_skill 1031 6|Disrupt Undead 6 (Lv 30)]%%
[admin_add_skill 1031 7|Disrupt Undead 7 (Lv 35)]%%
[admin_add_skill 1031 8|Disrupt Undead 8 (Lv 35)]%%
[admin_add_skill 1033 1|Resist Poison 1 (Lv 35)]%%
[admin_add_skill 1035 1|Mental Shield 1 (Lv 25)]%%
[admin_add_skill 1040 2|Shield 2 (Lv 25)]%%
[admin_add_skill 1043 1|Holy Weapon 1 (Lv 25)]%%
[admin_add_skill 1044 1|Regeneration 1 (Lv 35)]%%
[admin_add_skill 1068 2|Might 2 (Lv 20)]%%
[admin_add_skill 1069 1|Sleep 1 (Lv 25)]%%
[admin_add_skill 1069 2|Sleep 2 (Lv 25)]%%
[admin_add_skill 1069 3|Sleep 3 (Lv 25)]%%
[admin_add_skill 1069 4|Sleep 4 (Lv 30)]%%
[admin_add_skill 1069 5|Sleep 5 (Lv 30)]%%
[admin_add_skill 1069 6|Sleep 6 (Lv 30)]%%
[admin_add_skill 1069 7|Sleep 7 (Lv 35)]%%
[admin_add_skill 1069 8|Sleep 8 (Lv 35)]%%
[admin_add_skill 1069 9|Sleep 9 (Lv 35)]%%
[admin_add_skill 1073 1|Kiss of Eva 1 (Lv 20)]%%
[admin_add_skill 1078 1|Concentration 1 (Lv 20)]%%
[admin_add_skill 1078 2|Concentration 2 (Lv 30)]%%
[admin_add_skill 1087 1|Agility 1 (Lv 25)]%%
[admin_add_skill 1201 1|Dryad Root 1 (Lv 25)]%%
[admin_add_skill 1201 2|Dryad Root 2 (Lv 25)]%%
[admin_add_skill 1201 3|Dryad Root 3 (Lv 25)]%%
[admin_add_skill 1201 4|Dryad Root 4 (Lv 30)]%%
[admin_add_skill 1201 5|Dryad Root 5 (Lv 30)]%%
[admin_add_skill 1201 6|Dryad Root 6 (Lv 30)]%%
[admin_add_skill 1201 7|Dryad Root 7 (Lv 35)]%%
[admin_add_skill 1201 8|Dryad Root 8 (Lv 35)]%%
[admin_add_skill 1201 9|Dryad Root 9 (Lv 35)]%%
[admin_add_skill 1204 1|Wind Walk 1 (Lv 20)]%%
[admin_add_skill 1204 2|Wind Walk 2 (Lv 30)]%%
[admin_add_skill 1206 2|Wind Shackle 2 (Lv 20)]%%
[admin_add_skill 1206 3|Wind Shackle 3 (Lv 25)]%%
[admin_add_skill 1206 4|Wind Shackle 4 (Lv 30)]%%
[admin_add_skill 1206 5|Wind Shackle 5 (Lv 35)]%%
[admin_add_skill 1257 1|Decrease Weight 1 (Lv 35)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index Elf/E_Oracle" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
