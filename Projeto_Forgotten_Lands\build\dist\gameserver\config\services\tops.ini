#================================================
#			   For Russian Top		    		#
#================================================
# L2Top
# The system is designed for automatic distribution of bonuses for voting on L2Top.ru
# Awards are given only to those players who are online.
# Is the system automatically issuing bonus
L2TopManagerEnabled = false
# Interval connection l2top default 5 minutes, reduce the interval
L2TopManagerInterval = 300000
L2TopWebAddress =
L2TopSmsAddress =
# Address Server Site and / or the name of its publication in the report of successful ballot
L2TopServerAddress = www.FandC.com
# Days to keep voting
L2TopSaveDays = 30
# Reward format id1, count1; id2, count2;
# You can also specify the following options:
# -100 PC Bang
# -200 Clan reputation
# -300 Fame
L2TopReward = 57,20; 4037,1

# MMOTop
# The system is designed for automatic distribution of bonuses for voting on MMOTop.ru
# Awards are given only to those players who are online.
# Is the system automatically issuing bonus
MMOTopEnable = False
# Interval connection l2top default 5 minutes, reduce the interval
MMOTopManagerInterval = 300000
# Reference for a list of voters
# Example: http://la2.mmotop.ru/stats/64f74fd9670e5575f25e243e1d8598d4/112415/5771.txt
MMOTopUrl = https://mmotop.ru/votes/c09648d0919caab5e7990ff1251bd9056d7cdc80.txt?a9cb5e385f5ac9f9fe728fee9a3e2517
# Address Server Site and / or the name of its publication in the report of successful ballot
MMOTopServerAddress = http://Website.com/
# Days to keep voting
MMOTopSaveDays = 30
# Reward format id1, count1; id2, count2;
# You can also specify the following options:
# -100 PC Bang
# -200 Clan reputation
# -300 Fame
MMOTopReward = 57,20; 6673,1


# =================================================
# 					Hopzone/Topzone				  #
# =================================================


# Vote reward for Hopzone.
AllowHopzoneVoteReward = false
# Vote reward server link.
HopzoneServerLink = 
# First page of servers list link.
HopzoneFirstPageLink = http://l2.hopzone.net/lineage2/
# Votes for next reward needed.
HopzoneVotesDifference = 200
# Rank needed for server to be on first page.
HopzoneFirstPageRankNeeded = 15
# Minutes between rewards.
# Eg. You put 5 it checks every 5 minutes for reward.
HopzoneRewardCheckTime = 60
# Small reward (s).
HopZoneRewardId = 6673
# Big reward (s).
HopZoneTopRewardCount = 10
# Hopzone reward max dual boxes reward.
# For example if you put two and someone has three boxes open two will be rewarded.
HopzoneDuaboxesAllowed = 1
# Game server console report.
# If set to true, game server console will get a report of
# Current vote count, votes needed for next reward and votes needed for first page.
AllowHopzoneGameServerReport = false

# Vote reward for Topzone.
AllowTopzoneVoteReward = false
# Vote reward server link.
TopzoneServerLink = 
# First page of servers list link.
TopzoneFirstPageLink = http://l2topzone.com/lineage2/list
# Votes for next reward needed.
TopzoneVotesDifference = 5
# Rank needed for server to be on first page.
TopzoneFirstPageRankNeeded = 15
# Minutes between rewards.
# Eg. You put 5 it checks every 5 minutes for reward.
TopzoneRewardCheckTime = 5
# Small reward (s).
TopZoneRewardId = 6673
# Big reward (s).
TopZoneRewardCount = 10
# Hopzone reward max dual boxes reward.
# For example if you put two and someone has three boxes open two will be rewarded.
TopzoneDuaboxesAllowed = 1
# Game server console report.
# If set to true, game server console will get a report of
# Current vote count, votes needed for next reward and votes needed for first page.
AllowTopzoneGameServerReport = true

#============================================================                                                 
#     Hopzone / Topzone  - Individual Reward| DONT USE THIS #                                       
#============================================================
HopzoneUrl = 
TopzoneUrl = 

# The characters will have a choice between 3 rewards.Set id for them
VoteRewardId1 = 57
VoteRewardId2 = 10639
VoteRewardId3 = 6393

# Set the amount of each reward.
VoteRewardAmount1 = 15000000
VoteRewardAmount2 = 8
VoteRewardAmount3 = 8

# Set the seconds that the character has
# until he votes in the site's banners
SecondsToVote = 40

# Amount of votes a character must collect
# in order to get the 4th vote reward
ExtraRewVoteAm = 20

# Info of the extra reward item
VoteRewardId4 = 10639
VoteRewardAmount4 = 10