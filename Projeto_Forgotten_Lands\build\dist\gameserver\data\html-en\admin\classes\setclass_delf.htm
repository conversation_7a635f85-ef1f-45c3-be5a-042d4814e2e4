<html><title>Set Class</title><body>
<table width=260><tr>
<td width=40><button value="Main" action="bypass -h admin_admin" width=40 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td>
<td width=180><center>Setclass Menu</center></td>
<td><button value="EditChar" action="bypass -h admin_current_player" width=60 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td>
</tr></table><br>
<center>

<a action="bypass -h admin_show_html setclass.htm">Back</a><br1>

<button value="Dark Fighter" action="bypass -h admin_setclass 31" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Paulus Knight" action="bypass -h admin_setclass 32" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Shillien Knight" action="bypass -h admin_setclass 33" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="BladeDancer" action="bypass -h admin_setclass 34" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Assassin" action="bypass -h admin_setclass 35" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Abyss Walker" action="bypass -h admin_setclass 36" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Phantom Ranger" action="bypass -h admin_setclass 37" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Dark Mystic" action="bypass -h admin_setclass 38" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Dark Wizard" action="bypass -h admin_setclass 39" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Spellhowler" action="bypass -h admin_setclass 40" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Phantom Summoner" action="bypass -h admin_setclass 41" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Shillien Oracle" action="bypass -h admin_setclass 42" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Shillien Elder" action="bypass -h admin_setclass 43" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
3rd:<br1>
<button value="Shillien Templar" action="bypass -h admin_setclass 106" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Spectral Dancer" action="bypass -h admin_setclass 107" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Ghost Hunter" action="bypass -h admin_setclass 108" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Ghost Sentinel" action="bypass -h admin_setclass 109" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Storm Screamer" action="bypass -h admin_setclass 110" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Spectral Master" action="bypass -h admin_setclass 111" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Shillien Saint" action="bypass -h admin_setclass 112" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>

<a action="bypass -h admin_show_html setclass.htm">Back</a><br1>

<br>
</body></html>
