<html><title>Set Class</title><body>
<table width=260><tr>
<td width=40><button value="Main" action="bypass -h admin_admin" width=40 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td>
<td width=180><center>Setclass Menu</center></td>
<td><button value="EditChar" action="bypass -h admin_current_player" width=60 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td>
</tr></table><br>
<center>

<a action="bypass -h admin_show_html setclass.htm">Back</a><br1>

<button value="Elven Fighter" action="bypass -h admin_setclass 18" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Elven Knight" action="bypass -h admin_setclass 19" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Temple Knight" action="bypass -h admin_setclass 20" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Sword Singer" action="bypass -h admin_setclass 21" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Elven Scout" action="bypass -h admin_setclass 22" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Plains Walker" action="bypass -h admin_setclass 23" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Silver Ranger" action="bypass -h admin_setclass 24" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Elven Mystic" action="bypass -h admin_setclass 25" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Elven Wizard" action="bypass -h admin_setclass 26" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="SpellSinger" action="bypass -h admin_setclass 27" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Elemental Summoner" action="bypass -h admin_setclass 28" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Elven Oracle" action="bypass -h admin_setclass 29" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Elven Elder" action="bypass -h admin_setclass 30" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
3rd:<br1>
<button value="Eva's Templar" action="bypass -h admin_setclass 99" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Sword Muse" action="bypass -h admin_setclass 100" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Wind Rider" action="bypass -h admin_setclass 101" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Moonlight Sentinel" action="bypass -h admin_setclass 102" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Mystic Muse" action="bypass -h admin_setclass 103" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Elemental Master" action="bypass -h admin_setclass 104" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button value="Eva's Saint" action="bypass -h admin_setclass 105" width=180 height=18 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>

<a action="bypass -h admin_show_html setclass.htm">Back</a><br1>

<br>
</body></html>
