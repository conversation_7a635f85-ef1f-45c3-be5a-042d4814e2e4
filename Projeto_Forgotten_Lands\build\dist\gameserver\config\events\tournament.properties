#=====================================================================================
#                                   Tournament
#=====================================================================================
# Should we allow to access Tournament menu? It can be accessed by .tgl for example
# .tgl to something else can be changed in data/html-bypasses/community/tournament.xml
TournamentAllowVoicedCommand = True

# What should be Name of the Tournament?
TournamentName = TGL

# How many players each team should have?
TournamentPlayersInTeam = 3

# Should we allow debugging Tournament to log/features/tournament.log file?
TournamentDebug = false

#=====================================================================================
#                                   Registration
#=====================================================================================
# Should we allow registering to the Tournament?
AllowTournamentRegistering = True

# What item is required from EACH team member to participate?
# Syntax: itemId;itemCount
TournamentRequiredItem = 

# Maximum number of Teams that can register for the Tournament
# Set -1 to disable
TournamentMaxTeams = -1

# Should there be announcement to all players, when someone participated?
TournamentAnnounceRegister = True
# What Chat Type should that announcement have?
# For example: GM, ANNOUNCEMENT, HERO_VOICE, CRITICAL_ANNOUNCE, BATTLEFIELD
TournamentAnnounceRegisterChatType = CRITICAL_ANNOUNCE

# Day of the week that the registration for the tournament will automatically start
# If you choose monday for example, registration will be enable at 12:00 on monday and will last the entire day. On tuesday 12:00 registration will end and fights will be set
# Day Indexes: SUNDAY: 1, MONDAY: 2, TUESDAY: 3, WEDNESDAY: 4, THURSDAY: 5, FRIDAY: 6, SATURDAY: 7
TournamentRegistrationDay = 2

#=====================================================================================
#                                  Battle Schedule
#=====================================================================================
# Hours(Server Time) of when Battles have to start for specific Rounds
# Syntax: battleRound,minHour,maxHour;battleRound,minRound,maxRound
# maxHour - since this number, battles will be no longer played. If minHour = 18 and maxHour = 19, players will fight since 18:00 - 18:55
# battleRound: First Round = 0, last round depends on number of participated teams
# in battleRound you can add key word LAST_ROUND - if there will be totally 8 rounds, it will be replaced with 8
# In battleRound you can add also + and -. For example "LAST_ROUND - 1" - if there are 8 rounds then it will give 8-1=7
# If there will be only 1 round and you will put here 0,18,23;LAST_ROUND-1,20,21 then first value on the RIGHT will be taken = 20,21
# You do not have to put all rounds in here, if you will put just 0,18,23;1,19,22 - then for rest of the rounds FIRST VALUE ON THE LEFT FROM PLACE OF THAT ROUND(19,22) will be taken for not mentioned rounds
TournamentBattleHoursPerRound = 0,18,23;LAST_ROUND-1,19,22;LAST_ROUND,17,18

# Days of when Battles have to start for specific Rounds
# Syntax: battleRound,dayIndex,dayIndex,dayIndex;battleRound,dayIndex
# Day Indexes: SUNDAY: 1, MONDAY: 2, TUESDAY: 3, WEDNESDAY: 4, THURSDAY: 5, FRIDAY: 6, SATURDAY: 7
# LAST_ROUND explained in above config will work here too
TournamentBattleDaysPerRound = 0,3,4,5,6;LAST_ROUND-1,6;LAST_ROUND,7

# Minimum time in seconds to schedule next Battle
# After registration is over and first battle dates have to be chosen or when whole round is finished, this will be Minimum delay before first battle
# It should not be too small, because players should be notified when they have to fight
# 172800 = 48 Hours. 129600 = 36 Hours. 86400 = 24 Hours. 43200 = 12 Hours. 21600 = 6 Hours.
TournamentMinScheduleForBattle = 64800

# How many seconds delay there should be between Full battle end(players teleported to town) and next battle start(teleporting next players)
TournamentDelayBetweenBattles = 120

# Losers of which Rounds(Indexed from Last Round, which has index 0) should fight next matches, so we can find out their final places?
# If you want players(losers of round before last) to play for 3rd place, add 1
# If you want players to fight for 5th, 6th, 7th & 8th place, add 2
# If you want players to fight for 3rd-16th place, put: 1;2;3
# Syntax: lostInRoundIndex;lostInRoundIndex;lostInRoundIndex
TournamentDetermineLosersOfRounds = 1

#=====================================================================================
#                                     Notifications
#=====================================================================================
# At what seconds before the Teleportation time, should Fighters get Mail?
# Content of the Mail can be changed in data/strings
# Mail Title String Name:
# Mail Body String Name: 
# 24 Hours = 86400. 12 Hours = 43200. 6 Hours = 21600. 3 Hours = 10800. 1 Hour = 3600. 30 Minutes = 1800. 15 Minutes = 900.
# Split with ;
TournamentMailNotificationSeconds = 86400;10800

# At what seconds before the Teleportation time, should fighters get Private Message
# Message content can be changed in data/strings.
# String Name: Tournament.Notifications.PMAboutMatchDate
# Split with ;
TournamentPMNotificationSeconds = 3600;1800;900;600;300;180;60;30;15;5

# At which chat, all server(except the fighters) should be getting notifications about the fights in .tgl?
# Chat Types: ANNOUNCEMENT, COMMANDCHANNEL_ALL, HERO_VOICE, CRITICAL_ANNOUNCE, BATTLEFIELD
TournamentGlobalNotificationChat = CRITICAL_ANNOUNCE
# Note: Sender of the message can be found in strings under address: Tournament.GlobalNotification.SenderName
# At which seconds before fight starts, announcement should be made?
TournamentGlobalNotificationStartBattleSeconds = 60;30;15
# Should whole world be notified when battle starts(while fighters are teleported, at that moment ppl can observe)?
TournamentGlobalNotificationStartBattle = True
# Should whole world be notified when fight starts(fighters will begin to hit each other)?
TournamentGlobalNotificationStartFight = True
# Should whole world be notified when someone gets killed?
TournamentGlobalNotificationKill = True
# Should whole world be notified about who won the fight?
TournamentGlobalNotificationWonFight = True
# Should whole world be notified about who won the battle?
TournamentGlobalNotificationWonBattle = True

# Results of which places(1st = 1, 2nd = 2 etc) should be announced to the whole world after whole Tournament Ends?
# Syntax: place;place;place - order will be kept
# So if you put here 1;2;3 - system will announce 1st, 2nd and 3rd place winners
# NOTE: numbers higher than 4 are not handled!
TournamentGlobalNotificationAnnounceResultPlaces = 1;2;3

#=====================================================================================
#                                       Battle
#=====================================================================================
# After how many FIGHTS winner should be chosen?
TournamentFightsToResult = 3
# Should Battle be finished, when one of the teams have no longer chance to win?
# For example if TournamentFightsToResult = 5 and one of the teams won 3 fights, should battle end?
TournamentEndBattleOnCertainWin = True

# Minimum Level to Participate
# Put -1 to Disable
TournamentMinLevel = 80

# Minimum Class Level to Participate
# Mage = 1, Wizard = 2, Bishop = 3, Cardinal = 4
# So if you want players to have at least 3rd Class(like Cardinal), write 4
TournamentMinimumClassLevel = 4

# Class Ids that are unable to join the tournament
# Don't write Classes, that have got Class Level below TournamentMinimumClassLevel. If MinClassLevel = 4, don't write Bishop in here
# You can find Id of each class by going to Server Database Table "class_id". For example H_Hawkeye have got id 9
# Syntax: disabledClassId;disabledClassId;disabledClassId
TournamentDisabledClasses = 97;105;112

# Can players in PvP Mode be teleported to the Tournament?
TournamentAllowTeleportPvPFlag = True
# Can players with Karma be teleported to the Tournament?
TournamentAllowTeleportKarma = True

# At least how many members from each team must meet criteria and be online, so the match can start?
TournamentRequiredPlayersToFight = 1

# How many seconds should players have for preparation before first fight?
# During this time player will buff up, choose armor
TournamentFirstFightPreparation = 80
# How many seconds should players have for preparation before next fights?
TournamentNextFightsPreparation = 30

# After how many seconds should winner be chosen if none of the Team died?
# Winner will be chosen by done damage to the opponent team
TournamentMaxFightTimeForResult = 120
# At which seconds, should player be notified when Fight will end by time?
# Split with ;
TournamentFightEndByTimeAnnounceSeconds = 60;30;15;5;4;3;2;1

# What Armor Sets should be able to be chosen before the first round?
# Syntax: helmetId,chestId,legsId,bootsId,glovesId;helmetId,chestId,legsId,bootsId,glovesId
TournamentArmorsToBeChosen = 6378,6373,6374,6375,6376,6377;6382,6379,6380,6381,6377;6386,6383,6384,6384,6385,12811;512,365,388,5765,5777,641;512,2385,2389,5766,5778,641;512,2407,5767,5779,641;547,2382,5768,5780,641;547,2393,5769,5781,641;547,2400,2405,5770,5782,641;2418,374,5771,5783,2498;2418,2394,5772,5784,2498;2418,2408,5773,5785,2498;2419,2383,5774,5786,6377;2419,2395,5775,5787,6377;2419,2409,5776,5788,6377

# What Weapons should be able to be chosen before the first round?
# Syntax: weaponId;weaponId;weaponId
TournamentWeaponsToBeChosen = 6581;6582;6583;6584;6585;6586;6587;6588;6589;6590;6591;6592;6596;6597;6598;6599;6600;6601;6602;6603;6604;6605;6606;6607;6608;6609;6610;7576;7577;7578;6580;9369;9370;9371;9373;9374;9375;9365;9366;9367

# What items should be given to every player when fight starts?
# Syntax: itemId,itemCount;itemId,itemCount
TournamentItemsToEveryPlayer = 920,1;858,2;889,2

# What items should not be given, but they should be allowed to be used during the Tournament?
# Syntax: itemId;itemId;itemId
TournamentOtherItemsAllowedToUse = 1345;9637;1458;1459;1460;1461;1462;1785;3031;8875;8876;8873;8874;2508;5589;10410;10411;10412;1467;2514;3952;6645;6646;6647

# Should players be able to use Npc Buffer during Tournament?
TournamentAllowBuffer = True
# Should Premium Buffs be disabled during Battles?
TournamentPremiumBuffsDisabled = True
# Should Enchanted Buffs be disabled during Battles?
TournamentEnchantedBuffsDisabled = True

# Should Making Attributes be allowed during the Tournament?
TournamentAllowMakingAttribute = False
# Should Enchanting be allowed during the Tournament?
TournamentAllowEnchanting = False

# What Effect should be used for winners after won Battle?
# Syntax: SkillId;HitTime
# That skills will be used by every winner on himself
# Leave it empty to disable
TournamentWonBattleEffect = 6673;1000

# After how many seconds should players teleport to town, when last fight ends?
TournamentBackSeconds = 15

# X;Y;Z Location where players will be coming back after Battle
TournamentBackLocation = 83176;149592;-3472

#=====================================================================================
#                                       Observation
#=====================================================================================
# Should it be possible to observe the fights?
TournamentAllowObserve = True
# Should observers have same notifications as fighters during battle?
TournamentObserversHaveBattleNotifications = True
# Should Status(Party Window) of the fighters be visible for the observers?
TournamentObserversSeeFightersStatus = True
# How much spread(radius) should be observers while being teleported to the location?
TournamentObserversRadius = 100

# Should Score(At the top of the screen) be shown to the observers?
# NOTE: If this is disabled when server starts, only server restart can make it work
TournamentObserversShowScore = True
# How often(Millis) should packet with scores be shown?
# NOTE: Reloadable only by server restart
# NOTE: Do not put this to very low value, because it could lag the server
TournamentObserversScorePacketDelay = 800


#=====================================================================================
#                                       Rewards
#=====================================================================================
# Rewards should be edited in following html files in tournament folder:
# registration.htm, registeredInfo.htm
# Rewards should be given automatically to the teams that end in the x place when the tournaments finishes
# The syntax for this is: place,itemId,itemCount;place,itemId,itemCount
# You can add more than one reward for each team position, but you have to keep the syntax, like: place,itemId,itemCount,itemId,itemCount
TournamentWinnersReward = 1,57,10000;2,57,5000;3,57,2000

# Should every fighter who lost the battle, get extra reward?
# This reward will be given, only to fighters who were present during whole battle
# Syntax: ItemId;Count
TournamentExtraLoserReward = 6673;20