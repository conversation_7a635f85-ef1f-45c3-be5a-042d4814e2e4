
# Configuration actions on an item
# Allow to sell, transfer, send by mail
# Not an item Tradeable
CanBeTradedNoTradeable = false
# Do not sell an item
CanBeTradedNoSellable = false
# Do not pass an item
CanBeTradedNoStoreable = false
# Trade an item
CanBeTradedShadowItem = false
# Items Hero
CanBeTradedHeroWeapon = false
# Allow expanded in KVH augmented an item?
CanBeCwhIsAugmented = false

AllowStartItems = True
#Issue # item is if you create a character to fill in, 4037, 4038 and so on
# Default Adena
StartItemsMageIds = 177, 909, 846, 846, 878, 878, 1101, 1104, 51, 44, 39, 728, 1539, 3947
# Of item is to fill in the config above, 2, 3, etc.
# Default 1
StartItemsMageCount = 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 10, 10, 4999
#Issue # item is if you create a character to fill in, 4037, 4038 and so on
# Default Adena
StartItemsFigtherIds = 219, 68, 909, 846, 846, 878, 878, 2386, 23, 51, 43, 39, 728, 1539, 1835
# Of item is to fill in the config above, 2, 3, etc.
# Default 1
StartItemsFigtherCount = 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 10, 10, 4999
# Include endless shots?
AllowSoulSpiritShotInfinitely = False
# Change infinite arrows?
AllowArrowInfinitely = False

# This is the maximum amount of Adena that character can have in his inventory or warehouse.
# The maximum input amount is 9,223,372,036,854,775,807. (nine quintillion 
# two hundred twenty three quadrillion three hundred seventy two trillion thirty six billion 
# eight hundred fifty four million seven hundred seventy five thousand eight hundred seven)
# Setting negative values (-1 or others) will result in maximum amount available.
# Big values do not cause critical errors, although only 16 digits are visible in the inventory
# (example: 3,372,036,854,775,807 is visible out of 9,223,372,036,854,775,807)
# Default: 99900000000 (99 bilion and 900 milion)
MaxAdena = 99900000000