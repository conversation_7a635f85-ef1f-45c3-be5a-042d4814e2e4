<title>Choose skill to add</title>
<center>
[admin_add_skill 15 16|Charm 16 (Lv 40)]%%
[admin_add_skill 15 17|Charm 17 (Lv 40)]%%
[admin_add_skill 15 18|Charm 18 (Lv 40)]%%
[admin_add_skill 15 19|Charm 19 (Lv 43)]%%
[admin_add_skill 15 20|Charm 20 (Lv 43)]%%
[admin_add_skill 15 21|Charm 21 (Lv 43)]%%
[admin_add_skill 15 22|Charm 22 (Lv 46)]%%
[admin_add_skill 15 23|Charm 23 (Lv 46)]%%
[admin_add_skill 15 24|Charm 24 (Lv 46)]%%
[admin_add_skill 15 25|Charm 25 (Lv 49)]%%
[admin_add_skill 15 26|Charm 26 (Lv 49)]%%
[admin_add_skill 15 27|Charm 27 (Lv 49)]%%
[admin_add_skill 15 28|Charm 28 (Lv 52)]%%
[admin_add_skill 15 29|Charm 29 (Lv 52)]%%
[admin_add_skill 15 30|Charm 30 (Lv 52)]%%
[admin_add_skill 15 31|Charm 31 (Lv 55)]%%
[admin_add_skill 15 32|Charm 32 (Lv 55)]%%
[admin_add_skill 15 33|Charm 33 (Lv 55)]%%
[admin_add_skill 15 34|Charm 34 (Lv 58)]%%
[admin_add_skill 15 35|Charm 35 (Lv 58)]%%
[admin_add_skill 15 36|Charm 36 (Lv 58)]%%
[admin_add_skill 15 37|Charm 37 (Lv 60)]%%
[admin_add_skill 15 38|Charm 38 (Lv 60)]%%
[admin_add_skill 15 39|Charm 39 (Lv 62)]%%
[admin_add_skill 15 40|Charm 40 (Lv 62)]%%
[admin_add_skill 15 41|Charm 41 (Lv 64)]%%
[admin_add_skill 15 42|Charm 42 (Lv 64)]%%
[admin_add_skill 15 43|Charm 43 (Lv 66)]%%
[admin_add_skill 15 44|Charm 44 (Lv 66)]%%
[admin_add_skill 15 45|Charm 45 (Lv 68)]%%
[admin_add_skill 15 46|Charm 46 (Lv 68)]%%
[admin_add_skill 15 47|Charm 47 (Lv 70)]%%
[admin_add_skill 15 48|Charm 48 (Lv 70)]%%
[admin_add_skill 15 49|Charm 49 (Lv 72)]%%
[admin_add_skill 15 50|Charm 50 (Lv 72)]%%
[admin_add_skill 15 51|Charm 51 (Lv 74)]%%
[admin_add_skill 15 52|Charm 52 (Lv 74)]%%
[admin_add_skill 19 1|Double Shot 1 (Lv 40)]%%
[admin_add_skill 19 2|Double Shot 2 (Lv 40)]%%
[admin_add_skill 19 3|Double Shot 3 (Lv 40)]%%
[admin_add_skill 19 4|Double Shot 4 (Lv 43)]%%
[admin_add_skill 19 5|Double Shot 5 (Lv 43)]%%
[admin_add_skill 19 6|Double Shot 6 (Lv 43)]%%
[admin_add_skill 19 7|Double Shot 7 (Lv 46)]%%
[admin_add_skill 19 8|Double Shot 8 (Lv 46)]%%
[admin_add_skill 19 9|Double Shot 9 (Lv 46)]%%
[admin_add_skill 19 10|Double Shot 10 (Lv 49)]%%
[admin_add_skill 19 11|Double Shot 11 (Lv 49)]%%
[admin_add_skill 19 12|Double Shot 12 (Lv 49)]%%
[admin_add_skill 19 13|Double Shot 13 (Lv 52)]%%
[admin_add_skill 19 14|Double Shot 14 (Lv 52)]%%
[admin_add_skill 19 15|Double Shot 15 (Lv 52)]%%
[admin_add_skill 19 16|Double Shot 16 (Lv 55)]%%
[admin_add_skill 19 17|Double Shot 17 (Lv 55)]%%
[admin_add_skill 19 18|Double Shot 18 (Lv 55)]%%
[admin_add_skill 19 19|Double Shot 19 (Lv 58)]%%
[admin_add_skill 19 20|Double Shot 20 (Lv 58)]%%
[admin_add_skill 19 21|Double Shot 21 (Lv 58)]%%
[admin_add_skill 19 22|Double Shot 22 (Lv 60)]%%
[admin_add_skill 19 23|Double Shot 23 (Lv 60)]%%
[admin_add_skill 19 24|Double Shot 24 (Lv 62)]%%
[admin_add_skill 19 25|Double Shot 25 (Lv 62)]%%
[admin_add_skill 19 26|Double Shot 26 (Lv 64)]%%
[admin_add_skill 19 27|Double Shot 27 (Lv 64)]%%
[admin_add_skill 19 28|Double Shot 28 (Lv 66)]%%
[admin_add_skill 19 29|Double Shot 29 (Lv 66)]%%
[admin_add_skill 19 30|Double Shot 30 (Lv 68)]%%
[admin_add_skill 19 31|Double Shot 31 (Lv 68)]%%
[admin_add_skill 19 32|Double Shot 32 (Lv 70)]%%
[admin_add_skill 19 33|Double Shot 33 (Lv 70)]%%
[admin_add_skill 19 34|Double Shot 34 (Lv 72)]%%
[admin_add_skill 19 35|Double Shot 35 (Lv 72)]%%
[admin_add_skill 19 36|Double Shot 36 (Lv 74)]%%
[admin_add_skill 19 37|Double Shot 37 (Lv 74)]%%
[admin_add_skill 21 2|Poison Recovery 2 (Lv 40)]%%
[admin_add_skill 21 3|Poison Recovery 3 (Lv 60)]%%
[admin_add_skill 24 1|Burst Shot 1 (Lv 46)]%%
[admin_add_skill 24 2|Burst Shot 2 (Lv 46)]%%
[admin_add_skill 24 3|Burst Shot 3 (Lv 46)]%%
<br>
<button value="Page2" action="bypass -h admin_skill_index Elf/E_SilverRanger2" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index Elf/E_SilverRanger3" width=40 height=15><br>
<button value="Page4" action="bypass -h admin_skill_index Elf/E_SilverRanger4" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
