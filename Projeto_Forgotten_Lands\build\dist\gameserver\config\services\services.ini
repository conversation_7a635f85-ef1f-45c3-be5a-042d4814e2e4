# ================================================= 
# 					  Properties 				  #
#			Services Configs /PremiumPack	      #
# ================================================= 
# On / off cats (NPC ID 31860) to change professions.
# To turn should put 1,2,3
AllowClassMasters = 0,0,0
# Price change of profession.
# To disable set the values ​​0,0,0.
ClassMastersPrice = 0,0,0
#Item # impose as payment, default Aden
ClassMastersPriceItem = 57

# If set to True, a town near Gatekeeper will spawn EventGatekeeper
AllowEventGatekeeper = False

# If set to True, in each city will spawn near the GC UpdateAnnouncer (News about the current update)
AllowUpdateAnnouncer = False

# Services Car mounts
RideHireEnabled = False

# Tools Display name pet
PetNameChangeEnabled = False
PetNameChangePrice = 100
PetNameChangeItem = 37000

# Use our Improved baby pets
BabyPetExchangeEnabled = False
BabyPetExchangePrice = 100
BabyPetExchangeItem = 37000

# SRS Service
SexChangeEnabled = True
SexChangePrice = 50
SexChangeItem = 37000

# Service change the base class
BaseChangeEnabled = False
BaseChangePrice = 35
BaseChangeItem = 37000

# Announce Hero when log in game ?
AnnounceHero = False
# Announce Lord of the castles when log in game ?
AnnounceLord = False

# Service department saba
SeparateSubEnabled = False
SeparateSubPrice = 35
SeparateSubItem = 37000

# Service color change nickname
NickColorChangeEnabled = True
# The order of colors inverted - BBGGRR
NickColorChangeList = 000000, 000050, 660066, 00FFFF, CC0066, FFCC00, 000066, 006666, 800000, 191971, 00BFFF, 53868B, 00E5EE, 7FFFD4, 54FF9F, FFFF00, EE9A00, 8B5A00, 0080FF, 708090, 05C1FF, 9C9CF2, 05FFB5
NickColorChangePrice = 100
NickColorChangeItem = 57

# Change the color of the title of Service
TitleColorChangeEnabled = True
# The order of colors inverted - BBGGRR
TitleColorChangeList = 000000, 000050, 660066, 00FFFF, CC0066, FFCC00, 000066, 006666, 800000, 191971, 00BFFF, 53868B, 00E5EE, 7FFFD4, 54FF9F, FFFF00, EE9A00, 8B5A00, 0080FF, 708090, 05C1FF, 9C9CF2, 05FFB5
TitleColorChangePrice = 50
TitleColorChangeItem = 57

# Tools sale hero
HeroSellEnabled = False
HeroSellDay = 1, 5, 10, 15, 30
HeroSellPrice = 15, 50, 100, 150, 300
HeroSellItem = 37000, 37000, 37000, 37000, 37000
HeroChat = True
HeroSkills = True
HeroItems = True

# Set Service clean PK's
WashPkEnabled = True
# Item ID for clean PK's
WashPkItem = 6673
# Price Item clean PK's
WashPkPrice = 1
# Item ID for clean Karma
WashKarmaItem = 6673
# Price Item clean Karma. The price is for each 100 karma removed
WashKarmaPrice = 1

# Service password changes through the player .password
# ID / Count = 0 for no fee
ChangePassword = True
ChangePasswordPayId = 0
ChangePassowrdPayCount = 0
ApasswdTemplate = [A-Za-z0-9]{5,16}

# Service expansion of stock
ExpandInventoryEnabled = False
ExpandInventoryPrice = 1
ExpandInventoryItem = 37000
ExpandInventoryMax = 250

# Expansion of personal service warehouse
ExpandWarehouseEnabled = True
ExpandWarehousePrice = 1
ExpandWarehouseItem = 37000

# Service expansion clan warehouse
ExpandCWHEnabled = True
ExpandCWHPrice = 1
ExpandCWHItem = 37000

# Service nick change different by DonationStore.ini
NickChangeEnabled2 = True
NickChangeAllowSimbol2 = False
NickChangePrice2 = 50
NickChangeItem2 = 6673

# Tools Display name clan
ClanNameChangeEnabled2 = True
ClanNameChangePrice2 = 200
ClanNameChangeItem2 = 6673

# Clan promotion NPC
# Enable clan promotion.
EnableClanPromotion = True
# Clan above or equals that level will not be awarded.
MaxClanLevel = 6
# Minimum online members to recive the promotion.
MinOnlineMembers = 1
# Required item to use clan promotion.
ClanPromotionItemId = 57
ClanPromotionItemCOunt = 30000000
# Increase the clan level to
ClanPromotionSetLevel = 5
# Add clan reputation
ClanPromotionAddrep = 0
# Give eggs to the new clan ?
GiveEggsToNewClans = True
# What eggs to reward ?
# Split by id,count;id,count;
# Empty for no rewards.
ClanEggsToReward = 


# Service level changes
LevelChangeEnabled = True
LevelUPChangeMax = 85
LevelUPChangePrice = 1
LevelUPChangeItem = 37000
LevelDownChangeMax = 1
LevelDownChangePrice = 1
LevelDownChangeItem = 37000
# Sale at Pet collars Manager'ov, fully configurable, depending on the wishes of
# Example - Sale Light Purple-Maned Horse Mounting Bracelet
# 7 day - 10COL, 30 - 30COL, forever - 100COL
# SellPets = 37000:10 -> 13022; 37000:30 -> 20030; 37000:100 -> 20029
SellPets = 0

# Service "offline commerce" AKA offtrade
# Use the command. Offline for offline trades
AllowOfflineTrade = True
# Offline trading is allowed only in areas of offshore.xml
AllowOfflineTradeOnlyOffshore = False
# Minimum level for trade
OfflineMinLevel = 40
# Color name of offline players
OfflineTradeNameColor = A0FFFF
# Charge for to offline trade
OfflineTradePrice = 0
OfflineTradePriceItem = 57
# Offline merchant allowed to sit more days, the default 14 days
OfflineTradeDaysToKick = 366
# Restore after restart
OfflineRestoreAfterRestart = True
# Ban on trade in the region is only valid for offline traders
NoTradeOnlyOffline = False
# Ban on trade in the forbidden zone
NoTradeBlockZone = False
# Trade taxes as a percentage
TradeTax = 0.0
# Trade taxes in offshore areas
OffshoreTradeTax = 0.0
# The tax is charged only with offline traders
TradeTaxOnlyOffline = False
# Allowed to trade only at a distance from other merchants
TradeOnlyFar = False
# Search radius traders
TradeRadius = 30
# Set Offline Stores only in Giran Harbor
GiranHarborZone = False
# Set Offline Stores only in Parnassus
ParnassusZone = False
ParnassusNoTax = False
ParnassusPrice = 500000
# In the offshore area of ​​the castle did not pay taxes (otherwise the zone belongs Giran)
NoCastleTaxInOffshore = False
# Minimum level for the trading markets (Kraft \ purchase \ buying)
MinLevelForTrade = 0

#Allow Check for email on EnterWorld ?
AllowMailOption = False

# =================================
#           Away System           #
# =================================
# Away system, to take a rest from the game without logging out
AllowAwayStatus = True
# This service only for premium account holders?
AwayOnlyForPremium = False
# Nick title if in away mode? [ RED = 0000FF ]
AwayTitleColor = 0000FF
# Can use only in peace zone?
AwayOnlyInPeaceZone = True
# If in away mode can aggro mobs?
AwayPlayerTakeAggro = False
# Delay before entering the away mode?
AwayTimer = 4
# Delay before exiting the away mode?
BackTimer = 2

# ================================================= 
#					    Lottery                   #
# ================================================= 
# On \ off lottery manager
AllowLottery = True
# The prize for winning
LotteryPrize = 50000
# Set the value of the lottery Beleth (Default 2000 Adena).
AltLotteryPrice = 2000
# Ticket
LotteryTicketPrice = 2000
# Rtg the prize for matching 5-room
Lottery5NumberRate = 0.6
# Rtg the prize for matching 4 numbers
Lottery4NumberRate = 0.4
# Rtg the prize for matching 3 numbers
Lottery3NumberRate = 0.2
# The prize for matching one or two rooms
Lottery2and1NumberPrize = 200

# ================================================= 
#					    Roullete                  #
# ================================================= 
# On / off roulette
AllowRoulette = False
# The minimum bet.
RouletteMinBet = 1000
# The maximum rate, the difference with a minimum of 6 decimal orders limiting the Martingale system with a series of 20 losses.
RouletteMaxBet = 1000000000

# Service activates in Item Broker - Dealer search things.
UseItemBrokerItemSearch = True

# Leave a character in the game at break the connection.
# Time a character is in the game, the team is set .control
EnableNoCarrier = False
# The minimum allowable time (in seconds)
NoCarrierMinTime = 120
# The maximum allowable time (in seconds)
NoCarrierMaxTime = 300
# Time by default, if not set the player (in seconds)
NoCarrierDefaultTime = 120

# The system rewards for PVP
PkPvPKillEnable = False
PvPKillRewardCount = 1
PvPkillRewardItem = 37000
PkKillRewardCount = 1
PkkillRewardItem = 37000
PkPvPTieifSameIP = True

# DO NOT! CURRENTLY NOT WORKING
# Include multi language support teleporter?
# If True no matter what language the client patch, names of locations will be taken from the server
# If False names will be constantly on the EN., Regardless of the language selected team .lang
AllowMultiLangGatekeeper = False
# Language by default (when creating persanazha and entrance into the world if it has not chosen language)
DefaultGKLang = en

# Fake Players List
AllowFakePlayers = True
FakePlayersSit = False
FakePlayersPercent = 300
AllowParsTotalOnline = True
FirstOnlineUpdate = 1
OnlineUpdate = 1

# =================================
#	      Refferal System	      #
# =================================
# System is working only with AUTO LOOT !!!

# Enable refferal system
EnableReffSystem = False

# Save character info in sql table delay (mins)
refferSystemSaveInterval = 10

# Max refferals that player can recieve
maxRefferalsPerChar = 10

# Minimum online time that player must be online untill can reffer (hours)
MinOnlineTimeForReffering = 1

# Min level for reffering char
MinLevelForReffering = 1

# How many percents to give from the loot
RefferPercentToGive = 0.1

# Item list that percents will be transfered to reffered player.
ReffItemsList = 57,37000


# =================================
#	      	Hair Change	      	  #
# =================================

# Hair change System. Item Id used for payment. Use -1 to disable it
HairChangeItemID = 57

# Amount of items needed for hair change
HairChangeItemCount = 100

# =================================
#			Service Clear PK      #
# =================================
# Cost price for level down a pk
ClearPkPrice = 1
# ID of clear pk system
ClearPkPriceID = 6673
# Points PK relief at a time. (0 to disable the service)
ClearPkCount = 0

# =================================
#		Clear Instance Reuse      #
# =================================
# Cost Item Id and Count for clearing an instance reuse
ClearInstancePriceID = 6673
ClearInstancePriceCount = 1

# =================================
#		Transfer Weapon     #
# =================================
# If the transfer weapon system is enabled
TransferWeaponEnabled = True

# Cost Item Id and Count for transfering special weapons to another character
TransferWeaponPriceID = 6673
TransferWeaponPriceCount = 1

# -----------------------------------------
# Clan promotion NPC
# -----------------------------------------
# Enable clan promotion.
EnableClanPromotion = True
# Clan above or equals that level will not be awarded.
MaxClanLevel = 6
# Minimum online members to recive the promotion.
MinOnlineMembers = 1
# Required item to use clan promotion.
ClanPromotionItemId = 57
ClanPromotionItemCOunt = 30000000
# Increase the clan level to
ClanPromotionSetLevel = 5
# Add clan reputation
ClanPromotionAddrep = 0
# Give eggs to the new clan ?
GiveEggsToNewClans = True
# What eggs to reward ?
# Split by id,count;id,count;
# Empty for no rewards.
ClanEggsToReward = 

# anuncios 
AnnouncePK = False
AnnouncePvP = False

BashEnabled = False
BashSkipDownload = False
BashReloadTime = 24

AllowDelevel = False
DelevelItem = 57
DelevelCount = 1000
DelevelMinLevel = 1

Id_Item_Mall = 57