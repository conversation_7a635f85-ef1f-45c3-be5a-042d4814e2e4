<title>Choose skill to add</title>
<center>
[admin_add_skill 328 1|Wisdom 1 (Lv 76)]%%
[admin_add_skill 329 1|Health 1 (Lv 76)]%%
[admin_add_skill 1354 1|Arcane Protection 1 (Lv 76)]%%
[admin_add_skill 331 1|Skill Mastery 1 (Lv 77)]%%
[admin_add_skill 1353 1|Divine Protection 1 (Lv 77)]%%
[admin_add_skill 1359 1|Block Wind Walk 1 (Lv 77)]%%
[admin_add_skill 336 1|Arcane Wisdom 1 (Lv 78)]%%
[admin_add_skill 1355 1|Prophecy of Water 1 (Lv 78)]%%
</center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
