<html><body><table width=260>
<tr><td width=40>
<button value="Main" action="bypass -h admin_admin" width=40 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df">
</td><td width=180>
<center><PERSON><PERSON><PERSON></center>
</td><td width=40>
<button value="Back" action="bypass -h admin_show_html skills/Kamael/kamael.htm" width=40 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df">
</td></tr></table>
<img src="sek.cbui355" width=260 height=2><br>
<center><table width=260>
<tr><td>Level:</td><td><edit var="level" width=110 height=15></td></tr>
</table><br>
<font color="b5a6c6"><table width=260>
<tr><td>Ancient Sword Mastery (max 45)</td><td><button value="Add" action="bypass -h admin_add_skill 472 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Dark Smash (max 37)</td><td><button value="Add" action="bypass -h admin_add_skill 477 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Life to Soul (max 5)</td><td><button value="Add" action="bypass -h admin_add_skill 502 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Light Armor Mastery (max 50)</td><td><button value="Add" action="bypass -h admin_add_skill 465 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Magic Immunity (max 55)</td><td><button value="Add" action="bypass -h admin_add_skill 466 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Shoulder Charge (max 37)</td><td><button value="Add" action="bypass -h admin_add_skill 494 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Soul Mastery (max 23)</td><td><button value="Add" action="bypass -h admin_add_skill 467 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>True Berserker (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 500 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Vicious Stance (max 20)</td><td><button value="Add" action="bypass -h admin_add_skill 312 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Dark Armor (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 481 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Disarm (max 7)</td><td><button value="Add" action="bypass -h admin_add_skill 485 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Scorn (max 3)</td><td><button value="Add" action="bypass -h admin_add_skill 503 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Slashing Blade (max 31)</td><td><button value="Add" action="bypass -h admin_add_skill 496 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Courage (max 3)</td><td><button value="Add" action="bypass -h admin_add_skill 499 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Storm Assault (max 28)</td><td><button value="Add" action="bypass -h admin_add_skill 493 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Violent Temper (max 12)</td><td><button value="Add" action="bypass -h admin_add_skill 501 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Spread Wing (max 25)</td><td><button value="Add" action="bypass -h admin_add_skill 492 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Blade Rush (max 10)</td><td><button value="Add" action="bypass -h admin_add_skill 495 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Crushing Pain (max 16)</td><td><button value="Add" action="bypass -h admin_add_skill 497 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Contagion(max 12)</td><td><button value="Add" action="bypass -h admin_add_skill 498 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
</table></center></font>
</body></html>
