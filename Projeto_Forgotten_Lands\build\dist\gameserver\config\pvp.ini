# ================================================= 
# 					- KARMA VARS -				  #
# =================================================

# Minimum number of karma for killing one
MinKarma = 240
# The number to divide the sp received by, to calculate karma lost on sp gain
SPDivider = 4
# Karma lost on death
BaseKarmaLost = 1000

# Equipment loss
CanGMDropEquipment = False

# NOTE: Make sure the lists do NOT CONTAIN trailing spaces or spaces between the numbers!
# Lists of items (and pets) which should NEVER be dropped (note, adena will never be dropped) whether on this list or not
ListOfNonDroppableItems = 57
DropAugmented = False

# You can lose things at the death of the monsters
DropOnDie = False
ChanceOfNormalDropBase = 1.
# The odds of losing a thing to die for the PC, it is considered as ChanceOfDropBase + ChanceOfDropMod * PK_COUNT
ChanceOfPKDropBase = 20.
ChanceOfPKsDropMod = 5.
# Odds ratio
ChanceOfDropWeapon = 15
ChanceOfDropEquippment = 19
ChanceOfDropOther = 80

# Item drop related min / max
MaxItemsDroppable = 8
MaxDropThrowDistance = 70

MinPKToDropItems = 9
KarmaNeededToDrop = True

# Length one stays in PvP mode after hitting an innocent (in ms)
PvPTime = 78000