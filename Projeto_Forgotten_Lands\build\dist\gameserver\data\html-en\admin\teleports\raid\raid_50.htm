<html><title>Admin Teleports</title>
<body>
<center>
<table width=260>
<tr><td width=40><button value="Main" action="bypass -h admin_admin" width=40 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td>
<td width=180><center>Raid Boss 50-59</center></td>
<td width=40><button value="Back" action="bypass -h admin_show_html teleports.htm" width=40 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td>
</tr></table>
</center>
<br>
<br>
<center>
<button action="bypass -h admin_move_to 23800 119500 -8976" value="Carnamakos (50)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 169744 11920 -2732" value="Ghost of Peasant Leader (50)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 121872 64032 -3536" value="Messenger of Fairy Queen Berun (50)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 75488 -9360 -2720" value="Carnage Lord Gato (50)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 89904 105712 -3292" value="Cursed Clara (50)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 54651 180269 -4976" value="Lilith's Witch Marilion (50)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 43160 220463 -3680" value="Zaken's Chief Mate Tillion (50)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 125520 27216 -3632" value="Verfa (51)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 150304 67776 -3688" value="Deadman Ereve (51)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 94992 -23168 -2176" value="Captain of Red Flag Shaka (52)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 175712 29856 -3776" value="Grave Robber Kim (52)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 96524 -111070 -3335" value="Gigantic Chaos Golem (52)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 88300 258000 -10200" value="Fafurion's Envoy Pingolpin (52)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 54941 206705 -3728" value="Atraiban (53)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 53517 205413 -3728" value="Magus Kenishee (53)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 124984 43200 -3625" value="Paniel The Unicorn (54)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 113920 52960 -3735" value="Furious Thieles (55)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 125600 50100 -3600" value="Enchanted Forest Watcher Ruell (55)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 135872 94592 -3735" value="Sorcerer Isirr (55)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 125280 102576 -3305" value="Beleth's Seer Sephia (55)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 92544 115232 -3200" value="Black Lily (55)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 165984 88048 -2384" value="Harit Hero Tamash (55)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 42050 208107 -3752" value="Zaken's Butcher Krantz (55)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 85622 88766 -5120" value="Pagan Watcher Cerberon (55)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 104096 -16896 -1803" value="Bandit Leader Barda (55)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 183568 24560 -3184" value="Ghost Knight Kabed (55)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 83174 254428 -10873" value="Eva's Spirit Niniel (55)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 86300 -8200 -3000" value="Refugee Hopeful Leo (56)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 113600 47120 -4640" value="Fairy Queen Timiniel (56)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 166288 68096 -3264" value="Harit Guardian Garangky (56)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 66672 46704 -3920" value="Timak Seer Ragoth (57)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 88532 245798 -10376" value="Eva's Guardian Millenu (58)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 165424 93776 -2992" value="Soulless Wild Boar (59)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 41966 215417 -3728" value="Demon Kurikups (59)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 155000 85400 -3200" value="Abyss Brukunt (59)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 106000 -128000 -3000" value="Captain Of The Ice Queen's Royal Guard (59)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
</center></body></html>