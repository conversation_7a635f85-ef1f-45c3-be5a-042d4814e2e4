<title>Choose skill to add</title>
<center>
[admin_add_skill 101 14|Stun Shot 14 (Lv 49)]%%
[admin_add_skill 101 15|Stun Shot 15 (Lv 49)]%%
[admin_add_skill 101 16|Stun Shot 16 (Lv 52)]%%
[admin_add_skill 101 17|Stun Shot 17 (Lv 52)]%%
[admin_add_skill 101 18|Stun Shot 18 (Lv 52)]%%
[admin_add_skill 101 19|Stun Shot 19 (Lv 55)]%%
[admin_add_skill 101 20|Stun Shot 20 (Lv 55)]%%
[admin_add_skill 101 21|Stun Shot 21 (Lv 55)]%%
[admin_add_skill 101 22|Stun Shot 22 (Lv 58)]%%
[admin_add_skill 101 23|Stun Shot 23 (Lv 58)]%%
[admin_add_skill 101 24|Stun Shot 24 (Lv 58)]%%
[admin_add_skill 101 25|Stun Shot 25 (Lv 60)]%%
[admin_add_skill 101 26|Stun Shot 26 (Lv 60)]%%
[admin_add_skill 101 27|Stun Shot 27 (Lv 62)]%%
[admin_add_skill 101 28|Stun Shot 28 (Lv 62)]%%
[admin_add_skill 101 29|Stun Shot 29 (Lv 64)]%%
[admin_add_skill 101 30|Stun Shot 30 (Lv 64)]%%
[admin_add_skill 101 31|Stun Shot 31 (Lv 66)]%%
[admin_add_skill 101 32|Stun Shot 32 (Lv 66)]%%
[admin_add_skill 101 33|Stun Shot 33 (Lv 68)]%%
[admin_add_skill 101 34|Stun Shot 34 (Lv 68)]%%
[admin_add_skill 101 35|Stun Shot 35 (Lv 70)]%%
[admin_add_skill 101 36|Stun Shot 36 (Lv 70)]%%
[admin_add_skill 101 37|Stun Shot 37 (Lv 72)]%%
[admin_add_skill 101 38|Stun Shot 38 (Lv 72)]%%
[admin_add_skill 101 39|Stun Shot 39 (Lv 74)]%%
[admin_add_skill 101 40|Stun Shot 40 (Lv 74)]%%
[admin_add_skill 102 2|Entangle 2 (Lv 40)]%%
[admin_add_skill 102 3|Entangle 3 (Lv 43)]%%
[admin_add_skill 102 4|Entangle 4 (Lv 46)]%%
[admin_add_skill 102 5|Entangle 5 (Lv 49)]%%
[admin_add_skill 102 6|Entangle 6 (Lv 52)]%%
[admin_add_skill 102 7|Entangle 7 (Lv 55)]%%
[admin_add_skill 102 8|Entangle 8 (Lv 58)]%%
[admin_add_skill 102 9|Entangle 9 (Lv 60)]%%
[admin_add_skill 102 10|Entangle 10 (Lv 62)]%%
[admin_add_skill 102 11|Entangle 11 (Lv 64)]%%
[admin_add_skill 102 12|Entangle 12 (Lv 66)]%%
[admin_add_skill 102 13|Entangle 13 (Lv 68)]%%
[admin_add_skill 102 14|Entangle 14 (Lv 70)]%%
[admin_add_skill 102 15|Entangle 15 (Lv 72)]%%
[admin_add_skill 102 16|Entangle 16 (Lv 74)]%%
[admin_add_skill 113 2|Long Shot 2 (Lv 40)]%%
[admin_add_skill 123 1|Spirit Barrier 1 (Lv 40)]%%
[admin_add_skill 123 2|Spirit Barrier 2 (Lv 49)]%%
[admin_add_skill 123 3|Spirit Barrier 3 (Lv 58)]%%
[admin_add_skill 169 2|Quick Step 2 (Lv 43)]%%
[admin_add_skill 171 2|Esprit 2 (Lv 43)]%%
[admin_add_skill 171 3|Esprit 3 (Lv 46)]%%
[admin_add_skill 171 4|Esprit 4 (Lv 49)]%%
[admin_add_skill 171 5|Esprit 5 (Lv 52)]%%
[admin_add_skill 171 6|Esprit 6 (Lv 62)]%%
[admin_add_skill 171 7|Esprit 7 (Lv 68)]%%
[admin_add_skill 171 8|Esprit 8 (Lv 74)]%%
[admin_add_skill 208 16|Bow Mastery 16 (Lv 40)]%%
[admin_add_skill 208 17|Bow Mastery 17 (Lv 40)]%%
[admin_add_skill 208 18|Bow Mastery 18 (Lv 40)]%%
[admin_add_skill 208 19|Bow Mastery 19 (Lv 43)]%%
[admin_add_skill 208 20|Bow Mastery 20 (Lv 43)]%%
[admin_add_skill 208 21|Bow Mastery 21 (Lv 43)]%%
[admin_add_skill 208 22|Bow Mastery 22 (Lv 46)]%%
[admin_add_skill 208 23|Bow Mastery 23 (Lv 46)]%%
[admin_add_skill 208 24|Bow Mastery 24 (Lv 46)]%%
[admin_add_skill 208 25|Bow Mastery 25 (Lv 49)]%%
[admin_add_skill 208 26|Bow Mastery 26 (Lv 49)]%%
[admin_add_skill 208 27|Bow Mastery 27 (Lv 49)]%%
[admin_add_skill 208 28|Bow Mastery 28 (Lv 52)]%%
[admin_add_skill 208 29|Bow Mastery 29 (Lv 52)]%%
[admin_add_skill 208 30|Bow Mastery 30 (Lv 52)]%%
[admin_add_skill 208 31|Bow Mastery 31 (Lv 55)]%%
[admin_add_skill 208 32|Bow Mastery 32 (Lv 55)]%%
[admin_add_skill 208 33|Bow Mastery 33 (Lv 55)]%%
[admin_add_skill 208 34|Bow Mastery 34 (Lv 58)]%%
[admin_add_skill 208 35|Bow Mastery 35 (Lv 58)]%%
[admin_add_skill 208 36|Bow Mastery 36 (Lv 58)]%%
[admin_add_skill 208 37|Bow Mastery 37 (Lv 60)]%%
[admin_add_skill 208 38|Bow Mastery 38 (Lv 60)]%%
[admin_add_skill 208 39|Bow Mastery 39 (Lv 62)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index Elf/E_SilverRanger" width=40 height=15><br>
<button value="Page2" action="bypass -h admin_skill_index Elf/E_SilverRanger2" width=40 height=15><br>
<button value="Page4" action="bypass -h admin_skill_index Elf/E_SilverRanger4" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
