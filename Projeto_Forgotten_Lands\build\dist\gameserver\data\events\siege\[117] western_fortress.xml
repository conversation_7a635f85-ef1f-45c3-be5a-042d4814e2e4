<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE list SYSTEM "events.dtd">
<list>
	<event id="117" name="Western Fortress" type="SIEGE_EVENT" impl="FortressSiege">
		<on_init>
			<init name="upgradeable_doors" />
			<init name="commander_doors" />
			<init name="enter_doors" />
			<init name="machine_doors" />
			<init name="siege_zones" />
			<init name="flag_zones" />
			<spawn name="flag_pole" />
			
			<spawn name="peace_commanders" />
		</on_init>
		<on_start>
			<despawn name="peace_commanders" />
			<!--Спавним гвардов-->
			<spawn name="default_guards" />
			<spawn name="guards_live_with_c_center" />
			<spawn name="siege_commanders" />
			<spawn name="out_power_units" />
			<spawn name="in_power_units" />
			<if name="old_owner">
				<spawn name="owner_flagdef"/>
				<spawn name="mercenary" />
			</if>
			<else>
				<spawn name="noowner_flagdef" />
			</else>
			<if name="reinforce_1">
				<spawn name="reinforce_1" />
			</if>
			<if name="reinforce_2">
				<spawn name="reinforce_2" />
			</if>
			<if name="dwarvens">
				<spawn name="dwarvens" />
			</if>
			<!--Обновляем двери-->
			<refresh name="enter_doors" />
			<refresh name="upgradeable_doors" />
			<refresh name="commander_doors" />
			<refresh name="machine_doors" />
			<active name="siege_zones" />
			<teleport_players id="attackers"/>
			<teleport_players id="spectators"/>
		</on_start>
		<on_stop>
			<if name="old_owner">
				<despawn name="owner_flagdef"/>
				<despawn name="mercenary" />
			</if>
			<else>
				<despawn name="noowner_flagdef" />
			</else>
			<despawn name="reinforce_1"/>
			<despawn name="reinforce_2"/>
			<despawn name="dwarvens"/>
			<despawn name="default_guards" />
			<despawn name="guards_live_with_c_center" />
			<despawn name="siege_commanders" />
			<despawn name="out_power_units" />
			<despawn name="in_power_units" />

			<start name="registration" />
			<!--Капитаны и менчант-->
			<spawn name="peace_commanders" />
			
			<!--Обновляем двери-->
			<refresh name="enter_doors" />
			<refresh name="upgradeable_doors" />
			<refresh name="commander_doors" />
			<refresh name="machine_doors" />
			<teleport_players id="spectators"/>
			<deactive name="siege_zones" />
		</on_stop>
		<on_time>
			<on time="-3600">
				<announce val="3600" />
			</on>
			<on time="-1800">
				<announce val="1800" />
			</on>
			<on time="-600"> <!--10 min to siege-->
				<announce val="600" />
				<stop name="registration" />
				<despawn name="merchant"/>
			</on>
			<on time="-5">
				<announce val="5" />
			</on>
			<on time="-4">
				<announce val="4" />
			</on>
			<on time="-3">
				<announce val="3" />
			</on>
			<on time="-2">
				<announce val="2" />
			</on>
			<on time="-1">
				<announce val="1" />
			</on>
			<on time="0">
				<start name="event" />
				<play_sound type="SOUND" range="-1" sound="NS20_F" />
			</on>
			<on time="98">
				<play_sound type="SOUND" range="-1" sound="NB02_F" />
			</on>
			<on time="200">
				<play_sound type="SOUND" range="-1" sound="NS19_S01" />
			</on>
			<on time="300">
				<give_item id="-300" count="31" />
			</on>
			<on time="310">
				<play_sound type="SOUND" range="-1" sound="NS12_F" />
			</on>
			<on time="403">
				<play_sound type="SOUND" range="-1" sound="NS19_F" />
			</on>
			<on time="512">
				<play_sound type="SOUND" range="-1" sound="NS18_S01" />
			</on>
			<on time="596">
				<play_sound type="SOUND" range="-1" sound="NS02_F" />
			</on>
			<on time="600">
				<give_item id="-300" count="31" />
			</on>
			<on time="691">
				<play_sound type="SOUND" range="-1" sound="NS12_S01" />
			</on>
			<on time="785">
				<play_sound type="SOUND" range="-1" sound="NS18_F" />
			</on>
			<on time="883">
				<play_sound type="SOUND" range="-1" sound="NS02_S01" />
			</on>
			<on time="900">
				<give_item id="-300" count="31" />
			</on>
			<on time="978">
				<play_sound type="SOUND" range="-1" sound="NS20_F" />
			</on>
			<on time="1076">
				<play_sound type="SOUND" range="-1" sound="NB02_F" />
			</on>
			<on time="1178">
				<play_sound type="SOUND" range="-1" sound="NS19_S01" />
			</on>
			<on time="1200">
				<give_item id="-300" count="31" />
			</on>
			<on time="1288">
				<play_sound type="SOUND" range="-1" sound="NS12_F" />
			</on>
			<on time="1381">
				<play_sound type="SOUND" range="-1" sound="NS19_F" />
			</on>
			<on time="1490">
				<play_sound type="SOUND" range="-1" sound="NS18_S01" />
			</on>
			<on time="1500">
				<give_item id="-300" count="31" />
			</on>
			<on time="1574">
				<play_sound type="SOUND" range="-1" sound="NS02_F" />
			</on>
			<on time="1669">
				<play_sound type="SOUND" range="-1" sound="NS12_S01" />
			</on>
			<on time="1763">
				<play_sound type="SOUND" range="-1" sound="NS18_F" />
			</on>
			<on time="1800">
				<give_item id="-300" count="31" />
			</on>
			<on time="1861">
				<play_sound type="SOUND" range="-1" sound="NS02_S01" />
			</on>
			<on time="1956">
				<play_sound type="SOUND" range="-1" sound="NS20_F" />
			</on>
			<on time="2054">
				<play_sound type="SOUND" range="-1" sound="NB02_F" />
			</on>
			<on time="2100">
				<give_item id="-300" count="31" />
			</on>
			<on time="2156">
				<play_sound type="SOUND" range="-1" sound="NS19_S01" />
			</on>
			<on time="2266">
				<play_sound type="SOUND" range="-1" sound="NS12_F" />
			</on>
			<on time="2359">
				<play_sound type="SOUND" range="-1" sound="NS19_F" />
			</on>
			<on time="2400">
				<give_item id="-300" count="31" />
			</on>
			<on time="2468">
				<play_sound type="SOUND" range="-1" sound="NS18_S01" />
			</on>
			<on time="2552">
				<play_sound type="SOUND" range="-1" sound="NS02_F" />
			</on>
			<on time="2647">
				<play_sound type="SOUND" range="-1" sound="NS12_S01" />
			</on>
			<on time="2700">
				<give_item id="-300" count="31" />
			</on>
			<on time="2741">
				<play_sound type="SOUND" range="-1" sound="NS18_F" />
			</on>
			<on time="2839">
				<play_sound type="SOUND" range="-1" sound="NS02_S01" />
			</on>
			<on time="2934">
				<play_sound type="SOUND" range="-1" sound="NS20_F" />
			</on>
			<on time="3000">
				<give_item id="-300" count="31" />
			</on>
			<on time="3032">
				<play_sound type="SOUND" range="-1" sound="NB02_F" />
			</on>
			<on time="3134">
				<play_sound type="SOUND" range="-1" sound="NS19_S01" />
			</on>
			<on time="3227">
				<play_sound type="SOUND" range="-1" sound="NS12_F" />
			</on>
			<on time="3300">
				<give_item id="-300" count="31" />
			</on>
			<on time="3320">
				<play_sound type="SOUND" range="-1" sound="NS19_F" />
			</on>
			<on time="3429">
				<play_sound type="SOUND" range="-1" sound="NS18_S01" />
			</on>
			<on time="3513">
				<play_sound type="SOUND" range="-1" sound="NS02_F" />
			</on>
			<on time="3600">
				<stop name="event" />
			</on>
		</on_time>
		<objects name="merchant">
			<spawn_ex name="western_fortress_peace_merchant" />
		</objects>
		<objects name="flag_pole">
			<static_object id="23170500" />
		</objects>
		<objects name="combat_flags">
			<combat_flag x="111282" y="-14876" z="-864" />
			<combat_flag x="111362" y="-14863" z="-864" />
			<combat_flag x="111443" y="-14861" z="-864" />
		</objects>
		<objects name="peace_commanders">
			<spawn_ex name="western_fortress_peace_commanders" />
		</objects>
		<objects name="siege_commanders">
			<spawn_ex name="western_fortress_siege_commanders" />
		</objects>
		<objects name="envoy">
			<spawn_ex name="western_fortress_envoy" />
		</objects>
		<objects name="default_guards"> <!--дефольтные гварды спавнятся при любых условиях-->
			<spawn_ex name="western_fortress_default_guards" />
		</objects>
		<objects name="guards_live_with_c_center"> <!--гварды которые будут стоят пока не вворвутся в командый центр-->
			<spawn_ex name="western_fortress_live_with_c_center" />
		</objects>
		<objects name="noowner_flagdef"> <!--гварды охраняемые Флагполе, когда нету овнера-->
			<spawn_ex name="western_fortress_noowner_flagdef" />
		</objects>
		<objects name="owner_flagdef"> <!--гварды охраняемые Флагполе, когда есть овнера-->
			<spawn_ex name="western_fortress_owner_flagdef" />
		</objects>
		<objects name="dwarvens">
			<spawn_ex name="western_fortress_gunner" />
		</objects>
		<objects name="reinforce_1">
			<spawn_ex name="western_fortress_reinforce_1" />
		</objects>
		<objects name="reinforce_2">
			<spawn_ex name="western_fortress_reinforce_2" />
		</objects>
		<objects name="enter_doors">
			<door id="23170001" />
			<door id="23170012" />
		</objects>
		<objects name="machine_doors">
			<door id="23170006" />
			<door id="23170007" />
		</objects>
		<objects name="upgradeable_doors">
			<door id="23170001" />
			<door id="23170012" />
			<door id="23170003" />
			<door id="23170002" />
			<door id="23170010" />
			<door id="23170011" />
		</objects>
		<objects name="commander_doors">
			<door id="23170008" />
			<door id="23170009" />
			<door id="23170005" />
			<door id="23170004" />
		</objects>
		<objects name="mercenary_points">
			<point x="110634" y="-11306" z="-1752" />
			<point x="110120" y="-11093" z="-1656" />
			<point x="109817" y="-11360" z="-1488" />
			<point x="109668" y="-11697" z="-1400" />
			<point x="108377" y="-13026" z="-1000" />
			<point x="108528" y="-13286" z="-992" />
			<point x="110366" y="-13307" z="-992" />
			<point x="110337" y="-14038" z="-992" />
			<point x="110964" y="-14235" z="-992" />
			<point x="111346" y="-14444" z="-992" />
		</objects>
		<objects name="mercenary">
			<spawn_ex name="western_fortress_mercenary" />
		</objects>
		<objects name="out_power_units">
			<spawn_ex name="western_fortress_out_power_units" />
		</objects>
		<objects name="in_power_units">
			<spawn_ex name="western_fortress_in_power_units" />
		</objects>
		<objects name="siege_zones">
			<zone name="[a_o_g_fort_siege]" />
		</objects>
		<objects name="flag_zones">
			<zone name="[a_o_g_fort_hq]" />
		</objects>
	</event>
</list>