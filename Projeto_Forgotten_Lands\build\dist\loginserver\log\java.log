[01.07.25 11:18:27:162]  INFO loginserver.Config: Loaded 127 server names
[01.07.25 11:18:27:322]  INFO loginserver.AuthServer: ============================================================================
[01.07.25 11:18:27:323]  INFO loginserver.AuthServer: Nome: ............................................... LOGIN SERVER
[01.07.25 11:18:27:323]  INFO loginserver.AuthServer: Project Revision: ................................... LII [MultVerso]
[01.07.25 11:18:27:323]  INFO loginserver.AuthServer: Update: ............................................. High Five: Part 5
[01.07.25 11:18:27:323]  INFO loginserver.AuthServer: Numero da Revision:.................................. 2.0
[01.07.25 11:18:27:323]  INFO loginserver.AuthServer: Build Revision: ..................................... MultVerso
[01.07.25 11:18:27:323]  INFO loginserver.AuthServer: Build date: ......................................... 2025.06.30 12:18
[01.07.25 11:18:27:323]  INFO loginserver.AuthServer: Compiler version: ................................... 17.0.7+7 (Eclipse Adoptium)
[01.07.25 11:18:27:324]  INFO loginserver.AuthServer: ============================================================================
[01.07.25 11:18:27:324]  INFO loginserver.Config: Loaded whirlpool2 as default crypt.
[01.07.25 11:18:27:590]  INFO loginserver.Config: Cached 10 KeyPairs for RSA communication
[01.07.25 11:18:27:599]  INFO loginserver.Config: Stored 20 keys for Blowfish communication
[01.07.25 11:18:27:610]  INFO loginserver.GameServerManager: Loaded 1 registered GameServer(s).
[01.07.25 11:18:27:637]  INFO loginserver.AuthServer: Listening for gameservers on 127.0.0.1:9014
[01.07.25 11:18:27:638]  INFO loginserver.AuthServer: Listening for clients on 127.0.0.1:2106
[01.07.25 11:18:27:639]  INFO loginserver.AuthServer: Login Server will automatically restart in 7200 seconds!
