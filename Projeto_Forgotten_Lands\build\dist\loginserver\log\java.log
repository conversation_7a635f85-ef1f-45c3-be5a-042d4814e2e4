[01.07.25 11:55:15:964]  INFO loginserver.Config: Loaded 127 server names
[01.07.25 11:55:16:267]  INFO loginserver.AuthServer: ============================================================================
[01.07.25 11:55:16:268]  INFO loginserver.AuthServer: Nome: ............................................... LOGIN SERVER
[01.07.25 11:55:16:268]  INFO loginserver.AuthServer: Project Revision: ................................... LII [MultVerso]
[01.07.25 11:55:16:268]  INFO loginserver.AuthServer: Update: ............................................. High Five: Part 5
[01.07.25 11:55:16:268]  INFO loginserver.AuthServer: Numero da Revision:.................................. 2.0
[01.07.25 11:55:16:269]  INFO loginserver.AuthServer: Build Revision: ..................................... MultVerso
[01.07.25 11:55:16:269]  INFO loginserver.AuthServer: Build date: ......................................... 2025.07.01 11:54
[01.07.25 11:55:16:269]  INFO loginserver.AuthServer: Compiler version: ................................... 17.0.15+9-LTS-241 (Oracle Corporation)
[01.07.25 11:55:16:269]  INFO loginserver.AuthServer: ============================================================================
[01.07.25 11:55:16:270]  INFO loginserver.Config: Loaded whirlpool2 as default crypt.
[01.07.25 11:55:16:571]  INFO loginserver.Config: Cached 10 KeyPairs for RSA communication
[01.07.25 11:55:16:585]  INFO loginserver.Config: Stored 20 keys for Blowfish communication
[01.07.25 11:55:16:603]  INFO loginserver.GameServerManager: Loaded 1 registered GameServer(s).
[01.07.25 11:55:16:639]  INFO loginserver.AuthServer: Listening for gameservers on 127.0.0.1:9014
[01.07.25 11:55:16:640]  INFO loginserver.AuthServer: Listening for clients on 127.0.0.1:2106
[01.07.25 11:55:16:642]  INFO loginserver.AuthServer: Login Server will automatically restart in 7200 seconds!
[01.07.25 11:55:40:460]  INFO gspackets.AuthRequest: Trying to register gameserver: 1 [127.0.0.1]
[01.07.25 11:55:40:460]  INFO gspackets.AuthRequest: Gameserver registration successful.
[01.07.25 11:55:47:187]  INFO serverpackets.ServerList: IP: 127.0.0.1 Login: siniez Assigned to Proxy: 127.0.0.1
[01.07.25 12:03:29:892] ERROR gameservercon.GameServerCommunication: Gameserver I/O error: Connection reset
[01.07.25 12:03:29:893]  INFO gameservercon.GameServerConnection: Connection with gameserver 1 [Bartz] lost.
[01.07.25 12:03:29:893]  INFO gameservercon.GameServerConnection: Setting gameserver down.
