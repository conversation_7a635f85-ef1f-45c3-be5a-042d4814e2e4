<title>Choose skill to add</title>
<center>
[admin_add_skill 2 5|Confusion 5 (Lv 40)]%%
[admin_add_skill 2 6|Confusion 6 (Lv 43)]%%
[admin_add_skill 2 7|Confusion 7 (Lv 46)]%%
[admin_add_skill 2 8|Confusion 8 (Lv 49)]%%
[admin_add_skill 2 9|Confusion 9 (Lv 52)]%%
[admin_add_skill 2 10|Confusion 10 (Lv 55)]%%
[admin_add_skill 2 11|Confusion 11 (Lv 58)]%%
[admin_add_skill 2 12|Confusion 12 (Lv 60)]%%
[admin_add_skill 2 13|Confusion 13 (Lv 62)]%%
[admin_add_skill 2 14|Confusion 14 (Lv 64)]%%
[admin_add_skill 2 15|Confusion 15 (Lv 66)]%%
[admin_add_skill 2 16|Confusion 16 (Lv 68)]%%
[admin_add_skill 2 17|Confusion 17 (Lv 70)]%%
[admin_add_skill 2 18|Confusion 18 (Lv 72)]%%
[admin_add_skill 2 19|Confusion 19 (Lv 74)]%%
[admin_add_skill 11 1|Trick 1 (Lv 49)]%%
[admin_add_skill 11 2|Trick 2 (Lv 52)]%%
[admin_add_skill 11 3|Trick 3 (Lv 55)]%%
[admin_add_skill 11 4|Trick 4 (Lv 58)]%%
[admin_add_skill 11 5|Trick 5 (Lv 60)]%%
[admin_add_skill 11 6|Trick 6 (Lv 62)]%%
[admin_add_skill 11 7|Trick 7 (Lv 64)]%%
[admin_add_skill 11 8|Trick 8 (Lv 66)]%%
[admin_add_skill 11 9|Trick 9 (Lv 68)]%%
[admin_add_skill 11 10|Trick 10 (Lv 70)]%%
[admin_add_skill 11 11|Trick 11 (Lv 72)]%%
[admin_add_skill 11 12|Trick 12 (Lv 74)]%%
[admin_add_skill 27 6|Unlock 6 (Lv 40)]%%
[admin_add_skill 27 7|Unlock 7 (Lv 43)]%%
[admin_add_skill 27 8|Unlock 8 (Lv 46)]%%
[admin_add_skill 27 9|Unlock 9 (Lv 52)]%%
[admin_add_skill 27 10|Unlock 10 (Lv 55)]%%
[admin_add_skill 27 11|Unlock 11 (Lv 60)]%%
[admin_add_skill 27 12|Unlock 12 (Lv 64)]%%
[admin_add_skill 27 13|Unlock 13 (Lv 68)]%%
[admin_add_skill 27 14|Unlock 14 (Lv 72)]%%
[admin_add_skill 30 1|Backstab 1 (Lv 40)]%%
[admin_add_skill 30 2|Backstab 2 (Lv 40)]%%
[admin_add_skill 30 3|Backstab 3 (Lv 40)]%%
[admin_add_skill 30 4|Backstab 4 (Lv 43)]%%
[admin_add_skill 30 5|Backstab 5 (Lv 43)]%%
[admin_add_skill 30 6|Backstab 6 (Lv 43)]%%
[admin_add_skill 30 7|Backstab 7 (Lv 46)]%%
[admin_add_skill 30 8|Backstab 8 (Lv 46)]%%
[admin_add_skill 30 9|Backstab 9 (Lv 46)]%%
[admin_add_skill 30 10|Backstab 10 (Lv 49)]%%
[admin_add_skill 30 11|Backstab 11 (Lv 49)]%%
[admin_add_skill 30 12|Backstab 12 (Lv 49)]%%
[admin_add_skill 30 13|Backstab 13 (Lv 52)]%%
[admin_add_skill 30 14|Backstab 14 (Lv 52)]%%
[admin_add_skill 30 15|Backstab 15 (Lv 52)]%%
[admin_add_skill 30 16|Backstab 16 (Lv 55)]%%
[admin_add_skill 30 17|Backstab 17 (Lv 55)]%%
[admin_add_skill 30 18|Backstab 18 (Lv 55)]%%
[admin_add_skill 30 19|Backstab 19 (Lv 58)]%%
[admin_add_skill 30 20|Backstab 20 (Lv 58)]%%
[admin_add_skill 30 21|Backstab 21 (Lv 58)]%%
[admin_add_skill 30 22|Backstab 22 (Lv 60)]%%
[admin_add_skill 30 23|Backstab 23 (Lv 60)]%%
[admin_add_skill 30 24|Backstab 24 (Lv 62)]%%
[admin_add_skill 30 25|Backstab 25 (Lv 62)]%%
[admin_add_skill 30 26|Backstab 26 (Lv 64)]%%
[admin_add_skill 30 27|Backstab 27 (Lv 64)]%%
[admin_add_skill 30 28|Backstab 28 (Lv 66)]%%
[admin_add_skill 30 29|Backstab 29 (Lv 66)]%%
[admin_add_skill 30 30|Backstab 30 (Lv 68)]%%
[admin_add_skill 30 31|Backstab 31 (Lv 68)]%%
[admin_add_skill 30 32|Backstab 32 (Lv 70)]%%
[admin_add_skill 30 33|Backstab 33 (Lv 70)]%%
[admin_add_skill 30 34|Backstab 34 (Lv 72)]%%
[admin_add_skill 30 35|Backstab 35 (Lv 72)]%%
[admin_add_skill 30 36|Backstab 36 (Lv 74)]%%
[admin_add_skill 30 37|Backstab 37 (Lv 74)]%%
[admin_add_skill 70 17|Drain Health 17 (Lv 40)]%%
[admin_add_skill 70 18|Drain Health 18 (Lv 40)]%%
[admin_add_skill 70 19|Drain Health 19 (Lv 40)]%%
[admin_add_skill 70 20|Drain Health 20 (Lv 43)]%%
[admin_add_skill 70 21|Drain Health 21 (Lv 43)]%%
[admin_add_skill 70 22|Drain Health 22 (Lv 43)]%%
[admin_add_skill 70 23|Drain Health 23 (Lv 46)]%%
[admin_add_skill 70 24|Drain Health 24 (Lv 46)]%%
[admin_add_skill 70 25|Drain Health 25 (Lv 46)]%%
[admin_add_skill 70 26|Drain Health 26 (Lv 49)]%%
[admin_add_skill 70 27|Drain Health 27 (Lv 49)]%%
[admin_add_skill 70 28|Drain Health 28 (Lv 49)]%%
[admin_add_skill 70 29|Drain Health 29 (Lv 52)]%%
[admin_add_skill 70 30|Drain Health 30 (Lv 52)]%%
[admin_add_skill 70 31|Drain Health 31 (Lv 52)]%%
[admin_add_skill 70 32|Drain Health 32 (Lv 55)]%%
[admin_add_skill 70 33|Drain Health 33 (Lv 55)]%%
[admin_add_skill 70 34|Drain Health 34 (Lv 55)]%%
[admin_add_skill 70 35|Drain Health 35 (Lv 58)]%%
[admin_add_skill 70 36|Drain Health 36 (Lv 58)]%%
[admin_add_skill 70 37|Drain Health 37 (Lv 58)]%%
[admin_add_skill 70 38|Drain Health 38 (Lv 60)]%%
<br>
<button value="Page2" action="bypass -h admin_skill_index DarkElf/DE_AbyssWalker2" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index DarkElf/DE_AbyssWalker3" width=40 height=15><br>
<button value="Page4" action="bypass -h admin_skill_index DarkElf/DE_AbyssWalker4" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
