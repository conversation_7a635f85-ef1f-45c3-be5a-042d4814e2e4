<title>Choose skill to add</title>
<center>
[admin_add_skill 42 1|Sweeper 1 (Lv 10)]%%
[admin_add_skill 141 1|Weapon Mastery 1 (Lv 5)]%%
[admin_add_skill 141 2|Weapon Mastery 2 (Lv 10)]%%
[admin_add_skill 141 3|Weapon Mastery 3 (Lv 15)]%%
[admin_add_skill 142 1|Armor Mastery 1 (Lv 5)]%%
[admin_add_skill 142 2|Armor Mastery 2 (Lv 10)]%%
[admin_add_skill 142 3|Armor Mastery 3 (Lv 10)]%%
[admin_add_skill 142 4|Armor Mastery 4 (Lv 15)]%%
[admin_add_skill 142 5|Armor Mastery 5 (Lv 15)]%%
[admin_add_skill 150 1|Weight Limit 1 (Lv 10)]%%
[admin_add_skill 172 1|Create Item 1 (Lv 5)]%%
[admin_add_skill 194 1|Lucky 1 (Lv 1)]%%
[admin_add_skill 254 1|Spoil 1 (Lv 10)]%%
</center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
