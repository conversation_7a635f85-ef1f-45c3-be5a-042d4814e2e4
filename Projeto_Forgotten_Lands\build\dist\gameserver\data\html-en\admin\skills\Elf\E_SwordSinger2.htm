<title>Choose skill to add</title>
<center>
[admin_add_skill 58 46|Elemental Heal 46 (Lv 66)]%%
[admin_add_skill 58 47|Elemental Heal 47 (Lv 66)]%%
[admin_add_skill 58 48|Elemental Heal 48 (Lv 68)]%%
[admin_add_skill 58 49|Elemental Heal 49 (Lv 68)]%%
[admin_add_skill 58 50|Elemental Heal 50 (Lv 70)]%%
[admin_add_skill 58 51|Elemental Heal 51 (Lv 70)]%%
[admin_add_skill 58 52|Elemental Heal 52 (Lv 72)]%%
[admin_add_skill 58 53|Elemental Heal 53 (Lv 72)]%%
[admin_add_skill 58 54|Elemental Heal 54 (Lv 74)]%%
[admin_add_skill 58 55|Elemental Heal 55 (Lv 74)]%%
[admin_add_skill 61 2|Cure Bleeding 2 (Lv 46)]%%
[admin_add_skill 61 3|Cure Bleeding 3 (Lv 62)]%%
[admin_add_skill 98 1|Sword Symphony 1 (Lv 55)]%%
[admin_add_skill 98 2|Sword Symphony 2 (Lv 60)]%%
[admin_add_skill 98 3|Sword Symphony 3 (Lv 64)]%%
[admin_add_skill 98 4|Sword Symphony 4 (Lv 68)]%%
[admin_add_skill 98 5|Sword Symphony 5 (Lv 72)]%%
[admin_add_skill 102 2|Entangle 2 (Lv 40)]%%
[admin_add_skill 102 3|Entangle 3 (Lv 43)]%%
[admin_add_skill 102 4|Entangle 4 (Lv 46)]%%
[admin_add_skill 102 5|Entangle 5 (Lv 49)]%%
[admin_add_skill 102 6|Entangle 6 (Lv 52)]%%
[admin_add_skill 102 7|Entangle 7 (Lv 55)]%%
[admin_add_skill 102 8|Entangle 8 (Lv 58)]%%
[admin_add_skill 102 9|Entangle 9 (Lv 60)]%%
[admin_add_skill 102 10|Entangle 10 (Lv 62)]%%
[admin_add_skill 102 11|Entangle 11 (Lv 64)]%%
[admin_add_skill 102 12|Entangle 12 (Lv 66)]%%
[admin_add_skill 102 13|Entangle 13 (Lv 68)]%%
[admin_add_skill 102 14|Entangle 14 (Lv 70)]%%
[admin_add_skill 102 15|Entangle 15 (Lv 72)]%%
[admin_add_skill 102 16|Entangle 16 (Lv 74)]%%
[admin_add_skill 123 1|Spirit Barrier 1 (Lv 40)]%%
[admin_add_skill 123 2|Spirit Barrier 2 (Lv 49)]%%
[admin_add_skill 123 3|Spirit Barrier 3 (Lv 58)]%%
[admin_add_skill 147 15|Magic Resistance 15 (Lv 40)]%%
[admin_add_skill 147 16|Magic Resistance 16 (Lv 40)]%%
[admin_add_skill 147 17|Magic Resistance 17 (Lv 40)]%%
[admin_add_skill 147 18|Magic Resistance 18 (Lv 43)]%%
[admin_add_skill 147 19|Magic Resistance 19 (Lv 43)]%%
[admin_add_skill 147 20|Magic Resistance 20 (Lv 43)]%%
[admin_add_skill 147 21|Magic Resistance 21 (Lv 46)]%%
[admin_add_skill 147 22|Magic Resistance 22 (Lv 46)]%%
[admin_add_skill 147 23|Magic Resistance 23 (Lv 46)]%%
[admin_add_skill 147 24|Magic Resistance 24 (Lv 49)]%%
[admin_add_skill 147 25|Magic Resistance 25 (Lv 49)]%%
[admin_add_skill 147 26|Magic Resistance 26 (Lv 49)]%%
[admin_add_skill 147 27|Magic Resistance 27 (Lv 52)]%%
[admin_add_skill 147 28|Magic Resistance 28 (Lv 52)]%%
[admin_add_skill 147 29|Magic Resistance 29 (Lv 52)]%%
[admin_add_skill 147 30|Magic Resistance 30 (Lv 55)]%%
[admin_add_skill 147 31|Magic Resistance 31 (Lv 55)]%%
[admin_add_skill 147 32|Magic Resistance 32 (Lv 55)]%%
[admin_add_skill 147 33|Magic Resistance 33 (Lv 58)]%%
[admin_add_skill 147 34|Magic Resistance 34 (Lv 58)]%%
[admin_add_skill 147 35|Magic Resistance 35 (Lv 58)]%%
[admin_add_skill 147 36|Magic Resistance 36 (Lv 60)]%%
[admin_add_skill 147 37|Magic Resistance 37 (Lv 60)]%%
[admin_add_skill 147 38|Magic Resistance 38 (Lv 62)]%%
[admin_add_skill 147 39|Magic Resistance 39 (Lv 62)]%%
[admin_add_skill 147 40|Magic Resistance 40 (Lv 64)]%%
[admin_add_skill 147 41|Magic Resistance 41 (Lv 64)]%%
[admin_add_skill 147 42|Magic Resistance 42 (Lv 66)]%%
[admin_add_skill 147 43|Magic Resistance 43 (Lv 66)]%%
[admin_add_skill 147 44|Magic Resistance 44 (Lv 68)]%%
[admin_add_skill 147 45|Magic Resistance 45 (Lv 68)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index Elf/E_SwordSinger" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index Elf/E_SwordSinger3" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
