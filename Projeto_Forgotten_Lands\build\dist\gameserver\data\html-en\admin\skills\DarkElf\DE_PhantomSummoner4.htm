<title>Choose skill to add</title>
<center>
[admin_add_skill 1127 45|<PERSON><PERSON><PERSON> 45 (Lv 74)]%%
[admin_add_skill 1128 5|Summon Shadow 5 (Lv 40)]%%
[admin_add_skill 1128 6|Summon Shadow 6 (Lv 44)]%%
[admin_add_skill 1128 7|Summon Shadow 7 (Lv 48)]%%
[admin_add_skill 1128 8|Summon Shadow 8 (Lv 52)]%%
[admin_add_skill 1128 9|Summon Shadow 9 (Lv 56)]%%
[admin_add_skill 1128 10|Summon Shadow 10 (Lv 58)]%%
[admin_add_skill 1128 11|Summon Shadow 11 (Lv 60)]%%
[admin_add_skill 1128 12|Summon Shadow 12 (Lv 62)]%%
[admin_add_skill 1128 13|Summon Shadow 13 (Lv 64)]%%
[admin_add_skill 1128 14|Summon Shadow 14 (Lv 66)]%%
[admin_add_skill 1128 15|Summon Shadow 15 (Lv 68)]%%
[admin_add_skill 1128 16|Summon Shadow 16 (Lv 70)]%%
[admin_add_skill 1128 17|Summon Shadow 17 (Lv 72)]%%
[admin_add_skill 1128 18|Summon Shadow 18 (Lv 74)]%%
[admin_add_skill 1139 1|Servitor Magic Shield 1 (Lv 44)]%%
[admin_add_skill 1139 2|Servitor Magic Shield 2 (Lv 52)]%%
[admin_add_skill 1140 1|Servitor Physical Shield 1 (Lv 40)]%%
[admin_add_skill 1140 2|Servitor Physical Shield 2 (Lv 48)]%%
[admin_add_skill 1140 3|Servitor Physical Shield 3 (Lv 56)]%%
[admin_add_skill 1141 1|Servitor Haste 1 (Lv 44)]%%
[admin_add_skill 1141 2|Servitor Haste 2 (Lv 52)]%%
[admin_add_skill 1146 2|Mighty Servitor 2 (Lv 48)]%%
[admin_add_skill 1146 3|Mighty Servitor 3 (Lv 56)]%%
[admin_add_skill 1228 5|Summon Silhouette 5 (Lv 40)]%%
[admin_add_skill 1228 6|Summon Silhouette 6 (Lv 44)]%%
[admin_add_skill 1228 7|Summon Silhouette 7 (Lv 48)]%%
[admin_add_skill 1228 8|Summon Silhouette 8 (Lv 52)]%%
[admin_add_skill 1228 9|Summon Silhouette 9 (Lv 56)]%%
[admin_add_skill 1228 10|Summon Silhouette 10 (Lv 58)]%%
[admin_add_skill 1228 11|Summon Silhouette 11 (Lv 60)]%%
[admin_add_skill 1228 12|Summon Silhouette 12 (Lv 62)]%%
[admin_add_skill 1228 13|Summon Silhouette 13 (Lv 64)]%%
[admin_add_skill 1228 14|Summon Silhouette 14 (Lv 66)]%%
[admin_add_skill 1228 15|Summon Silhouette 15 (Lv 68)]%%
[admin_add_skill 1228 16|Summon Silhouette 16 (Lv 70)]%%
[admin_add_skill 1228 17|Summon Silhouette 17 (Lv 72)]%%
[admin_add_skill 1228 18|Summon Silhouette 18 (Lv 74)]%%
[admin_add_skill 1262 1|Transfer Pain 1 (Lv 40)]%%
[admin_add_skill 1262 2|Transfer Pain 2 (Lv 48)]%%
[admin_add_skill 1262 3|Transfer Pain 3 (Lv 56)]%%
[admin_add_skill 1262 4|Transfer Pain 4 (Lv 58)]%%
[admin_add_skill 1262 5|Transfer Pain 5 (Lv 70)]%%
[admin_add_skill 1278 1|Summon Soulless 1 (Lv 40)]%%
[admin_add_skill 1278 2|Summon Soulless 2 (Lv 44)]%%
[admin_add_skill 1278 3|Summon Soulless 3 (Lv 48)]%%
[admin_add_skill 1278 4|Summon Soulless 4 (Lv 52)]%%
[admin_add_skill 1278 5|Summon Soulless 5 (Lv 56)]%%
[admin_add_skill 1278 6|Summon Soulless 6 (Lv 58)]%%
[admin_add_skill 1278 7|Summon Soulless 7 (Lv 60)]%%
[admin_add_skill 1278 8|Summon Soulless 8 (Lv 62)]%%
[admin_add_skill 1278 9|Summon Soulless 9 (Lv 64)]%%
[admin_add_skill 1278 10|Summon Soulless 10 (Lv 66)]%%
[admin_add_skill 1278 11|Summon Soulless 11 (Lv 68)]%%
[admin_add_skill 1278 12|Summon Soulless 12 (Lv 70)]%%
[admin_add_skill 1278 13|Summon Soulless 13 (Lv 72)]%%
[admin_add_skill 1278 14|Summon Soulless 14 (Lv 74)]%%
[admin_add_skill 1281 1|Summon Spark Cubic 1 (Lv 40)]%%
[admin_add_skill 1281 2|Summon Spark Cubic 2 (Lv 44)]%%
[admin_add_skill 1281 3|Summon Spark Cubic 3 (Lv 48)]%%
[admin_add_skill 1281 4|Summon Spark Cubic 4 (Lv 52)]%%
[admin_add_skill 1281 5|Summon Spark Cubic 5 (Lv 56)]%%
[admin_add_skill 1281 6|Summon Spark Cubic 6 (Lv 60)]%%
[admin_add_skill 1281 7|Summon Spark Cubic 7 (Lv 64)]%%
[admin_add_skill 1281 8|Summon Spark Cubic 8 (Lv 68)]%%
[admin_add_skill 1281 9|Summon Spark Cubic 9 (Lv 72)]%%
[admin_add_skill 1299 1|Servitor Empower 1 (Lv 52)]%%
[admin_add_skill 1299 2|Servitor Empower 2 (Lv 70)]%%
[admin_add_skill 1300 1|Servitor Cure 1 (Lv 40)]%%
[admin_add_skill 1300 2|Servitor Cure 2 (Lv 48)]%%
[admin_add_skill 1300 3|Servitor Cure 3 (Lv 60)]%%
[admin_add_skill 1301 1|Servitor Blessing 1 (Lv 62)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index DarkElf/DE_PhantomSummoner" width=40 height=15><br>
<button value="Page2" action="bypass -h admin_skill_index DarkElf/DE_PhantomSummoner2" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index DarkElf/DE_PhantomSummoner3" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
