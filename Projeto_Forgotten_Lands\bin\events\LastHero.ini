#==========================================
#         Last Hero Event Settings        #
#==========================================
# Award - ID things (to be filled through ;)
#Example 57, 4037
LastHero_bonus_id = 6673
# The number of items for each dead
# If the number is not an integer, then the issuance of awards is round up (to be completed by ;)
#Example 5000 10000
LastHero_bonus_count = 1
# Multiplied by lvl killed?
LastHero_rate = True
# Award - ID things (to be filled through ;)
#Example 57, 4037
LastHero_final_bonus_id = 6673
# The number of items received by survivors (mi) charom (s) after the opening event (to be completed by ;)
#Sample 10000, 50000
LastHero_final_bonus_count = 7
# Multiplied by lvl reward?
LastHero_rate_final = False
# Time to start
LastHero_time = 5
# Time in seconds in paralysis
LastHero_Time_Paralyze = 30
# Length rounds per minute
LastHero_Time_Batle = 15
# Whether to use the days of the month in the settings of the start time?
LastHero_Allow_Calendar_Day = False
# Settings start time
# Format settings, day, hour, minute, group, etc.
# Ensure that the start-up time and will cover time is coming Event item
# Ie next time should be no closer than TvT_Time_Batle + 3 (Registration) + 5 (TP + validation)
# Groups:
# C 1st to 85th LVL = -1
# C 20th to 29th LVL = 1
# From 30th to 39th LVL = 2
# C 40 th to 51 th LVL = 3
# From 52 th to 61 th LVL = 4
# C 62 th to the 75 th of LVL = 5
# From 76 th to 85 th LVL = 6
LastHero_Time_Start = 11,30,6;15,30,6;19,30,6;23,30,6
# Remove all of the effects before eventa
LastHero_DisableEffect = True
# Remove all of the effects of c Samonov beginning eventa
LastHero_DisablePetEffect = True
# Let's aura hero winner?
LastHero_Allow_Hero_Aura = False