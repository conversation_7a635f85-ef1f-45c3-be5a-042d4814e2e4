# ====================================================================================
# 						            Residences
# ====================================================================================
# Should dead players or those in Safe Zones be able to get Fame during Sieges and Territory Wars?
SiegeAllowFameWhileDead = False
SiegeAllowFameInSafe = False

# What should be additional rewards while getting Fame?
# This reward will be given to players taking part in Territory Wars and all kind of Sieges
# Syntax: itemId,itemCount;itemId,itemCount
SiegeRewardsNearFame = 9912,50;6673,1


# ====================================================================================
# 						Clan Hall Auction bid limits								 #
# ====================================================================================
# Restrictions on buying CS for different grades									 #
# MinClanLevel - the minimum level of the clan to buy KH							 #
# MinClanMembers - the minimum number of members of the clan to buy KH				 #
# MinClanMembersAvgLevel - a minimum average of all members for the purchase of CH	 #
# ====================================================================================
ClanHallBid_Grade1_MinClanLevel = 3
ClanHallBid_Grade1_MinClanMembers = 15
ClanHallBid_Grade1_MinClanMembersAvgLevel = 70

ClanHallBid_Grade2_MinClanLevel = 4
ClanHallBid_Grade2_MinClanMembers = 20
ClanHallBid_Grade2_MinClanMembersAvgLevel = 70

ClanHallBid_Grade3_MinClanLevel = 5
ClanHallBid_Grade3_MinClanMembers = 30
ClanHallBid_Grade3_MinClanMembersAvgLevel = 70

# ====================================================================================
# 									Sieges				 					
# ====================================================================================

# Multipliers for rent KH and cost functions residences.
# It is recommended to establish equal rate adena.
ResidenceLeaseMultiplier = 1.
ResidenceLeaseFuncMultiplier = 1.

# Alt enable custom fame rewards in siege/fortress
AltEnableCustomFame = True

# Alt fame count for castles
CastleFame = 120

# Alt fame count for fortresses
FortressFame = 60

# How much time can stay Territory Ward out of zone
IntervalFlagDrop = 20

# It gives to the winner clan of the siege x reputation if > 0
SiegeWinnerReputationReward = 20000

# Return the flag if the flag holder leaves the siege zone?
ReturnFlagOnSiegeZoneLeave = False

# Returns the flag to their original castles just before the territory war begins. Meaning, reseting the wards on every TW
ReturnWardsWhenTWStarts = True

# ====================================================================================
# 									Castle				 					
# ====================================================================================
# From time to time, which is a validation of the siege of castles (TV)
CastleValidationDate = 1, 1, 2022

# A lot of time that is generated after the siege of the castle, gives user the choice of the time of the siege
CastleSelectHours = 16,20,22

# Castle siege, period on weeks
CastleSiegeIntervalWeeks = 1


# ====================================================================================
# 						            Fortress
# ====================================================================================
# Should Fee be taken, from the first clan or player who registers to attack Fortress?
AllowStartFortressSiegeFee = True

# What should be the Price that first Clan who signs for Fortress Siege should pay?
# NOTE: If AllowCustomSiegeDates is True, Price is 0
StartFortressSiegePriceId = 57
StartFortressSiegePriceAmount = 250000

# Should Single Players be able to register for Fortress Sieges?
# Default: False
FortressSiegeAllowSinglePlayers = False

# Return Fort flag if player holding it leave the zone.
FortFlagReturnOnLeaveZone = False