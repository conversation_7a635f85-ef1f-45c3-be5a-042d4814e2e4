<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE list SYSTEM "events.dtd">
<list>
	<event id="88" name="Rune Dominion" type="SIEGE_EVENT" impl="DominionSiege">
		<parameter name="day_of_week" value="7" />  <!--SATURDAY-->
		<parameter name="hour_of_day" value="20" />
		<on_init>
			<init name="doors" />
			<init name="doors2" />
			<init name="catapult_doors" />
			<init name="siege_zones" />
			<init name="flag_zones" />
		</on_init>
		<on_start>
			<refresh name="doors" />
			<refresh name="mass_gatekeeper" />
			<spawn name="control_towers" />
			<spawn name="catapult" />
			<spawn name="catapult_doors" />
			<spawn name="spawn" />

			<active name="siege_zones"/>

			<close name="doors2" />
			<teleport_players id="others" />
		</on_start>
		<on_stop>
			<refresh name="doors" />
			<refresh name="mass_gatekeeper" />
			<spawn name="control_towers" />
			<despawn name="catapult" />
			<despawn name="spawn" />

			<teleport_players id="others" />
			<deactive name="siege_zones" />

			<open name="doors2" />
		</on_stop>
		<on_time>
			<on time="0">
				<start name="event" />
			</on>
			<on time="300">
				<give_item id="-300" count="-1" />
			</on>
			<on time="600">
				<give_item id="-300" count="-1" />
			</on>
			<on time="900">
				<give_item id="-300" count="-1" />
			</on>
			<on time="1200">
				<give_item id="-300" count="-1" />
			</on>
			<on time="1500">
				<give_item id="-300" count="-1" />
			</on>
			<on time="1800">
				<give_item id="-300" count="-1" />
			</on>
			<on time="2100">
				<give_item id="-300" count="-1" />
			</on>
			<on time="2400">
				<give_item id="-300" count="-1" />
			</on>
			<on time="2700">
				<give_item id="-300" count="-1" />
			</on>
			<on time="3000">
				<give_item id="-300" count="-1" />
			</on>
			<on time="3300">
				<give_item id="-300" count="-1" />
			</on>
			<on time="3600">
				<give_item id="-300" count="-1" />
			</on>
			<on time="3900">
				<give_item id="-300" count="-1" />
			</on>
			<on time="4200">
				<give_item id="-300" count="-1" />
			</on>
			<on time="4500">
				<give_item id="-300" count="-1" />
			</on>
			<on time="4800">
				<give_item id="-300" count="-1" />
			</on>
			<on time="5100">
				<give_item id="-300" count="-1" />
			</on>
			<on time="5400">
				<give_item id="-300" count="-1" />
			</on>
			<on time="5700">
				<give_item id="-300" count="-1" />
			</on>
			<on time="6000">
				<give_item id="-300" count="-1" />
			</on>
			<on time="6300">
				<give_item id="-300" count="-1" />
			</on>
			<on time="6600">
				<give_item id="-300" count="-1" />
			</on>
			<on time="6900">
				<give_item id="-300" count="-1" />
			</on>
			<on time="7200">
				<stop name="event" />
			</on>
		</on_time>
		<objects name="doors">
			<door id="20160001" />
			<door id="20160002" />
			<door id="20160003" />
			<door id="20160004" />
			<door id="20160006" />
			<door id="20160007" />
			<door id="20160008" />
			<door id="20160009" />
			<door id="22160001" />
			<door id="22160012" />
			<door id="22160011" />
			<door id="22160010" />
			<door id="22160006" />
			<door id="22160007" />
		</objects>
		<objects name="catapult_doors">
			<door id="20160001" />
			<door id="20160002" />
			<door id="20160003" />
			<door id="20160004" />
			<door id="20160006" />
		</objects>
		<objects name="doors2">
			<door id="20160005" />
		</objects>
		<objects name="siege_zones">
			<zone name="[rune_castle_siege]" />
			<zone name="[rune_fort_siege]" />
		</objects>
		<objects name="flag_zones">
			<zone name="[gludio_castle_hq]" />
			<zone name="[dion_castle_hq]" />
			<zone name="[giran_castle_hq]" />
			<zone name="[oren_castle_hq]" />
			<zone name="[aden_castle_hq]" />
			<zone name="[innadrile_castle_hq]" />
			<zone name="[godad_castle_hq]" />
			<zone name="[rune_castle_hq]" />
			<zone name="[schuttgart_castle_hq]" />
			<zone name="[gludio_fort_a_hq]" />
			<zone name="[dion_fort_hq]" />
			<zone name="[giran_fort_hq]" />
			<zone name="[oren_fort_hq]" />
			<zone name="[aden_fort_a_hq]" />
			<zone name="[inna_fort_hq]" />
			<zone name="[godad_fort_hq]" />
			<zone name="[rune_fort_hq]" />
			<zone name="[schutt_fort_hq]" />
		</objects>
		<objects name="territory_npc">
			<spawn_ex name="territory_manager_8" />
		</objects>
		<objects name="catapult">
			<siege_toggle_npc id="36506" fake_id="36588" x="70409" y="-60006" z="-2784" hp="339687" />
		</objects>
		<objects name="ward_81">
			<territory_ward item_id="13560" npc_id="36572" x="11300" y="-49470" z="-536" /> <!--Gludio Ward-->
		</objects>
		<objects name="ward_82">
			<territory_ward item_id="13561" npc_id="36573" x="11300" y="-49390" z="-536" /> <!--Dion Ward-->
		</objects>
		<objects name="ward_83">
			<territory_ward item_id="13562" npc_id="36574" x="11300" y="-49310" z="-536" /> <!--Giran Ward-->
		</objects>
		<objects name="ward_84">
			<territory_ward item_id="13563" npc_id="36575" x="11300" y="-49230" z="-536" /> <!--Oren Ward-->
		</objects>
		<objects name="ward_85">
			<territory_ward item_id="13564" npc_id="36576" x="11300" y="-48910" z="-536" /> <!--Aden Ward-->
		</objects>
		<objects name="ward_86">
			<territory_ward item_id="13565" npc_id="36577" x="11300" y="-48990" z="-536" /> <!--Innadril Ward-->
		</objects>
		<objects name="ward_87">
			<territory_ward item_id="13566" npc_id="36578" x="11300" y="-49070" z="-536" /> <!--Goddard Ward-->
		</objects>
		<objects name="ward_88">
			<territory_ward item_id="13567" npc_id="36579" x="11300" y="-49150" z="-536" /> <!--Rune Ward-->
		</objects>
		<objects name="ward_89">
			<territory_ward item_id="13568" npc_id="36580" x="11300" y="-48830" z="-536" /> <!--Schuttgart Ward-->
		</objects>
		<objects name="spawn">
			<spawn_ex name="rune_dominion_siege" />
		</objects>
	</event>
</list>