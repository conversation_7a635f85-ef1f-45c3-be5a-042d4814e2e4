<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE list SYSTEM "../events.dtd">
<list>
	<event id="2" name="Team VS Team" type="FIGHT_CLUB_EVENT" impl="fightclub.TeamVSTeam">
		<parameter name="icon" value="l2ui_ch3.msnlogin01"/>
		<parameter name="desc" value="Team Event. Your goal is to kill players from opposite teams. Each kill brings score to your Team. Respawn after 5 seconds. Event Time: 10 minutes"/>
		<parameter name="roundRunTime" value="600"/>
		<parameter name="roundEvent" value="false"/>
		<parameter name="isAutoTimed" value="true"/>
		<parameter name="autoTimes" value="01:00,09:00,17:00"/>
		<parameter name="teamed" value="true"/>
		<parameter name="buffer" value="false"/>
		<parameter name="fighterBuffs" value="1204,2;1086,2;1068,3;1045,6;1048,6;1040,3;1062,2;1036,2"/>
		<parameter name="mageBuffs" value="1204,2;1085,3;1059,3;1045,6;1048,6;1040,3;1062,2;1036,2"/>
		<parameter name="rootBetweenRounds" value="true"/>
		<parameter name="excludedClasses" value=""/>
		<parameter name="respawnTime" value="5"/>
		<parameter name="ressAllowed" value="false"/>
		<parameter name="showPersonality" value="true"/>
		<parameter name="badgesKillPlayer" value="0"/>
		<parameter name="badgesKillPet" value="0"/>
		<parameter name="badgesDie" value="0"/>
		<parameter name="badgesWin" value="6"/>
		<parameter name="badgesLose" value="2"/>
		<parameter name="topKillerReward" value="3"/>
	</event>
</list>