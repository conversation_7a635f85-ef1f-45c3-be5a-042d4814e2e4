	// Message: Welcome to the World of Lineage II.
	WELCOME_TO_THE_WORLD_OF_LINEAGE_II(34),
	// Message: You hit for $s1 damage.
	YOU_HIT_FOR_S1_DAMAGE(35),
	// Message: $c1 hit you for $s2 damage.
	C1_HIT_YOU_FOR_S2_DAMAGE(36),
	// Message: $c1 hit you for $s2 damage.
	C1_HIT_YOU_FOR_S2_DAMAGE_(37),
	// Message: The TGS2002 event begins!
	THE_TGS2002_EVENT_BEGINS(38),
	// Message: The TGS2002 event is over. Thank you very much.
	THE_TGS2002_EVENT_IS_OVER(39),
	// Message: This is the TGS demo: the character will immediately be restored.
	THIS_IS_THE_TGS_DEMO_THE_CHARACTER_WILL_IMMEDIATELY_BE_RESTORED(40),
	// Message: You carefully nock an arrow.
	YOU_CAREFULLY_NOCK_AN_ARROW(41),
	// Message: You have avoided $c1's attack.
	YOU_HAVE_AVOIDED_C1S_ATTACK(42),
	// Message: You have missed.
	YOU_HAVE_MISSED(43),
	// Message: Critical hit!
	CRITICAL_HIT(44),
	// Message: You have earned $s1 experience.
	YOU_HAVE_EARNED_S1_EXPERIENCE(45),
	// Message: You use $s1.
	YOU_USE_S1(46),
	// Message: You begin to use a(n) $s1.
	YOU_BEGIN_TO_USE_AN_S1(47),
	// Message: $s1 is not available at this time: being prepared for reuse.
	S1_IS_NOT_AVAILABLE_AT_THIS_TIME_BEING_PREPARED_FOR_REUSE(48),
	// Message: You have equipped your $s1.
	YOU_HAVE_EQUIPPED_YOUR_S1(49),
	// Message: Your target cannot be found.
	YOUR_TARGET_CANNOT_BE_FOUND(50),
	// Message: You cannot use this on yourself.
	YOU_CANNOT_USE_THIS_ON_YOURSELF(51),
	// Message: You have earned $s1 adena.
	YOU_HAVE_EARNED_S1_ADENA(52),
	// Message: You have earned $s2 $s1(s).
	YOU_HAVE_EARNED_S2_S1S(53),
	// Message: You have earned $s1.
	YOU_HAVE_EARNED_S1(54),
	// Message: Nothing happened.
	NOTHING_HAPPENED(61),
	// Message: You may not attack in a peaceful zone.
	YOU_MAY_NOT_ATTACK_IN_A_PEACEFUL_ZONE(84),
	// Message: You may not attack this target in a peaceful zone.
	YOU_MAY_NOT_ATTACK_THIS_TARGET_IN_A_PEACEFUL_ZONE(85),
	// Message: Please enter your ID.
	PLEASE_ENTER_YOUR_ID(86),
	// Message: Please enter your password.
	PLEASE_ENTER_YOUR_PASSWORD(87),
	// Message: Your protocol version is different, please restart your client and run a full check.
	YOUR_PROTOCOL_VERSION_IS_DIFFERENT_PLEASE_RESTART_YOUR_CLIENT_AND_RUN_A_FULL_CHECK(88),
	// Message: Your protocol version is different, please continue.
	YOUR_PROTOCOL_VERSION_IS_DIFFERENT_PLEASE_CONTINUE(89),
	// Message: You are unable to connect to the server.
	YOU_ARE_UNABLE_TO_CONNECT_TO_THE_SERVER(90),
	// Message: Please select your hairstyle.
	PLEASE_SELECT_YOUR_HAIRSTYLE(91),
	// Message: $s1 has worn off.
	S1_HAS_WORN_OFF(92),
	// Message: You do not have enough SP for this.
	YOU_DO_NOT_HAVE_ENOUGH_SP_FOR_THIS(93),
	// Message: 2004-2011 © NC Interactive, Inc. All Rights Reserved.
	_20042011__NC_INTERACTIVE_INC(94),
	// Message: You have earned $s1 experience and $s2 SP.
	YOU_HAVE_EARNED_S1_EXPERIENCE_AND_S2_SP(95),
	// Message: Your level has increased!
	YOUR_LEVEL_HAS_INCREASED(96),
	// Message: This item cannot be moved.
	THIS_ITEM_CANNOT_BE_MOVED(97),
	// Message: This item cannot be discarded.
	THIS_ITEM_CANNOT_BE_DISCARDED(98),
	// Message: This item cannot be traded or sold.
	THIS_ITEM_CANNOT_BE_TRADED_OR_SOLD(99),
	// Message: $c1 is requesting a trade. Do you wish to continue?
	C1_IS_REQUESTING_A_TRADE(100),
	// Message: You cannot exit the game while in combat.
	YOU_CANNOT_EXIT_THE_GAME_WHILE_IN_COMBAT(101),
	// Message: You cannot restart while in combat.
	YOU_CANNOT_RESTART_WHILE_IN_COMBAT(102),
	// Message: This ID is currently logged in.
	THIS_ID_IS_CURRENTLY_LOGGED_IN(103),
	// Message: You cannot change weapons during an attack.
	YOU_CANNOT_CHANGE_WEAPONS_DURING_AN_ATTACK(104),
	// Message: $c1 has been invited to the party.
	C1_HAS_BEEN_INVITED_TO_THE_PARTY(105),
	// Message: You have joined $s1's party.
	YOU_HAVE_JOINED_S1S_PARTY(106),
	// Message: $c1 has joined the party.
	C1_HAS_JOINED_THE_PARTY(107),
	// Message: $c1 has left the party.
	C1_HAS_LEFT_THE_PARTY(108),
	// Message: Invalid target.
	INVALID_TARGET(109),
	// Message: $s1’s effect can be felt.
	S1S_EFFECT_CAN_BE_FELT(110),
	// Message: Your shield defense has succeeded.
	YOUR_SHIELD_DEFENSE_HAS_SUCCEEDED(111),
	// Message: You have run out of arrows.
	YOU_HAVE_RUN_OUT_OF_ARROWS(112),
	// Message: $s1 cannot be used due to unsuitable terms.
	S1_CANNOT_BE_USED_DUE_TO_UNSUITABLE_TERMS(113),
	// Message: $c1 has denied your request to trade.
	C1_HAS_DENIED_YOUR_REQUEST_TO_TRADE(119),
	CHATTING_IS_NOW_PERMITTED(146),
	// Message: Chatting is currently prohibited.
	CHATTING_IS_CURRENTLY_PROHIBITED(147),
	// Message: You cannot use quest items.
	YOU_CANNOT_USE_QUEST_ITEMS(148),
	// Message: You cannot pick up or use items while trading.
	YOU_CANNOT_PICK_UP_OR_USE_ITEMS_WHILE_TRADING(149),
	// Message: You cannot discard or destroy an item while trading at a private store.
	YOU_CANNOT_DISCARD_OR_DESTROY_AN_ITEM_WHILE_TRADING_AT_A_PRIVATE_STORE(150),
	// Message: You cannot discard something that far away from you.
	YOU_CANNOT_DISCARD_SOMETHING_THAT_FAR_AWAY_FROM_YOU(151),
	// Message: You have invited the wrong target.
	YOU_HAVE_INVITED_THE_WRONG_TARGET(152),
	// Message: $c1 is on another task. Please try again later.
	C1_IS_ON_ANOTHER_TASK(153),
	// Message: Only the leader can give out invitations.
	ONLY_THE_LEADER_CAN_GIVE_OUT_INVITATIONS(154),
	// Message: The party is full.
	THE_PARTY_IS_FULL(155),
	// Message: Drain was only 50 percent successful.
	DRAIN_WAS_ONLY_50_PERCENT_SUCCESSFUL(156),
	// Message: You resisted $c1's drain.
	YOU_RESISTED_C1S_DRAIN(157),
	// Message: Your attack has failed.
	YOUR_ATTACK_HAS_FAILED(158),
	// Message: You resisted $c1's magic.
	YOU_RESISTED_C1S_MAGIC(159),
	// Message: $c1 is a member of another party and cannot be invited.
	C1_IS_A_MEMBER_OF_ANOTHER_PARTY_AND_CANNOT_BE_INVITED(160),
	// Message: Waiting for another reply.
	WAITING_FOR_ANOTHER_REPLY(164),
	// Message: You cannot add yourself to your own friend list.
	YOU_CANNOT_ADD_YOURSELF_TO_YOUR_OWN_FRIEND_LIST(165),
	// Message: Friend list is not ready yet. Please register again later.
	FRIEND_LIST_IS_NOT_READY_YET(166),
	// Message: $c1 is already on your friend list.
	C1_IS_ALREADY_ON_YOUR_FRIEND_LIST(167),
	// Message: $c1 has sent a friend request.
	C1_HAS_SENT_A_FRIEND_REQUEST(168),
	// Message: Accept friendship 0/1 (1 to accept, 0 to deny)
	ACCEPT_FRIENDSHIP_01_1_TO_ACCEPT_0_TO_DENY(169),
	// Message: The user who requested to become friends is not found in the game.
	THE_USER_WHO_REQUESTED_TO_BECOME_FRIENDS_IS_NOT_FOUND_IN_THE_GAME(170),
	// Message: $c1 is not on your friend list.
	C1_IS_NOT_ON_YOUR_FRIEND_LIST(171),
	// Message: You lack the funds needed to pay for this transaction.
	YOU_LACK_THE_FUNDS_NEEDED_TO_PAY_FOR_THIS_TRANSACTION(172),
	// Message: That person is in message refusal mode.
	THAT_PERSON_IS_IN_MESSAGE_REFUSAL_MODE(176),
	// Message: Message refusal mode.
	MESSAGE_REFUSAL_MODE(177),
	// Message: Entered the clan.
	ENTERED_THE_CLAN(195),
	// Message: $s1 declined your clan invitation.
	S1_DECLINED_YOUR_CLAN_INVITATION(196),
	// Message: You have withdrawn from the clan.
	YOU_HAVE_WITHDRAWN_FROM_THE_CLAN(197),
	// Message: You have failed to withdraw from the $s1 clan.
	YOU_HAVE_FAILED_TO_WITHDRAW_FROM_THE_S1_CLAN(198),
	// Message: You have recently been dismissed from a clan.  You are not allowed to join another clan for 24-hours.
	YOU_HAVE_RECENTLY_BEEN_DISMISSED_FROM_A_CLAN(199),
	// Message: Incorrect name. Please try again.
	S1_HAS_JOINED_THE_CLAN(222),
	// Message: $s1 has withdrawn from the clan.
	S1_HAS_WITHDRAWN_FROM_THE_CLAN(223),
	// Message: After leaving or having been dismissed from a clan, you must wait at least a day before joining another clan.
	AFTER_LEAVING_OR_HAVING_BEEN_DISMISSED_FROM_A_CLAN_YOU_MUST_WAIT_AT_LEAST_A_DAY_BEFORE_JOINING_ANOTHER_CLAN(232),
	// Message: The Academy/Royal Guard/Order of Knights is full and cannot accept new members at this time.
	THE_ACADEMYROYAL_GUARDORDER_OF_KNIGHTS_IS_FULL_AND_CANNOT_ACCEPT_NEW_MEMBERS_AT_THIS_TIME(233),
	// Message: The target must be a clan member.
	THE_TARGET_MUST_BE_A_CLAN_MEMBER(234),
	// Message: You are not authorized to bestow these rights.
	YOU_ARE_NOT_AUTHORIZED_TO_BESTOW_THESE_RIGHTS(235),
	// Message: Only the clan leader is enabled.
	ONLY_THE_CLAN_LEADER_IS_ENABLED(236),
	// Message: A clan leader cannot withdraw from their own clan.
	A_CLAN_LEADER_CANNOT_WITHDRAW_FROM_THEIR_OWN_CLAN(239),
	// Message: You do not have the necessary materials or prerequisites to learn this skill.
	YOU_DO_NOT_HAVE_THE_NECESSARY_MATERIALS_OR_PREREQUISITES_TO_LEARN_THIS_SKILL(276),
	// Message: You have earned $s1.
	YOU_HAVE_EARNED_S1_(277),
	// Message: You do not have enough SP to learn this skill.
	YOU_DO_NOT_HAVE_ENOUGH_SP_TO_LEARN_THIS_SKILL(278),
	// Message: You do not have enough adena.
	YOU_DO_NOT_HAVE_ENOUGH_ADENA(279),
	// Message: You do not have any items to sell.
	YOU_DO_NOT_HAVE_ANY_ITEMS_TO_SELL(280),
	// Message: You do not have enough adena to pay the fee.
	YOU_DO_NOT_HAVE_ENOUGH_ADENA_TO_PAY_THE_FEE(281),
	// Message: You have not deposited any items in your warehouse.
	YOU_HAVE_NOT_DEPOSITED_ANY_ITEMS_IN_YOUR_WAREHOUSE(282),
	// Message: You have entered a combat zone.
	YOU_HAVE_ENTERED_A_COMBAT_ZONE(283),
	// Message: You have left a combat zone.
	YOU_HAVE_LEFT_A_COMBAT_ZONE(284),
	// Message: Clan $s1 has successfully engraved the holy artifact!
	CLAN_S1_HAS_SUCCESSFULLY_ENGRAVED_THE_HOLY_ARTIFACT(285),
	// Message: Your base is being attacked.
	YOUR_BASE_IS_BEING_ATTACKED(286),
	// Message: The opposing clan has started to engrave the holy artifact!
	THE_OPPOSING_CLAN_HAS_STARTED_TO_ENGRAVE_THE_HOLY_ARTIFACT(287),
	// Message: The castle gate has been destroyed.
	THE_CASTLE_GATE_HAS_BEEN_DESTROYED(288),
	// Message: An outpost or headquarters cannot be built because one already exists.
	AN_OUTPOST_OR_HEADQUARTERS_CANNOT_BE_BUILT_BECAUSE_ONE_ALREADY_EXISTS(289),
	// Message: You cannot set up a base here.
	YOU_CANNOT_SET_UP_A_BASE_HERE(290),
	// Message: Clan $s1 is victorious over $s2's castle siege!
	CLAN_S1_IS_VICTORIOUS_OVER_S2S_CASTLE_SIEGE(291),
	// Message: $s1 has announced the next castle siege time.
	S1_HAS_ANNOUNCED_THE_NEXT_CASTLE_SIEGE_TIME(292),
	// Message: The registration term for $s1 has ended.
	THE_REGISTRATION_TERM_FOR_S1_HAS_ENDED(293),
	// Message: You cannot summon the encampment because you are not a member of the siege clan involved in the castle / fortress / hideout siege.
	YOU_CANNOT_SUMMON_THE_ENCAMPMENT_BECAUSE_YOU_ARE_NOT_A_MEMBER_OF_THE_SIEGE_CLAN_INVOLVED_IN_THE_CASTLE__FORTRESS__HIDEOUT_SIEGE(294),
	// Message: $s1's siege was canceled because there were no clans that participated.
	S1S_SIEGE_WAS_CANCELED_BECAUSE_THERE_WERE_NO_CLANS_THAT_PARTICIPATED(295),
	// Message: You received $s1 falling damage.
	YOU_RECEIVED_S1_FALLING_DAMAGE(296),
	// Message: You have taken $s1 damage because you were unable to breathe.
	YOU_HAVE_TAKEN_S1_DAMAGE_BECAUSE_YOU_WERE_UNABLE_TO_BREATHE(297),
	// Message: You have dropped $s1.
	YOU_HAVE_DROPPED_S1(298),
	// Message: $c1 has obtained $s3 $s2.
	C1_HAS_OBTAINED_S3_S2(299),
	// Message: $c1 has obtained $s2.
	C1_HAS_OBTAINED_S2(300),
	// Message: $s2 $s1 has disappeared.
	S2_S1_HAS_DISAPPEARED(301),
	// Message: $s1 has disappeared.
	S1_HAS_DISAPPEARED(302),
	// Message: Select item to enchant.
	SELECT_ITEM_TO_ENCHANT(303),
	// Message: Clan member $s1 has logged into game.
	CLAN_MEMBER_S1_HAS_LOGGED_INTO_GAME(304),
	// Message: Incorrect item count.
	INCORRECT_ITEM_COUNT_(351),
	// Message: Incorrect item.
	INCORRECT_ITEM(352),
	// Message: Cannot purchase.
	CANNOT_PURCHASE(353),
	// Message: Cancel enchant.
	CANCEL_ENCHANT(354),
	// Message: Inappropriate enchant conditions.
	INAPPROPRIATE_ENCHANT_CONDITIONS(355),
	// Message: Reject resurrection.
	REJECT_RESURRECTION(356),
	// Message: $s1 hour(s) until castle siege conclusion.
	S1_HOURS_UNTIL_CASTLE_SIEGE_CONCLUSION(358),
	// Message: $s1 minute(s) until castle siege conclusion.
	S1_MINUTES_UNTIL_CASTLE_SIEGE_CONCLUSION(359),
	// Message: This castle siege will end in $s1 second(s)!
	THIS_CASTLE_SIEGE_WILL_END_IN_S1_SECONDS(360),
	// Message: Over-hit!
	OVERHIT(361),
	// Message: You have acquired $s1 bonus experience from a successful over-hit.
	YOU_HAVE_ACQUIRED_S1_BONUS_EXPERIENCE_FROM_A_SUCCESSFUL_OVERHIT(362),
	// Message: Chat available time: $s1 minute.
	CHAT_AVAILABLE_TIME_S1_MINUTE(363),
	// Message: Enter user's name to search.
	ENTER_USERS_NAME_TO_SEARCH(364),
	// Message: Are you sure?
	ARE_YOU_SURE(365),
	// Message: Please select your hair color.
	PLEASE_SELECT_YOUR_HAIR_COLOR(366),
	// Message: You cannot remove that clan character at this time.
	YOU_CANNOT_REMOVE_THAT_CLAN_CHARACTER_AT_THIS_TIME(367),
	// Message: Equipped +$s1 $s2.
	EQUIPPED_S1_S2(368),
	// Message: You have obtained a +$s1 $s2.
	YOU_HAVE_OBTAINED_A_S1_S2(369),
	// Message: Failed to pick up $s1.
	FAILED_TO_PICK_UP_S1(370),
	// Message: Acquired +$s1 $s2.
	ACQUIRED_S1_S2(371),
	// Message: Failed to earn $s1.
	FAILED_TO_EARN_S1(372),
	// Message: You are trying to destroy +$s1 $s2.  Do you wish to continue?
	YOU_ARE_TRYING_TO_DESTROY_S1_S2(373),
	// Message: You are attempting to crystallize +$s1 $s2.  Do you wish to continue?
	YOU_ARE_ATTEMPTING_TO_CRYSTALLIZE_S1_S2(374),
	// Message: You have dropped +$s1 $s2.
	YOU_HAVE_DROPPED_S1_S2(375),
	// Message: $c1 has obtained +$s2$s3.
	C1_HAS_OBTAINED_S2S3(376),
	// Message: $S1 $S2 disappeared.
	S1_S2_DISAPPEARED(377),
	// Message: $c1 purchased $s2.
	C1_PURCHASED_S2(378),
	// Message: $c1 purchased +$s2$s3.
	C1_PURCHASED_S2S3(379),
	// Message: $c1 purchased $s3 $s2(s).
	C1_PURCHASED_S3_S2S(380),
	// Message: You do not possess the correct ticket to board the boat.
	YOU_DO_NOT_POSSESS_THE_CORRECT_TICKET_TO_BOARD_THE_BOAT(402),
	// Message: You have exceeded your out-of-pocket adena limit.
	// Message: You cannot summon during a trade or while using a private store.
	YOU_CANNOT_SUMMON_DURING_A_TRADE_OR_WHILE_USING_A_PRIVATE_STORE(577),
	// Message: You may not restore a hungry pet.
	YOU_MAY_NOT_RESTORE_A_HUNGRY_PET(594),
	// Message: Your pet is very hungry.
	YOUR_PET_IS_VERY_HUNGRY(595),
	// Message: Your pet ate a little, but is still hungry.
	YOUR_PET_ATE_A_LITTLE_BUT_IS_STILL_HUNGRY(596),
	// Message: Your pet is very hungry. Please be careful.
	YOUR_PET_IS_VERY_HUNGRY_(597),
	// Message: You may not chat while you are invisible.
	YOU_MAY_NOT_CHAT_WHILE_YOU_ARE_INVISIBLE(598),
	// Message: The GM has an important notice. Chat has been temporarily disabled.
	THE_GM_HAS_AN_IMPORTANT_NOTICE(599),
	// Message: You may not equip a pet item.
	YOU_MAY_NOT_EQUIP_A_PET_ITEM(600),
	// Message: There are $S1 petitions currently on the waiting list.
	THERE_ARE_S1_PETITIONS_CURRENTLY_ON_THE_WAITING_LIST(601),
	// Message: The petition system is currently unavailable. Please try again later.
	THE_PETITION_SYSTEM_IS_CURRENTLY_UNAVAILABLE(602),
	// Message: That item cannot be discarded or exchanged.
	THAT_ITEM_CANNOT_BE_DISCARDED_OR_EXCHANGED(603),
	// Message: You may not call forth a pet or summoned creature from this location.
	YOU_MAY_NOT_CALL_FORTH_A_PET_OR_SUMMONED_CREATURE_FROM_THIS_LOCATION(604),
	// Message: You can only enter up 128 names in your friends list.
	YOU_CAN_ONLY_ENTER_UP_128_NAMES_IN_YOUR_FRIENDS_LIST(605),
	// Message: The Friend's List of the person you are trying to add is full, so registration is not possible.
	THE_FRIENDS_LIST_OF_THE_PERSON_YOU_ARE_TRYING_TO_ADD_IS_FULL_SO_REGISTRATION_IS_NOT_POSSIBLE(606),
	// Message: You do not have any further skills to learn. Come back when you have reached Level $s1.
	YOU_DO_NOT_HAVE_ANY_FURTHER_SKILLS_TO_LEARN(607),
	// Message: You have already requested a Castle Siege.
	YOU_HAVE_ALREADY_REQUESTED_A_CASTLE_SIEGE(638),
	// Message: Your application has been denied because you have already submitted a request for another Castle Siege.
	YOUR_APPLICATION_HAS_BEEN_DENIED_BECAUSE_YOU_HAVE_ALREADY_SUBMITTED_A_REQUEST_FOR_ANOTHER_CASTLE_SIEGE(639),
	// Message: You have failed to refuse castle defense aid.
	YOU_HAVE_FAILED_TO_REFUSE_CASTLE_DEFENSE_AID(640),
	// Message: You have failed to approve castle defense aid.
	YOU_HAVE_FAILED_TO_APPROVE_CASTLE_DEFENSE_AID(641),
	// Message: You are already registered to the attacker side and must cancel your registration before submitting your request.
	YOU_ARE_ALREADY_REGISTERED_TO_THE_ATTACKER_SIDE_AND_MUST_CANCEL_YOUR_REGISTRATION_BEFORE_SUBMITTING_YOUR_REQUEST(642),
	// Message: You have already registered to the defender side and must cancel your registration before submitting your request.
	YOU_HAVE_ALREADY_REGISTERED_TO_THE_DEFENDER_SIDE_AND_MUST_CANCEL_YOUR_REGISTRATION_BEFORE_SUBMITTING_YOUR_REQUEST(643),
	// Message: You are not yet registered for the castle siege.
	YOU_ARE_NOT_YET_REGISTERED_FOR_THE_CASTLE_SIEGE(644),
	// Message: Only clans of level 5 or higher may register for a castle siege.
	ONLY_CLANS_OF_LEVEL_5_OR_HIGHER_MAY_REGISTER_FOR_A_CASTLE_SIEGE(645),
	// Message: You do not have the authority to modify the castle defender list.
	YOU_DO_NOT_HAVE_THE_AUTHORITY_TO_MODIFY_THE_CASTLE_DEFENDER_LIST(646),
	// Message: You do not have the authority to modify the siege time.
	YOU_DO_NOT_HAVE_THE_AUTHORITY_TO_MODIFY_THE_SIEGE_TIME(647),
	// Message: No more registrations may be accepted for the attacker side.
	NO_MORE_REGISTRATIONS_MAY_BE_ACCEPTED_FOR_THE_ATTACKER_SIDE(648),
	// Message: No more registrations may be accepted for the defender side.
	NO_MORE_REGISTRATIONS_MAY_BE_ACCEPTED_FOR_THE_DEFENDER_SIDE(649),
	// Message: You may not summon from your current location.
	YOU_MAY_NOT_SUMMON_FROM_YOUR_CURRENT_LOCATION(650),
	// Message: Place $s1 in the current location and direction. Do you wish to continue?
	PLACE_S1_IN_THE_CURRENT_LOCATION_AND_DIRECTION(651),
	// Message: The target of the summoned monster is wrong.
	THE_TARGET_OF_THE_SUMMONED_MONSTER_IS_WRONG(652),
	// Message: You do not have the authority to position mercenaries.
	YOU_DO_NOT_HAVE_THE_AUTHORITY_TO_POSITION_MERCENARIES(653),
	// Message: You do not have the authority to cancel mercenary positioning.
	YOU_DO_NOT_HAVE_THE_AUTHORITY_TO_CANCEL_MERCENARY_POSITIONING(654),
	// Message: Mercenaries cannot be positioned here.
	MERCENARIES_CANNOT_BE_POSITIONED_HERE(655),
	// Message: This mercenary cannot be positioned anymore.
	THIS_MERCENARY_CANNOT_BE_POSITIONED_ANYMORE(656),
	// Message: Positioning cannot be done here because the distance between mercenaries is too short.
	POSITIONING_CANNOT_BE_DONE_HERE_BECAUSE_THE_DISTANCE_BETWEEN_MERCENARIES_IS_TOO_SHORT(657),
	// Message: This is not a mercenary of a castle that you own and so you cannot cancel its positioning.
	THIS_IS_NOT_A_MERCENARY_OF_A_CASTLE_THAT_YOU_OWN_AND_SO_YOU_CANNOT_CANCEL_ITS_POSITIONING(658),
	// Message: This is not the time for siege registration and so registrations cannot be accepted or rejected.
	THIS_IS_NOT_THE_TIME_FOR_SIEGE_REGISTRATION_AND_SO_REGISTRATIONS_CANNOT_BE_ACCEPTED_OR_REJECTED(659),
	// Message: This is not the time for siege registration and so registration and cancellation cannot be done.
	THIS_IS_NOT_THE_TIME_FOR_SIEGE_REGISTRATION_AND_SO_REGISTRATION_AND_CANCELLATION_CANNOT_BE_DONE(660),
	// Message: This character cannot be spoiled.
	THIS_CHARACTER_CANNOT_BE_SPOILED(661),
	// Message: The other player is rejecting friend invitations.
	THE_OTHER_PLAYER_IS_REJECTING_FRIEND_INVITATIONS(662),
	// Message: The siege time has been declared for $s. It is not possible to change the time after a siege time has been declared. Do you want to continue?
	THE_SIEGE_TIME_HAS_BEEN_DECLARED_FOR_S(663),
	// Message: Please choose a person to receive.
	PLEASE_CHOOSE_A_PERSON_TO_RECEIVE(664),
	// Message: $s2 of $s1 alliance is applying for alliance war. Do you want to accept the challenge?
	S2_OF_S1_ALLIANCE_IS_APPLYING_FOR_ALLIANCE_WAR(665),
	// Message: A request for ceasefire has been received from $s1 alliance. Do you agree?
	A_REQUEST_FOR_CEASEFIRE_HAS_BEEN_RECEIVED_FROM_S1_ALLIANCE(666),
	// Message: You are registering on the attacking side of the $s1 siege. Do you want to continue?
	YOU_ARE_REGISTERING_ON_THE_ATTACKING_SIDE_OF_THE_S1_SIEGE(667),
	// Message: You are registering on the defending side of the $s1 siege. Do you want to continue?
	YOU_ARE_REGISTERING_ON_THE_DEFENDING_SIDE_OF_THE_S1_SIEGE(668),
	// Message: You are canceling your application to participate in the $s1 castle siege. Do you want to continue?
	YOU_ARE_CANCELING_YOUR_APPLICATION_TO_PARTICIPATE_IN_THE_S1_CASTLE_SIEGE(669),
	// Message: You are declining the registration of clan $s1 as a defender. Do you want to continue?
	YOU_ARE_DECLINING_THE_REGISTRATION_OF_CLAN_S1_AS_A_DEFENDER(670),
	// Message: You are accepting the registration of clan $s1 as a defender. Do you want to continue?
	YOU_ARE_ACCEPTING_THE_REGISTRATION_OF_CLAN_S1_AS_A_DEFENDER(671),
	// Message: $s1 adena disappeared.
	S1_ADENA_DISAPPEARED(672),
	// Message: Only a clan leader whose clan is of level 2 or higher is allowed to participate in a clan hall auction.
	ONLY_A_CLAN_LEADER_WHOSE_CLAN_IS_OF_LEVEL_2_OR_HIGHER_IS_ALLOWED_TO_PARTICIPATE_IN_A_CLAN_HALL_AUCTION(673),
	// Message: It has not yet been seven days since canceling an auction.
	IT_HAS_NOT_YET_BEEN_SEVEN_DAYS_SINCE_CANCELING_AN_AUCTION(674),
	// Message: There are no clan halls up for auction.
	THERE_ARE_NO_CLAN_HALLS_UP_FOR_AUCTION(675),
	// Message: Since you have already submitted a bid, you are not allowed to participate in another auction at this time.
	SINCE_YOU_HAVE_ALREADY_SUBMITTED_A_BID_YOU_ARE_NOT_ALLOWED_TO_PARTICIPATE_IN_ANOTHER_AUCTION_AT_THIS_TIME(676),
	// Message: Your bid price must be higher than the minimum price currently being bid.
	YOUR_BID_PRICE_MUST_BE_HIGHER_THAN_THE_MINIMUM_PRICE_CURRENTLY_BEING_BID(677),
	// Message: You cannot move while frozen. Please wait.
	YOU_CANNOT_MOVE_WHILE_FROZEN(687),
	// Message: Castle-owning clans are automatically registered on the defending side.
	CASTLEOWNING_CLANS_ARE_AUTOMATICALLY_REGISTERED_ON_THE_DEFENDING_SIDE(688),
	// Message: A clan that owns a castle cannot participate in another siege.
	A_CLAN_THAT_OWNS_A_CASTLE_CANNOT_PARTICIPATE_IN_ANOTHER_SIEGE(689),
	// Message: You cannot register as an attacker because you are in an alliance with the castle-owning clan.
	YOU_CANNOT_REGISTER_AS_AN_ATTACKER_BECAUSE_YOU_ARE_IN_AN_ALLIANCE_WITH_THE_CASTLEOWNING_CLAN(690),
	// Message: The other party is frozen. Please wait a moment.
	THE_OTHER_PARTY_IS_FROZEN(692),
	// Message: The package that arrived is in another warehouse.
	THE_PACKAGE_THAT_ARRIVED_IS_IN_ANOTHER_WAREHOUSE(693),
	// Message: No packages have arrived.
	NO_PACKAGES_HAVE_ARRIVED(694),
	// Message: You cannot set the name of the pet.
	YOU_CANNOT_SET_THE_NAME_OF_THE_PET(695),
	// Message: Your account is restricted for not paying your PC room usage fees.
	YOUR_ACCOUNT_IS_RESTRICTED_FOR_NOT_PAYING_YOUR_PC_ROOM_USAGE_FEES(696),
	// Message: The item enchant value is strange.
	THE_ITEM_ENCHANT_VALUE_IS_STRANGE(697),
	// Message: The price is different than the same item on the sales list.
	THE_PRICE_IS_DIFFERENT_THAN_THE_SAME_ITEM_ON_THE_SALES_LIST(698),
	// Message: Currently not purchasing.
	CURRENTLY_NOT_PURCHASING(699),
	// Message: The purchase is complete.
	THE_PURCHASE_IS_COMPLETE(700),
	// Message: You do not have enough required items.
	YOU_DO_NOT_HAVE_ENOUGH_REQUIRED_ITEMS(701),
	// Message: There are no GMs currently visible in the public list as they may be performing other functions at the moment.
	THERE_ARE_NO_GMS_CURRENTLY_VISIBLE_IN_THE_PUBLIC_LIST_AS_THEY_MAY_BE_PERFORMING_OTHER_FUNCTIONS_AT_THE_MOMENT(702),
	// Message: ======<GM List>======
	GM_LIST(703),
	// Message: GM : $c1
	GM__C1(704),
	// Message: You cannot exclude yourself.
	YOU_CANNOT_EXCLUDE_YOURSELF(705),
	// Message: You can only enter up to 128 names in your block list.
	YOU_CAN_ONLY_ENTER_UP_TO_128_NAMES_IN_YOUR_BLOCK_LIST(706),
	// Message: You cannot teleport to a village that is in a siege.
	YOU_CANNOT_TELEPORT_TO_A_VILLAGE_THAT_IS_IN_A_SIEGE(707),
	// Message: You do not have the right to use the castle warehouse.
	YOU_DO_NOT_HAVE_THE_RIGHT_TO_USE_THE_CASTLE_WAREHOUSE(708),
	// Message: You do not have the right to use the clan warehouse.
	YOU_DO_NOT_HAVE_THE_RIGHT_TO_USE_THE_CLAN_WAREHOUSE(709),
	// Message: Only clans of clan level 1 or higher can use a clan warehouse.
	ONLY_CLANS_OF_CLAN_LEVEL_1_OR_HIGHER_CAN_USE_A_CLAN_WAREHOUSE(710),
	// Message: The $s1 siege has started.
	THE_S1_SIEGE_HAS_STARTED(711),
	// Message: The $s1 siege has finished.
	THE_S1_SIEGE_HAS_FINISHED(712),
	// Message: $s1/$s2/$s3 $s4:$s5
	S1S2S3_S4S5(713),
	// Message: A trap device has been tripped.
	A_TRAP_DEVICE_HAS_BEEN_TRIPPED(714),
	// Message: The trap device has been stopped.
	THE_TRAP_DEVICE_HAS_BEEN_STOPPED(715),
	// Message: If a base camp does not exist, resurrection is not possible.
	IF_A_BASE_CAMP_DOES_NOT_EXIST_RESURRECTION_IS_NOT_POSSIBLE(716),
	// Message: The guardian tower has been destroyed and resurrection is not possible.
	THE_GUARDIAN_TOWER_HAS_BEEN_DESTROYED_AND_RESURRECTION_IS_NOT_POSSIBLE(717),
	// Message: That item cannot be discarded.
	THAT_ITEM_CANNOT_BE_DISCARDED(729),
	// Message: The effect of $s1 has been removed.
	THE_EFFECT_OF_S1_HAS_BEEN_REMOVED(749),
	// Message: There are no other skills to learn.
	THERE_ARE_NO_OTHER_SKILLS_TO_LEARN(750),
	// Message: As there is a conflict in the siege relationship with a clan in the alliance, you cannot invite that clan to the alliance.
	AS_THERE_IS_A_CONFLICT_IN_THE_SIEGE_RELATIONSHIP_WITH_A_CLAN_IN_THE_ALLIANCE_YOU_CANNOT_INVITE_THAT_CLAN_TO_THE_ALLIANCE(751),
	// Message: That name cannot be used.
	THAT_NAME_CANNOT_BE_USED(752),
	// Message: You cannot position mercenaries here.
	YOU_CANNOT_POSITION_MERCENARIES_HERE(753),
	// Message: The clan hall which was put up for auction has been awarded to $s1 clan.
	THE_CLAN_HALL_WHICH_WAS_PUT_UP_FOR_AUCTION_HAS_BEEN_AWARDED_TO_S1_CLAN(776),
	// Message: The clan hall which had been put up for auction was not sold and therefore has been re-listed.
	THE_CLAN_HALL_WHICH_HAD_BEEN_PUT_UP_FOR_AUCTION_WAS_NOT_SOLD_AND_THEREFORE_HAS_BEEN_RELISTED(777),
	// Message: Observation is only possible during a siege.
	OBSERVATION_IS_ONLY_POSSIBLE_DURING_A_SIEGE(780),
	// Message: The tryouts are finished.
	THE_TRYOUTS_ARE_FINISHED(787),
	// Message: The finals are finished.
	THE_FINALS_ARE_FINISHED(788),
	// Message: The tryouts have begun.
	THE_TRYOUTS_HAVE_BEGUN(789),
	// Message: The finals have begun.
	THE_FINALS_HAVE_BEGUN(790),
	// Message: The final match is about to begin. Line up!
	THE_FINAL_MATCH_IS_ABOUT_TO_BEGIN(791),
	// Message: The siege of the clan hall is finished.
	THE_SIEGE_OF_THE_CLAN_HALL_IS_FINISHED(792),
	// Message: The siege of the clan hall has begun.
	THE_SIEGE_OF_THE_CLAN_HALL_HAS_BEGUN(793),
	// Message: You are not authorized to do that.
	YOU_ARE_NOT_AUTHORIZED_TO_DO_THAT(794),
	// Message: You are too late. The registration period is over.
	YOU_ARE_TOO_LATE(800),
	// Message: Registration for the clan hall siege is closed.
	REGISTRATION_FOR_THE_CLAN_HALL_SIEGE_IS_CLOSED(801),
	// Message: Petitions are not being accepted at this time. You may submit your petition after $s1 a.m./p.m.
	PETITIONS_ARE_NOT_BEING_ACCEPTED_AT_THIS_TIME(802),
	// Message: Enter the specifics of your petition.
	ENTER_THE_SPECIFICS_OF_YOUR_PETITION(803),
	// Message: Select your type and check the FAQ content.
	SELECT_YOUR_TYPE_AND_CHECK_THE_FAQ_CONTENT(804),
	// Message: Petitions are not being accepted at this time. You may submit your petition after $s1 a.m./p.m.
	PETITIONS_ARE_NOT_BEING_ACCEPTED_AT_THIS_TIME_(805),
	// Message: If you are unable to move, try typing "/unstuck".
	IF_YOU_ARE_UNABLE_TO_MOVE_TRY_TYPING_UNSTUCK(806),
	// Message: This terrain is navigable. Prepare for transport to the nearest village.
	THIS_TERRAIN_IS_NAVIGABLE(807),
	// Message: You are stuck. You may submit a petition by typing "/gm".
	YOU_ARE_STUCK(808),
	// Message: You are stuck. You will be transported to the nearest village in five minutes.
	YOU_ARE_STUCK_(809),
	// Message: Invalid macro. Refer to the Help file for instructions.
	INVALID_MACRO(810),
	// Message: You have requested a teleport to ($s1). Do you wish to continue?
	YOU_HAVE_REQUESTED_A_TELEPORT_TO_S1(811),
	// Message: The secret trap has inflicted $s1 damage on you.
	THE_SECRET_TRAP_HAS_INFLICTED_S1_DAMAGE_ON_YOU(812),
	// Message: You have been poisoned by a Secret Trap.
	YOU_HAVE_BEEN_POISONED_BY_A_SECRET_TRAP(813),
	// Message: Your speed has been decreased by a Secret Trap.
	YOUR_SPEED_HAS_BEEN_DECREASED_BY_A_SECRET_TRAP(814),
	// Message: The tryouts are about to begin. Line up!
	THE_TRYOUTS_ARE_ABOUT_TO_BEGIN(815),
	// Message: The siege of $s1 is finished.
	THE_SIEGE_OF_S1_IS_FINISHED(843),
	// Message: The siege to conquer $s1 has begun.
	THE_SIEGE_TO_CONQUER_S1_HAS_BEGUN(844),
	// Message: The deadline to register for the siege of $s1 has passed.
	THE_DEADLINE_TO_REGISTER_FOR_THE_SIEGE_OF_S1_HAS_PASSED(845),
	// Message: The siege of $s1 has been canceled due to lack of interest.
	THE_SIEGE_OF_S1_HAS_BEEN_CANCELED_DUE_TO_LACK_OF_INTEREST(846),
	// Message: The seed has been sown.
	THE_SEED_HAS_BEEN_SOWN(871),
	// Message: This seed may not be sown here.
	THIS_SEED_MAY_NOT_BE_SOWN_HERE(872),
	// Message: That character does not exist.
	THAT_CHARACTER_DOES_NOT_EXIST(873),
	// Message: The capacity of the warehouse has been exceeded.
	THE_CAPACITY_OF_THE_WAREHOUSE_HAS_BEEN_EXCEEDED(874),
	// Message: The transport of the cargo has been canceled.
	THE_TRANSPORT_OF_THE_CARGO_HAS_BEEN_CANCELED(875),
	// Message: The cargo was not delivered.
	THE_CARGO_WAS_NOT_DELIVERED(876),
	// Message: The symbol has been added.
	THE_SYMBOL_HAS_BEEN_ADDED(877),
	// Message: The symbol has been deleted.
	THE_SYMBOL_HAS_BEEN_DELETED(878),
	// Message: The manor system is currently under maintenance.
	THE_MANOR_SYSTEM_IS_CURRENTLY_UNDER_MAINTENANCE(879),
	// Message: The transaction is complete.
	THE_TRANSACTION_IS_COMPLETE(880),
	// Message: The manor information has been updated.
	THE_MANOR_INFORMATION_HAS_BEEN_UPDATED(884),
	// Message: The symbol cannot be drawn.
	THE_SYMBOL_CANNOT_BE_DRAWN(899),
	// Message: The soul crystal was not able to absorb the soul.
	THE_SOUL_CRYSTAL_WAS_NOT_ABLE_TO_ABSORB_THE_SOUL(975),
	// Message: The soul crystal broke because it was not able to endure the soul energy.
	THE_SOUL_CRYSTAL_BROKE_BECAUSE_IT_WAS_NOT_ABLE_TO_ENDURE_THE_SOUL_ENERGY(976),
	// Message: The soul crystal caused resonation and failed at absorbing a soul.
	THE_SOUL_CRYSTAL_CAUSED_RESONATION_AND_FAILED_AT_ABSORBING_A_SOUL(977),
	// Message: The soul crystal is refusing to absorb the soul.
	THE_SOUL_CRYSTAL_IS_REFUSING_TO_ABSORB_THE_SOUL(978),
	// Message: The ferry has arrived at Talking Island Harbor.
	THE_FERRY_HAS_ARRIVED_AT_TALKING_ISLAND_HARBOR(979),
	// Message: The ferry will leave for Gludin Harbor after anchoring for ten minutes.
	THE_FERRY_WILL_LEAVE_FOR_GLUDIN_HARBOR_AFTER_ANCHORING_FOR_TEN_MINUTES(980),
	// Message: The ferry will leave for Gludin Harbor in five minutes.
	THE_FERRY_WILL_LEAVE_FOR_GLUDIN_HARBOR_IN_FIVE_MINUTES(981),
	// Message: The ferry will leave for Gludin Harbor in one minute.
	THE_FERRY_WILL_LEAVE_FOR_GLUDIN_HARBOR_IN_ONE_MINUTE(982),
	// Message: Those wishing to ride the ferry should make haste to get on.
	THOSE_WISHING_TO_RIDE_THE_FERRY_SHOULD_MAKE_HASTE_TO_GET_ON(983),
	// Message: The ferry will be leaving soon for Gludin Harbor.
	THE_FERRY_WILL_BE_LEAVING_SOON_FOR_GLUDIN_HARBOR(984),
	// Message: The ferry is leaving for Gludin Harbor.
	THE_FERRY_IS_LEAVING_FOR_GLUDIN_HARBOR(985),
	// Message: The ferry has arrived at Gludin Harbor.
	THE_FERRY_HAS_ARRIVED_AT_GLUDIN_HARBOR(986),
	// Message: The ferry will leave for Talking Island Harbor after anchoring for ten minutes.
	THE_FERRY_WILL_LEAVE_FOR_TALKING_ISLAND_HARBOR_AFTER_ANCHORING_FOR_TEN_MINUTES(987),
	// Message: The ferry will leave for Talking Island Harbor in five minutes.
	THE_FERRY_WILL_LEAVE_FOR_TALKING_ISLAND_HARBOR_IN_FIVE_MINUTES(988),
	// Message: The ferry will leave for Talking Island Harbor in one minute.
	THE_FERRY_WILL_LEAVE_FOR_TALKING_ISLAND_HARBOR_IN_ONE_MINUTE(989),
	// Message: The ferry will be leaving soon for Talking Island Harbor.
	THE_FERRY_WILL_BE_LEAVING_SOON_FOR_TALKING_ISLAND_HARBOR(990),
	// Message: The ferry is leaving for Talking Island Harbor.
	THE_FERRY_IS_LEAVING_FOR_TALKING_ISLAND_HARBOR(991),
	// Message: The ferry has arrived at Giran Harbor.
	THE_FERRY_HAS_ARRIVED_AT_GIRAN_HARBOR(992),
	// Message: The ferry will leave for Giran Harbor after anchoring for ten minutes.
	THE_FERRY_WILL_LEAVE_FOR_GIRAN_HARBOR_AFTER_ANCHORING_FOR_TEN_MINUTES(993),
	// Message: The ferry will leave for Giran Harbor in five minutes.
	THE_FERRY_WILL_LEAVE_FOR_GIRAN_HARBOR_IN_FIVE_MINUTES(994),
	// Message: The ferry will leave for Giran Harbor in one minute.
	THE_FERRY_WILL_LEAVE_FOR_GIRAN_HARBOR_IN_ONE_MINUTE(995),
	// Message: The ferry will be leaving soon for Giran Harbor.
	THE_FERRY_WILL_BE_LEAVING_SOON_FOR_GIRAN_HARBOR(996),
	// Message: The ferry is leaving for Giran Harbor.
	THE_FERRY_IS_LEAVING_FOR_GIRAN_HARBOR(997),
	// Message: The Innadril pleasure boat has arrived. It will anchor for ten minutes.
	THE_INNADRIL_PLEASURE_BOAT_HAS_ARRIVED(998),
	// Message: The Innadril pleasure boat will leave in five minutes.
	THE_INNADRIL_PLEASURE_BOAT_WILL_LEAVE_IN_FIVE_MINUTES(999),
	// Message: The Innadril pleasure boat will leave in one minute.
	THE_INNADRIL_PLEASURE_BOAT_WILL_LEAVE_IN_ONE_MINUTE(1000),
	// Message: The Innadril pleasure boat will be leaving soon.
	THE_INNADRIL_PLEASURE_BOAT_WILL_BE_LEAVING_SOON(1001),
	// Message: The Innadril pleasure boat is leaving.
	THE_INNADRIL_PLEASURE_BOAT_IS_LEAVING(1002),
	// Message: Cannot process a monster race ticket.
	CANNOT_PROCESS_A_MONSTER_RACE_TICKET(1003),
	// Message: You have registered for a clan hall auction.
	YOU_HAVE_REGISTERED_FOR_A_CLAN_HALL_AUCTION(1004),
	// Message: There is not enough adena in the clan hall warehouse.
	THERE_IS_NOT_ENOUGH_ADENA_IN_THE_CLAN_HALL_WAREHOUSE(1005),
	// Message: Your bid has been successfully placed.
	YOUR_BID_HAS_BEEN_SUCCESSFULLY_PLACED(1006),
	// Message: The preliminary match registration for $s1 has finished.
	THE_PRELIMINARY_MATCH_REGISTRATION_FOR_S1_HAS_FINISHED(1007),
	// Message: A hungry strider cannot be mounted or dismounted.
	A_HUNGRY_STRIDER_CANNOT_BE_MOUNTED_OR_DISMOUNTED(1008),
	// Message: A strider cannot be ridden when dead.
	A_STRIDER_CANNOT_BE_RIDDEN_WHEN_DEAD(1009),
	// Message: A dead strider cannot be ridden.
	A_DEAD_STRIDER_CANNOT_BE_RIDDEN(1010),
	// Message: A strider in battle cannot be ridden.
	A_STRIDER_IN_BATTLE_CANNOT_BE_RIDDEN(1011),
	// Message: A strider cannot be ridden while in battle.
	A_STRIDER_CANNOT_BE_RIDDEN_WHILE_IN_BATTLE(1012),
	// Message: A strider can be ridden only when standing.
	A_STRIDER_CAN_BE_RIDDEN_ONLY_WHEN_STANDING(1013),
	// Message: Your pet gained $s1 experience points.
	YOUR_PET_GAINED_S1_EXPERIENCE_POINTS(1014),
	// Message: Your pet hit for $s1 damage.
	YOUR_PET_HIT_FOR_S1_DAMAGE(1015),
	// Message: Your pet received $s2 damage by $c1.
	YOUR_PET_RECEIVED_S2_DAMAGE_BY_C1(1016),
	// Message: Pet's critical hit!
	PETS_CRITICAL_HIT(1017),
	// Message: Your pet uses $s1.
	YOUR_PET_USES_S1(1018),
	// Message: Your pet uses $s1.
	YOUR_PET_USES_S1_(1019),
	// Message: Your pet picked up $s1.
	YOUR_PET_PICKED_UP_S1(1020),
	// Message: Your pet picked up $s2 $s1(s).
	YOUR_PET_PICKED_UP_S2_S1S(1021),
	// Message: Your pet picked up +$s1 $s2.
	YOUR_PET_PICKED_UP_S1_S2(1022),
	// Message: Your pet picked up $s1 adena.
	YOUR_PET_PICKED_UP_S1_ADENA(1023),
	// Message: Your pet put on $s1.
	YOUR_PET_PUT_ON_S1(1024),
	// Message: Your pet took off $s1.
	YOUR_PET_TOOK_OFF_S1(1025),
	// Message: The summoned monster gave damage of $s1.
	THE_SUMMONED_MONSTER_GAVE_DAMAGE_OF_S1(1026),
	// Message: Servitor received $s2 damage caused by $s1.
	SERVITOR_RECEIVED_S2_DAMAGE_CAUSED_BY_S1(1027),
	// Message: Summoned monster's critical hit!
	SUMMONED_MONSTERS_CRITICAL_HIT(1028),
	// Message: You have exceeded the quantity that can be inputted.
	YOU_HAVE_EXCEEDED_THE_QUANTITY_THAT_CAN_BE_INPUTTED(1036),
	// Message: $c1 manufactured $s2.
	C1_MANUFACTURED_S2(1037),
	// Message: $c1 manufactured $s3 $s2(s).
	C1_MANUFACTURED_S3_S2S(1038),
	// Message: Items left at the clan hall warehouse can only be retrieved by the clan leader. Do you want to continue?
	ITEMS_LEFT_AT_THE_CLAN_HALL_WAREHOUSE_CAN_ONLY_BE_RETRIEVED_BY_THE_CLAN_LEADER(1039),
	// Message: The vitamin item sent that way can be retrieved from any vitamin manager in all regions. Do you wish to continue?
	THE_VITAMIN_ITEM_SENT_THAT_WAY_CAN_BE_RETRIEVED_FROM_ANY_VITAMIN_MANAGER_IN_ALL_REGIONS(1040),
	// Message: The next seed purchase price is $s1 adena.
	THE_NEXT_SEED_PURCHASE_PRICE_IS_S1_ADENA(1041),
	// Message: The next farm goods purchase price is $s1 adena.
	THE_NEXT_FARM_GOODS_PURCHASE_PRICE_IS_S1_ADENA(1042),
	// Message: At the current time, the "/unstuck" command cannot be used. Please send in a petition.
	AT_THE_CURRENT_TIME_THE_UNSTUCK_COMMAND_CANNOT_BE_USED(1043),
	// Message: Monster race payout information is not available while tickets are being sold.
	MONSTER_RACE_PAYOUT_INFORMATION_IS_NOT_AVAILABLE_WHILE_TICKETS_ARE_BEING_SOLD(1044),
	// Message: Currently, a monster race is not being set up.
	CURRENTLY_A_MONSTER_RACE_IS_NOT_BEING_SET_UP(1045),
	// Message: Monster race tickets are no longer available.
	MONSTER_RACE_TICKETS_ARE_NO_LONGER_AVAILABLE(1046),
	// Message: We did not succeed in producing $s1 item.
	WE_DID_NOT_SUCCEED_IN_PRODUCING_S1_ITEM(1047),
	// Message: While 'blocking' everything, whispering is not possible.
	WHILE_BLOCKING_EVERYTHING_WHISPERING_IS_NOT_POSSIBLE(1048),
	// Message: While 'blocking' everything, it is not possible to send invitations for organizing parties.
	WHILE_BLOCKING_EVERYTHING_IT_IS_NOT_POSSIBLE_TO_SEND_INVITATIONS_FOR_ORGANIZING_PARTIES(1049),
	// Message: There are no communities in my clan. Clan communities are allowed for clans with skill levels of 2 and higher.
	THERE_ARE_NO_COMMUNITIES_IN_MY_CLAN(1050),
	// Message: Payment for your clan hall has not been made. Please make payment to your clan warehouse by $s1 tomorrow.
	PAYMENT_FOR_YOUR_CLAN_HALL_HAS_NOT_BEEN_MADE(1051),
	// Message: The clan hall fee is one week overdue; therefore the clan hall ownership has been revoked.
	THE_CLAN_HALL_FEE_IS_ONE_WEEK_OVERDUE_THEREFORE_THE_CLAN_HALL_OWNERSHIP_HAS_BEEN_REVOKED(1052),
	// Message: It is not possible to resurrect in battlefields where a siege war is taking place.
	IT_IS_NOT_POSSIBLE_TO_RESURRECT_IN_BATTLEFIELDS_WHERE_A_SIEGE_WAR_IS_TAKING_PLACE(1053),
	// Message: You have entered a mystical land.
	YOU_HAVE_ENTERED_A_MYSTICAL_LAND(1054),
	// Message: You have left a mystical land.
	YOU_HAVE_LEFT_A_MYSTICAL_LAND(1055),
	// Message: You have exceeded the storage capacity of the castle's vault.
	YOU_HAVE_EXCEEDED_THE_STORAGE_CAPACITY_OF_THE_CASTLES_VAULT(1056),
	// Message: This command can only be used in the relax server.
	THIS_COMMAND_CAN_ONLY_BE_USED_IN_THE_RELAX_SERVER(1057),
	// Message: The sales price for seeds is $s1 adena.
	THE_SALES_PRICE_FOR_SEEDS_IS_S1_ADENA(1058),
	// Message: The remaining purchasing amount is $s1 adena.
	THE_REMAINING_PURCHASING_AMOUNT_IS_S1_ADENA(1059),
	// Message: The remainder after selling the seeds is $s1.
	THE_REMAINDER_AFTER_SELLING_THE_SEEDS_IS_S1(1060),
	// Message: The recipe cannot be registered.  You do not have the ability to create items.
	THE_RECIPE_CANNOT_BE_REGISTERED(1061),
	// Message: Writing something new is possible after level 10.
	WRITING_SOMETHING_NEW_IS_POSSIBLE_AFTER_LEVEL_10(1062),
	// Message: The Petition Service is currently unavailable, please try again later; in the interim, if you become trapped or unable to move, please use the '/unstuck' command.
	THE_PETITION_SERVICE_IS_CURRENTLY_UNAVAILABLE_PLEASE_TRY_AGAIN_LATER_IN_THE_INTERIM_IF_YOU_BECOME_TRAPPED_OR_UNABLE_TO_MOVE_PLEASE_USE_THE_UNSTUCK_COMMAND(1063),
	// Message: The equipment, +$s1 $s2, has been removed.
	THE_EQUIPMENT_S1_S2_HAS_BEEN_REMOVED(1064),
	// Message: While operating a private store or workshop, you cannot discard, destroy, or trade an item.
	WHILE_OPERATING_A_PRIVATE_STORE_OR_WORKSHOP_YOU_CANNOT_DISCARD_DESTROY_OR_TRADE_AN_ITEM(1065),
	// Message: $s1 HP has been restored.
	S1_HP_HAS_BEEN_RESTORED(1066),
	// Message: $s2 HP has been restored by $c1.
	S2_HP_HAS_BEEN_RESTORED_BY_C1(1067),
	// Message: $s1 MP has been restored.
	S1_MP_HAS_BEEN_RESTORED(1068),
	// Message: $s2 MP has been restored by $c1.
	S2_MP_HAS_BEEN_RESTORED_BY_C1(1069),
	// Message: You do not have 'read' permission.
	YOU_DO_NOT_HAVE_READ_PERMISSION(1070),
	// Message: You do not have 'write' permission.
	YOU_DO_NOT_HAVE_WRITE_PERMISSION(1071),
	// Message: You have obtained a ticket for the Monster Race #$s1  -  Single.
	YOU_HAVE_OBTAINED_A_TICKET_FOR_THE_MONSTER_RACE_S1____SINGLE(1072),
	// Message: You have obtained a ticket for the Monster Race #$s1  -  Single.
	YOU_HAVE_OBTAINED_A_TICKET_FOR_THE_MONSTER_RACE_S1____SINGLE_(1073),
	// Message: You do not meet the age requirement to purchase a Monster Race Ticket.
	YOU_DO_NOT_MEET_THE_AGE_REQUIREMENT_TO_PURCHASE_A_MONSTER_RACE_TICKET(1074),
	// Message: The bid amount must be higher than the previous bid.
	THE_BID_AMOUNT_MUST_BE_HIGHER_THAN_THE_PREVIOUS_BID(1075),
	// Message: A clan member may not be dismissed during combat.
	A_CLAN_MEMBER_MAY_NOT_BE_DISMISSED_DURING_COMBAT(1117),
	// Message: Progress in a quest is possible only when your inventory's weight and slot count are less than 80 percent of capacity.
	PROGRESS_IN_A_QUEST_IS_POSSIBLE_ONLY_WHEN_YOUR_INVENTORYS_WEIGHT_AND_SLOT_COUNT_ARE_LESS_THAN_80_PERCENT_OF_CAPACITY(1118),
	// Message: Quest was automatically canceled when you attempted to settle the accounts of your quest while your inventory exceeded 80 percent of capacity.
	QUEST_WAS_AUTOMATICALLY_CANCELED_WHEN_YOU_ATTEMPTED_TO_SETTLE_THE_ACCOUNTS_OF_YOUR_QUEST_WHILE_YOUR_INVENTORY_EXCEEDED_80_PERCENT_OF_CAPACITY(1119),
	// Message: You are still a member of the clan.
	YOU_ARE_STILL_A_MEMBER_OF_THE_CLAN(1120),
	// Message: You do not have the right to vote.
	YOU_DO_NOT_HAVE_THE_RIGHT_TO_VOTE(1121),
	// Message: There is no candidate.
	THERE_IS_NO_CANDIDATE(1122),
	// Message: Weight and volume limit have been exceeded. That skill is currently unavailable.
	WEIGHT_AND_VOLUME_LIMIT_HAVE_BEEN_EXCEEDED(1123),
	// Message: Your recipe book may not be accessed while using a skill.
	YOUR_RECIPE_BOOK_MAY_NOT_BE_ACCESSED_WHILE_USING_A_SKILL(1124),
	// Message: Item creation is not possible while engaged in a trade.
	ITEM_CREATION_IS_NOT_POSSIBLE_WHILE_ENGAGED_IN_A_TRADE(1125),
	// Message: You cannot enter a negative number.
	YOU_CANNOT_ENTER_A_NEGATIVE_NUMBER(1126),
	// Message: The reward must be less than 10 times the standard price.
	THE_REWARD_MUST_BE_LESS_THAN_10_TIMES_THE_STANDARD_PRICE(1127),
	// Message: A private store may not be opened while using a skill.
	A_PRIVATE_STORE_MAY_NOT_BE_OPENED_WHILE_USING_A_SKILL(1128),
	// Message: This is not allowed while riding a ferry or boat.
	THIS_IS_NOT_ALLOWED_WHILE_RIDING_A_FERRY_OR_BOAT(1129),
	// Message: You have dealt $s1 damage to your target and $s2 damage to the servitor.
	YOU_HAVE_DEALT_S1_DAMAGE_TO_YOUR_TARGET_AND_S2_DAMAGE_TO_THE_SERVITOR(1130),
	// Message: It is now midnight and the effect of $s1 can be felt.
	IT_IS_NOW_MIDNIGHT_AND_THE_EFFECT_OF_S1_CAN_BE_FELT(1131),
	// Message: It is dawn and the effect of $s1 will now disappear.
	IT_IS_DAWN_AND_THE_EFFECT_OF_S1_WILL_NOW_DISAPPEAR(1132),
	// Message: Since your HP has decreased, the effect of $s1 can be felt.
	SINCE_YOUR_HP_HAS_DECREASED_THE_EFFECT_OF_S1_CAN_BE_FELT(1133),
	// Message: Since your HP has increased, the effect of $s1 will disappear.
	SINCE_YOUR_HP_HAS_INCREASED_THE_EFFECT_OF_S1_WILL_DISAPPEAR(1134),
	// Message: While you are engaged in combat, you cannot operate a private store or private workshop.
	WHILE_YOU_ARE_ENGAGED_IN_COMBAT_YOU_CANNOT_OPERATE_A_PRIVATE_STORE_OR_PRIVATE_WORKSHOP(1135),
	// Message: Would you like to open the gate?
	WOULD_YOU_LIKE_TO_OPEN_THE_GATE(1140),
	// Message: Would you like to close the gate?
	WOULD_YOU_LIKE_TO_CLOSE_THE_GATE(1141),
	// Message: The temporary alliance of the Castle Attacker team is in effect. It will be dissolved when the Castle Lord is replaced.
	THE_TEMPORARY_ALLIANCE_OF_THE_CASTLE_ATTACKER_TEAM_IS_IN_EFFECT(1189),
	// Message: The temporary alliance of the Castle Attacker team has been dissolved.
	THE_TEMPORARY_ALLIANCE_OF_THE_CASTLE_ATTACKER_TEAM_HAS_BEEN_DISSOLVED(1190),
	// Message: The ferry from Gludin Harbor will be arriving at Talking Island in approximately 10 minutes.
	THE_FERRY_FROM_GLUDIN_HARBOR_WILL_BE_ARRIVING_AT_TALKING_ISLAND_IN_APPROXIMATELY_10_MINUTES(1191),
	// Message: The ferry from Gludin Harbor will be arriving at Talking Island in approximately 5 minutes.
	THE_FERRY_FROM_GLUDIN_HARBOR_WILL_BE_ARRIVING_AT_TALKING_ISLAND_IN_APPROXIMATELY_5_MINUTES(1192),
	// Message: The ferry from Gludin Harbor will be arriving at Talking Island in approximately 1 minute.
	THE_FERRY_FROM_GLUDIN_HARBOR_WILL_BE_ARRIVING_AT_TALKING_ISLAND_IN_APPROXIMATELY_1_MINUTE(1193),
	// Message: A mercenary can be assigned to a position from the beginning of the Seal Validation period until the time when a siege starts.
	A_MERCENARY_CAN_BE_ASSIGNED_TO_A_POSITION_FROM_THE_BEGINNING_OF_THE_SEAL_VALIDATION_PERIOD_UNTIL_THE_TIME_WHEN_A_SIEGE_STARTS(1194),
	// Message: This mercenary cannot be assigned to a position by using the Seal of Strife.
	THIS_MERCENARY_CANNOT_BE_ASSIGNED_TO_A_POSITION_BY_USING_THE_SEAL_OF_STRIFE(1195),
	// Message: You have successfully switched to your subclass.
	YOU_HAVE_SUCCESSFULLY_SWITCHED_TO_YOUR_SUBCLASS(1270),
	// Message: Do you wish to participate? Until the next seal validation period, you will be a member of the Lords of Dawn.
	DO_YOU_WISH_TO_PARTICIPATE_UNTIL_THE_NEXT_SEAL_VALIDATION_PERIOD_YOU_WILL_BE_A_MEMBER_OF_THE_LORDS_OF_DAWN(1271),
	// Message: Do you wish to participate? Until the next seal validation period, you will be a member of the Revolutionaries of Dusk.
	DO_YOU_WISH_TO_PARTICIPATE_UNTIL_THE_NEXT_SEAL_VALIDATION_PERIOD_YOU_WILL_BE_A_MEMBER_OF_THE_REVOLUTIONARIES_OF_DUSK(1272),
	// Message: You will participate in the Seven Signs as a member of the Lords of Dawn.
	YOU_WILL_PARTICIPATE_IN_THE_SEVEN_SIGNS_AS_A_MEMBER_OF_THE_LORDS_OF_DAWN(1273),
	// Message: You will participate in the Seven Signs as a member of the Revolutionaries of Dusk.
	YOU_WILL_PARTICIPATE_IN_THE_SEVEN_SIGNS_AS_A_MEMBER_OF_THE_REVOLUTIONARIES_OF_DUSK(1274),
	// Message: You've chosen to fight for the Seal of Avarice during this quest event period.
	YOUVE_CHOSEN_TO_FIGHT_FOR_THE_SEAL_OF_AVARICE_DURING_THIS_QUEST_EVENT_PERIOD(1275),
	// Message: You've chosen to fight for the Seal of Gnosis during this quest event period.
	YOUVE_CHOSEN_TO_FIGHT_FOR_THE_SEAL_OF_GNOSIS_DURING_THIS_QUEST_EVENT_PERIOD(1276),
	// Message: You've chosen to fight for the Seal of Strife during this quest event period.
	YOUVE_CHOSEN_TO_FIGHT_FOR_THE_SEAL_OF_STRIFE_DURING_THIS_QUEST_EVENT_PERIOD(1277),
	// Message: The NPC server is not operating at this time.
	THE_NPC_SERVER_IS_NOT_OPERATING_AT_THIS_TIME(1278),
	// Message: Contribution level has exceeded the limit. You may not continue.
	CONTRIBUTION_LEVEL_HAS_EXCEEDED_THE_LIMIT(1279),
	// Message: Magic Critical Hit!
	MAGIC_CRITICAL_HIT(1280),
	// Message: Your excellent shield defense was a success!
	YOUR_EXCELLENT_SHIELD_DEFENSE_WAS_A_SUCCESS(1281),
	// Message: Your Karma has been changed to $s1.
	YOUR_KARMA_HAS_BEEN_CHANGED_TO_S1(1282),
	// Message: The Lower Detail option has been activated.
	THE_LOWER_DETAIL_OPTION_HAS_BEEN_ACTIVATED(1283),
	// Message: The Lower Detail option has been deactivated.
	THE_LOWER_DETAIL_OPTION_HAS_BEEN_DEACTIVATED(1284),
	// Message: No inventory exists. You cannot purchase an item.
	NO_INVENTORY_EXISTS(1285),
	// Message: (Until next Monday at 6:00 p.m.)
	UNTIL_NEXT_MONDAY_AT_600_P(1286),
	// Message: (Until today at 6:00 p.m.)
	UNTIL_TODAY_AT_600_P(1287),
	// Message: If trends continue, $s1 will win and the seal will belong to:
	IF_TRENDS_CONTINUE_S1_WILL_WIN_AND_THE_SEAL_WILL_BELONG_TO(1288),
	// Message: Since the seal was owned during the previous period and 10% or more people have participated.
	SINCE_THE_SEAL_WAS_OWNED_DURING_THE_PREVIOUS_PERIOD_AND_10_OR_MORE_PEOPLE_HAVE_PARTICIPATED(1289),
	// Message: Although the seal was not owned, 35% or more people have participated.
	ALTHOUGH_THE_SEAL_WAS_NOT_OWNED_35_OR_MORE_PEOPLE_HAVE_PARTICIPATED(1290),
	// Message: Although the seal was owned during the previous period, less than 10% of people have voted.
	ALTHOUGH_THE_SEAL_WAS_OWNED_DURING_THE_PREVIOUS_PERIOD_LESS_THAN_10_OF_PEOPLE_HAVE_VOTED(1291),
	// Message: Since the seal was not owned during the previous period, and since less than 35 percent of people have voted.
	SINCE_THE_SEAL_WAS_NOT_OWNED_DURING_THE_PREVIOUS_PERIOD_AND_SINCE_LESS_THAN_35_PERCENT_OF_PEOPLE_HAVE_VOTED(1292),
	// Message: If current trends continue, it will end in a tie.
	IF_CURRENT_TRENDS_CONTINUE_IT_WILL_END_IN_A_TIE(1293),
	// Message: The competition has ended in a tie. Therefore, nobody has been awarded the seal.
	THE_COMPETITION_HAS_ENDED_IN_A_TIE(1294),
	// Message: Subclasses may not be created or changed while a skill is in use.
	SUBCLASSES_MAY_NOT_BE_CREATED_OR_CHANGED_WHILE_A_SKILL_IS_IN_USE(1295),
	// Message: You have exited the party room.
	YOU_HAVE_EXITED_THE_PARTY_ROOM(1391),
	// Message: $c1 has left the party room.
	C1_HAS_LEFT_THE_PARTY_ROOM(1392),
	// Message: You have been ousted from the party room.
	YOU_HAVE_BEEN_OUSTED_FROM_THE_PARTY_ROOM(1393),
	// Message: $c1 has been kicked from the party room.
	C1_HAS_BEEN_KICKED_FROM_THE_PARTY_ROOM(1394),
	// Message: The party room has been disbanded.
	THE_PARTY_ROOM_HAS_BEEN_DISBANDED(1395),
	// Message: The list of party rooms can only be viewed by a person who is not part of a party.
	THE_LIST_OF_PARTY_ROOMS_CAN_ONLY_BE_VIEWED_BY_A_PERSON_WHO_IS_NOT_PART_OF_A_PARTY(1396),
	// Message: $s1 CP has been restored.
	S1_CP_HAS_BEEN_RESTORED(1405),
	// Message: $s2 CP has been restored by $c1.
	S2_CP_HAS_BEEN_RESTORED_BY_C1(1406),
	// Message: You are using a computer that does not allow you to log in with two accounts at the same time.
	YOU_ARE_USING_A_COMPUTER_THAT_DOES_NOT_ALLOW_YOU_TO_LOG_IN_WITH_TWO_ACCOUNTS_AT_THE_SAME_TIME(1407),
	// Message: Your prepaid remaining usage time is $s1 hours and $s2 minutes.  You have $s3 paid reservations lel2ft.
	YOUR_PREPAID_REMAINING_USAGE_TIME_IS_S1_HOURS_AND_S2_MINUTES(1408),
	// Message: Your prepaid usage time has expired. Your new prepaid reservation will be used. The remaining usage time is $s1 hours and $s2 minutes.
	YOUR_PREPAID_USAGE_TIME_HAS_EXPIRED(1409),
	// Message: Your prepaid usage time has expired. You do not have any more prepaid reservations lel2ft.
	YOUR_PREPAID_USAGE_TIME_HAS_EXPIRED_(1410),
	// Message: The number of your prepaid reservations has changed.
	THE_NUMBER_OF_YOUR_PREPAID_RESERVATIONS_HAS_CHANGED(1411),
	// Message: Your prepaid usage time has $s1 minutes lel2ft.
	YOUR_PREPAID_USAGE_TIME_HAS_S1_MINUTES_LEFT(1412),
	// Message: There is no skill that enables enchant.
	THERE_IS_NO_SKILL_THAT_ENABLES_ENCHANT(1438),
	// Message: You do not have all of the items needed to enchant that skill.
	YOU_DO_NOT_HAVE_ALL_OF_THE_ITEMS_NEEDED_TO_ENCHANT_THAT_SKILL(1439),
	// Message: Skill enchant was successful! $s1 has been enchanted.
	SKILL_ENCHANT_WAS_SUCCESSFUL_S1_HAS_BEEN_ENCHANTED(1440),
	// Message: Skill enchant failed. The skill will be initialized.
	SKILL_ENCHANT_FAILED(1441),
	// Message: Remaining Time: $s1 second(s)
	REMAINING_TIME_S1_SECONDS(1442),
	// Message: You do not have enough SP to enchant that skill.
	YOU_DO_NOT_HAVE_ENOUGH_SP_TO_ENCHANT_THAT_SKILL(1443),
	// Message: You do not have enough experience (Exp) to enchant that skill.
	YOU_DO_NOT_HAVE_ENOUGH_EXPERIENCE_EXP_TO_ENCHANT_THAT_SKILL(1444),
	// Message: You've got a bite!
	YOUVE_GOT_A_BITE(1449),
	// Message: That fish is more determined than you are - it spit the hook!
	THAT_FISH_IS_MORE_DETERMINED_THAN_YOU_ARE__IT_SPIT_THE_HOOK(1450),
	// Message: Your bait was stolen by that fish!
	YOUR_BAIT_WAS_STOLEN_BY_THAT_FISH(1451),
	// Message: The bait has been lost because the fish got away.
	THE_BAIT_HAS_BEEN_LOST_BECAUSE_THE_FISH_GOT_AWAY(1452),
	// Message: You do not have a fishing pole equipped.
	YOU_DO_NOT_HAVE_A_FISHING_POLE_EQUIPPED(1453),
	// Message: You must put bait on your hook before you can fish.
	YOU_MUST_PUT_BAIT_ON_YOUR_HOOK_BEFORE_YOU_CAN_FISH(1454),
	// Message: You cannot fish while under water.
	YOU_CANNOT_FISH_WHILE_UNDER_WATER(1455),
	// Message: You cannot fish while riding as a passenger of a boat - it's against the rules.
	YOU_CANNOT_FISH_WHILE_RIDING_AS_A_PASSENGER_OF_A_BOAT__ITS_AGAINST_THE_RULES(1456),
	// Message: You can't fish here.
	YOU_CANT_FISH_HERE(1457),
	// Message: Your attempt at fishing has been cancelled.
	YOUR_ATTEMPT_AT_FISHING_HAS_BEEN_CANCELLED(1458),
	// Message: You do not have enough bait.
	YOU_DO_NOT_HAVE_ENOUGH_BAIT(1459),
	// Message: You reel your line in and stop fishing.
	YOU_REEL_YOUR_LINE_IN_AND_STOP_FISHING(1460),
	// Message: You cast your line and start to fish.
	YOU_CAST_YOUR_LINE_AND_START_TO_FISH(1461),
	// Message: The fish has resisted your attempt to bring it in.
	THE_FISH_HAS_RESISTED_YOUR_ATTEMPT_TO_BRING_IT_IN(1464),
	// Message: Your pumping is successful, causing $s1 damage.
	YOUR_PUMPING_IS_SUCCESSFUL_CAUSING_S1_DAMAGE(1465),
	// Message: You failed to do anything with the fish and it regains $s1 HP.
	YOU_FAILED_TO_DO_ANYTHING_WITH_THE_FISH_AND_IT_REGAINS_S1_HP(1466),
	// Message: You reel that fish in closer and cause $s1 damage.
	YOU_REEL_THAT_FISH_IN_CLOSER_AND_CAUSE_S1_DAMAGE(1467),
	// Message: You failed to reel that fish in further and it regains $s1 HP.
	YOU_FAILED_TO_REEL_THAT_FISH_IN_FURTHER_AND_IT_REGAINS_S1_HP(1468),
	// Message: You caught something!
	YOU_CAUGHT_SOMETHING(1469),
	// Message: You cannot do that while fishing.
	YOU_CANNOT_DO_THAT_WHILE_FISHING_(1470),
	// Message: You cannot do that while fishing.
	YOU_CANNOT_DO_THAT_WHILE_FISHING__(1471),
	// Message: Your opponent made haste with their tail between their legs; the match has been cancelled.
	YOUR_OPPONENT_MADE_HASTE_WITH_THEIR_TAIL_BETWEEN_THEIR_LEGS_THE_MATCH_HAS_BEEN_CANCELLED(1493),
	// Message: Your opponent does not meet the requirements to do battle; the match has been cancelled.
	YOUR_OPPONENT_DOES_NOT_MEET_THE_REQUIREMENTS_TO_DO_BATTLE_THE_MATCH_HAS_BEEN_CANCELLED(1494),
	// Message: The match will start in $s1 second(s).
	THE_MATCH_WILL_START_IN_S1_SECONDS(1495),
	// Message: The match has started. Fight!
	THE_MATCH_HAS_STARTED(1496),
	// Message: Congratulations, $c1! You win the match!
	CONGRATULATIONS_C1_YOU_WIN_THE_MATCH(1497),
	// Message: There is no victor; the match ends in a tie.
	THERE_IS_NO_VICTOR_THE_MATCH_ENDS_IN_A_TIE(1498),
	// Message: You will be moved back to town in $s1 second(s).
	YOU_WILL_BE_MOVED_BACK_TO_TOWN_IN_S1_SECONDS(1499),
	// Message: $c1 does not meet the participation requirements. A subclass character cannot participate in the Olympiad.
	C1_DOES_NOT_MEET_THE_PARTICIPATION_REQUIREMENTS(1500),
	// Message: $c1 does not meet the participation requirements. Only Noblesse characters can participate in the Olympiad.
	C1_DOES_NOT_MEET_THE_PARTICIPATION_REQUIREMENTS_(1501),
	// Message: $c1 is already registered on the match waiting list.
	C1_IS_ALREADY_REGISTERED_ON_THE_MATCH_WAITING_LIST(1502),
	// Message: You have been registered for the Grand Olympiad waiting list for a class specific match.
	YOU_HAVE_BEEN_REGISTERED_FOR_THE_GRAND_OLYMPIAD_WAITING_LIST_FOR_A_CLASS_SPECIFIC_MATCH(1503),
	// Message: You are currently registered for a 1v1 class irrelevant match.
	YOU_ARE_CURRENTLY_REGISTERED_FOR_A_1V1_CLASS_IRRELEVANT_MATCH(1504),
	// Message: You have been removed from the Grand Olympiad waiting list.
	YOU_HAVE_BEEN_REMOVED_FROM_THE_GRAND_OLYMPIAD_WAITING_LIST(1505),
	// Message: You are not currently registered for the Grand Olympiad.
	YOU_ARE_NOT_CURRENTLY_REGISTERED_FOR_THE_GRAND_OLYMPIAD(1506),
	// Message: You cannot equip that item in a Grand Olympiad match.
	YOU_CANNOT_EQUIP_THAT_ITEM_IN_A_GRAND_OLYMPIAD_MATCH(1507),
	// Message: You cannot use that item in a Grand Olympiad match.
	YOU_CANNOT_USE_THAT_ITEM_IN_A_GRAND_OLYMPIAD_MATCH(1508),
	// Message: You cannot use that skill in a Grand Olympiad match.
	YOU_CANNOT_USE_THAT_SKILL_IN_A_GRAND_OLYMPIAD_MATCH(1509),
	// Message: $c1 is making an attempt to resurrect you. If you choose this path, $s2 experience points will be returned to you. Do you want to be resurrected?
	C1_IS_MAKING_AN_ATTEMPT_TO_RESURRECT_YOU(1510),
	// Message: The target is unavailable for seeding.
	THE_TARGET_IS_UNAVAILABLE_FOR_SEEDING(1516),
	// Message: The Blessed Enchant failed. The enchant value of the item became 0.
	THE_BLESSED_ENCHANT_FAILED(1517),
	// Message: You do not meet the required condition to equip that item.
	YOU_DO_NOT_MEET_THE_REQUIRED_CONDITION_TO_EQUIP_THAT_ITEM(1518),
	// Message: The pet has been killed. If you don't resurrect it within 24 hours, the pet's body will disappear along with all the pet's items.
	THE_PET_HAS_BEEN_KILLED(1519),
	// Message: Your servitor passed away.
	YOUR_SERVITOR_PASSED_AWAY(1520),
	// Message: Your servitor has vanished! You'll need to summon a new one.
	YOUR_SERVITOR_HAS_VANISHED_YOULL_NEED_TO_SUMMON_A_NEW_ONE(1521),
	// Message: Your pet's corpse has decayed!
	YOUR_PETS_CORPSE_HAS_DECAYED(1522),
	// Message: You should release your pet or servitor so that it does not fall off of the boat and drown!
	YOU_SHOULD_RELEASE_YOUR_PET_OR_SERVITOR_SO_THAT_IT_DOES_NOT_FALL_OFF_OF_THE_BOAT_AND_DROWN(1523),
	// Message: Welcome to Rune Harbor.
	WELCOME_TO_RUNE_HARBOR(1620),
	// Message: The Grand Olympiad Games are not currently in progress.
	THE_GRAND_OLYMPIAD_GAMES_ARE_NOT_CURRENTLY_IN_PROGRESS(1651),
	// Message: You are now recording gameplay.
	YOU_ARE_NOW_RECORDING_GAMEPLAY(1652),
	// Message: Your recording has been successfully stored. ($s1)
	YOUR_RECORDING_HAS_BEEN_SUCCESSFULLY_STORED(1653),
	// Message: The attempt to record the replay file has failed.
	THE_ATTEMPT_TO_RECORD_THE_REPLAY_FILE_HAS_FAILED(1654),
	// Message: You caught something smelly and scary, maybe you should throw it back!?
	YOU_CAUGHT_SOMETHING_SMELLY_AND_SCARY_MAYBE_YOU_SHOULD_THROW_IT_BACK(1655),
	// Message: You have successfully traded the item with the NPC.
	YOU_HAVE_SUCCESSFULLY_TRADED_THE_ITEM_WITH_THE_NPC(1656),
	// Message: Lethal Strike!
	LETHAL_STRIKE(1667),
	// Message: Your lethal strike was successful!
	YOUR_LETHAL_STRIKE_WAS_SUCCESSFUL(1668),
	// Message: There was nothing found inside.
	THERE_WAS_NOTHING_FOUND_INSIDE(1669),
	// Message: Your reeling was successful! (Mastery Penalty:$s1 )
	YOUR_REELING_WAS_SUCCESSFUL_MASTERY_PENALTYS1_(1671),
	// Message: Your pumping was successful! (Mastery Penalty:$s1 )
	YOUR_PUMPING_WAS_SUCCESSFUL_MASTERY_PENALTYS1_(1672),
	// Message: For the current Grand Olympiad you have participated in $s1 match(es). $s2 win(s) and $s3 defeat(s). You currently have $s4 Olympiad Point(s).
	FOR_THE_CURRENT_GRAND_OLYMPIAD_YOU_HAVE_PARTICIPATED_IN_S1_MATCHES(1673),
	// Message: This command can only be used by a Noblesse.
	THIS_COMMAND_CAN_ONLY_BE_USED_BY_A_NOBLESSE(1674),
	// Message: A manor cannot be set up between 4:30 am and 8 pm.
	A_MANOR_CANNOT_BE_SET_UP_BETWEEN_430_AM_AND_8_PM(1675),
	// Message: You do not have a servitor or pet and therefore cannot use the automatic-use function.
	YOU_DO_NOT_HAVE_A_SERVITOR_OR_PET_AND_THEREFORE_CANNOT_USE_THE_AUTOMATICUSE_FUNCTION(1676),
	// Message: A cease-fire during a Clan War can not be called while members of your clan are engaged in battle.
	A_CEASEFIRE_DURING_A_CLAN_WAR_CAN_NOT_BE_CALLED_WHILE_MEMBERS_OF_YOUR_CLAN_ARE_ENGAGED_IN_BATTLE(1677),
	// Message: You have not declared a Clan War against the clan $s1.
	YOU_HAVE_NOT_DECLARED_A_CLAN_WAR_AGAINST_THE_CLAN_S1(1678),
	// Message: Only the creator of a command channel can issue a global command.
	ONLY_THE_CREATOR_OF_A_COMMAND_CHANNEL_CAN_ISSUE_A_GLOBAL_COMMAND(1679),
	// Message: $c1 has declined the channel invitation.
	C1_HAS_DECLINED_THE_CHANNEL_INVITATION(1680),
	// Message: Since $c1 did not respond, your channel invitation has failed.
	SINCE_C1_DID_NOT_RESPOND_YOUR_CHANNEL_INVITATION_HAS_FAILED(1681),
	// Message: Only the creator of a command channel can use the channel dismiss command.
	ONLY_THE_CREATOR_OF_A_COMMAND_CHANNEL_CAN_USE_THE_CHANNEL_DISMISS_COMMAND(1682),
	// Message: Only a party leader can leave a command channel.
	ONLY_A_PARTY_LEADER_CAN_LEAVE_A_COMMAND_CHANNEL(1683),
	// Message: A Clan War can not be declared against a clan that is being dissolved.
	A_CLAN_WAR_CAN_NOT_BE_DECLARED_AGAINST_A_CLAN_THAT_IS_BEING_DISSOLVED(1684),
	// Message: You are unable to equip this item when your PK count is greater than or equal to one.
	YOU_ARE_UNABLE_TO_EQUIP_THIS_ITEM_WHEN_YOUR_PK_COUNT_IS_GREATER_THAN_OR_EQUAL_TO_ONE(1685),
	// Message: Stones and mortar tumble to the earth - the castle wall has taken damage!
	STONES_AND_MORTAR_TUMBLE_TO_THE_EARTH__THE_CASTLE_WALL_HAS_TAKEN_DAMAGE(1686),
	// Message: This area cannot be entered while mounted atop of a Wyvern.  You will be dismounted from your Wyvern if you do not leave!
	THIS_AREA_CANNOT_BE_ENTERED_WHILE_MOUNTED_ATOP_OF_A_WYVERN(1687),
	// Message: You cannot enchant while operating a Private Store or Private Workshop.
	YOU_CANNOT_ENCHANT_WHILE_OPERATING_A_PRIVATE_STORE_OR_PRIVATE_WORKSHOP(1688),
	// Message: $c1 is already registered on the class match waiting list.
	C1_IS_ALREADY_REGISTERED_ON_THE_CLASS_MATCH_WAITING_LIST(1689),
	// Message: $c1 is already registered on the waiting list for the class irrelevant individual match.
	C1_IS_ALREADY_REGISTERED_ON_THE_WAITING_LIST_FOR_THE_CLASS_IRRELEVANT_INDIVIDUAL_MATCH(1690),
	// Message: $c1 does not meet the participation requirements. You cannot participate in the Olympiad because your inventory slot exceeds 80%.
	C1_DOES_NOT_MEET_THE_PARTICIPATION_REQUIREMENTS__(1691),
	// Message: $c1 does not meet the participation requirements. You cannot participate in the Olympiad because you have changed to your sub-class.
	C1_DOES_NOT_MEET_THE_PARTICIPATION_REQUIREMENTS___(1692),
	// Message: You may not observe a Grand Olympiad Games match while you are on the waiting list.
	YOU_MAY_NOT_OBSERVE_A_GRAND_OLYMPIAD_GAMES_MATCH_WHILE_YOU_ARE_ON_THE_WAITING_LIST(1693),
	// Message: Only a clan leader that is a Noblesse can view the Siege War Status window during a siege war.
	ONLY_A_CLAN_LEADER_THAT_IS_A_NOBLESSE_CAN_VIEW_THE_SIEGE_WAR_STATUS_WINDOW_DURING_A_SIEGE_WAR(1694),
	// Message: Your apprentice, $s1, has logged in.
	YOUR_APPRENTICE_S1_HAS_LOGGED_IN(1756),
	// Message: Your apprentice, $c1, has logged out.
	YOUR_APPRENTICE_C1_HAS_LOGGED_OUT(1757),
	// Message: Your sponsor, $c1, has logged in.
	YOUR_SPONSOR_C1_HAS_LOGGED_IN(1758),
	// Message: Your sponsor, $c1, has logged out.
	YOUR_SPONSOR_C1_HAS_LOGGED_OUT(1759),
	// Message: Since the Clan Reputation Score has dropped to 0 or lower, your clan skill(s) will be de-activated.
	SINCE_THE_CLAN_REPUTATION_SCORE_HAS_DROPPED_TO_0_OR_LOWER_YOUR_CLAN_SKILLS_WILL_BE_DEACTIVATED(1789),
	// Message: The conditions necessary to increase the clan's level have not been met.
	THE_CONDITIONS_NECESSARY_TO_INCREASE_THE_CLANS_LEVEL_HAVE_NOT_BEEN_MET(1790),
	// Message: The conditions necessary to create a military unit have not been met.
	THE_CONDITIONS_NECESSARY_TO_CREATE_A_MILITARY_UNIT_HAVE_NOT_BEEN_MET(1791),
	// Message: Please assign a manager for your new Order of Knights.
	PLEASE_ASSIGN_A_MANAGER_FOR_YOUR_NEW_ORDER_OF_KNIGHTS(1792),
	// Message: $c1 has been selected as the captain of $s2.
	C1_HAS_BEEN_SELECTED_AS_THE_CAPTAIN_OF_S2(1793),
	// Message: The Knights of $s1 have been created.
	THE_KNIGHTS_OF_S1_HAVE_BEEN_CREATED(1794),
	// Message: The Royal Guard of $s1 have been created.
	THE_ROYAL_GUARD_OF_S1_HAVE_BEEN_CREATED(1795),
	// Message: Your account has been temporarily suspended because of involvement in account theft or other abnormal game play which has harmed or inconvenienced other players. If you feel that you were not involved with any of these violations, please visit the Support Center on the PlayNC website (http://us.ncsol2ft.com/support/).
	YOUR_ACCOUNT_HAS_BEEN_TEMPORARILY_SUSPENDED_BECAUSE_OF_INVOLVEMENT_IN_ACCOUNT_THEFT_OR_OTHER_ABNORMAL_GAME_PLAY_WHICH_HAS_HARMED_OR_INCONVENIENCED_OTHER_PLAYERS(1796),
	// Message: $c1 has been promoted to $s2.
	C1_HAS_BEEN_PROMOTED_TO_S2(1797),
	// Message: Clan lord privileges have been transferred to $c1.
	CLAN_LORD_PRIVILEGES_HAVE_BEEN_TRANSFERRED_TO_C1(1798),
	// Message: We are searching for BOT users. Please try again later.
	WE_ARE_SEARCHING_FOR_BOT_USERS(1799),
	// Message: The registration period for a clan hall war has ended.
	THE_REGISTRATION_PERIOD_FOR_A_CLAN_HALL_WAR_HAS_ENDED(1823),
	// Message: You have been registered for a clan hall war.  Please move to the left side of the clan hall's arena and get ready.
	YOU_HAVE_BEEN_REGISTERED_FOR_A_CLAN_HALL_WAR(1824),
	// Message: You have failed in your attempt to register for the clan hall war. Please try again.
	YOU_HAVE_FAILED_IN_YOUR_ATTEMPT_TO_REGISTER_FOR_THE_CLAN_HALL_WAR(1825),
	// Message: In $s1 minute(s), the game will begin. All players must hurry and move to the left side of the clan hall's arena.
	IN_S1_MINUTES_THE_GAME_WILL_BEGIN(1826),
	// Message: In $s1 minute(s), the game will begin. All players, please enter the arena now.
	IN_S1_MINUTES_THE_GAME_WILL_BEGIN_(1827),
	// Message: In $s1 second(s), the game will begin.
	IN_S1_SECONDS_THE_GAME_WILL_BEGIN(1828),
	// Message: The Command Channel is full.
	THE_COMMAND_CHANNEL_IS_FULL(1829),
	// Message: $c1 is not allowed to use the party room invite command. Please update the waiting list.
	C1_IS_NOT_ALLOWED_TO_USE_THE_PARTY_ROOM_INVITE_COMMAND(1830),
	// Message: $c1 does not meet the conditions of the party room. Please update the waiting list.
	C1_DOES_NOT_MEET_THE_CONDITIONS_OF_THE_PARTY_ROOM(1831),
	// Message: Only a room leader may invite others to a party room.
	ONLY_A_ROOM_LEADER_MAY_INVITE_OTHERS_TO_A_PARTY_ROOM(1832),
	// Message: All of $s1 will be dropped. Would you like to continue?
	ALL_OF_S1_WILL_BE_DROPPED(1833),
	// Message: The party room is full. No more characters can be invited in.
	THE_PARTY_ROOM_IS_FULL(1834),
	// Message: You cannot join a Clan Academy because you have successfully completed your 2nd class transfer.
	YOU_CANNOT_JOIN_A_CLAN_ACADEMY_BECAUSE_YOU_HAVE_SUCCESSFULLY_COMPLETED_YOUR_2ND_CLASS_TRANSFER(1836),
	// Message: $c1 has sent you an invitation to join the $s3 Royal Guard under the $s2 clan. Would you like to join?
	C1_HAS_SENT_YOU_AN_INVITATION_TO_JOIN_THE_S3_ROYAL_GUARD_UNDER_THE_S2_CLAN(1837),
	// Message: 1. The coupon can be used once per character.
	_1(1838),
	// Message: 2. A used serial number may not be used again.
	_2(1839),
	// Message: 3. If you enter the incorrect serial number more than 5 times,\n   you may use it again after a certain amount of time passes.
	_3(1840),
	// Message: This clan hall war has been cancelled.  Not enough clans have registered.
	THIS_CLAN_HALL_WAR_HAS_BEEN_CANCELLED(1841),
	// Message: $c1 wishes to summon you from $s2. Do you accept?
	C1_WISHES_TO_SUMMON_YOU_FROM_S2(1842),
	// Message: $c1 is engaged in combat and cannot be summoned.
	C1_IS_ENGAGED_IN_COMBAT_AND_CANNOT_BE_SUMMONED(1843),
	// Message: $c1 is dead at the moment and cannot be summoned.
	C1_IS_DEAD_AT_THE_MOMENT_AND_CANNOT_BE_SUMMONED(1844),
	// Message: Hero weapons cannot be destroyed.
	HERO_WEAPONS_CANNOT_BE_DESTROYED(1845),
	// Message: You are too far away from your mount to ride.
	YOU_ARE_TOO_FAR_AWAY_FROM_YOUR_MOUNT_TO_RIDE(1846),
	// Message: You caught a fish $s1 in length.
	YOU_CAUGHT_A_FISH_S1_IN_LENGTH(1847),
	// Message: Because of the size of fish caught, you will be registered in the ranking.
	BECAUSE_OF_THE_SIZE_OF_FISH_CAUGHT_YOU_WILL_BE_REGISTERED_IN_THE_RANKING(1848),
	// Message: All of $s1 will be discarded. Would you like to continue?
	ALL_OF_S1_WILL_BE_DISCARDED(1849),
	// Message: The Captain of the Order of Knights cannot be appointed.
	THE_CAPTAIN_OF_THE_ORDER_OF_KNIGHTS_CANNOT_BE_APPOINTED(1850),
	// Message: The Captain of the Royal Guard cannot be appointed.
	THE_CAPTAIN_OF_THE_ROYAL_GUARD_CANNOT_BE_APPOINTED(1851),
	// Message: The attempt to acquire the skill has failed because of an insufficient Clan Reputation Score.
	THE_ATTEMPT_TO_ACQUIRE_THE_SKILL_HAS_FAILED_BECAUSE_OF_AN_INSUFFICIENT_CLAN_REPUTATION_SCORE(1852),
	// Message: Quantity items of the same type cannot be exchanged at the same time.
	QUANTITY_ITEMS_OF_THE_SAME_TYPE_CANNOT_BE_EXCHANGED_AT_THE_SAME_TIME(1853),
	// Message: The item was converted successfully.
	THE_ITEM_WAS_CONVERTED_SUCCESSFULLY(1854),
	// Message: Another military unit is already using that name. Please enter a different name.
	ANOTHER_MILITARY_UNIT_IS_ALREADY_USING_THAT_NAME(1855),
	// Message: Since your opponent is now the owner of $s1, the Olympiad has been cancelled.
	SINCE_YOUR_OPPONENT_IS_NOW_THE_OWNER_OF_S1_THE_OLYMPIAD_HAS_BEEN_CANCELLED(1856),
	// Message: $c1 is the owner of $s2 and cannot participate in the Olympiad.
	C1_IS_THE_OWNER_OF_S2_AND_CANNOT_PARTICIPATE_IN_THE_OLYMPIAD(1857),
	// Message: $c1 is currently dead and cannot participate in the Olympiad.
	C1_IS_CURRENTLY_DEAD_AND_CANNOT_PARTICIPATE_IN_THE_OLYMPIAD(1858),
	// Message: You have exceeded the quantity that can be moved at one time.
	YOU_HAVE_EXCEEDED_THE_QUANTITY_THAT_CAN_BE_MOVED_AT_ONE_TIME(1859),
	// Message: The Clan Reputation Score is too low.
	THE_CLAN_REPUTATION_SCORE_IS_TOO_LOW(1860),
	// Message: The clan's crest has been deleted.
	THE_CLANS_CREST_HAS_BEEN_DELETED(1861),
	// Message: Clan skills will now be activated since the clan's reputation score is 0 or higher.
	CLAN_SKILLS_WILL_NOW_BE_ACTIVATED_SINCE_THE_CLANS_REPUTATION_SCORE_IS_0_OR_HIGHER(1862),
	// Message: $c1 purchased a clan item, reducing the Clan Reputation by $s2 points.
	C1_PURCHASED_A_CLAN_ITEM_REDUCING_THE_CLAN_REPUTATION_BY_S2_POINTS(1863),
	// Message: Your pet/servitor is unresponsive and will not obey any orders.
	YOUR_PETSERVITOR_IS_UNRESPONSIVE_AND_WILL_NOT_OBEY_ANY_ORDERS(1864),
	// Message: Your pet/servitor is currently in a state of distress.
	YOUR_PETSERVITOR_IS_CURRENTLY_IN_A_STATE_OF_DISTRESS(1865),
	// Message: MP was reduced by $s1.
	MP_WAS_REDUCED_BY_S1(1866),
	// Message: Your opponent's MP was reduced by $s1.
	YOUR_OPPONENTS_MP_WAS_REDUCED_BY_S1(1867),
	// Message: You cannot exchange an item while it is being used.
	YOU_CANNOT_EXCHANGE_AN_ITEM_WHILE_IT_IS_BEING_USED(1868),
	// Message: $c1 has granted the Command Channel's master party the privilege of item looting.
	C1_HAS_GRANTED_THE_COMMAND_CHANNELS_MASTER_PARTY_THE_PRIVILEGE_OF_ITEM_LOOTING(1869),
	// Message: A Command Channel with looting rights already exists.
	A_COMMAND_CHANNEL_WITH_LOOTING_RIGHTS_ALREADY_EXISTS(1870),
	// Message: Do you want to dismiss $c1 from the clan?
	DO_YOU_WANT_TO_DISMISS_C1_FROM_THE_CLAN(1871),
	// Message: You have $s1 hour(s) and $s2 minute(s) lel2ft.
	YOU_HAVE_S1_HOURS_AND_S2_MINUTES_LEFT(1872),
	// Message: There are $s1 hour(s) and $s2 minute(s) left in the fixed use time for this PC Cafй.
	THERE_ARE_S1_HOURS_AND_S2_MINUTES_LEFT_IN_THE_FIXED_USE_TIME_FOR_THIS_PC_CAF(1873),
	// Message: There are $s1 minute(s) left for this individual user.
	THERE_ARE_S1_MINUTES_LEFT_FOR_THIS_INDIVIDUAL_USER(1874),
	// Message: There are $s1 minute(s) left in the fixed use time for this PC Cafй.
	THERE_ARE_S1_MINUTES_LEFT_IN_THE_FIXED_USE_TIME_FOR_THIS_PC_CAF(1875),
	// Message: Do you want to leave $s1 clan?
	DO_YOU_WANT_TO_LEAVE_S1_CLAN(1876),
	// Message: The game will end in $s1 minute(s).
	THE_GAME_WILL_END_IN_S1_MINUTES(1877),
	// Message: The game will end in $s1 second(s).
	THE_GAME_WILL_END_IN_S1_SECONDS(1878),
	// Message: In $s1 minute(s), you will be teleported outside of the game arena.
	IN_S1_MINUTES_YOU_WILL_BE_TELEPORTED_OUTSIDE_OF_THE_GAME_ARENA(1879),
	// Message: In $s1 second(s), you will be teleported outside of the game arena.
	IN_S1_SECONDS_YOU_WILL_BE_TELEPORTED_OUTSIDE_OF_THE_GAME_ARENA(1880),
	// Message: The preliminary match will begin in $s1 second(s). Prepare yourself.
	THE_PRELIMINARY_MATCH_WILL_BEGIN_IN_S1_SECONDS(1881),
	// Message: Characters cannot be created from this server.
	CHARACTERS_CANNOT_BE_CREATED_FROM_THIS_SERVER(1882),
	// Message: There are no offerings I own or I made a bid for.
	THERE_ARE_NO_OFFERINGS_I_OWN_OR_I_MADE_A_BID_FOR(1883),

	// Message: You may not use items in a private store or private work shop.
	YOU_MAY_NOT_USE_ITEMS_IN_A_PRIVATE_STORE_OR_PRIVATE_WORK_SHOP(1891),

	// Message: A sub-class cannot be created or changed while you are over your weight limit.
	A_SUBCLASS_CANNOT_BE_CREATED_OR_CHANGED_WHILE_YOU_ARE_OVER_YOUR_WEIGHT_LIMIT(1894),
	// Message: $c1 has entered the party room.
	C1_HAS_ENTERED_THE_PARTY_ROOM(1900),
	// Message: $s1 has sent an invitation to room <$s2>.
	S1_HAS_SENT_AN_INVITATION_TO_ROOM_S2(1901),
	// Message: Incompatible item grade.  This item cannot be used.
	INCOMPATIBLE_ITEM_GRADE(1902),
	// Message: Those of you who have requested NCOTP should run NCOTP \n by using your cell phone to get the NCOTP \n password and enter it within 1 minute.\n  If you have not requested NCOTP, leave this field blank and\n click the Login button.
	THOSE_OF_YOU_WHO_HAVE_REQUESTED_NCOTP_SHOULD_RUN_NCOTP_N_BY_USING_YOUR_CELL_PHONE_TO_GET_THE_NCOTP_N_PASSWORD_AND_ENTER_IT_WITHIN_1_MINUTE(1903),
	// Message: A sub-class may not be created or changed while a servitor or pet is summoned.
	A_SUBCLASS_MAY_NOT_BE_CREATED_OR_CHANGED_WHILE_A_SERVITOR_OR_PET_IS_SUMMONED(1904),
	// Message: $s2 of $s1 will be replaced with $c4 of $s3.
	S2_OF_S1_WILL_BE_REPLACED_WITH_C4_OF_S3(1905),
	// Message: Select the combat unit\n to transfer to.
	SELECT_THE_COMBAT_UNITN_TO_TRANSFER_TO(1906),
	// Message: Select the  character who will\n replace the current character.
	SELECT_THE__CHARACTER_WHO_WILLN_REPLACE_THE_CURRENT_CHARACTER(1907),
	// Message: $c1 is in a state which prevents summoning.
	C1_IS_IN_A_STATE_WHICH_PREVENTS_SUMMONING(1908),
	// Message: ==< List of Clan Academy Graduates During the Past Week >==
	_LIST_OF_CLAN_ACADEMY_GRADUATES_DURING_THE_PAST_WEEK_(1909),
	// Message: Graduates: $c1.
	GRADUATES_C1(1910),
	// Message: You cannot summon players who are currently participating in the Grand Olympiad.
	YOU_CANNOT_SUMMON_PLAYERS_WHO_ARE_CURRENTLY_PARTICIPATING_IN_THE_GRAND_OLYMPIAD(1911),
	// Message: Only those requesting NCOTP should make an entry into this field.
	ONLY_THOSE_REQUESTING_NCOTP_SHOULD_MAKE_AN_ENTRY_INTO_THIS_FIELD(1912),
	// Message: The remaining recycle time for $s1 is $s2 minute(s).
	THE_REMAINING_RECYCLE_TIME_FOR_S1_IS_S2_MINUTES(1913),
	// Message: The remaining recycle time for $s1 is $s2 second(s).
	THE_REMAINING_RECYCLE_TIME_FOR_S1_IS_S2_SECONDS(1914),
	// Message: The game will end in $s1 second(s).
	THE_GAME_WILL_END_IN_S1_SECONDS_(1915),
	// Message: Your Death Penalty is now level $s1.
	YOUR_DEATH_PENALTY_IS_NOW_LEVEL_S1(1916),
	// Message: Your Death Penalty has been lifted.
	YOUR_DEATH_PENALTY_HAS_BEEN_LIFTED(1917),
	// Message: Your pet is too high level to control.
	YOUR_PET_IS_TOO_HIGH_LEVEL_TO_CONTROL(1918),
	// Message: The Grand Olympiad registration period has ended.
	THE_GRAND_OLYMPIAD_REGISTRATION_PERIOD_HAS_ENDED(1919),
	// Message: Your account is currently inactive because you have not logged into the game for some time. You may reactivate your account by visiting the PlayNC website (http://us.ncsol2ft.com/support/).
	YOUR_ACCOUNT_IS_CURRENTLY_INACTIVE_BECAUSE_YOU_HAVE_NOT_LOGGED_INTO_THE_GAME_FOR_SOME_TIME(1920),
	// Message: $s2 hour(s) and $s3 minute(s) have passed since $s1 has killed.
	S2_HOURS_AND_S3_MINUTES_HAVE_PASSED_SINCE_S1_HAS_KILLED(1921),
	// Message: Because $s1 failed to kill for one full day, it has expired.
	BECAUSE_S1_FAILED_TO_KILL_FOR_ONE_FULL_DAY_IT_HAS_EXPIRED(1922),
	// Message: Court Magician: The portal has been created!
	COURT_MAGICIAN_THE_PORTAL_HAS_BEEN_CREATED(1923),
	// Message: Due to the affects of the Seal of Strife, it is not possible to summon at this time.
	DUE_TO_THE_AFFECTS_OF_THE_SEAL_OF_STRIFE_IT_IS_NOT_POSSIBLE_TO_SUMMON_AT_THIS_TIME(1925),
	// Message: There is no opponent to receive your challenge for a duel.
	THERE_IS_NO_OPPONENT_TO_RECEIVE_YOUR_CHALLENGE_FOR_A_DUEL(1926),
	// Message: $c1 has been challenged to a duel.
	C1_HAS_BEEN_CHALLENGED_TO_A_DUEL(1927),
	// Message: $c1's party has been challenged to a duel.
	C1S_PARTY_HAS_BEEN_CHALLENGED_TO_A_DUEL(1928),
	// Message: $c1 has accepted your challenge to a duel. The duel will begin in a few moments.
	C1_HAS_ACCEPTED_YOUR_CHALLENGE_TO_A_DUEL(1929),
	// Message: You have accepted $c1's challenge a duel. The duel will begin in a few moments.
	YOU_HAVE_ACCEPTED_C1S_CHALLENGE_A_DUEL(1930),
	// Message: $c1 has declined your challenge to a duel.
	C1_HAS_DECLINED_YOUR_CHALLENGE_TO_A_DUEL(1931),
	// Message: $c1 has declined your challenge to a duel.
	C1_HAS_DECLINED_YOUR_CHALLENGE_TO_A_DUEL_(1932),
	// Message: You have accepted $c1's challenge to a party duel. The duel will begin in a few moments.
	YOU_HAVE_ACCEPTED_C1S_CHALLENGE_TO_A_PARTY_DUEL(1933),
	// Message: $s1 has accepted your challenge to duel against their party. The duel will begin in a few moments.
	S1_HAS_ACCEPTED_YOUR_CHALLENGE_TO_DUEL_AGAINST_THEIR_PARTY(1934),
	// Message: $c1 has declined your challenge to a party duel.
	C1_HAS_DECLINED_YOUR_CHALLENGE_TO_A_PARTY_DUEL(1935),
	// Message: The opposing party has declined your challenge to a duel.
	THE_OPPOSING_PARTY_HAS_DECLINED_YOUR_CHALLENGE_TO_A_DUEL(1936),
	// Message: Since the person you challenged is not currently in a party, they cannot duel against your party.
	SINCE_THE_PERSON_YOU_CHALLENGED_IS_NOT_CURRENTLY_IN_A_PARTY_THEY_CANNOT_DUEL_AGAINST_YOUR_PARTY(1937),
	// Message: $c1 has challenged you to a duel.
	C1_HAS_CHALLENGED_YOU_TO_A_DUEL(1938),
	// Message: $c1's party has challenged your party to a duel.
	C1S_PARTY_HAS_CHALLENGED_YOUR_PARTY_TO_A_DUEL(1939),
	// Message: You are unable to request a duel at this time.
	YOU_ARE_UNABLE_TO_REQUEST_A_DUEL_AT_THIS_TIME(1940),
	// Message: This is not a suitable place to challenge anyone or party to a duel.
	THIS_IS_NOT_A_SUITABLE_PLACE_TO_CHALLENGE_ANYONE_OR_PARTY_TO_A_DUEL(1941),
	// Message: The opposing party is currently unable to accept a challenge to a duel.
	THE_OPPOSING_PARTY_IS_CURRENTLY_UNABLE_TO_ACCEPT_A_CHALLENGE_TO_A_DUEL(1942),
	// Message: The opposing party is currently not in a suitable location for a duel.
	THE_OPPOSING_PARTY_IS_CURRENTLY_NOT_IN_A_SUITABLE_LOCATION_FOR_A_DUEL(1943),
	// Message: In a moment, you will be transported to the site where the duel will take place.
	IN_A_MOMENT_YOU_WILL_BE_TRANSPORTED_TO_THE_SITE_WHERE_THE_DUEL_WILL_TAKE_PLACE(1944),
	// Message: The duel will begin in $s1 second(s).
	THE_DUEL_WILL_BEGIN_IN_S1_SECONDS(1945),
	// Message: $c1 has challenged you to a duel. Will you accept?
	C1_HAS_CHALLENGED_YOU_TO_A_DUEL_(1946),
	// Message: $c1's party has challenged your party to a duel. Will you accept?
	C1S_PARTY_HAS_CHALLENGED_YOUR_PARTY_TO_A_DUEL_(1947),
	// Message: The duel will begin in $s1 second(s).
	THE_DUEL_WILL_BEGIN_IN_S1_SECONDS_(1948),
	// Message: Let the duel begin!
	LET_THE_DUEL_BEGIN(1949),
	// Message: $c1 has won the duel.
	C1_HAS_WON_THE_DUEL(1950),
	// Message: $c1's party has won the duel.
	C1S_PARTY_HAS_WON_THE_DUEL(1951),
	// Message: The duel has ended in a tie.
	THE_DUEL_HAS_ENDED_IN_A_TIE(1952),
	// Message: Since $c1 was disqualified, $s2 has won.
	SINCE_C1_WAS_DISQUALIFIED_S2_HAS_WON(1953),
	// Message: Since $c1's party was disqualified, $s2's party has won.
	SINCE_C1S_PARTY_WAS_DISQUALIFIED_S2S_PARTY_HAS_WON(1954),
	// Message: Since $c1 withdrew from the duel, $s2 has won.
	SINCE_C1_WITHDREW_FROM_THE_DUEL_S2_HAS_WON(1955),
	// Message: Since $c1's party withdrew from the duel, $s2's party has won.
	SINCE_C1S_PARTY_WITHDREW_FROM_THE_DUEL_S2S_PARTY_HAS_WON(1956),
	// Message: Only the clan leader may issue commands.
	ONLY_THE_CLAN_LEADER_MAY_ISSUE_COMMANDS(1966),
	// Message: The gate is firmly locked. Please try again later.
	THE_GATE_IS_FIRMLY_LOCKED(1967),
	// Message: $s1's owner.
	S1S_OWNER(1968),
	// Message: Area where $s1 appears.
	AREA_WHERE_S1_APPEARS(1969),
	// Message: Once an item is augmented, it cannot be augmented again.
	ONCE_AN_ITEM_IS_AUGMENTED_IT_CANNOT_BE_AUGMENTED_AGAIN(1970),
	// Message: The level of the hardener is too high to be used.
	THE_LEVEL_OF_THE_HARDENER_IS_TOO_HIGH_TO_BE_USED(1971),
	// Message: You cannot augment items while a private store or private workshop is in operation.
	YOU_CANNOT_AUGMENT_ITEMS_WHILE_A_PRIVATE_STORE_OR_PRIVATE_WORKSHOP_IS_IN_OPERATION(1972),
	// Message: You cannot augment items while frozen.
	YOU_CANNOT_AUGMENT_ITEMS_WHILE_FROZEN(1973),
	// Message: You cannot augment items while dead.
	YOU_CANNOT_AUGMENT_ITEMS_WHILE_DEAD(1974),
	// Message: You cannot augment items while engaged in trade activities.
	YOU_CANNOT_AUGMENT_ITEMS_WHILE_ENGAGED_IN_TRADE_ACTIVITIES(1975),
	// Message: You cannot augment items while paralyzed.
	YOU_CANNOT_AUGMENT_ITEMS_WHILE_PARALYZED(1976),
	// Message: You cannot augment items while fishing.
	YOU_CANNOT_AUGMENT_ITEMS_WHILE_FISHING(1977),
	// Message: You cannot augment items while sitting down.
	YOU_CANNOT_AUGMENT_ITEMS_WHILE_SITTING_DOWN(1978),
	// Message: $s1's remaining Mana is now 10.
	S1S_REMAINING_MANA_IS_NOW_10(1979),
	// Message: $s1's remaining Mana is now 5.
	S1S_REMAINING_MANA_IS_NOW_5(1980),
	// Message: $s1's remaining Mana is now 1. It will disappear soon.
	S1S_REMAINING_MANA_IS_NOW_1(1981),
	// Message: $s1's remaining Mana is now 0, and the item has disappeared.
	S1S_REMAINING_MANA_IS_NOW_0_AND_THE_ITEM_HAS_DISAPPEARED(1982),
	// Message: The ferry has arrived at Primeval Isle.
	THE_FERRY_HAS_ARRIVED_AT_PRIMEVAL_ISLE(1988),
	// Message: The ferry will leave for Rune Harbor after anchoring for three minutes.
	THE_FERRY_WILL_LEAVE_FOR_RUNE_HARBOR_AFTER_ANCHORING_FOR_THREE_MINUTES(1989),
	// Message: The ferry is now departing Primeval Isle for Rune Harbor.
	THE_FERRY_IS_NOW_DEPARTING_PRIMEVAL_ISLE_FOR_RUNE_HARBOR(1990),
	// Message: The ferry will leave for Primeval Isle after anchoring for three minutes.
	THE_FERRY_WILL_LEAVE_FOR_PRIMEVAL_ISLE_AFTER_ANCHORING_FOR_THREE_MINUTES(1991),
	// Message: The ferry is now departing Rune Harbor for Primeval Isle.
	THE_FERRY_IS_NOW_DEPARTING_RUNE_HARBOR_FOR_PRIMEVAL_ISLE(1992),
	// Message: The ferry from Primeval Isle to Rune Harbor has been delayed.
	THE_FERRY_FROM_PRIMEVAL_ISLE_TO_RUNE_HARBOR_HAS_BEEN_DELAYED(1993),
	// Message: The ferry from Rune Harbor to Primeval Isle has been delayed.
	THE_FERRY_FROM_RUNE_HARBOR_TO_PRIMEVAL_ISLE_HAS_BEEN_DELAYED(1994),
	// Message: $c1 cannot duel because $c1 is currently engaged in a private store or manufacture.
	C1_CANNOT_DUEL_BECAUSE_C1_IS_CURRENTLY_ENGAGED_IN_A_PRIVATE_STORE_OR_MANUFACTURE(2017),
	// Message: $c1 cannot duel because $c1 is currently fishing.
	C1_CANNOT_DUEL_BECAUSE_C1_IS_CURRENTLY_FISHING(2018),
	// Message: $c1 cannot duel because $c1's HP or MP is below 50%.
	C1_CANNOT_DUEL_BECAUSE_C1S_HP_OR_MP_IS_BELOW_50(2019),
	// Message: $c1 cannot make a challenge to a duel because $c1 is currently in a duel-prohibited area (Peaceful Zone / Seven Signs Zone / Near Water / Restart Prohibited Area).
	C1_CANNOT_MAKE_A_CHALLENGE_TO_A_DUEL_BECAUSE_C1_IS_CURRENTLY_IN_A_DUELPROHIBITED_AREA_PEACEFUL_ZONE__SEVEN_SIGNS_ZONE__NEAR_WATER__RESTART_PROHIBITED_AREA(2020),
	// Message: $c1 cannot duel because $c1 is currently engaged in battle.
	C1_CANNOT_DUEL_BECAUSE_C1_IS_CURRENTLY_ENGAGED_IN_BATTLE(2021),
	// Message: $c1 cannot duel because $c1 is already engaged in a duel.
	C1_CANNOT_DUEL_BECAUSE_C1_IS_ALREADY_ENGAGED_IN_A_DUEL(2022),
	// Message: $c1 cannot duel because $c1 is in a chaotic state.
	C1_CANNOT_DUEL_BECAUSE_C1_IS_IN_A_CHAOTIC_STATE(2023),
	// Message: $c1 cannot duel because $c1 is participating in the Olympiad.
	C1_CANNOT_DUEL_BECAUSE_C1_IS_PARTICIPATING_IN_THE_OLYMPIAD(2024),
	// Message: $c1 cannot duel because $c1 is participating in a clan hall war.
	C1_CANNOT_DUEL_BECAUSE_C1_IS_PARTICIPATING_IN_A_CLAN_HALL_WAR(2025),
	// Message: $c1 cannot duel because $c1 is participating in a siege war.
	C1_CANNOT_DUEL_BECAUSE_C1_IS_PARTICIPATING_IN_A_SIEGE_WAR(2026),
	// Message: $c1 cannot duel because $c1 is currently riding a boat, steed, or strider.
	C1_CANNOT_DUEL_BECAUSE_C1_IS_CURRENTLY_RIDING_A_BOAT_STEED_OR_STRIDER(2027),
	// Message: $c1 cannot receive a duel challenge because $c1 is too far away.
	C1_CANNOT_RECEIVE_A_DUEL_CHALLENGE_BECAUSE_C1_IS_TOO_FAR_AWAY(2028),
	// Message: $c1 is currently teleporting and cannot participate in the Olympiad.
	C1_IS_CURRENTLY_TELEPORTING_AND_CANNOT_PARTICIPATE_IN_THE_OLYMPIAD(2029),
	// Message: You are currently logging in.
	YOU_ARE_CURRENTLY_LOGGING_IN(2030),
	// Message: Please wait a moment.
	PLEASE_WAIT_A_MOMENT(2031),
	// Message: It is not the right time for purchasing the item.
	IT_IS_NOT_THE_RIGHT_TIME_FOR_PURCHASING_THE_ITEM(2032),
	// Message: A sub-class cannot be created or changed because you have exceeded your inventory limit.
	A_SUBCLASS_CANNOT_BE_CREATED_OR_CHANGED_BECAUSE_YOU_HAVE_EXCEEDED_YOUR_INVENTORY_LIMIT(2033),
	// Message: $s1 clan is trying to display a flag.
	S1_CLAN_IS_TRYING_TO_DISPLAY_A_FLAG(2050),
	// Message: That weapon cannot perform any attacks.
	THAT_WEAPON_CANNOT_PERFORM_ANY_ATTACKS(2066),
	// Message: Enemy Blood Pledges have intruded into the fortress.
	ENEMY_BLOOD_PLEDGES_HAVE_INTRUDED_INTO_THE_FORTRESS(2084),
	// Message: Shout and trade chatting cannot be used while possessing a cursed weapon.
	SHOUT_AND_TRADE_CHATTING_CANNOT_BE_USED_WHILE_POSSESSING_A_CURSED_WEAPON(2085),
	// Message: Search on user $c2 for third-party program use will be completed in $s1 minute(s).
	SEARCH_ON_USER_C2_FOR_THIRDPARTY_PROGRAM_USE_WILL_BE_COMPLETED_IN_S1_MINUTES(2086),
	// Message: A fortress is under attack!
	A_FORTRESS_IS_UNDER_ATTACK(2087),
	// Message: $s1 minute(s) until the fortress battle starts.
	S1_MINUTES_UNTIL_THE_FORTRESS_BATTLE_STARTS(2088),
	// Message: $s1 second(s) until the fortress battle starts.
	S1_SECONDS_UNTIL_THE_FORTRESS_BATTLE_STARTS(2089),
	// Message: The fortress battle $s1 has begun.
	THE_FORTRESS_BATTLE_S1_HAS_BEGUN(2090),
	// Message: Your account can only be used after changing your password and quiz. \n Services will be available after changing your password and quiz from the PlayNC website (http://us.ncsol2ft.com).
	YOUR_ACCOUNT_CAN_ONLY_BE_USED_AFTER_CHANGING_YOUR_PASSWORD_AND_QUIZ(2091),
	// Message: You cannot bid due to a passed-in price.
	YOU_CANNOT_BID_DUE_TO_A_PASSEDIN_PRICE(2092),
	// Message: The bid amount was $s1 adena. Would you like to retrieve the bid amount?
	THE_BID_AMOUNT_WAS_S1_ADENA(2093),
	// Message: Another user is purchasing. Please try again later.
	ANOTHER_USER_IS_PURCHASING(2094),
	// Message: Some Lineage II features have been limited for free trials. Trial accounts have limited chatting capabilities. To unlock all of the features of Lineage II, purchase the full version today.
	SOME_LINEAGE_II_FEATURES_HAVE_BEEN_LIMITED_FOR_FREE_TRIALS_______(2095),
	// Message: $c1 is in a location which cannot be entered, therefore it cannot be processed.
	C1_IS_IN_A_LOCATION_WHICH_CANNOT_BE_ENTERED_THEREFORE_IT_CANNOT_BE_PROCESSED(2096),
	// Message: $c1's level does not correspond to the requirements for entry.
	C1S_LEVEL_DOES_NOT_CORRESPOND_TO_THE_REQUIREMENTS_FOR_ENTRY(2097),
	// Message: $c1's quest requirement is not sufficient and cannot be entered.
	C1S_QUEST_REQUIREMENT_IS_NOT_SUFFICIENT_AND_CANNOT_BE_ENTERED(2098),
	// Message: $c1's item requirement is not sufficient and cannot be entered.
	C1S_ITEM_REQUIREMENT_IS_NOT_SUFFICIENT_AND_CANNOT_BE_ENTERED(2099),
	// Message: $c1 may not re-enter yet.
	C1_MAY_NOT_REENTER_YET(2100),
	// Message: You are not currently in a party, so you cannot enter.
	YOU_ARE_NOT_CURRENTLY_IN_A_PARTY_SO_YOU_CANNOT_ENTER(2101),
	// Message: You cannot enter due to the party having exceeded the limit.
	YOU_CANNOT_ENTER_DUE_TO_THE_PARTY_HAVING_EXCEEDED_THE_LIMIT(2102),
	// Message: You cannot enter because you are not associated with the current command channel.
	YOU_CANNOT_ENTER_BECAUSE_YOU_ARE_NOT_ASSOCIATED_WITH_THE_CURRENT_COMMAND_CHANNEL(2103),
	// Message: The maximum number of instance zones has been exceeded. You cannot enter.
	THE_MAXIMUM_NUMBER_OF_INSTANCE_ZONES_HAS_BEEN_EXCEEDED(2104),
	// Message: You have entered another instance zone, therefore you cannot enter corresponding dungeon.
	YOU_HAVE_ENTERED_ANOTHER_INSTANCE_ZONE_THEREFORE_YOU_CANNOT_ENTER_CORRESPONDING_DUNGEON(2105),
	// Message: This dungeon will expire in $s1 minute(s). You will be forced out of the dungeon when the time expires.
	THIS_DUNGEON_WILL_EXPIRE_IN_S1_MINUTES(2106),
	// Message: This instance zone will be terminated in $s1 minute(s). You will be forced out of the dungeon when the time expires.
	THIS_INSTANCE_ZONE_WILL_BE_TERMINATED_IN_S1_MINUTES(2107),
	// Message: Your account has been suspended for 10 days for use of illegal software and may be permanently suspended. For more information, please visit the Support Center on the PlayNC website (http://us.ncsol2ft.com/support/).
	YOUR_ACCOUNT_HAS_BEEN_SUSPENDED_FOR_10_DAYS_FOR_USE_OF_ILLEGAL_SOFTWARE_AND_MAY_BE_PERMANENTLY_SUSPENDED(2108),
	// Message: During the server merge, your character name, $s1, conflicted with another. Your name may still be available. Please enter your desired name.
	DURING_THE_SERVER_MERGE_YOUR_CHARACTER_NAME_S1_CONFLICTED_WITH_ANOTHER(2109),
	// Message: This character name already exists or is an invalid name. Please enter a new name.
	THIS_CHARACTER_NAME_ALREADY_EXISTS_OR_IS_AN_INVALID_NAME(2110),
	// Message: Enter a shortcut to assign.
	ENTER_A_SHORTCUT_TO_ASSIGN(2111),
	// Message: Sub-key can be CTRL, ALT, SHIFT and you may enter two sub-keys at a time. \n Example) "CTRL + ALT + A"
	SUBKEY_CAN_BE_CTRL_ALT_SHIFT_AND_YOU_MAY_ENTER_TWO_SUBKEYS_AT_A_TIME(2112),
	// Message: CTRL, ALT, SHIFT keys may be used as sub-key in expanded sub-key mode, and only ALT may be used as a sub-key in standard sub-key mode.
	CTRL_ALT_SHIFT_KEYS_MAY_BE_USED_AS_SUBKEY_IN_EXPANDED_SUBKEY_MODE_AND_ONLY_ALT_MAY_BE_USED_AS_A_SUBKEY_IN_STANDARD_SUBKEY_MODE(2113),
	// Message: Forced attack and stand-in-place attacks assigned previously to Ctrl and Shift will be changed to Alt + Q and Alt + E when set as expanded sub-key mode, and CTRL and SHIFT will be available to assign to another shortcut. Will you continue?
	FORCED_ATTACK_AND_STANDINPLACE_ATTACKS_ASSIGNED_PREVIOUSLY_TO_CTRL_AND_SHIFT_WILL_BE_CHANGED_TO_ALT__Q_AND_ALT__E_WHEN_SET_AS_EXPANDED_SUBKEY_MODE_AND_CTRL_AND_SHIFT_WILL_BE_AVAILABLE_TO_ASSIGN_TO_ANOTHER_SHORTCUT(2114),
	// Message: Your account has been suspended for abusing a bug related to the NCcoin. For more information, please visit the Support Center on the PlayNC website (http://us.ncsol2ft.com/support/).
	YOUR_ACCOUNT_HAS_BEEN_SUSPENDED_FOR_ABUSING_A_BUG_RELATED_TO_THE_NCCOIN(2115),
	// Message: Your account has been suspended for abusing a free NCcoin. For more information, please visit the Support Center on the PlayNC website (http://us.ncsol2ft.com/support/).
	YOUR_ACCOUNT_HAS_BEEN_SUSPENDED_FOR_ABUSING_A_FREE_NCCOIN(2116),
	// Message: Your account has been suspended for using another person's identification. If you were not involved with any of these violations, please verify your identity. For more information, please visit the PlayNC website Customer's Center (http://us.ncsol2ft.com).
	YOUR_ACCOUNT_HAS_BEEN_SUSPENDED_FOR_USING_ANOTHER_PERSONS_IDENTIFICATION(2117),
	// Message: Your account has been suspended for misappropriating payment under another player's account. For more information, please visit the Customer Service Center of the PlayNC website (http://us.ncsol2ft.com).
	YOUR_ACCOUNT_HAS_BEEN_SUSPENDED_FOR_MISAPPROPRIATING_PAYMENT_UNDER_ANOTHER_PLAYERS_ACCOUNT(2118),
	// Message: Your account has been suspended from all game services after being detected with dealing an account.\nFor more information, please visit the PlayNC website Customer's Center (http://us.ncsol2ft.com).
	YOUR_ACCOUNT_HAS_BEEN_SUSPENDED_FROM_ALL_GAME_SERVICES_AFTER_BEING_DETECTED_WITH_DEALING_AN_ACCOUNT(2119),
	// Message: Your account has been suspended for 10 days for using illegal software. Your account may be suspended permanently if you have been previously caught for the same violation. For more information, please visit the PlayNC website Customer's Center (http://us.ncsol2ft.com).
	YOUR_ACCOUNT_HAS_BEEN_SUSPENDED_FOR_10_DAYS_FOR_USING_ILLEGAL_SOFTWARE(2120),
	// Message: Your account has been suspended from all game services for use of illegal software. For more information, please visit the Support Center on the PlayNC website (http://us.ncsol2ft.com/support/).
	YOUR_ACCOUNT_HAS_BEEN_SUSPENDED_FROM_ALL_GAME_SERVICES_FOR_USE_OF_ILLEGAL_SOFTWARE(2121),
	// Message: Your account has been suspended from all game services for use of illegal software. For more information, please visit the Support Center on the PlayNC website (http://us.ncsol2ft.com/support/).
	YOUR_ACCOUNT_HAS_BEEN_SUSPENDED_FROM_ALL_GAME_SERVICES_FOR_USE_OF_ILLEGAL_SOFTWARE_(2122),
	// Message: Your account has been suspended at your own request. For more information, please visit the Support Center on the PlayNC website (http://us.ncsol2ft.com/support/).
	YOUR_ACCOUNT_HAS_BEEN_SUSPENDED_AT_YOUR_OWN_REQUEST(2123),
	// Message: During the server merge, your clan name, $s1, conflicted with another. Your clan name may still be available. Please enter your desired name.
	DURING_THE_SERVER_MERGE_YOUR_CLAN_NAME_S1_CONFLICTED_WITH_ANOTHER(2124),
	// Message: The clan name already exists or is an invalid name. Please enter aother clan name.
	THE_CLAN_NAME_ALREADY_EXISTS_OR_IS_AN_INVALID_NAME(2125),
	// Message: Your account has been suspended for regularly posting illegal messages. For more information, please visit the Support Center on the PlayNC website (http://us.ncsol2ft.com/support/).
	YOUR_ACCOUNT_HAS_BEEN_SUSPENDED_FOR_REGULARLY_POSTING_ILLEGAL_MESSAGES(2126),
	// Message: Your account has been suspended after being detected with an illegal message. For more information, please visit the Support Center on the PlayNC website (http://us.ncsol2ft.com/support/).
	YOUR_ACCOUNT_HAS_BEEN_SUSPENDED_AFTER_BEING_DETECTED_WITH_AN_ILLEGAL_MESSAGE(2127),
	// Message: Your account has been suspended from all game services for using the game for commercial purposes. For more information, please visit the Support Center on the PlayNC website (http://us.ncsol2ft.com/support/).
	YOUR_ACCOUNT_HAS_BEEN_SUSPENDED_FROM_ALL_GAME_SERVICES_FOR_USING_THE_GAME_FOR_COMMERCIAL_PURPOSES(2128),
	// Message: The augmented item cannot be converted. Please convert after the augmentation has been removed.
	THE_AUGMENTED_ITEM_CANNOT_BE_CONVERTED(2129),
	// Message: You cannot convert this item.
	YOU_CANNOT_CONVERT_THIS_ITEM(2130),
	// Message: You have bid the highest price and have won the item. The item can be found in your personal warehouse.
	YOU_HAVE_BID_THE_HIGHEST_PRICE_AND_HAVE_WON_THE_ITEM(2131),
	// Message: You have entered a common sever.
	YOU_HAVE_ENTERED_A_COMMON_SEVER(2132),
	// Message: You have entered an adults-only sever.
	YOU_HAVE_ENTERED_AN_ADULTSONLY_SEVER(2133),
	// Message: You have entered a server for juveniles.
	YOU_HAVE_ENTERED_A_SERVER_FOR_JUVENILES(2134),
	// Message: Because of your Fatigue level, this is not allowed.
	BECAUSE_OF_YOUR_FATIGUE_LEVEL_THIS_IS_NOT_ALLOWED(2135),
	// Message: A clan name change application has been submitted.
	A_CLAN_NAME_CHANGE_APPLICATION_HAS_BEEN_SUBMITTED(2136),
	// Message: You are about to bid $s1 item with $s2 adena. Will you continue?
	YOU_ARE_ABOUT_TO_BID_S1_ITEM_WITH_S2_ADENA(2137),
	// Message: Please enter a bid price.
	PLEASE_ENTER_A_BID_PRICE(2138),
	// Message: $c1's Pet.
	C1S_PET(2139),
	// Message: $c1's Servitor.
	C1S_SERVITOR(2140),
	// Message: You slightly resisted $c1's magic.
	YOU_SLIGHTLY_RESISTED_C1S_MAGIC(2141),
	// Message: You cannot expel $c1 because $c1 is not a party member.
	YOU_CANNOT_EXPEL_C1_BECAUSE_C1_IS_NOT_A_PARTY_MEMBER(2142),
	// Message: You cannot add elemental power while operating a Private Store or Private Workshop.
	YOU_CANNOT_ADD_ELEMENTAL_POWER_WHILE_OPERATING_A_PRIVATE_STORE_OR_PRIVATE_WORKSHOP(2143),
	// Message: Please select item to add elemental power.
	PLEASE_SELECT_ITEM_TO_ADD_ELEMENTAL_POWER(2144),
	// Message: Attribute item usage has been cancelled.
	ATTRIBUTE_ITEM_USAGE_HAS_BEEN_CANCELLED(2145),
	// Message: Elemental power enhancer usage requirement is not sufficient.
	ELEMENTAL_POWER_ENHANCER_USAGE_REQUIREMENT_IS_NOT_SUFFICIENT(2146),
	// Message: $s2 elemental power has been added successfully to $s1.
	S2_ELEMENTAL_POWER_HAS_BEEN_ADDED_SUCCESSFULLY_TO_S1(2147),
	// Message: $s3 elemental power has been added successfully to +$s1 $s2.
	S3_ELEMENTAL_POWER_HAS_BEEN_ADDED_SUCCESSFULLY_TO_S1_S2(2148),
	// Message: You have failed to add elemental power.
	YOU_HAVE_FAILED_TO_ADD_ELEMENTAL_POWER(2149),
	// Message: Another elemental power has already been added. This elemental power cannot be added.
	ANOTHER_ELEMENTAL_POWER_HAS_ALREADY_BEEN_ADDED(2150),
	// Message: Your opponent has resistance to magic, the damage was decreased.
	YOUR_OPPONENT_HAS_RESISTANCE_TO_MAGIC_THE_DAMAGE_WAS_DECREASED(2151),
	// Message: The assigned shortcut will be deleted and the initial shortcut setting restored. Will you continue?
	THE_ASSIGNED_SHORTCUT_WILL_BE_DELETED_AND_THE_INITIAL_SHORTCUT_SETTING_RESTORED(2152),
	// Message: You are currently logged into 10 of your accounts and can no longer access your other accounts.
	YOU_ARE_CURRENTLY_LOGGED_INTO_10_OF_YOUR_ACCOUNTS_AND_CAN_NO_LONGER_ACCESS_YOUR_OTHER_ACCOUNTS(2153),
	// Message: The target is not a flagpole so a flag cannot be displayed.
	THE_TARGET_IS_NOT_A_FLAGPOLE_SO_A_FLAG_CANNOT_BE_DISPLAYED(2154),
	// Message: A flag is already being displayed, another flag cannot be displayed.
	A_FLAG_IS_ALREADY_BEING_DISPLAYED_ANOTHER_FLAG_CANNOT_BE_DISPLAYED(2155),
	// Message: There are not enough necessary items to use the skill.
	THERE_ARE_NOT_ENOUGH_NECESSARY_ITEMS_TO_USE_THE_SKILL(2156),
	// Message: Bid will be attempted with $s1 adena.
	BID_WILL_BE_ATTEMPTED_WITH_S1_ADENA(2157),
	// Message: Force attack is impossible against a temporary allied member during a siege.
	FORCE_ATTACK_IS_IMPOSSIBLE_AGAINST_A_TEMPORARY_ALLIED_MEMBER_DURING_A_SIEGE(2158),
	// Message: Bidder exists, the auction time has been extended by 5 minutes.
	BIDDER_EXISTS_THE_AUCTION_TIME_HAS_BEEN_EXTENDED_BY_5_MINUTES(2159),
	// Message: Your soul count has increased by $s1. It is now at $s2.
	YOUR_SOUL_COUNT_HAS_INCREASED_BY_S1(2162),
	// Message: Soul cannot be increased anymore.
	SOUL_CANNOT_BE_INCREASED_ANYMORE(2163),
	// Message: The barracks have been seized.
	THE_BARRACKS_HAVE_BEEN_SEIZED(2164),
	// Message: The barracks function has been restored.
	THE_BARRACKS_FUNCTION_HAS_BEEN_RESTORED(2165),
	// Message: All barracks are occupied.
	ALL_BARRACKS_ARE_OCCUPIED(2166),
	// Message: $c1 has acquired the flag.
	C1_HAS_ACQUIRED_THE_FLAG(2168),
	// Message: Your clan has been registered to $s1's fortress battle.
	YOUR_CLAN_HAS_BEEN_REGISTERED_TO_S1S_FORTRESS_BATTLE(2169),
	// Message: A malicious skill cannot be used when an opponent is in the peace zone.
	A_MALICIOUS_SKILL_CANNOT_BE_USED_WHEN_AN_OPPONENT_IS_IN_THE_PEACE_ZONE(2170),
	// Message: This item cannot be crystallized.
	THIS_ITEM_CANNOT_BE_CRYSTALLIZED(2171),
	// Message: +$s1$s2's auction has ended.
	S1S2S_AUCTION_HAS_ENDED(2172),
	// Message: $s1's auction has ended.
	S1S_AUCTION_HAS_ENDED(2173),
	// Message: $c1 cannot duel because $c1 is currently polymorphed.
	C1_CANNOT_DUEL_BECAUSE_C1_IS_CURRENTLY_POLYMORPHED(2174),
	// Message: Party duel cannot be initiated due to a polymorphed party member.
	PARTY_DUEL_CANNOT_BE_INITIATED_DUE_TO_A_POLYMORPHED_PARTY_MEMBER(2175),
	// Message: You have the highest bid submitted in an Aden Castle auction.
	YOU_HAVE_THE_HIGHEST_BID_SUBMITTED_IN_AN_ADEN_CASTLE_AUCTION(2180),
	// Message: You have highest the bid submitted in a Rune Castle auction.
	YOU_HAVE_HIGHEST_THE_BID_SUBMITTED_IN_A_RUNE_CASTLE_AUCTION(2181),
	// Message: You cannot polymorph while riding a boat.
	YOU_CANNOT_POLYMORPH_WHILE_RIDING_A_BOAT(2182),
	// Message: The fortress battle of $s1 has finished.
	THE_FORTRESS_BATTLE_OF_S1_HAS_FINISHED(2183),
	// Message: $s1 is victorious in the fortress battle of $s2.
	S1_IS_VICTORIOUS_IN_THE_FORTRESS_BATTLE_OF_S2(2184),
	// Message: Only a party leader can make the request to enter.
	ONLY_A_PARTY_LEADER_CAN_MAKE_THE_REQUEST_TO_ENTER(2185),
	// Message: The ballista has been successfully destroyed. The clan's reputation will be increased.
	THE_BALLISTA_HAS_BEEN_SUCCESSFULLY_DESTROYED(2217),
	// Message: This is a main class skill only.
	THIS_IS_A_MAIN_CLASS_SKILL_ONLY(2218),
	// Message: This squad skill has already been acquired.
	THIS_SQUAD_SKILL_HAS_ALREADY_BEEN_ACQUIRED(2219),
	// Message: The previous level skill has not been learned.
	THE_PREVIOUS_LEVEL_SKILL_HAS_NOT_BEEN_LEARNED(2220),
	// Message: Do you wish to activate the selected functions?
	DO_YOU_WISH_TO_ACTIVATE_THE_SELECTED_FUNCTIONS(2221),
	// Message: It will cost 150,000 adena to place scouts. Do you wish to continue?
	IT_WILL_COST_150000_ADENA_TO_PLACE_SCOUTS(2222),
	// Message: It will cost 200,000 adena for a fortress gate enhancement. Do you wish to continue?
	IT_WILL_COST_200000_ADENA_FOR_A_FORTRESS_GATE_ENHANCEMENT(2223),
	// Message: Your crossbow is preparing to fire.
	YOUR_CROSSBOW_IS_PREPARING_TO_FIRE(2224),
	// Message: There are no other skills to learn. Please come back after $s1nd class change.
	THERE_ARE_NO_OTHER_SKILLS_TO_LEARN_(2225),
	// Message: Not enough bolts.
	NOT_ENOUGH_BOLTS(2226),
	// Message: It is not possible to register for the castle siege side or castle siege of a higher castle in the contract.
	IT_IS_NOT_POSSIBLE_TO_REGISTER_FOR_THE_CASTLE_SIEGE_SIDE_OR_CASTLE_SIEGE_OF_A_HIGHER_CASTLE_IN_THE_CONTRACT(2227),
	// Message: Instance zone time limit:
	INSTANCE_ZONE_TIME_LIMIT(2228),
	// Message: There is no instance zone under a time limit.
	THERE_IS_NO_INSTANCE_ZONE_UNDER_A_TIME_LIMIT(2229),
	// Message: $s1 will be available for re-use after $s2 hour(s) $s3 minute(s).
	S1_WILL_BE_AVAILABLE_FOR_REUSE_AFTER_S2_HOURS_S3_MINUTES(2230),
	// Message: The supply items have not  been provided because the castle you are in contract with doesn't have enough clan reputation.
	THE_SUPPLY_ITEMS_HAVE_NOT__BEEN_PROVIDED_BECAUSE_THE_CASTLE_YOU_ARE_IN_CONTRACT_WITH_DOESNT_HAVE_ENOUGH_CLAN_REPUTATION(2231),
	// Message: $s1 will be crystallized before destruction. Will you continue?
	S1_WILL_BE_CRYSTALLIZED_BEFORE_DESTRUCTION(2232),
	// Message: Siege registration is not possible due to your castle contract.
	SIEGE_REGISTRATION_IS_NOT_POSSIBLE_DUE_TO_YOUR_CASTLE_CONTRACT(2233),
	// Message: Do you wish to use this Kamael exclusive Hero Weapon?
	DO_YOU_WISH_TO_USE_THIS_KAMAEL_EXCLUSIVE_HERO_WEAPON(2234),
	// Message: The instance zone in use has been deleted and cannot be accessed.
	THE_INSTANCE_ZONE_IN_USE_HAS_BEEN_DELETED_AND_CANNOT_BE_ACCESSED(2235),
	// Message: You have $s1 minute(s) left on your wyvern.
	YOU_HAVE_S1_MINUTES_LEFT_ON_YOUR_WYVERN(2236),
	// Message: You have $s1 second(s) left on your wyvern.
	YOU_HAVE_S1_SECONDS_LEFT_ON_YOUR_WYVERN(2237),
	// Message: You are participating in the siege of $s1. This siege is scheduled for 2 hours.
	YOU_ARE_PARTICIPATING_IN_THE_SIEGE_OF_S1(2238),
	// Message: The siege of $s1, in which you are participating, has finished.
	THE_SIEGE_OF_S1_IN_WHICH_YOU_ARE_PARTICIPATING_HAS_FINISHED(2239),
	// Message: You cannot register for a Clan Hall War when the Clan Lord is in the process of re-delegating clan authority to another leader.
	YOU_CANNOT_REGISTER_FOR_A_CLAN_HALL_WAR_WHEN_THE_CLAN_LORD_IS_IN_THE_PROCESS_OF_REDELEGATING_CLAN_AUTHORITY_TO_ANOTHER_LEADER(2240),
	// Message: You cannot apply for a Clan Lord delegation request if your clan has registered for a Clan Hall War.
	YOU_CANNOT_APPLY_FOR_A_CLAN_LORD_DELEGATION_REQUEST_IF_YOUR_CLAN_HAS_REGISTERED_FOR_A_CLAN_HALL_WAR(2241),
	// Message: Clan members cannot leave or be expelled when they are registered for a Clan Hall War.
	CLAN_MEMBERS_CANNOT_LEAVE_OR_BE_EXPELLED_WHEN_THEY_ARE_REGISTERED_FOR_A_CLAN_HALL_WAR(2242),
	// Message: During the Bandit Stronghold or Wild Beast Reserve clan hall war, the previous clan lord rather than the new clan lord participates in battle.
	DURING_THE_BANDIT_STRONGHOLD_OR_WILD_BEAST_RESERVE_CLAN_HALL_WAR_THE_PREVIOUS_CLAN_LORD_RATHER_THAN_THE_NEW_CLAN_LORD_PARTICIPATES_IN_BATTLE(2243),
	// Message: $s1 minute(s) remaining.
	S1_MINUTES_REMAINING(2244),
	// Message: $s1 second(s) remaining.
	S1_SECONDS_REMAINING(2245),
	// Message: The contest will begin in $s1 minute(s).
	THE_CONTEST_WILL_BEGIN_IN_S1_MINUTES(2246),
	// Message: You cannot board an airship while transformed.
	YOU_CANNOT_BOARD_AN_AIRSHIP_WHILE_TRANSFORMED(2247),
	// Message: You cannot board an airship while petrified.
	YOU_CANNOT_BOARD_AN_AIRSHIP_WHILE_PETRIFIED(2248),
	// Message: You cannot board an airship while dead.
	YOU_CANNOT_BOARD_AN_AIRSHIP_WHILE_DEAD(2249),
	// Message: You cannot board an airship while fishing.
	YOU_CANNOT_BOARD_AN_AIRSHIP_WHILE_FISHING(2250),
	// Message: You cannot board an airship while in battle.
	YOU_CANNOT_BOARD_AN_AIRSHIP_WHILE_IN_BATTLE(2251),
	// Message: You cannot board an airship while in a duel.
	YOU_CANNOT_BOARD_AN_AIRSHIP_WHILE_IN_A_DUEL(2252),
	// Message: You cannot board an airship while sitting.
	YOU_CANNOT_BOARD_AN_AIRSHIP_WHILE_SITTING(2253),
	// Message: You cannot board an airship while casting.
	YOU_CANNOT_BOARD_AN_AIRSHIP_WHILE_CASTING(2254),
	// Message: You cannot board an airship when a cursed weapon is equipped.
	YOU_CANNOT_BOARD_AN_AIRSHIP_WHEN_A_CURSED_WEAPON_IS_EQUIPPED(2255),
	// Message: You cannot board an airship while holding a flag.
	YOU_CANNOT_BOARD_AN_AIRSHIP_WHILE_HOLDING_A_FLAG(2256),
	// Message: You cannot board an airship while a pet or a servitor is summoned.
	YOU_CANNOT_BOARD_AN_AIRSHIP_WHILE_A_PET_OR_A_SERVITOR_IS_SUMMONED(2257),
	// Message: You have already boarded another airship.
	YOU_HAVE_ALREADY_BOARDED_ANOTHER_AIRSHIP(2258),
	// Message: Current Location: $s1, $s2, $s3 (near Fantasy Isle)
	CURRENT_LOCATION_S1_S2_S3_NEAR_FANTASY_ISLE(2259),
	// Message: Your pet's hunger gauge is below 10%. If your pet isn't fed soon, it may run away.
	YOUR_PETS_HUNGER_GAUGE_IS_BELOW_10(2260),
	// Message: $c1 has done $s3 points of damage to $c2.
	C1_HAS_DONE_S3_POINTS_OF_DAMAGE_TO_C2(2261),
	// Message: $c1 has received $s3 damage from $c2.
	C1_HAS_RECEIVED_S3_DAMAGE_FROM_C2(2262),
	// Message: $c1 has received damage of $s3 through $c2.
	C1_HAS_RECEIVED_DAMAGE_OF_S3_THROUGH_C2(2263),
	// Message: $c1 has evaded $c2's attack.
	C1_HAS_EVADED_C2S_ATTACK(2264),
	// Message: $c1's attack went astray.
	C1S_ATTACK_WENT_ASTRAY(2265),
	// Message: $c1 landed a critical hit!
	C1_LANDED_A_CRITICAL_HIT(2266),
	// Message: $c1 resisted $c2's drain.
	C1_RESISTED_C2S_DRAIN(2267),
	// Message: $c1's attack failed.
	C1S_ATTACK_FAILED(2268),
	// Message: $c1 resisted $c2's magic.
	C1_RESISTED_C2S_MAGIC(2269),
	// Message: $c1 has received damage from $s2 through the fire of magic.
	C1_HAS_RECEIVED_DAMAGE_FROM_S2_THROUGH_THE_FIRE_OF_MAGIC(2270),
	// Message: $c1 weakly resisted $c2's magic.
	C1_WEAKLY_RESISTED_C2S_MAGIC(2271),
	// Message: The key you assigned as a shortcut key is not available in regular chatting mode.
	THE_KEY_YOU_ASSIGNED_AS_A_SHORTCUT_KEY_IS_NOT_AVAILABLE_IN_REGULAR_CHATTING_MODE(2272),
	// Message: This skill cannot be learned while in the sub-class state. Please try again after changing to the main class.
	THIS_SKILL_CANNOT_BE_LEARNED_WHILE_IN_THE_SUBCLASS_STATE(2273),
	// Message: You cannot wear $s1 because you are not wearing a bracelet.
	YOU_CANNOT_WEAR_S1_BECAUSE_YOU_ARE_NOT_WEARING_A_BRACELET(2286),
	// Message: You cannot equip $s1 because you do not have any available slots.
	YOU_CANNOT_EQUIP_S1_BECAUSE_YOU_DO_NOT_HAVE_ANY_AVAILABLE_SLOTS(2287),
	// Message: Resurrection will occur in $s1 seconds.
	RESURRECTION_WILL_OCCUR_IN_S1_SECONDS(2288),
	// Message: The match between the parties cannot commence because one of the party members is being teleported.
	THE_MATCH_BETWEEN_THE_PARTIES_CANNOT_COMMENCE_BECAUSE_ONE_OF_THE_PARTY_MEMBERS_IS_BEING_TELEPORTED(2289),
	// Message: You cannot assign shortcut keys before you log in.
	YOU_CANNOT_ASSIGN_SHORTCUT_KEYS_BEFORE_YOU_LOG_IN(2290),
	// Message: You must be in a party in order to operate the machine.
	YOU_MUST_BE_IN_A_PARTY_IN_ORDER_TO_OPERATE_THE_MACHINE(2291),
	// Message: Agathion skills can be used only when your Agathion is summoned.
	AGATHION_SKILLS_CAN_BE_USED_ONLY_WHEN_YOUR_AGATHION_IS_SUMMONED(2292),
	// Message: Current location: $s1, $s2, $s3 (inside the Steel Citadel)
	CURRENT_LOCATION_S1_S2_S3_INSIDE_THE_STEEL_CITADEL(2293),
	// Message: The width of the crest or insignia does not meet the standard requirements.
	THE_WIDTH_OF_THE_CREST_OR_INSIGNIA_DOES_NOT_MEET_THE_STANDARD_REQUIREMENTS(2294),
	// Message: The length of the crest or insignia does not meet the standard requirements.
	THE_LENGTH_OF_THE_CREST_OR_INSIGNIA_DOES_NOT_MEET_THE_STANDARD_REQUIREMENTS(2295),
	// Message: You have gained Vitality points.
	YOU_HAVE_GAINED_VITALITY_POINTS(2296),
	// Message: Round $s1
	ROUND_S1(2297),
	// Message: The color of the crest or insignia that you want to register does not meet the standard requirements.
	THE_COLOR_OF_THE_CREST_OR_INSIGNIA_THAT_YOU_WANT_TO_REGISTER_DOES_NOT_MEET_THE_STANDARD_REQUIREMENTS(2298),
	// Message: The file format of the crest or insignia that you want to register does not meet the standard requirements.
	THE_FILE_FORMAT_OF_THE_CREST_OR_INSIGNIA_THAT_YOU_WANT_TO_REGISTER_DOES_NOT_MEET_THE_STANDARD_REQUIREMENTS(2299),
	// Message: Failed to load keyboard security module. For effective gaming functionality, when the game is over, please check all the files in the Lineage II automatic update.
	FAILED_TO_LOAD_KEYBOARD_SECURITY_MODULE(2300),
	// Message: Current location: Steel Citadel
	CURRENT_LOCATION_STEEL_CITADEL(2301),
	// Message: Your Vitamin Item has arrived! Visit the Vitamin Manager in any village to obtain it.
	YOUR_VITAMIN_ITEM_HAS_ARRIVED_VISIT_THE_VITAMIN_MANAGER_IN_ANY_VILLAGE_TO_OBTAIN_IT(2302),
	// Message: There are $s2 second(s) remaining in $s1's re-use time.
	THERE_ARE_S2_SECONDS_REMAINING_IN_S1S_REUSE_TIME(2303),
	// Message: There are $s2 minute(s), $s3 second(s) remaining in $s1's re-use time.
	THERE_ARE_S2_MINUTES_S3_SECONDS_REMAINING_IN_S1S_REUSE_TIME(2304),
	// Message: There are $s2 hour(s), $s3 minute(s), and $s4 second(s) remaining in $s1's re-use time.
	THERE_ARE_S2_HOURS_S3_MINUTES_AND_S4_SECONDS_REMAINING_IN_S1S_REUSE_TIME(2305),
	// Message: Resurrection will take place in the waiting room after $s1 seconds.
	RESURRECTION_WILL_TAKE_PLACE_IN_THE_WAITING_ROOM_AFTER_S1_SECONDS(2370),
	// Message: End match!
	END_MATCH(2374),
	// Message: The Vitamin Item cannot be located because of a temporary connection error.
	THE_VITAMIN_ITEM_CANNOT_BE_LOCATED_BECAUSE_OF_A_TEMPORARY_CONNECTION_ERROR(2392),
	// Message: You have acquired $s1 PC Cafe points.
	YOU_HAVE_ACQUIRED_S1_PC_CAFE_POINTS(2393),
	// Message: That skill cannot be used because your pet/servitor lacks sufficient MP.
	THAT_SKILL_CANNOT_BE_USED_BECAUSE_YOUR_PETSERVITOR_LACKS_SUFFICIENT_MP(2394),
	// Message: That skill cannot be used because your pet/servitor lacks sufficient HP.
	THAT_SKILL_CANNOT_BE_USED_BECAUSE_YOUR_PETSERVITOR_LACKS_SUFFICIENT_HP(2395),
	// Message: That pet/servitor skill cannot be used because it is recharging.
	THAT_PETSERVITOR_SKILL_CANNOT_BE_USED_BECAUSE_IT_IS_RECHARGING(2396),
	// Message: Please use a My Teleport Scroll.
	PLEASE_USE_A_MY_TELEPORT_SCROLL(2397),
	// Message: You have no open My Teleports slots.
	YOU_HAVE_NO_OPEN_MY_TELEPORTS_SLOTS(2398),
	// Message: $s1's ownership expires in $s2 minutes.
	S1S_OWNERSHIP_EXPIRES_IN_S2_MINUTES(2399),
	// Message: Instant Zone currently in use: $s1
	INSTANT_ZONE_CURRENTLY_IN_USE_S1(2400),
	// Message: Clan lord $c2, who leads clan $s1, has been declared the lord of the $s3 territory.
	CLAN_LORD_C2_WHO_LEADS_CLAN_S1_HAS_BEEN_DECLARED_THE_LORD_OF_THE_S3_TERRITORY(2401),
	// Message: The Territory War request period has ended.
	THE_TERRITORY_WAR_REQUEST_PERIOD_HAS_ENDED(2402),
	// Message: The Territory War begins in 10 minutes!
	THE_TERRITORY_WAR_BEGINS_IN_10_MINUTES(2403),
	// Message: The Territory War begins in 5 minutes!
	THE_TERRITORY_WAR_BEGINS_IN_5_MINUTES(2404),
	// Message: The Territory War begins in 1 minute!
	THE_TERRITORY_WAR_BEGINS_IN_1_MINUTE(2405),
	// Message: $s1's territory war has begun.
	S1S_TERRITORY_WAR_HAS_BEGUN(2406),
	// Message: $s1's territory war has ended.
	S1S_TERRITORY_WAR_HAS_ENDED(2407),
	// Message: You are currently registered for a 3 vs. 3 class irrelevant team match.
	YOU_ARE_CURRENTLY_REGISTERED_FOR_A_3_VS(2408),
	// Message: The number of My Teleports slots has been increased.
	THE_NUMBER_OF_MY_TELEPORTS_SLOTS_HAS_BEEN_INCREASED(2409),
	// Message: You cannot use My Teleports to reach this area!
	YOU_CANNOT_USE_MY_TELEPORTS_TO_REACH_THIS_AREA(2410),
	// Message: $c1 has issued a party invitation which you automatically rejected. To receive party invitations, please change the Party Invitation Reject setting in the Options window.
	C1_HAS_ISSUED_A_PARTY_INVITATION_WHICH_YOU_AUTOMATICALLY_REJECTED(2411),
	// Message: Your birthday gift has been delivered! Visit the Dimensional Merchant in any village to obtain it.
	YOUR_BIRTHDAY_GIFT_HAS_BEEN_DELIVERED_VISIT_THE_DIMENSIONAL_MERCHANT_IN_ANY_VILLAGE_TO_OBTAIN_IT(2412),
	// Message: You are registering as a reserve for the Red Team. Do you wish to continue?
	YOU_ARE_REGISTERING_AS_A_RESERVE_FOR_THE_RED_TEAM(2413),
	// Message: You are registering as a reserve for the Blue Team. Do you wish to continue?
	YOU_ARE_REGISTERING_AS_A_RESERVE_FOR_THE_BLUE_TEAM(2414),
	// Message: You have registered as a reserve for the Red Team. When in battle, the team can change its composition using the Maintain Team Balance function.
	YOU_HAVE_REGISTERED_AS_A_RESERVE_FOR_THE_RED_TEAM(2415),
	// Message: You have registered as a reserve for the Blue Team. When in battle, the team can change its composition using the Maintain Team Balance function.
	YOU_HAVE_REGISTERED_AS_A_RESERVE_FOR_THE_BLUE_TEAM(2416),
	// Message: You are canceling your Aerial Cleft registration. Do you wish to continue?
	YOU_ARE_CANCELING_YOUR_AERIAL_CLEFT_REGISTRATION(2417),
	// Message: The Aerial Cleft registration has been canceled.
	THE_AERIAL_CLEFT_REGISTRATION_HAS_BEEN_CANCELED(2418),
	// Message: The Aerial Cleft has been activated. Flight transformation will be possible in approximately 40 seconds.
	THE_AERIAL_CLEFT_HAS_BEEN_ACTIVATED(2419),
	// Message: The battlefield closes in 1 minute.
	THE_BATTLEFIELD_CLOSES_IN_1_MINUTE(2420),
	// Message: The battlefield closes in 10 seconds.
	THE_BATTLEFIELD_CLOSES_IN_10_SECONDS(2421),
	// Message: EP, or Energy Points, refers to fuel.
	EP_OR_ENERGY_POINTS_REFERS_TO_FUEL(2422),
	// Message: EP can be refilled by using a $s1 while sailing on an airship.
	EP_CAN_BE_REFILLED_BY_USING_A_S1_WHILE_SAILING_ON_AN_AIRSHIP(2423),
	// Message: The collection has failed.
	THE_COLLECTION_HAS_FAILED(2424),
	// Message: The Aerial Cleft battlefield has been closed.
	THE_AERIAL_CLEFT_BATTLEFIELD_HAS_BEEN_CLOSED(2425),
	// Message: $c1 has been expelled from the team.
	C1_HAS_BEEN_EXPELLED_FROM_THE_TEAM(2426),
	// Message: The Red Team is victorious.
	THE_RED_TEAM_IS_VICTORIOUS(2427),
	// Message: The Blue Team is victorious.
	THE_BLUE_TEAM_IS_VICTORIOUS(2428),
	// Message: $c1 has been designated as the target.
	C1_HAS_BEEN_DESIGNATED_AS_THE_TARGET(2429),
	// Message: $c1 has fallen. The Red Team's points have increased.
	C1_HAS_FALLEN(2430),
	// Message: $c2 has fallen. The Blue Team's points have increased.
	C2_HAS_FALLEN(2431),
	// Message: The central stronghold's compressor has been destroyed.
	THE_CENTRAL_STRONGHOLDS_COMPRESSOR_HAS_BEEN_DESTROYED(2432),
	// Message: The first stronghold's compressor has been destroyed.
	THE_FIRST_STRONGHOLDS_COMPRESSOR_HAS_BEEN_DESTROYED(2433),
	// Message: The second stronghold's compressor has been destroyed.
	THE_SECOND_STRONGHOLDS_COMPRESSOR_HAS_BEEN_DESTROYED(2434),
	// Message: The third stronghold's compressor has been destroyed.
	THE_THIRD_STRONGHOLDS_COMPRESSOR_HAS_BEEN_DESTROYED(2435),
	// Message: The central stronghold's compressor is working.
	THE_CENTRAL_STRONGHOLDS_COMPRESSOR_IS_WORKING(2436),
	// Message: The first stronghold's compressor is working.
	THE_FIRST_STRONGHOLDS_COMPRESSOR_IS_WORKING(2437),
	// Message: The second stronghold's compressor is working.
	THE_SECOND_STRONGHOLDS_COMPRESSOR_IS_WORKING(2438),
	// Message: The third stronghold's compressor is working.
	THE_THIRD_STRONGHOLDS_COMPRESSOR_IS_WORKING(2439),
	// Message: $c1 is already registered on the waiting list for the 3 vs. 3 class irrelevant team match.
	C1_IS_ALREADY_REGISTERED_ON_THE_WAITING_LIST_FOR_THE_3_VS(2440),
	// Message: Only a party leader can request a team match.
	ONLY_A_PARTY_LEADER_CAN_REQUEST_A_TEAM_MATCH(2441),
	// Message: The request cannot be made because the requirements have not been met. To participate in a team match, you must first form a 3-member party.
	THE_REQUEST_CANNOT_BE_MADE_BECAUSE_THE_REQUIREMENTS_HAVE_NOT_BEEN_MET(2442),
	// Message: Flames filled with the Wrath of Valakas are engulfing you.
	FLAMES_FILLED_WITH_THE_WRATH_OF_VALAKAS_ARE_ENGULFING_YOU(2443),
	// Message: Flames filled with the Authority of Valakas are binding your mind.
	FLAMES_FILLED_WITH_THE_AUTHORITY_OF_VALAKAS_ARE_BINDING_YOUR_MIND(2444),
	// Message: The battlefield channel has been activated.
	THE_BATTLEFIELD_CHANNEL_HAS_BEEN_ACTIVATED(2445),
	// Message: The battlefield channel has been deactivated.
	THE_BATTLEFIELD_CHANNEL_HAS_BEEN_DEACTIVATED(2446),
	// Message: Five years have passed since this character's creation.
	FIVE_YEARS_HAVE_PASSED_SINCE_THIS_CHARACTERS_CREATION(2447),
	// Message: Happy birthday! Alegria has sent you a birthday gil2ft.
	HAPPY_BIRTHDAY_ALEGRIA_HAS_SENT_YOU_A_BIRTHDAY_GIFT(2448),
	// Message: There are $s1 days remaining until your birthday. On your birthday, you will receive a gift that Alegria has carefully prepared.
	THERE_ARE_S1_DAYS_REMAINING_UNTIL_YOUR_BIRTHDAY(2449),
	// Message: $c1's birthday is $s3/$s4/$s2.
	C1S_BIRTHDAY_IS_S3S4S2(2450),
	// Message: Your cloak has been unequipped because your armor set is no longer complete.
	YOUR_CLOAK_HAS_BEEN_UNEQUIPPED_BECAUSE_YOUR_ARMOR_SET_IS_NO_LONGER_COMPLETE(2451),
	// Message: You inventory currently exceeds the normal amount of inventory slots available, so your belt cannot be removed.
	YOU_INVENTORY_CURRENTLY_EXCEEDS_THE_NORMAL_AMOUNT_OF_INVENTORY_SLOTS_AVAILABLE_SO_YOUR_BELT_CANNOT_BE_REMOVED(2452),
	// Message: The cloak cannot be equipped because your armor set is not complete.
	THE_CLOAK_CANNOT_BE_EQUIPPED_BECAUSE_YOUR_ARMOR_SET_IS_NOT_COMPLETE(2453),
	// Message: Kresnik Class Airship
	KRESNIK_CLASS_AIRSHIP(2454),
	// Message: The airship must be summoned in order for you to board.
	THE_AIRSHIP_MUST_BE_SUMMONED_IN_ORDER_FOR_YOU_TO_BOARD(2455),
	// Message: In order to acquire an airship, the clan's level must be level 5 or higher.
	IN_ORDER_TO_ACQUIRE_AN_AIRSHIP_THE_CLANS_LEVEL_MUST_BE_LEVEL_5_OR_HIGHER(2456),
	// Message: An airship cannot be summoned because either you have not registered your airship license, or the airship has not yet been summoned.
	AN_AIRSHIP_CANNOT_BE_SUMMONED_BECAUSE_EITHER_YOU_HAVE_NOT_REGISTERED_YOUR_AIRSHIP_LICENSE_OR_THE_AIRSHIP_HAS_NOT_YET_BEEN_SUMMONED(2457),
	// Message: Your clan's airship is already being used by another clan member.
	YOUR_CLANS_AIRSHIP_IS_ALREADY_BEING_USED_BY_ANOTHER_CLAN_MEMBER(2458),
	// Message: The Airship Summon License has already been acquired.
	THE_AIRSHIP_SUMMON_LICENSE_HAS_ALREADY_BEEN_ACQUIRED(2459),
	// Message: The clan owned airship already exists.
	THE_CLAN_OWNED_AIRSHIP_ALREADY_EXISTS(2460),
	// Message: Airship Summon License registration can only be done by the clan leader.
	AIRSHIP_SUMMON_LICENSE_REGISTRATION_CAN_ONLY_BE_DONE_BY_THE_CLAN_LEADER(2461),
	// Message: An airship cannot be summoned because you don't have enough $s1.
	AN_AIRSHIP_CANNOT_BE_SUMMONED_BECAUSE_YOU_DONT_HAVE_ENOUGH_S1(2462),
	// Message: The airship's fuel (EP) will soon run out.
	THE_AIRSHIPS_FUEL_EP_WILL_SOON_RUN_OUT(2463),
	// Message: The airship's fuel (EP) has run out. The airship's speed will be greatly decreased in this condition.
	THE_AIRSHIPS_FUEL_EP_HAS_RUN_OUT(2464),
	// Message: You have selected a 3 vs. 3 class irrelevant team match. Do you wish to participate?
	YOU_HAVE_SELECTED_A_3_VS(2465),
	// Message: A pet on auxiliary mode cannot use skills.
	A_PET_ON_AUXILIARY_MODE_CANNOT_USE_SKILLS(2466),
	// Message: Do you wish to begin the game now?
	DO_YOU_WISH_TO_BEGIN_THE_GAME_NOW(2467),
	// Message: You have used a report point on $c1. You have $s2 points remaining on this account.
	YOU_HAVE_USED_A_REPORT_POINT_ON_C1(2468),
	// Message: You have used all available points. Points are reset everyday at noon.
	YOU_HAVE_USED_ALL_AVAILABLE_POINTS(2469),
	// Message: This character cannot make a report. You cannot make a report while located inside a peace zone or a battlefield, while you are an opposing clan member during a clan war, or while participating in the Olympiad.
	THIS_CHARACTER_CANNOT_MAKE_A_REPORT(2470),
	// Message: This character cannot make a report. The target has already been reported by either your clan or alliance, or has already been reported from your current IP.
	THIS_CHARACTER_CANNOT_MAKE_A_REPORT_(2471),
	// Message: This character cannot make a report because another character from this account has already done so.
	THIS_CHARACTER_CANNOT_MAKE_A_REPORT_BECAUSE_ANOTHER_CHARACTER_FROM_THIS_ACCOUNT_HAS_ALREADY_DONE_SO(2472),
	// Message: You have been reported as an illegal program user, so your chatting will be blocked for 10 minutes.
	YOU_HAVE_BEEN_REPORTED_AS_AN_ILLEGAL_PROGRAM_USER_SO_YOUR_CHATTING_WILL_BE_BLOCKED_FOR_10_MINUTES(2473),
	// Message: You have been reported as an illegal program user, so your party participation will be blocked for 60 minutes.
	YOU_HAVE_BEEN_REPORTED_AS_AN_ILLEGAL_PROGRAM_USER_SO_YOUR_PARTY_PARTICIPATION_WILL_BE_BLOCKED_FOR_60_MINUTES(2474),
	// Message: You have been reported as an illegal program user, so your party participation will be blocked for 120 minutes.
	YOU_HAVE_BEEN_REPORTED_AS_AN_ILLEGAL_PROGRAM_USER_SO_YOUR_PARTY_PARTICIPATION_WILL_BE_BLOCKED_FOR_120_MINUTES(2475),
	// Message: You have been reported as an illegal program user, so your party participation will be blocked for 180 minutes.
	YOU_HAVE_BEEN_REPORTED_AS_AN_ILLEGAL_PROGRAM_USER_SO_YOUR_PARTY_PARTICIPATION_WILL_BE_BLOCKED_FOR_180_MINUTES(2476),
	// Message: You have been reported as an illegal program user, so your actions will be restricted for 120 minutes.
	YOU_HAVE_BEEN_REPORTED_AS_AN_ILLEGAL_PROGRAM_USER_SO_YOUR_ACTIONS_WILL_BE_RESTRICTED_FOR_120_MINUTES(2477),
	// Message: You have been reported as an illegal program user, so your actions will be restricted for 180 minutes.
	YOU_HAVE_BEEN_REPORTED_AS_AN_ILLEGAL_PROGRAM_USER_SO_YOUR_ACTIONS_WILL_BE_RESTRICTED_FOR_180_MINUTES(2478),
	// Message: You have been reported as an illegal program user, so your actions will be restricted for 180 minutes.
	YOU_HAVE_BEEN_REPORTED_AS_AN_ILLEGAL_PROGRAM_USER_SO_YOUR_ACTIONS_WILL_BE_RESTRICTED_FOR_180_MINUTES_(2479),
	// Message: You have been reported as an illegal program user, so movement is prohibited for 120 minutes.
	YOU_HAVE_BEEN_REPORTED_AS_AN_ILLEGAL_PROGRAM_USER_SO_MOVEMENT_IS_PROHIBITED_FOR_120_MINUTES(2480),
	// Message: $c1 has been reported as an illegal program user and is currently being investigated.
	C1_HAS_BEEN_REPORTED_AS_AN_ILLEGAL_PROGRAM_USER_AND_IS_CURRENTLY_BEING_INVESTIGATED(2481),
	// Message: $c1 has been reported as an illegal program user and cannot join a party.
	C1_HAS_BEEN_REPORTED_AS_AN_ILLEGAL_PROGRAM_USER_AND_CANNOT_JOIN_A_PARTY(2482),
	// Message: You have been reported as an illegal program user, so chatting is not allowed.
	YOU_HAVE_BEEN_REPORTED_AS_AN_ILLEGAL_PROGRAM_USER_SO_CHATTING_IS_NOT_ALLOWED(2483),
	// Message: You have been reported as an illegal program user, so participating in a party is not allowed.
	YOU_HAVE_BEEN_REPORTED_AS_AN_ILLEGAL_PROGRAM_USER_SO_PARTICIPATING_IN_A_PARTY_IS_NOT_ALLOWED(2484),
	// Message: You have been reported as an illegal program user so your actions have been restricted.
	YOU_HAVE_BEEN_REPORTED_AS_AN_ILLEGAL_PROGRAM_USER_SO_YOUR_ACTIONS_HAVE_BEEN_RESTRICTED(2485),
	// Message: You have been blocked due to verification that you are using a third party program. Subsequent violations may result in termination of your account rather than a penalty within the game.
	YOU_HAVE_BEEN_BLOCKED_DUE_TO_VERIFICATION_THAT_YOU_ARE_USING_A_THIRD_PARTY_PROGRAM(2486),
	// Message: You have been reported as an illegal program user, and your connection has been ended. Please contact our CS team to confirm your identity.
	YOU_HAVE_BEEN_REPORTED_AS_AN_ILLEGAL_PROGRAM_USER_AND_YOUR_CONNECTION_HAS_BEEN_ENDED(2487),
	// Message: You cannot enter the Aerial Cleft because you are below the required level. Entry is possible only at level 75 or above.
	YOU_CANNOT_ENTER_THE_AERIAL_CLEFT_BECAUSE_YOU_ARE_BELOW_THE_REQUIRED_LEVEL(2488),
	// Message: You must target a control device in order to perform this action.
	YOU_MUST_TARGET_A_CONTROL_DEVICE_IN_ORDER_TO_PERFORM_THIS_ACTION(2489),
	// Message: You cannot perform this action because you are too far away from the control device.
	YOU_CANNOT_PERFORM_THIS_ACTION_BECAUSE_YOU_ARE_TOO_FAR_AWAY_FROM_THE_CONTROL_DEVICE(2490),
	// Message: Your ship cannot teleport because it does not have enough fuel for the trip.
	YOUR_SHIP_CANNOT_TELEPORT_BECAUSE_IT_DOES_NOT_HAVE_ENOUGH_FUEL_FOR_THE_TRIP(2491),
	// Message: The airship has been summoned. It will automatically depart in %s minutes.
	THE_AIRSHIP_HAS_BEEN_SUMMONED(2492),
	// Message: Enter chat mode is automatically enabled when you are in a flying transformation state.
	ENTER_CHAT_MODE_IS_AUTOMATICALLY_ENABLED_WHEN_YOU_ARE_IN_A_FLYING_TRANSFORMATION_STATE(2493),
	// Message: Enter chat mode is automatically enabled when you are in airship control mode.
	ENTER_CHAT_MODE_IS_AUTOMATICALLY_ENABLED_WHEN_YOU_ARE_IN_AIRSHIP_CONTROL_MODE(2494),
	// Message: W (go forward), S (stop), A (turn left), D (turn right), E (increase altitude) and Q (decrease altitude).
	W_GO_FORWARD_S_STOP_A_TURN_LEFT_D_TURN_RIGHT_E_INCREASE_ALTITUDE_AND_Q_DECREASE_ALTITUDE(2495),
	// Message: When you click on a skill designated on your shortcut bar, that slot is activated. Once activated, you can press the spacebar to execute the designated skill.
	WHEN_YOU_CLICK_ON_A_SKILL_DESIGNATED_ON_YOUR_SHORTCUT_BAR_THAT_SLOT_IS_ACTIVATED(2496),
	// Message: To stop receiving the above tip, please check the box next to Disable Game Tips from your Options menu.
	TO_STOP_RECEIVING_THE_ABOVE_TIP_PLEASE_CHECK_THE_BOX_NEXT_TO_DISABLE_GAME_TIPS_FROM_YOUR_OPTIONS_MENU(2497),
	// Message: While piloting an airship, you can change your altitude using the button at the center of the helm image.
	WHILE_PILOTING_AN_AIRSHIP_YOU_CAN_CHANGE_YOUR_ALTITUDE_USING_THE_BUTTON_AT_THE_CENTER_OF_THE_HELM_IMAGE(2498),
	// Message: You cannot collect because someone else is already collecting.
	YOU_CANNOT_COLLECT_BECAUSE_SOMEONE_ELSE_IS_ALREADY_COLLECTING(2499),
	// Message: Your collection has succeeded.
	YOUR_COLLECTION_HAS_SUCCEEDED(2500),
	// Message: You will be moved to the previous chatting channel tab.
	YOU_WILL_BE_MOVED_TO_THE_PREVIOUS_CHATTING_CHANNEL_TAB(2501),
	// Message: You will be moved to the next chatting channel tab.
	YOU_WILL_BE_MOVED_TO_THE_NEXT_CHATTING_CHANNEL_TAB(2502),
	// Message: The currently selected target will be cancelled.
	THE_CURRENTLY_SELECTED_TARGET_WILL_BE_CANCELLED(2503),
	// Message: Focus will be moved to chat window.
	FOCUS_WILL_BE_MOVED_TO_CHAT_WINDOW(2504),
	// Message: Opens or closes the inventory window.
	OPENS_OR_CLOSES_THE_INVENTORY_WINDOW(2505),
	// Message: Temporarily hides all open windows.
	TEMPORARILY_HIDES_ALL_OPEN_WINDOWS(2506),
	// Message: Closes all open windows.
	CLOSES_ALL_OPEN_WINDOWS(2507),
	// Message: Opens the GM manager window.
	OPENS_THE_GM_MANAGER_WINDOW(2508),
	// Message: Opens the GM petition window.
	OPENS_THE_GM_PETITION_WINDOW(2509),
	// Message: The buff in the party window is toggled. Buff for one input, debuff for two inputs, a song and dance for three inputs, turnoff for 4 inputs
	THE_BUFF_IN_THE_PARTY_WINDOW_IS_TOGGLED(2510),
	// Message: Activates or deactivates minimum frame function.
	ACTIVATES_OR_DEACTIVATES_MINIMUM_FRAME_FUNCTION(2511),
	// Message: Runs or closes the MSN messenger window.
	RUNS_OR_CLOSES_THE_MSN_MESSENGER_WINDOW(2512),
	// Message: Assign 1st slot shortcut in the shortcut base window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_1ST_SLOT_SHORTCUT_IN_THE_SHORTCUT_BASE_WINDOW(2513),
	// Message: Assign 2nd slot shortcut in the shortcut base window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_2ND_SLOT_SHORTCUT_IN_THE_SHORTCUT_BASE_WINDOW(2514),
	// Message: Assign 3rd slot shortcut in the shortcut base window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_3RD_SLOT_SHORTCUT_IN_THE_SHORTCUT_BASE_WINDOW(2515),
	// Message: Assign 4th slot shortcut in the shortcut base window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_4TH_SLOT_SHORTCUT_IN_THE_SHORTCUT_BASE_WINDOW(2516),
	// Message: Assign 5th slot shortcut in the shortcut base window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_5TH_SLOT_SHORTCUT_IN_THE_SHORTCUT_BASE_WINDOW(2517),
	// Message: Assign 6th slot shortcut in the shortcut base window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_6TH_SLOT_SHORTCUT_IN_THE_SHORTCUT_BASE_WINDOW(2518),
	// Message: Assign 7th slot shortcut in the shortcut base window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_7TH_SLOT_SHORTCUT_IN_THE_SHORTCUT_BASE_WINDOW(2519),
	// Message: Assign 8th slot shortcut in the shortcut base window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_8TH_SLOT_SHORTCUT_IN_THE_SHORTCUT_BASE_WINDOW(2520),
	// Message: Assign 9th slot shortcut in the shortcut base window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_9TH_SLOT_SHORTCUT_IN_THE_SHORTCUT_BASE_WINDOW(2521),
	// Message: Assign 10th slot shortcut in the shortcut base window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_10TH_SLOT_SHORTCUT_IN_THE_SHORTCUT_BASE_WINDOW(2522),
	// Message: Assign 11th slot shortcut in the shortcut base window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_11TH_SLOT_SHORTCUT_IN_THE_SHORTCUT_BASE_WINDOW(2523),
	// Message: Assign 12th slot shortcut in the shortcut base window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_12TH_SLOT_SHORTCUT_IN_THE_SHORTCUT_BASE_WINDOW(2524),
	// Message: Assign 1st slot shortcut in the 1st shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_1ST_SLOT_SHORTCUT_IN_THE_1ST_SHORTCUT_EXPANDED_WINDOW(2525),
	// Message: Assign 2nd slot shortcut in the 1st shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_2ND_SLOT_SHORTCUT_IN_THE_1ST_SHORTCUT_EXPANDED_WINDOW(2526),
	// Message: Assign 3rd slot shortcut in the 1st shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_3RD_SLOT_SHORTCUT_IN_THE_1ST_SHORTCUT_EXPANDED_WINDOW(2527),
	// Message: Assign 4th slot shortcut in the 1st shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_4TH_SLOT_SHORTCUT_IN_THE_1ST_SHORTCUT_EXPANDED_WINDOW(2528),
	// Message: Assign 5th slot shortcut in the 1st shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_5TH_SLOT_SHORTCUT_IN_THE_1ST_SHORTCUT_EXPANDED_WINDOW(2529),
	// Message: Assign 6th slot shortcut in the 1st shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_6TH_SLOT_SHORTCUT_IN_THE_1ST_SHORTCUT_EXPANDED_WINDOW(2530),
	// Message: Assign 7th slot shortcut in the 1st shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_7TH_SLOT_SHORTCUT_IN_THE_1ST_SHORTCUT_EXPANDED_WINDOW(2531),
	// Message: Assign 8th slot shortcut in the 1st shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_8TH_SLOT_SHORTCUT_IN_THE_1ST_SHORTCUT_EXPANDED_WINDOW(2532),
	// Message: Assign 9th slot shortcut in the 1st shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_9TH_SLOT_SHORTCUT_IN_THE_1ST_SHORTCUT_EXPANDED_WINDOW(2533),
	// Message: Assign 10th slot shortcut in the 1st shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_10TH_SLOT_SHORTCUT_IN_THE_1ST_SHORTCUT_EXPANDED_WINDOW(2534),
	// Message: Assign 11th slot shortcut in the 1st shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_11TH_SLOT_SHORTCUT_IN_THE_1ST_SHORTCUT_EXPANDED_WINDOW(2535),
	// Message: Assign 12th slot shortcut in the 1st shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_12TH_SLOT_SHORTCUT_IN_THE_1ST_SHORTCUT_EXPANDED_WINDOW(2536),
	// Message: Assign 1st slot shortcut in the 2nd shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_1ST_SLOT_SHORTCUT_IN_THE_2ND_SHORTCUT_EXPANDED_WINDOW(2537),
	// Message: Assign 2nd slot shortcut in the 2nd shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_2ND_SLOT_SHORTCUT_IN_THE_2ND_SHORTCUT_EXPANDED_WINDOW(2538),
	// Message: Assign 3rd slot shortcut in the 2nd shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_3RD_SLOT_SHORTCUT_IN_THE_2ND_SHORTCUT_EXPANDED_WINDOW(2539),
	// Message: Assign 4th slot shortcut in the 2nd shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_4TH_SLOT_SHORTCUT_IN_THE_2ND_SHORTCUT_EXPANDED_WINDOW(2540),
	// Message: Assign 5th slot shortcut in the 2nd shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_5TH_SLOT_SHORTCUT_IN_THE_2ND_SHORTCUT_EXPANDED_WINDOW(2541),
	// Message: Assign 6th slot shortcut in the 2nd shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_6TH_SLOT_SHORTCUT_IN_THE_2ND_SHORTCUT_EXPANDED_WINDOW(2542),
	// Message: Assign 7th slot shortcut in the 2nd shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_7TH_SLOT_SHORTCUT_IN_THE_2ND_SHORTCUT_EXPANDED_WINDOW(2543),
	// Message: Assign 8th slot shortcut in the 2nd shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_8TH_SLOT_SHORTCUT_IN_THE_2ND_SHORTCUT_EXPANDED_WINDOW(2544),
	// Message: Assign 9th slot shortcut in the 2nd shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_9TH_SLOT_SHORTCUT_IN_THE_2ND_SHORTCUT_EXPANDED_WINDOW(2545),
	// Message: Assign 10th slot shortcut in the 2nd shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_10TH_SLOT_SHORTCUT_IN_THE_2ND_SHORTCUT_EXPANDED_WINDOW(2546),
	// Message: Assign 11th slot shortcut in the 2nd shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_11TH_SLOT_SHORTCUT_IN_THE_2ND_SHORTCUT_EXPANDED_WINDOW(2547),
	// Message: Assign 12th slot shortcut in the 2nd shortcut expanded window. Combination of Ctrl and Shift cannot be assigned.
	ASSIGN_12TH_SLOT_SHORTCUT_IN_THE_2ND_SHORTCUT_EXPANDED_WINDOW(2548),
	// Message: Move the shortcut page in the shortcut base window to page 1.
	MOVE_THE_SHORTCUT_PAGE_IN_THE_SHORTCUT_BASE_WINDOW_TO_PAGE_1(2549),
	// Message: Move the shortcut page in the shortcut base window to page 2.
	MOVE_THE_SHORTCUT_PAGE_IN_THE_SHORTCUT_BASE_WINDOW_TO_PAGE_2(2550),
	// Message: Move the shortcut page in the shortcut base window to page 3.
	MOVE_THE_SHORTCUT_PAGE_IN_THE_SHORTCUT_BASE_WINDOW_TO_PAGE_3(2551),
	// Message: Move the shortcut page in the shortcut base window to page 4.
	MOVE_THE_SHORTCUT_PAGE_IN_THE_SHORTCUT_BASE_WINDOW_TO_PAGE_4(2552),
	// Message: Move the shortcut page in the shortcut base window to page 5.
	MOVE_THE_SHORTCUT_PAGE_IN_THE_SHORTCUT_BASE_WINDOW_TO_PAGE_5(2553),
	// Message: Move the shortcut page in the shortcut base window to page 6.
	MOVE_THE_SHORTCUT_PAGE_IN_THE_SHORTCUT_BASE_WINDOW_TO_PAGE_6(2554),
	// Message: Move the shortcut page in the shortcut base window to page 7.
	MOVE_THE_SHORTCUT_PAGE_IN_THE_SHORTCUT_BASE_WINDOW_TO_PAGE_7(2555),
	// Message: Move the shortcut page in the shortcut base window to page 8.
	MOVE_THE_SHORTCUT_PAGE_IN_THE_SHORTCUT_BASE_WINDOW_TO_PAGE_8(2556),
	// Message: Move the shortcut page in the shortcut base window to page 9.
	MOVE_THE_SHORTCUT_PAGE_IN_THE_SHORTCUT_BASE_WINDOW_TO_PAGE_9(2557),
	// Message: Move the shortcut page in the shortcut base window to page 10.
	MOVE_THE_SHORTCUT_PAGE_IN_THE_SHORTCUT_BASE_WINDOW_TO_PAGE_10(2558),
	// Message: Opens and closes the action window, executing character actions and game commands.
	OPENS_AND_CLOSES_THE_ACTION_WINDOW_EXECUTING_CHARACTER_ACTIONS_AND_GAME_COMMANDS(2559),
	// Message: Opens and closes the game bulletin board.
	OPENS_AND_CLOSES_THE_GAME_BULLETIN_BOARD(2560),
	// Message: Opens and closes the calculator.
	OPENS_AND_CLOSES_THE_CALCULATOR(2561),
	// Message: Hides or shows the chat window, the window always shows by default.
	HIDES_OR_SHOWS_THE_CHAT_WINDOW_THE_WINDOW_ALWAYS_SHOWS_BY_DEFAULT(2562),
	// Message: Opens and closes the clan window, confirming information of the included clan and performs the various set-ups related to the clan.
	OPENS_AND_CLOSES_THE_CLAN_WINDOW_CONFIRMING_INFORMATION_OF_THE_INCLUDED_CLAN_AND_PERFORMS_THE_VARIOUS_SETUPS_RELATED_TO_THE_CLAN(2563),
	// Message: Opens and closes the status window, showing the detailed status of a character that you created.
	OPENS_AND_CLOSES_THE_STATUS_WINDOW_SHOWING_THE_DETAILED_STATUS_OF_A_CHARACTER_THAT_YOU_CREATED(2564),
	// Message: Opens and closes the help window.
	OPENS_AND_CLOSES_THE_HELP_WINDOW(2565),
	// Message: Opens or closes the inventory window.
	OPENS_OR_CLOSES_THE_INVENTORY_WINDOW_(2566),
	// Message: Opens and closes the macro window for macro settings.
	OPENS_AND_CLOSES_THE_MACRO_WINDOW_FOR_MACRO_SETTINGS(2567),
	// Message: Opens and closes the skill window, displaying the list of skills that you can use.
	OPENS_AND_CLOSES_THE_SKILL_WINDOW_DISPLAYING_THE_LIST_OF_SKILLS_THAT_YOU_CAN_USE(2568),
	// Message: Hides or shows the menu window, the window shows by default.
	HIDES_OR_SHOWS_THE_MENU_WINDOW_THE_WINDOW_SHOWS_BY_DEFAULT(2569),
	// Message: Opens and closes the mini map, showing detailed information about the game world.
	OPENS_AND_CLOSES_THE_MINI_MAP_SHOWING_DETAILED_INFORMATION_ABOUT_THE_GAME_WORLD(2570),
	// Message: Opens and closes the option window.
	OPENS_AND_CLOSES_THE_OPTION_WINDOW(2571),
	// Message: Open and close the party matching window, useful in organizing a party by helping to easily find other members looking for a party.
	OPEN_AND_CLOSE_THE_PARTY_MATCHING_WINDOW_USEFUL_IN_ORGANIZING_A_PARTY_BY_HELPING_TO_EASILY_FIND_OTHER_MEMBERS_LOOKING_FOR_A_PARTY(2572),
	// Message: Open and close the quest journal, displaying the progress of quests.
	OPEN_AND_CLOSE_THE_QUEST_JOURNAL_DISPLAYING_THE_PROGRESS_OF_QUESTS(2573),
	// Message: Hides or re-opens the Radar Map, which always appears by default.
	HIDES_OR_REOPENS_THE_RADAR_MAP_WHICH_ALWAYS_APPEARS_BY_DEFAULT(2574),
	// Message: Hide or show the status window, the window will show by default.
	HIDE_OR_SHOW_THE_STATUS_WINDOW_THE_WINDOW_WILL_SHOW_BY_DEFAULT(2575),
	// Message: Opens and closes the system menu window, enables detailed menu selection.
	OPENS_AND_CLOSES_THE_SYSTEM_MENU_WINDOW_ENABLES_DETAILED_MENU_SELECTION(2576),
	// Message: Do not show drop items dropped in the world. Game performance speed can be enhanced by using this option.
	DO_NOT_SHOW_DROP_ITEMS_DROPPED_IN_THE_WORLD(2577),
	// Message: A key to automatically send whispers to a targeted character.
	A_KEY_TO_AUTOMATICALLY_SEND_WHISPERS_TO_A_TARGETED_CHARACTER(2578),
	// Message: Turns off all game sounds.
	TURNS_OFF_ALL_GAME_SOUNDS(2579),
	// Message: Expands each shortcut window.
	EXPANDS_EACH_SHORTCUT_WINDOW(2580),
	// Message: Initialize user interface location to a default location.
	INITIALIZE_USER_INTERFACE_LOCATION_TO_A_DEFAULT_LOCATION(2581),
	// Message: Spin my character or mountable to the lel2ft.
	SPIN_MY_CHARACTER_OR_MOUNTABLE_TO_THE_LEFT(2582),
	// Message: Spin my character or mountable to the right.
	SPIN_MY_CHARACTER_OR_MOUNTABLE_TO_THE_RIGHT(2583),
	// Message: Spin my character or mountable forward.
	SPIN_MY_CHARACTER_OR_MOUNTABLE_FORWARD(2584),
	// Message: Spin my character or mountable to the rear.
	SPIN_MY_CHARACTER_OR_MOUNTABLE_TO_THE_REAR(2585),
	// Message: Continue moving in the present direction.
	CONTINUE_MOVING_IN_THE_PRESENT_DIRECTION(2586),
	// Message: Reduce the viewing point of my character or mountable.
	REDUCE_THE_VIEWING_POINT_OF_MY_CHARACTER_OR_MOUNTABLE(2587),
	// Message: Enlarge the viewing point of my character or mountable.
	ENLARGE_THE_VIEWING_POINT_OF_MY_CHARACTER_OR_MOUNTABLE(2588),
	// Message: Quickly spin in all directions the viewing point of my character or mountable.
	QUICKLY_SPIN_IN_ALL_DIRECTIONS_THE_VIEWING_POINT_OF_MY_CHARACTER_OR_MOUNTABLE(2589),
	// Message: Opens the GM manager window.
	OPENS_THE_GM_MANAGER_WINDOW_(2590),
	// Message: Opens the GM petition window.
	OPENS_THE_GM_PETITION_WINDOW_(2591),
	// Message: Quickly switch the content of the expanded shortcut window.
	QUICKLY_SWITCH_THE_CONTENT_OF_THE_EXPANDED_SHORTCUT_WINDOW(2592),
	// Message: Advance by a set distance the viewing point of my character or mountable.
	ADVANCE_BY_A_SET_DISTANCE_THE_VIEWING_POINT_OF_MY_CHARACTER_OR_MOUNTABLE(2593),
	// Message: Retreat by a set distance the viewing point of my character or mountable.
	RETREAT_BY_A_SET_DISTANCE_THE_VIEWING_POINT_OF_MY_CHARACTER_OR_MOUNTABLE(2594),
	// Message: Reset the viewing point of my character or mountable.
	RESET_THE_VIEWING_POINT_OF_MY_CHARACTER_OR_MOUNTABLE(2595),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED(2596),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED_(2597),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED__(2598),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED___(2599),
	// Message: The match is being prepared. Please try again later.
	THE_MATCH_IS_BEING_PREPARED(2701),
	// Message: You were excluded from the match because the registration count was not correct.
	YOU_WERE_EXCLUDED_FROM_THE_MATCH_BECAUSE_THE_REGISTRATION_COUNT_WAS_NOT_CORRECT(2702),
	// Message: Team members were modified because the teams were unbalanced.
	TEAM_MEMBERS_WERE_MODIFIED_BECAUSE_THE_TEAMS_WERE_UNBALANCED(2703),
	// Message: You cannot register because capacity has been exceeded.
	YOU_CANNOT_REGISTER_BECAUSE_CAPACITY_HAS_BEEN_EXCEEDED(2704),
	// Message: The match waiting time was extended by 1 minute.
	THE_MATCH_WAITING_TIME_WAS_EXTENDED_BY_1_MINUTE(2705),
	// Message: You cannot enter because you do not meet the requirements.
	YOU_CANNOT_ENTER_BECAUSE_YOU_DO_NOT_MEET_THE_REQUIREMENTS(2706),
	// Message: You must wait 10 seconds before attempting to register again.
	YOU_MUST_WAIT_10_SECONDS_BEFORE_ATTEMPTING_TO_REGISTER_AGAIN(2707),
	// Message: You cannot register while in possession of a cursed weapon.
	YOU_CANNOT_REGISTER_WHILE_IN_POSSESSION_OF_A_CURSED_WEAPON(2708),
	// Message: Applicants for the Olympiad, Underground Coliseum, or Kratei's Cube matches cannot register.
	APPLICANTS_FOR_THE_OLYMPIAD_UNDERGROUND_COLISEUM_OR_KRATEIS_CUBE_MATCHES_CANNOT_REGISTER(2709),
	// Message: Current location: $s1, $s2, $s3 (near the Keucereus Alliance Base)
	CURRENT_LOCATION_S1_S2_S3_NEAR_THE_KEUCEREUS_ALLIANCE_BASE(2710),
	// Message: Current location: $s1, $s2, $s3 (inside the Seed of Infinity)
	CURRENT_LOCATION_S1_S2_S3_INSIDE_THE_SEED_OF_INFINITY(2711),
	// Message: Current location: $s1, $s2, $s3 (outside the Seed of Infinity)
	CURRENT_LOCATION_S1_S2_S3_OUTSIDE_THE_SEED_OF_INFINITY(2712),
	// Message: ------------------------------------------------------
	//___(2713),
	// Message: ----------------------------------------------------------------------
	//____(2714),
	// Message: Airships cannot be boarded in the current area.
	AIRSHIPS_CANNOT_BE_BOARDED_IN_THE_CURRENT_AREA(2715),
	// Message: Current location: $s1, $s2, $s3 (inside Aerial Cleft)
	CURRENT_LOCATION_S1_S2_S3_INSIDE_AERIAL_CLEFT(2716),
	// Message: The airship will dock at the wharf shortly.
	THE_AIRSHIP_WILL_DOCK_AT_THE_WHARF_SHORTLY(2717),
	// Message: That skill cannot be used because your target's location is too high or low.
	THAT_SKILL_CANNOT_BE_USED_BECAUSE_YOUR_TARGETS_LOCATION_IS_TOO_HIGH_OR_LOW(2718),
	// Message: Only non-compressed 256 color bitmap files can be registered.
	ONLY_NONCOMPRESSED_256_COLOR_BITMAP_FILES_CAN_BE_REGISTERED(2719),
	// Message: Instant zone: $s1's entry has been restricted. You can check the next possible entry time by using the command "/instancezone."
	INSTANT_ZONE_S1S_ENTRY_HAS_BEEN_RESTRICTED(2720),
	// Message: You are too high to perform this action. Please lower your altitude and try again.
	YOU_ARE_TOO_HIGH_TO_PERFORM_THIS_ACTION(2721),
	// Message: Another airship has already been summoned. Please try again later.
	ANOTHER_AIRSHIP_HAS_ALREADY_BEEN_SUMMONED(2722),
	// Message: The airship cannot be summoned because you don't have enough $s1.
	THE_AIRSHIP_CANNOT_BE_SUMMONED_BECAUSE_YOU_DONT_HAVE_ENOUGH_S1(2723),
	// Message: The airship cannot be purchased because you don't have enough $s1.
	THE_AIRSHIP_CANNOT_BE_PURCHASED_BECAUSE_YOU_DONT_HAVE_ENOUGH_S1(2724),
	// Message: You cannot summon the airship because you do not meet the requirements.
	YOU_CANNOT_SUMMON_THE_AIRSHIP_BECAUSE_YOU_DO_NOT_MEET_THE_REQUIREMENTS(2725),
	// Message: You cannot purchase the airship because you do not meet the requirements.
	YOU_CANNOT_PURCHASE_THE_AIRSHIP_BECAUSE_YOU_DO_NOT_MEET_THE_REQUIREMENTS(2726),
	// Message: You cannot board because you do not meet the requirements.
	YOU_CANNOT_BOARD_BECAUSE_YOU_DO_NOT_MEET_THE_REQUIREMENTS(2727),
	// Message: This action is prohibited while mounted or on an airship.
	THIS_ACTION_IS_PROHIBITED_WHILE_MOUNTED_OR_ON_AN_AIRSHIP(2728),
	// Message: You cannot control the helm while transformed.
	YOU_CANNOT_CONTROL_THE_HELM_WHILE_TRANSFORMED(2729),
	// Message: You cannot control the helm while you are petrified.
	YOU_CANNOT_CONTROL_THE_HELM_WHILE_YOU_ARE_PETRIFIED(2730),
	// Message: You cannot control the helm when you are dead.
	YOU_CANNOT_CONTROL_THE_HELM_WHEN_YOU_ARE_DEAD(2731),
	// Message: You cannot control the helm while fishing.
	YOU_CANNOT_CONTROL_THE_HELM_WHILE_FISHING(2732),
	// Message: You cannot control the helm while in a battle.
	YOU_CANNOT_CONTROL_THE_HELM_WHILE_IN_A_BATTLE(2733),
	// Message: You cannot control the helm while in a duel.
	YOU_CANNOT_CONTROL_THE_HELM_WHILE_IN_A_DUEL(2734),
	// Message: You cannot control the helm while in a sitting position.
	YOU_CANNOT_CONTROL_THE_HELM_WHILE_IN_A_SITTING_POSITION(2735),
	// Message: You cannot control the helm while using a skill.
	YOU_CANNOT_CONTROL_THE_HELM_WHILE_USING_A_SKILL(2736),
	// Message: You cannot control the helm while a cursed weapon is equipped.
	YOU_CANNOT_CONTROL_THE_HELM_WHILE_A_CURSED_WEAPON_IS_EQUIPPED(2737),
	// Message: You cannot control the helm while holding a flag.
	YOU_CANNOT_CONTROL_THE_HELM_WHILE_HOLDING_A_FLAG(2738),
	// Message: You cannot control the helm because you do not meet the requirements.
	YOU_CANNOT_CONTROL_THE_HELM_BECAUSE_YOU_DO_NOT_MEET_THE_REQUIREMENTS(2739),
	// Message: This action is prohibited while steering.
	THIS_ACTION_IS_PROHIBITED_WHILE_STEERING(2740),
	// Message: You can control an airship by targeting the airship's helm and clicking the "Steer" button from your Actions list.
	YOU_CAN_CONTROL_AN_AIRSHIP_BY_TARGETING_THE_AIRSHIPS_HELM_AND_CLICKING_THE_STEER_BUTTON_FROM_YOUR_ACTIONS_LIST(2741),
	// Message: Any character riding the airship can control it.
	ANY_CHARACTER_RIDING_THE_AIRSHIP_CAN_CONTROL_IT(2742),
	// Message: If you restart while on an airship, you will return to the departure location.
	IF_YOU_RESTART_WHILE_ON_AN_AIRSHIP_YOU_WILL_RETURN_TO_THE_DEPARTURE_LOCATION(2743),
	// Message: If you press the "Control Cancel" action button, you can exit the control state at any time.
	IF_YOU_PRESS_THE_CONTROL_CANCEL_ACTION_BUTTON_YOU_CAN_EXIT_THE_CONTROL_STATE_AT_ANY_TIME(2744),
	// Message: The "Exit Airship" button allows you to disembark before the airship departs.
	THE_EXIT_AIRSHIP_BUTTON_ALLOWS_YOU_TO_DISEMBARK_BEFORE_THE_AIRSHIP_DEPARTS(2745),
	// Message: Use the "Depart" button from your Destination Map to make the airship depart.
	USE_THE_DEPART_BUTTON_FROM_YOUR_DESTINATION_MAP_TO_MAKE_THE_AIRSHIP_DEPART(2746),
	// Message: The Destination Map contains convenient travel locations. Clicking on a dot will display how much fuel (EP) is consumed for that location.
	THE_DESTINATION_MAP_CONTAINS_CONVENIENT_TRAVEL_LOCATIONS(2747),
	// Message: You have been reported as an illegal program user and cannot report other users.
	YOU_HAVE_BEEN_REPORTED_AS_AN_ILLEGAL_PROGRAM_USER_AND_CANNOT_REPORT_OTHER_USERS(2748),
	// Message: You have reached your crystallization limit and cannot crystallize any more.
	YOU_HAVE_REACHED_YOUR_CRYSTALLIZATION_LIMIT_AND_CANNOT_CRYSTALLIZE_ANY_MORE(2749),
	// Message: The $s1 ward has been destroyed! $c2 now has the territory ward.
	THE_S1_WARD_HAS_BEEN_DESTROYED_C2_NOW_HAS_THE_TERRITORY_WARD(2750),
	// Message: The character that acquired $s1's ward has been killed.
	THE_CHARACTER_THAT_ACQUIRED_S1S_WARD_HAS_BEEN_KILLED(2751),
	// Message: The war for $s1 has been declared.
	THE_WAR_FOR_S1_HAS_BEEN_DECLARED(2752),
	// Message: This type of attack is prohibited when allied troops are the target.
	THIS_TYPE_OF_ATTACK_IS_PROHIBITED_WHEN_ALLIED_TROOPS_ARE_THE_TARGET(2753),
	// Message: You cannot be simultaneously registered for PVP matches such as the Olympiad, Underground Coliseum, Aerial Cleft, Kratei's Cube, and Handy's Block Checkers.
	YOU_CANNOT_BE_SIMULTANEOUSLY_REGISTERED_FOR_PVP_MATCHES_SUCH_AS_THE_OLYMPIAD_UNDERGROUND_COLISEUM_AERIAL_CLEFT_KRATEIS_CUBE_AND_HANDYS_BLOCK_CHECKERS(2754),
	// Message: $c1 has been designated as CAT (Combat Aerial Target).
	C1_HAS_BEEN_DESIGNATED_AS_CAT_COMBAT_AERIAL_TARGET(2755),
	// Message: Another player is probably controlling the target.
	ANOTHER_PLAYER_IS_PROBABLY_CONTROLLING_THE_TARGET(2756),
	// Message: The ship is already moving so you have failed to board.
	THE_SHIP_IS_ALREADY_MOVING_SO_YOU_HAVE_FAILED_TO_BOARD(2757),
	// Message: You cannot control the target while a pet or servitor is summoned.
	YOU_CANNOT_CONTROL_THE_TARGET_WHILE_A_PET_OR_SERVITOR_IS_SUMMONED(2758),
	// Message: When actions are prohibited, you cannot mount a mountable.
	WHEN_ACTIONS_ARE_PROHIBITED_YOU_CANNOT_MOUNT_A_MOUNTABLE(2759),
	// Message: When actions are prohibited, you cannot control the target.
	WHEN_ACTIONS_ARE_PROHIBITED_YOU_CANNOT_CONTROL_THE_TARGET(2760),
	// Message: You must target the one you wish to control.
	YOU_MUST_TARGET_THE_ONE_YOU_WISH_TO_CONTROL(2761),
	// Message: You cannot control because you are too far.
	YOU_CANNOT_CONTROL_BECAUSE_YOU_ARE_TOO_FAR(2762),
	// Message: You cannot enter the battlefield while in a party state.
	YOU_CANNOT_ENTER_THE_BATTLEFIELD_WHILE_IN_A_PARTY_STATE(2763),
	// Message: You cannot enter because the corresponding alliance channel's maximum number of entrants has been reached.
	YOU_CANNOT_ENTER_BECAUSE_THE_CORRESPONDING_ALLIANCE_CHANNELS_MAXIMUM_NUMBER_OF_ENTRANTS_HAS_BEEN_REACHED(2764),
	// Message: Only the alliance channel leader can attempt entry.
	ONLY_THE_ALLIANCE_CHANNEL_LEADER_CAN_ATTEMPT_ENTRY(2765),
	// Message: Seed of Infinity Stage 1 Attack In Progress
	SEED_OF_INFINITY_STAGE_1_ATTACK_IN_PROGRESS(2766),
	// Message: Seed of Infinity Stage 2 Attack In Progress
	SEED_OF_INFINITY_STAGE_2_ATTACK_IN_PROGRESS(2767),
	// Message: Seed of Infinity Conquest Complete
	SEED_OF_INFINITY_CONQUEST_COMPLETE(2768),
	// Message: Seed of Infinity Stage 1 Defense In Progress
	SEED_OF_INFINITY_STAGE_1_DEFENSE_IN_PROGRESS(2769),
	// Message: Seed of Infinity Stage 2 Defense In Progress
	SEED_OF_INFINITY_STAGE_2_DEFENSE_IN_PROGRESS(2770),
	// Message: Seed of Destruction Attack in Progress
	SEED_OF_DESTRUCTION_ATTACK_IN_PROGRESS(2771),
	// Message: Seed of Destruction Conquest Complete
	SEED_OF_DESTRUCTION_CONQUEST_COMPLETE(2772),
	// Message: Seed of Destruction Defense in Progress
	SEED_OF_DESTRUCTION_DEFENSE_IN_PROGRESS(2773),
	// Message: You can make another report in $s1-minute(s). You have $s2 points remaining on this account.
	YOU_CAN_MAKE_ANOTHER_REPORT_IN_S1MINUTES(2774),
	// Message: The match cannot take place because a party member is in the process of boarding.
	THE_MATCH_CANNOT_TAKE_PLACE_BECAUSE_A_PARTY_MEMBER_IS_IN_THE_PROCESS_OF_BOARDING(2775),
	// Message: The effect of territory ward is disappearing.
	THE_EFFECT_OF_TERRITORY_WARD_IS_DISAPPEARING(2776),
	// Message: The airship summon license has been entered. Your clan can now summon the airship.
	THE_AIRSHIP_SUMMON_LICENSE_HAS_BEEN_ENTERED(2777),
	// Message: You cannot teleport while in possession of a ward.
	YOU_CANNOT_TELEPORT_WHILE_IN_POSSESSION_OF_A_WARD(2778),
	// Message: Further increase in altitude is not allowed.
	FURTHER_INCREASE_IN_ALTITUDE_IS_NOT_ALLOWED(2779),
	// Message: Further decrease in altitude is not allowed.
	FURTHER_DECREASE_IN_ALTITUDE_IS_NOT_ALLOWED(2780),
	// Message: Number of units: $s1
	NUMBER_OF_UNITS_S1(2781),
	// Message: Number of people: $s1
	NUMBER_OF_PEOPLE_S1(2782),
	// Message: No one is left from the opposing team, thus victory is yours.
	NO_ONE_IS_LEFT_FROM_THE_OPPOSING_TEAM_THUS_VICTORY_IS_YOURS(2783),
	// Message: The battlefield has been closed. The match has ended in a tie because the match lasted for $s1-minute(s) and $s2-second(s) and the requirements were not met.
	THE_BATTLEFIELD_HAS_BEEN_CLOSED(2784),
	// Message: It's a large scaled airship for transportations and battles and can be owned by the unit of clan.
	ITS_A_LARGE_SCALED_AIRSHIP_FOR_TRANSPORTATIONS_AND_BATTLES_AND_CAN_BE_OWNED_BY_THE_UNIT_OF_CLAN(2785),
	// Message: Start action is available only when controlling the airship.
	START_ACTION_IS_AVAILABLE_ONLY_WHEN_CONTROLLING_THE_AIRSHIP(2786),
	// Message: $c1 has drained you of $s2 HP.
	C1_HAS_DRAINED_YOU_OF_S2_HP(2787),
	// Message: Mercenary participation is requested in $s1 territory.
	MERCENARY_PARTICIPATION_IS_REQUESTED_IN_S1_TERRITORY(2788),
	// Message: Mercenary participation request is cancelled in $s1 territory.
	MERCENARY_PARTICIPATION_REQUEST_IS_CANCELLED_IN_S1_TERRITORY(2789),
	// Message: Clan participation is requested in $s1 territory.
	CLAN_PARTICIPATION_IS_REQUESTED_IN_S1_TERRITORY(2790),
	// Message: Clan participation request is cancelled in $s1 territory.
	CLAN_PARTICIPATION_REQUEST_IS_CANCELLED_IN_S1_TERRITORY(2791),
	// Message: 50 clan reputation points will be awarded. Do you wish to continue?
	_50_CLAN_REPUTATION_POINTS_WILL_BE_AWARDED(2792),
	// Message: You must have a minimum of ($s1) people to enter this Instant Zone. Your request for entry is denied.
	YOU_MUST_HAVE_A_MINIMUM_OF_S1_PEOPLE_TO_ENTER_THIS_INSTANT_ZONE(2793),
	// Message: The territory war channel and functions will now be deactivated.
	THE_TERRITORY_WAR_CHANNEL_AND_FUNCTIONS_WILL_NOW_BE_DEACTIVATED(2794),
	// Message: You've already requested a territory war in another territory elsewhere.
	YOUVE_ALREADY_REQUESTED_A_TERRITORY_WAR_IN_ANOTHER_TERRITORY_ELSEWHERE(2795),
	// Message: The clan who owns the territory cannot participate in the territory war as mercenaries.
	THE_CLAN_WHO_OWNS_THE_TERRITORY_CANNOT_PARTICIPATE_IN_THE_TERRITORY_WAR_AS_MERCENARIES(2796),
	// Message: It is not a territory war registration period, so a request cannot be made at this time.
	IT_IS_NOT_A_TERRITORY_WAR_REGISTRATION_PERIOD_SO_A_REQUEST_CANNOT_BE_MADE_AT_THIS_TIME(2797),
	// Message: The territory war will end in $s1-hour(s).
	THE_TERRITORY_WAR_WILL_END_IN_S1HOURS(2798),
	// Message: The territory war will end in $s1-minute(s).
	THE_TERRITORY_WAR_WILL_END_IN_S1MINUTES(2799),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED____(2800),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED_____(2801),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED______(2802),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED_______(2803),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED________(2804),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED_________(2805),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED__________(2806),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED___________(2807),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED____________(2808),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED_____________(2809),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED______________(2810),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED_______________(2811),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED________________(2812),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED_________________(2813),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED__________________(2814),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED___________________(2815),
	// Message: Designate a shortcut key for the Flying Transformed Object Exclusive use shortcut window's No 1 slot.  The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_FLYING_TRANSFORMED_OBJECT_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_1_SLOT(2816),
	// Message: Designate a shortcut key for the Flying Transformed Object Exclusive use shortcut window's No 2 slot.  The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_FLYING_TRANSFORMED_OBJECT_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_2_SLOT(2817),
	// Message: Designate a shortcut key for the Flying Transformed Object Exclusive use shortcut window's No 3 slot.  The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_FLYING_TRANSFORMED_OBJECT_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_3_SLOT(2818),
	// Message: Designate a shortcut key for the Flying Transformed Object Exclusive use shortcut window's No 4 slot.  The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_FLYING_TRANSFORMED_OBJECT_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_4_SLOT(2819),
	// Message: Designate a shortcut key for the Flying Transformed Object Exclusive use shortcut window's No 5 slot.  The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_FLYING_TRANSFORMED_OBJECT_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_5_SLOT(2820),
	// Message: Designate a shortcut key for the Flying Transformed Object Exclusive use shortcut window's No 6 slot.  The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_FLYING_TRANSFORMED_OBJECT_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_6_SLOT(2821),
	// Message: Designate a shortcut key for the Flying Transformed Object Exclusive use shortcut window's No 7 slot.  The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_FLYING_TRANSFORMED_OBJECT_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_7_SLOT(2822),
	// Message: Designate a shortcut key for the Flying Transformed Object Exclusive use shortcut window's No 8 slot.  The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_FLYING_TRANSFORMED_OBJECT_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_8_SLOT(2823),
	// Message: Designate a shortcut key for the Flying Transformed Object Exclusive use shortcut window's No 9 slot.  The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_FLYING_TRANSFORMED_OBJECT_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_9_SLOT(2824),
	// Message: Designate a shortcut key for the Flying Transformed Object Exclusive use shortcut window's No 10 slot.  The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_FLYING_TRANSFORMED_OBJECT_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_10_SLOT(2825),
	// Message: Designate a shortcut key for the Flying Transformed Object Exclusive use shortcut window's No 11 slot.  The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_FLYING_TRANSFORMED_OBJECT_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_11_SLOT(2826),
	// Message: Designate a shortcut key for the Flying Transformed Object Exclusive use shortcut window's No 12 slot.  The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_FLYING_TRANSFORMED_OBJECT_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_12_SLOT(2827),
	// Message: Designate a shortcut key for the Mountable Exclusive use shortcut window's No 1 slot. The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_MOUNTABLE_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_1_SLOT(2828),
	// Message: Designate a shortcut key for the Mountable Exclusive use shortcut window's No 2 slot. The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_MOUNTABLE_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_2_SLOT(2829),
	// Message: Designate a shortcut key for the Mountable Exclusive use shortcut window's No 3 slot. The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_MOUNTABLE_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_3_SLOT(2830),
	// Message: Designate a shortcut key for the Mountable Exclusive use shortcut window's No 4 slot. The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_MOUNTABLE_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_4_SLOT(2831),
	// Message: Designate a shortcut key for the Mountable Exclusive use shortcut window's No 5 slot. The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_MOUNTABLE_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_5_SLOT(2832),
	// Message: Designate a shortcut key for the Mountable Exclusive use shortcut window's No 6 slot. The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_MOUNTABLE_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_6_SLOT(2833),
	// Message: Designate a shortcut key for the Mountable Exclusive use shortcut window's No 7 slot. The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_MOUNTABLE_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_7_SLOT(2834),
	// Message: Designate a shortcut key for the Mountable Exclusive use shortcut window's No 8 slot. The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_MOUNTABLE_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_8_SLOT(2835),
	// Message: Designate a shortcut key for the Mountable Exclusive use shortcut window's No 9 slot. The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_MOUNTABLE_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_9_SLOT(2836),
	// Message: Designate a shortcut key for the Mountable Exclusive use shortcut window's No 10 slot. The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_MOUNTABLE_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_10_SLOT(2837),
	// Message: Designate a shortcut key for the Mountable Exclusive use shortcut window's No 11 slot. The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_MOUNTABLE_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_11_SLOT(2838),
	// Message: Designate a shortcut key for the Mountable Exclusive use shortcut window's No 12 slot. The CTRL and SHIFT keys cannot be designated.
	DESIGNATE_A_SHORTCUT_KEY_FOR_THE_MOUNTABLE_EXCLUSIVE_USE_SHORTCUT_WINDOWS_NO_12_SLOT(2839),
	// Message: Execute the designated shortcut's action/skill/macro.
	EXECUTE_THE_DESIGNATED_SHORTCUTS_ACTIONSKILLMACRO(2840),
	// Message: Raise my character to the top.
	RAISE_MY_CHARACTER_TO_THE_TOP(2841),
	// Message: Lower my character to the bottom.
	LOWER_MY_CHARACTER_TO_THE_BOTTOM(2842),
	// Message: Raise the controlled mountable to the top.
	RAISE_THE_CONTROLLED_MOUNTABLE_TO_THE_TOP(2843),
	// Message: Lower the controlled mountable to the bottom.
	LOWER_THE_CONTROLLED_MOUNTABLE_TO_THE_BOTTOM(2844),
	// Message: Automatically send forward my character or mountable.
	AUTOMATICALLY_SEND_FORWARD_MY_CHARACTER_OR_MOUNTABLE(2845),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED____________________(2846),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED_____________________(2847),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED______________________(2848),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED_______________________(2849),
	// Message: No translation required
	NO_TRANSLATION_REQUIRED________________________(2850),
	// Message: Stop all actions of my character.
	STOP_ALL_ACTIONS_OF_MY_CHARACTER(2851),
	// Message: Stop all actions of my controlled mountable.
	STOP_ALL_ACTIONS_OF_MY_CONTROLLED_MOUNTABLE(2852),
	// Message: If you join the clan academy, you can become a clan member and learn the game system until you become level 40. If you want more fun, we recommend that you join the clan academy.
	IF_YOU_JOIN_THE_CLAN_ACADEMY_YOU_CAN_BECOME_A_CLAN_MEMBER_AND_LEARN_THE_GAME_SYSTEM_UNTIL_YOU_BECOME_LEVEL_40(2875),
	// Message: If you become level 40, the second class change is available. If you complete the second class change, the character's capability is enhanced.
	IF_YOU_BECOME_LEVEL_40_THE_SECOND_CLASS_CHANGE_IS_AVAILABLE(2876),
	// Message: $s1-second(s) to the end of territory war!
	S1SECONDS_TO_THE_END_OF_TERRITORY_WAR(2900),
	// Message: You cannot force attack a member of the same territory.
	YOU_CANNOT_FORCE_ATTACK_A_MEMBER_OF_THE_SAME_TERRITORY(2901),
	// Message: You've acquired the ward. Move quickly to your forces' outpost.
	YOUVE_ACQUIRED_THE_WARD(2902),
	// Message: Territory war has begun.
	TERRITORY_WAR_HAS_BEGUN(2903),
	// Message: Territory war has ended.
	TERRITORY_WAR_HAS_ENDED(2904),
	// Message: Altitude cannot be decreased any further.
	ALTITUDE_CANNOT_BE_DECREASED_ANY_FURTHER(2905),
	// Message: Altitude cannot be increased any further.
	ALTITUDE_CANNOT_BE_INCREASED_ANY_FURTHER(2906),
	// Message: You have entered a potentially hostile environment so the airship's speed has been greatly decreased.
	YOU_HAVE_ENTERED_A_POTENTIALLY_HOSTILE_ENVIRONMENT_SO_THE_AIRSHIPS_SPEED_HAS_BEEN_GREATLY_DECREASED(2907),
	// Message: As you are leaving the hostile environment, the airship's speed has been returned to normal.
	AS_YOU_ARE_LEAVING_THE_HOSTILE_ENVIRONMENT_THE_AIRSHIPS_SPEED_HAS_BEEN_RETURNED_TO_NORMAL(2908),
	// Message: A servitor or pet cannot be summoned while on an airship.
	A_SERVITOR_OR_PET_CANNOT_BE_SUMMONED_WHILE_ON_AN_AIRSHIP(2909),
	// Message: You have entered an incorrect command.
	YOU_HAVE_ENTERED_AN_INCORRECT_COMMAND(2910),
	// Message: You've requested $c1 to be on your Friends List.
	YOUVE_REQUESTED_C1_TO_BE_ON_YOUR_FRIENDS_LIST(2911),
	// Message: You've invited $c1 to join your clan.
	YOUVE_INVITED_C1_TO_JOIN_YOUR_CLAN(2912),
	// Message: Clan $s1 has succeeded in capturing $s2's territory ward.
	CLAN_S1_HAS_SUCCEEDED_IN_CAPTURING_S2S_TERRITORY_WARD(2913),
	// Message: The territory war will begin in 20 minutes! Territory related functions (i.e.: battlefield channel, Disguise Scrolls, Transformations, etc...) can now be used.
	THE_TERRITORY_WAR_WILL_BEGIN_IN_20_MINUTES_TERRITORY_RELATED_FUNCTIONS_I(2914),
	// Message: This clan member cannot withdraw or be expelled while participating in a territory war.
	THIS_CLAN_MEMBER_CANNOT_WITHDRAW_OR_BE_EXPELLED_WHILE_PARTICIPATING_IN_A_TERRITORY_WAR(2915),
	// Message: $s1 in battle
	S1_IN_BATTLE(2916),
	// Message: Territories are at peace.
	TERRITORIES_ARE_AT_PEACE(2917),
	// Message: Only characters who are level 40 or above who have completed their second class transfer can register in a territory war.
	ONLY_CHARACTERS_WHO_ARE_LEVEL_40_OR_ABOVE_WHO_HAVE_COMPLETED_THEIR_SECOND_CLASS_TRANSFER_CAN_REGISTER_IN_A_TERRITORY_WAR(2918),
	// Message: While disguised, you cannot operate a private or manufacture store.
	WHILE_DISGUISED_YOU_CANNOT_OPERATE_A_PRIVATE_OR_MANUFACTURE_STORE(2919),
	// Message: No more airships can be summoned as the maximum airship limit has been met.
	NO_MORE_AIRSHIPS_CAN_BE_SUMMONED_AS_THE_MAXIMUM_AIRSHIP_LIMIT_HAS_BEEN_MET(2920),
	// Message: You cannot board the airship because the maximum number for occupants is met.
	YOU_CANNOT_BOARD_THE_AIRSHIP_BECAUSE_THE_MAXIMUM_NUMBER_FOR_OCCUPANTS_IS_MET(2921),
	// Message: Block Checker will end in 5 seconds!
	BLOCK_CHECKER_WILL_END_IN_5_SECONDS(2922),
	// Message: Block Checker will end in 4 seconds!!
	BLOCK_CHECKER_WILL_END_IN_4_SECONDS(2923),
	// Message: You cannot enter a Seed while in a flying transformation state.
	YOU_CANNOT_ENTER_A_SEED_WHILE_IN_A_FLYING_TRANSFORMATION_STATE(2924),
	// Message: Block Checker will end in 3 seconds!!!
	BLOCK_CHECKER_WILL_END_IN_3_SECONDS(2925),
	// Message: Block Checker will end in 2 seconds!!!!
	BLOCK_CHECKER_WILL_END_IN_2_SECONDS(2926),
	// Message: Block Checker will end in 1 second!!!!!
	BLOCK_CHECKER_WILL_END_IN_1_SECOND(2927),
	// Message: The $c1 team has won.
	THE_C1_TEAM_HAS_WON(2928),
	// Message: Your request cannot be processed because there's no enough available memory on your graphic card. Please try again after reducing the resolution.
	YOUR_REQUEST_CANNOT_BE_PROCESSED_BECAUSE_THERES_NO_ENOUGH_AVAILABLE_MEMORY_ON_YOUR_GRAPHIC_CARD(2929),
	// Message: A graphic card internal error has occurred. Please install the latest version of the graphic card driver and try again.
	A_GRAPHIC_CARD_INTERNAL_ERROR_HAS_OCCURRED(2930),
	// Message: The system file may have been damaged. After ending the game, please check the file using the Lineage II auto update.
	THE_SYSTEM_FILE_MAY_HAVE_BEEN_DAMAGED(2931),
	// Message: $s1 adena
	S1_ADENA(2932),
	// Message: Thomas D. Turkey has appeared. Please save Santa.
	THOMAS_D(2933),
	// Message: You have defeated Thomas D. Turkey and rescued Santa.
	YOU_HAVE_DEFEATED_THOMAS_D(2934),
	// Message: You failed to rescue Santa, and Thomas D. Turkey has disappeared.
	YOU_FAILED_TO_RESCUE_SANTA_AND_THOMAS_D(2935),
	// Message: The disguise scroll cannot be used because it is meant for use in a different territory.
	THE_DISGUISE_SCROLL_CANNOT_BE_USED_BECAUSE_IT_IS_MEANT_FOR_USE_IN_A_DIFFERENT_TERRITORY(2936),
	// Message: A territory owning clan member cannot use a disguise scroll.
	A_TERRITORY_OWNING_CLAN_MEMBER_CANNOT_USE_A_DISGUISE_SCROLL(2937),
	// Message: The disguise scroll cannot be used while you are engaged in a private store or manufacture workshop.
	THE_DISGUISE_SCROLL_CANNOT_BE_USED_WHILE_YOU_ARE_ENGAGED_IN_A_PRIVATE_STORE_OR_MANUFACTURE_WORKSHOP(2938),
	// Message: A disguise cannot be used when you are in a chaotic state.
	A_DISGUISE_CANNOT_BE_USED_WHEN_YOU_ARE_IN_A_CHAOTIC_STATE(2939),
	// Message: +3 to +9 enchant success can be improved with special items.
	_3_TO_9_ENCHANT_SUCCESS_CAN_BE_IMPROVED_WITH_SPECIAL_ITEMS(2940),
	// Message: The request cannot be completed because the requirements are not met. In order to participate in a team match, all team members must have an Olympiad score of 1 or more.
	THE_REQUEST_CANNOT_BE_COMPLETED_BECAUSE_THE_REQUIREMENTS_ARE_NOT_MET(2941),
	// Message: The first gift's remaining resupply time is $s1 hour(s) $s2 minute(s) $s3 second(s).  (If you resummon the Agathion at the gift supply time, the supply time can take an additional 10 minutes.)
	THE_FIRST_GIFTS_REMAINING_RESUPPLY_TIME_IS_S1_HOURS_S2_MINUTES_S3_SECONDS(2942),
	// Message: The first gift's remaining resupply time is $s1 minute(s) $s2 second(s). (If you resummon the Agathion at the gift supply time, the supply time can take an additional 10 minutes.)
	THE_FIRST_GIFTS_REMAINING_RESUPPLY_TIME_IS_S1_MINUTES_S2_SECONDS(2943),
	// Message: The first gift's remaining resupply time is $s1 second(s).  (If you resummon the Agathion at the gift supply time, the supply time can take an additional 10 minutes.)
	THE_FIRST_GIFTS_REMAINING_RESUPPLY_TIME_IS_S1_SECONDS(2944),
	// Message: The second gift's remaining resupply time is $s1 hour(s) $s2 minute(s) $s3 second(s).  (If you resummon the Agathion at the gift supply time, the supply time can take an additional 1 hour 10 minutes.)
	THE_SECOND_GIFTS_REMAINING_RESUPPLY_TIME_IS_S1_HOURS_S2_MINUTES_S3_SECONDS(2945),
	// Message: The second gift's remaining resupply time is $s1 minute(s) $s2 second(s). (If you resummon the Agathion at the gift supply time, the supply time can take an additional 1 hour 10 minutes.)
	THE_SECOND_GIFTS_REMAINING_RESUPPLY_TIME_IS_S1_MINUTES_S2_SECONDS(2946),
	// Message: The second gift's remaining resupply time is $s1 second(s). (If you resummon the Agathion at the gift supply time, the supply time can take an additional 1 hour 10 minutes.)
	THE_SECOND_GIFTS_REMAINING_RESUPPLY_TIME_IS_S1_SECONDS(2947),
	// Message: The territory war exclusive disguise and transformation can be used 20 minutes before the start of the territory war to 10 minutes after its end.
	THE_TERRITORY_WAR_EXCLUSIVE_DISGUISE_AND_TRANSFORMATION_CAN_BE_USED_20_MINUTES_BEFORE_THE_START_OF_THE_TERRITORY_WAR_TO_10_MINUTES_AFTER_ITS_END(2955),
	// Message: A user participating in the Olympiad cannot witness the battle.
	A_USER_PARTICIPATING_IN_THE_OLYMPIAD_CANNOT_WITNESS_THE_BATTLE(2956),
	// Message: A character born on February 29 will receive a gift on February 28.
	A_CHARACTER_BORN_ON_FEBRUARY_29_WILL_RECEIVE_A_GIFT_ON_FEBRUARY_28(2957),
	// Message: You cannot cancel in a non-peace zone location.
	YOU_CANNOT_CANCEL_IN_A_NONPEACE_ZONE_LOCATION(2982),
	// Message: You cannot cancel during an exchange.
	YOU_CANNOT_CANCEL_DURING_AN_EXCHANGE(2983),
	// Message: You cannot cancel because the private shop or workshop is in progress.
	YOU_CANNOT_CANCEL_BECAUSE_THE_PRIVATE_SHOP_OR_WORKSHOP_IS_IN_PROGRESS(2984),
	// Message: You cannot cancel during an item enhancement or attribute enhancement.
	YOU_CANNOT_CANCEL_DURING_AN_ITEM_ENHANCEMENT_OR_ATTRIBUTE_ENHANCEMENT(2985),
	// Message: Please set the amount of adena to send.
	PLEASE_SET_THE_AMOUNT_OF_ADENA_TO_SEND(2986),
	// Message: Please set the amount of adena to receive.
	PLEASE_SET_THE_AMOUNT_OF_ADENA_TO_RECEIVE(2987),
	// Message: You could not cancel receipt because your inventory is full.
	YOU_COULD_NOT_CANCEL_RECEIPT_BECAUSE_YOUR_INVENTORY_IS_FULL(2988),
	// Message: Vitamin item $s1 is being used.
	VITAMIN_ITEM_S1_IS_BEING_USED(2989),
	// Message: $2 units of vitamin item $s1 was consumed.
	_2_UNITS_OF_VITAMIN_ITEM_S1_WAS_CONSUMED(2990),
	// Message: True input must be entered by someone over 15 years old.
	TRUE_INPUT_MUST_BE_ENTERED_BY_SOMEONE_OVER_15_YEARS_OLD(2991),
	// Message: Please choose the 2nd stage type.
	PLEASE_CHOOSE_THE_2ND_STAGE_TYPE(2992),
	// Message: If the Command Channel leader leaves the party matching room, then the sessions ends. Do you really wish to exit the room?
	IF_THE_COMMAND_CHANNEL_LEADER_LEAVES_THE_PARTY_MATCHING_ROOM_THEN_THE_SESSIONS_ENDS(2993),
	// Message: The Command Channel matching room was cancelled.
	THE_COMMAND_CHANNEL_MATCHING_ROOM_WAS_CANCELLED(2994),
	// Message: This Command Channel matching room is already cancelled.
	THIS_COMMAND_CHANNEL_MATCHING_ROOM_IS_ALREADY_CANCELLED(2995),
	// Message: You cannot enter the Command Channel matching room because you do not meet the requirements.
	YOU_CANNOT_ENTER_THE_COMMAND_CHANNEL_MATCHING_ROOM_BECAUSE_YOU_DO_NOT_MEET_THE_REQUIREMENTS(2996),
	// Message: You exited from the Command Channel matching room.
	YOU_EXITED_FROM_THE_COMMAND_CHANNEL_MATCHING_ROOM(2997),
	// Message: You were expelled from the Command Channel matching room.
	YOU_WERE_EXPELLED_FROM_THE_COMMAND_CHANNEL_MATCHING_ROOM(2998),
	// Message: The Command Channel affiliated party's party member cannot use the matching screen.
	THE_COMMAND_CHANNEL_AFFILIATED_PARTYS_PARTY_MEMBER_CANNOT_USE_THE_MATCHING_SCREEN(2999),
	// Message: The Command Channel matching room was created.
	THE_COMMAND_CHANNEL_MATCHING_ROOM_WAS_CREATED(3000),
	// Message: The Command Channel matching room information was edited.
	THE_COMMAND_CHANNEL_MATCHING_ROOM_INFORMATION_WAS_EDITED(3001),
	// Message: When the recipient doesn't exist or the character has been deleted, sending mail is not possible.
	WHEN_THE_RECIPIENT_DOESNT_EXIST_OR_THE_CHARACTER_HAS_BEEN_DELETED_SENDING_MAIL_IS_NOT_POSSIBLE(3002),
	// Message: $c1 entered the Command Channel matching room.
	C1_ENTERED_THE_COMMAND_CHANNEL_MATCHING_ROOM(3003),
	// Message: I'm sorry to give you a satisfactory response.\n\nIf you send your comments regarding the unsatisfying parts, we will be able to provide even greater service.\n\nPlease send us your comments.
	IM_SORRY_TO_GIVE_YOU_A_SATISFACTORY_RESPONSE(3004),
	// Message: This skill cannot be enhanced.
	THIS_SKILL_CANNOT_BE_ENHANCED(3005),
	// Message: Newly used PC cafe $s1 points were withdrawn.
	NEWLY_USED_PC_CAFE_S1_POINTS_WERE_WITHDRAWN(3006),
	// Message: Shyeed's roar filled with wrath rings throughout the Stakato Nest.
	SHYEEDS_ROAR_FILLED_WITH_WRATH_RINGS_THROUGHOUT_THE_STAKATO_NEST(3007),
	// Message: The mail has arrived.
	THE_MAIL_HAS_ARRIVED(3008),
	// Message: Mail successfully sent.
	MAIL_SUCCESSFULLY_SENT(3009),
	// Message: Mail successfully returned.
	MAIL_SUCCESSFULLY_RETURNED(3010),
	// Message: Mail successfully cancelled.
	MAIL_SUCCESSFULLY_CANCELLED(3011),
	// Message: You have acquired $s2 $s1.
	YOU_HAVE_ACQUIRED_S2_S1(3073),
	// Message: The allowed length for recipient exceeded.
	THE_ALLOWED_LENGTH_FOR_RECIPIENT_EXCEEDED(3074),
	// Message: The allowed length for a title exceeded.
	THE_ALLOWED_LENGTH_FOR_A_TITLE_EXCEEDED(3075),
	// Message: The allowed length for a title exceeded.
	THE_ALLOWED_LENGTH_FOR_A_TITLE_EXCEEDED_(3076),
	// Message: The mail limit (240) of the opponent's character has been exceeded and this cannot be forwarded.
	THE_MAIL_LIMIT_240_OF_THE_OPPONENTS_CHARACTER_HAS_BEEN_EXCEEDED_AND_THIS_CANNOT_BE_FORWARDED(3077),
	// Message: You're making a request for payment. Do you want to proceed?
	YOURE_MAKING_A_REQUEST_FOR_PAYMENT(3078),
	// Message: There are items in your Pet Inventory rendering you unable to sell/trade/drop pet summoning items. Please empty your Pet Inventory.
	THERE_ARE_ITEMS_IN_YOUR_PET_INVENTORY_RENDERING_YOU_UNABLE_TO_SELLTRADEDROP_PET_SUMMONING_ITEMS(3079),
	// Message: You cannot reset the Skill Link because there is not enough Adena.
	YOU_CANNOT_RESET_THE_SKILL_LINK_BECAUSE_THERE_IS_NOT_ENOUGH_ADENA(3080),
	// Message: You cannot receive it because you are under the condition that the opponent cannot acquire any Adena for payment.
	YOU_CANNOT_RECEIVE_IT_BECAUSE_YOU_ARE_UNDER_THE_CONDITION_THAT_THE_OPPONENT_CANNOT_ACQUIRE_ANY_ADENA_FOR_PAYMENT(3081),
	// Message: You cannot send mails to any character that has blocked you.
	YOU_CANNOT_SEND_MAILS_TO_ANY_CHARACTER_THAT_HAS_BLOCKED_YOU(3082),
	// Message: In the process of working on the previous clan declaration/retreat. Please try again later.
	IN_THE_PROCESS_OF_WORKING_ON_THE_PREVIOUS_CLAN_DECLARATIONRETREAT(3083),
	// Message: Currently, we are in the process of choosing a hero. Please try again later.
	CURRENTLY_WE_ARE_IN_THE_PROCESS_OF_CHOOSING_A_HERO(3084),
	// Message: You can summon the pet you are trying to summon now only when you own a hideout.
	YOU_CAN_SUMMON_THE_PET_YOU_ARE_TRYING_TO_SUMMON_NOW_ONLY_WHEN_YOU_OWN_A_HIDEOUT(3085),
	// Message: Would you like to give $s2 $s1?
	WOULD_YOU_LIKE_TO_GIVE_S2_S1(3086),
	// Message: This mail is being sent with a Payment Request. Would you like to continue?
	THIS_MAIL_IS_BEING_SENT_WITH_A_PAYMENT_REQUEST(3087),
	// Message: You have $s1 hours $s2 minutes and $s3 seconds left in the Proof of Space and Time. If Agathion is summoned within this time, 10 minutes or more can be added.
	YOU_HAVE_S1_HOURS_S2_MINUTES_AND_S3_SECONDS_LEFT_IN_THE_PROOF_OF_SPACE_AND_TIME(3088),
	// Message: You have $s1 minutes and $s2 seconds left in the Proof of Space and Time. If Agathion is summoned within this time, 10 minutes or more can be added.
	YOU_HAVE_S1_MINUTES_AND_S2_SECONDS_LEFT_IN_THE_PROOF_OF_SPACE_AND_TIME(3089),
	// Message: You have $s1 seconds left in the Proof of Space and Time. If Agathion is summoned within this time, 10 minutes or more can be added.
	YOU_HAVE_S1_SECONDS_LEFT_IN_THE_PROOF_OF_SPACE_AND_TIME(3090),
	// Message: You cannot delete characters on this server right now.
	YOU_CANNOT_DELETE_CHARACTERS_ON_THIS_SERVER_RIGHT_NOW(3091),
	// Message: Transaction completed.
	TRANSACTION_COMPLETED(3092),
	// Message: It exeeds the proper calculation range. Please enter again.
	IT_EXEEDS_THE_PROPER_CALCULATION_RANGE(3093),
	// Message: A user currently participating in the Olympiad cannot send party and friend invitations.
	A_USER_CURRENTLY_PARTICIPATING_IN_THE_OLYMPIAD_CANNOT_SEND_PARTY_AND_FRIEND_INVITATIONS(3094),
	// Message: The certification failed because you did not enter a valid certification number or you did not enter a certification number at all. If you fail 3 times in a row, you will be blocked from the game for 30 minutes.
	THE_CERTIFICATION_FAILED_BECAUSE_YOU_DID_NOT_ENTER_A_VALID_CERTIFICATION_NUMBER_OR_YOU_DID_NOT_ENTER_A_CERTIFICATION_NUMBER_AT_ALL(3095),
	// Message: Due to problems with communications, our telephone certification service is currently unavailable. Please try again later.
	DUE_TO_PROBLEMS_WITH_COMMUNICATIONS_OUR_TELEPHONE_CERTIFICATION_SERVICE_IS_CURRENTLY_UNAVAILABLE(3096),
	// Message: Due to problems with communications, telephone signals are being delayed. Please try again later.
	DUE_TO_PROBLEMS_WITH_COMMUNICATIONS_TELEPHONE_SIGNALS_ARE_BEING_DELAYED(3097),
	// Message: The certification failed because the line was busy or the call was not received. Please try again.
	THE_CERTIFICATION_FAILED_BECAUSE_THE_LINE_WAS_BUSY_OR_THE_CALL_WAS_NOT_RECEIVED(3098),
	// Message: The telephone certification service subscription number is expired or incorrect. Please check the telephone certification service subscription information. (plaync > Security Center > Telephone Certification Service)
	THE_TELEPHONE_CERTIFICATION_SERVICE_SUBSCRIPTION_NUMBER_IS_EXPIRED_OR_INCORRECT(3099),
	// Message: The telephone certification service is currently being checked. Please try again later.
	THE_TELEPHONE_CERTIFICATION_SERVICE_IS_CURRENTLY_BEING_CHECKED(3100),
	// Message: Due to heavy volume, the telephone certification service cannot be used at this time. Please try again later.
	DUE_TO_HEAVY_VOLUME_THE_TELEPHONE_CERTIFICATION_SERVICE_CANNOT_BE_USED_AT_THIS_TIME(3101),
	// Message: The telephone certification service use period has expired, so game play has been blocked. Please check the game play block setup. (plaync > Security Center > Telephone Certification Service)
	THE_TELEPHONE_CERTIFICATION_SERVICE_USE_PERIOD_HAS_EXPIRED_SO_GAME_PLAY_HAS_BEEN_BLOCKED(3102),
	// Message: The telephone certification failed 3 times in a row, so game play has been blocked for 30 minutes. Please try again later.
	THE_TELEPHONE_CERTIFICATION_FAILED_3_TIMES_IN_A_ROW_SO_GAME_PLAY_HAS_BEEN_BLOCKED_FOR_30_MINUTES(3103),
	// Message: The number of uses of the daily telephone certification service has been exceeded.
	THE_NUMBER_OF_USES_OF_THE_DAILY_TELEPHONE_CERTIFICATION_SERVICE_HAS_BEEN_EXCEEDED(3104),
	// Message: Telephone certification is already underway. Please try again later.
	TELEPHONE_CERTIFICATION_IS_ALREADY_UNDERWAY(3105),
	// Message: Telephone certification is underway.
	TELEPHONE_CERTIFICATION_IS_UNDERWAY(3106),
	// Message: One moment please.
	ONE_MOMENT_PLEASE(3107),
	// Message: You are no longer protected from aggressive monsters.
	YOU_ARE_NO_LONGER_PROTECTED_FROM_AGGRESSIVE_MONSTERS(3108),
	// Message: $s1 has achieved $s2 wins in a row in Jack's game.
	S1_HAS_ACHIEVED_S2_WINS_IN_A_ROW_IN_JACKS_GAME(3109),
	// Message: In reward for $s2 wins in a row, $s1님 has received $s4 of $s3(s).
	IN_REWARD_FOR_S2_WINS_IN_A_ROW_S1_HAS_RECEIVED_S4_OF_S3S(3110),
	// Message: World: $s1 wins in a row ($s2 ppl)
	WORLD_S1_WINS_IN_A_ROW_S2_PPL(3111),
	// Message: My record: $s1 wins in a row
	MY_RECORD_S1_WINS_IN_A_ROW(3112),
	// Message: World: Below 4 wins in a row
	WORLD_BELOW_4_WINS_IN_A_ROW(3113),
	// Message: My record: Below 4 wins in a row
	MY_RECORD_BELOW_4_WINS_IN_A_ROW(3114),
	// Message: This is the Halloween event period.
	THIS_IS_THE_HALLOWEEN_EVENT_PERIOD(3115),
	// Message: No record of 10 or more wins in a row.
	NO_RECORD_OF_10_OR_MORE_WINS_IN_A_ROW(3116),
	// Message: You can no longer bestow attributes that are the opposite of the currently bestowed attribute.
	YOU_CAN_NO_LONGER_BESTOW_ATTRIBUTES_THAT_ARE_THE_OPPOSITE_OF_THE_CURRENTLY_BESTOWED_ATTRIBUTE(3117),
	// Message: Do you wish to accept $c1's $s2 request?
	DO_YOU_WISH_TO_ACCEPT_C1S_S2_REQUEST(3118),
	// Message: The couple action was denied.
	THE_COUPLE_ACTION_WAS_DENIED(3119),
	// Message: The request cannot be completed because the target does not meet location requirements.
	THE_REQUEST_CANNOT_BE_COMPLETED_BECAUSE_THE_TARGET_DOES_NOT_MEET_LOCATION_REQUIREMENTS(3120),
	// Message: The couple action was cancelled.
	THE_COUPLE_ACTION_WAS_CANCELLED(3121),
	// Message: The size of the uploaded crest or insignia does not meet the standard requirements.
	THE_SIZE_OF_THE_UPLOADED_CREST_OR_INSIGNIA_DOES_NOT_MEET_THE_STANDARD_REQUIREMENTS(3122),
	// Message: $c1 is in Private Shop mode or in a battle and cannot be requested for a couple action.
	C1_IS_IN_PRIVATE_SHOP_MODE_OR_IN_A_BATTLE_AND_CANNOT_BE_REQUESTED_FOR_A_COUPLE_ACTION(3123),
	// Message: $c1 is fishing and cannot be requested for a couple action.
	C1_IS_FISHING_AND_CANNOT_BE_REQUESTED_FOR_A_COUPLE_ACTION(3124),
	// Message: $c1 is in a battle and cannot be requested for a couple action.
	C1_IS_IN_A_BATTLE_AND_CANNOT_BE_REQUESTED_FOR_A_COUPLE_ACTION(3125),
	// Message: $c1 is already participating in a couple action and cannot be requested for another couple action.
	C1_IS_ALREADY_PARTICIPATING_IN_A_COUPLE_ACTION_AND_CANNOT_BE_REQUESTED_FOR_ANOTHER_COUPLE_ACTION(3126),
	// Message: $c1 is in a chaotic state and cannot be requested for a couple action.
	C1_IS_IN_A_CHAOTIC_STATE_AND_CANNOT_BE_REQUESTED_FOR_A_COUPLE_ACTION(3127),
	// Message: $c1 is participating in the Olympiad and cannot be requested for a couple action.
	C1_IS_PARTICIPATING_IN_THE_OLYMPIAD_AND_CANNOT_BE_REQUESTED_FOR_A_COUPLE_ACTION(3128),
	// Message: $c1 is participating in a hideout siege and cannot be requested for a couple action.
	C1_IS_PARTICIPATING_IN_A_HIDEOUT_SIEGE_AND_CANNOT_BE_REQUESTED_FOR_A_COUPLE_ACTION(3129),
	// Message: $c1 is in a castle siege and cannot be requested for a couple action.
	C1_IS_IN_A_CASTLE_SIEGE_AND_CANNOT_BE_REQUESTED_FOR_A_COUPLE_ACTION(3130),
	// Message: $c1 is riding a ship, steed, or strider and cannot be requested for a couple action.
	C1_IS_RIDING_A_SHIP_STEED_OR_STRIDER_AND_CANNOT_BE_REQUESTED_FOR_A_COUPLE_ACTION(3131),
	// Message: $c1 is currently teleporting and cannot be requested for a couple action.
	C1_IS_CURRENTLY_TELEPORTING_AND_CANNOT_BE_REQUESTED_FOR_A_COUPLE_ACTION(3132),
	// Message: $c1 is currently transforming and cannot be requested for a couple action.
	C1_IS_CURRENTLY_TRANSFORMING_AND_CANNOT_BE_REQUESTED_FOR_A_COUPLE_ACTION(3133),
	// Message: Party loot was changed to "$s1". Do you consent?
	PARTY_LOOT_WAS_CHANGED_TO_S1(3134),
	// Message: Requesting approval for changing party loot to "$s1".
	REQUESTING_APPROVAL_FOR_CHANGING_PARTY_LOOT_TO_S1(3135),
	// Message: Party loot can now be changed.
	PARTY_LOOT_CAN_NOW_BE_CHANGED(3136),
	// Message: Party loot change was cancelled.
	PARTY_LOOT_CHANGE_WAS_CANCELLED(3137),
	// Message: Party loot was changed to "$s1".
	PARTY_LOOT_WAS_CHANGED_TO_S1_(3138),
	// Message: $c1 is currently dead and cannot be requested for a couple action.
	C1_IS_CURRENTLY_DEAD_AND_CANNOT_BE_REQUESTED_FOR_A_COUPLE_ACTION(3139),
	// Message: The crest was successfully registered.
	THE_CREST_WAS_SUCCESSFULLY_REGISTERED(3140),
	// Message: $c1 is in the process of changing the party loot. Please try again later.
	C1_IS_IN_THE_PROCESS_OF_CHANGING_THE_PARTY_LOOT(3141),
	// Message: While party loot change is taking place, another 1:1 request cannot be made.
	WHILE_PARTY_LOOT_CHANGE_IS_TAKING_PLACE_ANOTHER_11_REQUEST_CANNOT_BE_MADE(3142),
	// Message: You can only register 8x12 pixel 256 color bmp files for clan crests.
	YOU_CAN_ONLY_REGISTER_8X12_PIXEL_256_COLOR_BMP_FILES_FOR_CLAN_CRESTS(3143),
	// Message: The $s2's attribute was successfully bestowed on $s1, and resistance to $s3 was increased.
	THE_S2S_ATTRIBUTE_WAS_SUCCESSFULLY_BESTOWED_ON_S1_AND_RESISTANCE_TO_S3_WAS_INCREASED(3144),
	// Message: This item cannot be used because you are already participating in the quest that can be started with this item.
	THIS_ITEM_CANNOT_BE_USED_BECAUSE_YOU_ARE_ALREADY_PARTICIPATING_IN_THE_QUEST_THAT_CAN_BE_STARTED_WITH_THIS_ITEM(3145),
	// Message: Do you really wish to remove $s1's $s2 attribute?
	DO_YOU_REALLY_WISH_TO_REMOVE_S1S_S2_ATTRIBUTE(3146),
	// Message: If you are not resurrected within $s1 minutes, you will be expelled from the instant zone.
	IF_YOU_ARE_NOT_RESURRECTED_WITHIN_S1_MINUTES_YOU_WILL_BE_EXPELLED_FROM_THE_INSTANT_ZONE(3147),
	// Message: The number of instant zones that can be created has been exceeded. Please try again later.
	THE_NUMBER_OF_INSTANT_ZONES_THAT_CAN_BE_CREATED_HAS_BEEN_EXCEEDED(3148),
	// Message: One-piece upper and lower body armor can use enchant rate increasing items from +4.
	ONEPIECE_UPPER_AND_LOWER_BODY_ARMOR_CAN_USE_ENCHANT_RATE_INCREASING_ITEMS_FROM_4(3149),
	// Message: You have requested a couple action with $c1.
	YOU_HAVE_REQUESTED_A_COUPLE_ACTION_WITH_C1(3150),
	// Message: $c1 accepted the couple action.
	C1_ACCEPTED_THE_COUPLE_ACTION(3151),
	// Message: $s1's $s2 attribute was removed, and resistance to $s3 was decreased.
	S1S_S2_ATTRIBUTE_WAS_REMOVED_AND_RESISTANCE_TO_S3_WAS_DECREASED(3152),
	// Message: The attribute that you are trying to bestow has already reached its maximum, so you cannot proceed.
	THE_ATTRIBUTE_THAT_YOU_ARE_TRYING_TO_BESTOW_HAS_ALREADY_REACHED_ITS_MAXIMUM_SO_YOU_CANNOT_PROCEED(3153),
	// Message: This item can only have one attribute. An attribute has already been bestowed, so you cannot proceed.
	THIS_ITEM_CAN_ONLY_HAVE_ONE_ATTRIBUTE(3154),
	// Message: All attributes have already been maximized, so you cannot proceed.
	ALL_ATTRIBUTES_HAVE_ALREADY_BEEN_MAXIMIZED_SO_YOU_CANNOT_PROCEED(3155),
	// Message: You do not have enough funds to cancel this attribute.
	YOU_DO_NOT_HAVE_ENOUGH_FUNDS_TO_CANCEL_THIS_ATTRIBUTE(3156),
	// Message: Do you really wish to delete the clan crest?
	DO_YOU_REALLY_WISH_TO_DELETE_THE_CLAN_CREST(3157),
	// Message: This is not the Lilith server. This command can only be used on the Lilith server.
	THIS_IS_NOT_THE_LILITH_SERVER(3158),
	// Message: First, please select the shortcut key category to be changed.
	FIRST_PLEASE_SELECT_THE_SHORTCUT_KEY_CATEGORY_TO_BE_CHANGED(3159),
	// Message: +$s1$s2's $s3 attribute was removed, so resistance to $s4 was decreased.
	S1S2S_S3_ATTRIBUTE_WAS_REMOVED_SO_RESISTANCE_TO_S4_WAS_DECREASED(3160),
	// Message: Attribute enchant and attribute cancel cannot take place at the same time. Please complete the current task and try again.
	ATTRIBUTE_ENCHANT_AND_ATTRIBUTE_CANCEL_CANNOT_TAKE_PLACE_AT_THE_SAME_TIME(3161),
	// Message: The skill cannot be used because the opponent is in a different instant zone.
	THE_SKILL_CANNOT_BE_USED_BECAUSE_THE_OPPONENT_IS_IN_A_DIFFERENT_INSTANT_ZONE(3162),
	// Message: The $s3's attribute was successfully bestowed on +$s1$s2, and resistance to $s4 was increased.
	THE_S3S_ATTRIBUTE_WAS_SUCCESSFULLY_BESTOWED_ON_S1S2_AND_RESISTANCE_TO_S4_WAS_INCREASED(3163),
	// Message: $c1 is set to refuse couple actions and cannot be requested for a couple action.
	C1_IS_SET_TO_REFUSE_COUPLE_ACTIONS_AND_CANNOT_BE_REQUESTED_FOR_A_COUPLE_ACTION(3164),
	// Message: No crest is registered.
	NO_CREST_IS_REGISTERED(3165),
	// Message: No insignia is registered.
	NO_INSIGNIA_IS_REGISTERED(3166),
	// Message: The crest was successfully deleted.
	THE_CREST_WAS_SUCCESSFULLY_DELETED(3167),
	// Message: $c1 is set to refuse party requests and cannot receive a party request.
	C1_IS_SET_TO_REFUSE_PARTY_REQUESTS_AND_CANNOT_RECEIVE_A_PARTY_REQUEST(3168),
	// Message: $c1 is set to refuse duel requests and cannot receive a duel request.
	C1_IS_SET_TO_REFUSE_DUEL_REQUESTS_AND_CANNOT_RECEIVE_A_DUEL_REQUEST(3169),
	// Message: Current location: $s1, $s2, $s3 (outside the Seed of Annihilation)
	CURRENT_LOCATION_S1_S2_S3_OUTSIDE_THE_SEED_OF_ANNIHILATION(3170),
	// Message: The gift's remaining resupply time is $s1 minute(s) $s2 second(s). (If you resummon the Agathion at the gift supply time, the supply time can take an additional 30 minutes.)
	THE_GIFTS_REMAINING_RESUPPLY_TIME_IS_S1_MINUTES_S2_SECONDS(3171),
	// Message: The gift's remaining resupply time is $s1 second(s). (If you resummon the Agathion at the gift supply time, the supply time can take an additional 30 minutes.)
	THE_GIFTS_REMAINING_RESUPPLY_TIME_IS_S1_SECONDS(3172),
	// Message: Hero exclusive items cannot be bestowed with attributes.
	HERO_EXCLUSIVE_ITEMS_CANNOT_BE_BESTOWED_WITH_ATTRIBUTES(3173),
	// Message: You dare to shatter the quiet of my castle, again.
	YOU_DARE_TO_SHATTER_THE_QUIET_OF_MY_CASTLE_AGAIN(3174),
	// Message: I, Freya Queen of Ice, shall curse you all with eternal winter sleep.
	I_FREYA_QUEEN_OF_ICE_SHALL_CURSE_YOU_ALL_WITH_ETERNAL_WINTER_SLEEP(3175),
	// Message: Your hearts will freeze to stillness... Even your memories shall disappear.
	YOUR_HEARTS_WILL_FREEZE_TO_STILLNESS(3176),
	// Message: So you wish to challenge my powers!  Hahaha…
	SO_YOU_WISH_TO_CHALLENGE_MY_POWERS_HAHAHA(3177),
	// Message: Behold my frozen minions.
	BEHOLD_MY_FROZEN_MINIONS(3178),
	// Message: Obey my command and attack these invaders.
	OBEY_MY_COMMAND_AND_ATTACK_THESE_INVADERS(3179),
	// Message: No!  How could this be... You are but mere mortals?!
	NO_HOW_COULD_THIS_BE(3180),
	// Message: Muhaha... Very well. I will bring you your icy death!
	MUHAHA(3181),
	// Message: There's no turning back! This ends now!
	THERES_NO_TURNING_BACK_THIS_ENDS_NOW(3182),
	// Message: Oh furious winds of light, slice through the darkness and defeat this evil!
	OH_FURIOUS_WINDS_OF_LIGHT_SLICE_THROUGH_THE_DARKNESS_AND_DEFEAT_THIS_EVIL(3183),
	// Message: To die this way... Such a shameful defeat... Sirra... How could you do this to me…
	TO_DIE_THIS_WAY(3184),
	// Message: Freya! This is the end for you! Rawr.
	FREYA_THIS_IS_THE_END_FOR_YOU_RAWR(3185),
	// Message: Ah-hahahaha! Ice Queen, really? You didn't deserve this power.
	AHHAHAHAHA_ICE_QUEEN_REALLY_YOU_DIDNT_DESERVE_THIS_POWER(3186),
	// Message: Ahh.. This feeling... Now... All her power is mine. Hahaha-ha!
	AHH(3187),
	// Message: You who feel warm life force coursing through your veins.
	YOU_WHO_FEEL_WARM_LIFE_FORCE_COURSING_THROUGH_YOUR_VEINS(3188),
	// Message: I shall take your last breath. But not this day, return to me.
	I_SHALL_TAKE_YOUR_LAST_BREATH(3189),
	// Message: How dare you ignore my warning... Foolish creatures. Hahaha...
	HOW_DARE_YOU_IGNORE_MY_WARNING(3190),
	// Message: Say goodbye to sunlight and welcome eternal ice.
	SAY_GOODBYE_TO_SUNLIGHT_AND_WELCOME_ETERNAL_ICE(3191),
	// Message: Muhahaha... If you wish to be chilled to the bone, I'll gladly oblige.
	MUHAHAHA(3192),
	// Message: How dare you enter my castle! Hahaha... Foolish ones... Leave this place before the frost chills your blood.
	HOW_DARE_YOU_ENTER_MY_CASTLEHAHAHA(3193),
	// Message: Hmph! You will not dodge my blizzard again!
	HMPHYOU_WILL_NOT_DODGE_MY_BLIZZARD_AGAIN(3194),
	// Message: All those who challenge my power shall feel the curse of ice.
	ALL_THOSE_WHO_CHALLENGE_MY_POWER_SHALL_FEEL_THE_CURSE_OF_ICE(3195),
	// Message: I will seal your hearts with ice. Not even your breath will escape.
	I_WILL_SEAL_YOUR_HEARTS_WITH_ICE(3196),
	// Message: Hahaha… You are much too weak. Even my snow flakes could defeat you.
	HAHAHA_YOU_ARE_MUCH_TOO_WEAK(3197),
	// Message: This is all futile.
	THIS_IS_ALL_FUTILE(3198),
	// Message: By my power, you will live in ice for all eternity.
	BY_MY_POWER_YOU_WILL_LIVE_IN_ICE_FOR_ALL_ETERNITY(3199),
	// Message: Oh, great power of destruction. Come forth and obey me!
	OH_GREAT_POWER_OF_DESTRUCTION(3200),
	// Message: The deep cold and its unwavering eternity. Cover this world with your frigid silence.
	THE_DEEP_COLD_AND_ITS_UNWAVERING_ETERNITY(3201),
	// Message: Oh blizzard of frozen death... I summon thee... Oh frozen darkness. Heed my call!
	OH_BLIZZARD_OF_FROZEN_DEATH(3202),
	// Message: Hahaha... Foolish ones. Today, I will not treat you so gently.
	HAHAHA(3203),
	// Message: This body is under my complete control now. You shall kneel before me.
	THIS_BODY_IS_UNDER_MY_COMPLETE_CONTROL_NOW(3204),
	// Message: You say you will defeat me? With all of my powers finally released? Ridiculous!
	YOU_SAY_YOU_WILL_DEFEAT_MEWITH_ALL_OF_MY_POWERS_FINALLY_RELEASED_RIDICULOUS(3205),
	// Message: You are out of Recommendations. Try again later.
	YOU_ARE_OUT_OF_RECOMMENDATIONS(3206),
	// Message: You obtained $s1 Recommendation(s).
	YOU_OBTAINED_S1_RECOMMENDATIONS(3207),
	// Message: You will go to the Lineage 2 homepage. Do you wish to continue?
	YOU_WILL_GO_TO_THE_LINEAGE_2_HOMEPAGE(3208),
	// Message: You obtained a Maguen Pet Collar.
	YOU_OBTAINED_A_MAGUEN_PET_COLLAR(3209),
	// Message: You obtained an Elite Maguen Pet Collar.
	YOU_OBTAINED_AN_ELITE_MAGUEN_PET_COLLAR(3210),
	// Message: You will go to the $s1. Do you wish to continue?
	YOU_WILL_GO_TO_THE_S1(3211),
	// Message: When your pet's hunger gauge is at 0%, you cannot use your pet.
	WHEN_YOUR_PETS_HUNGER_GAUGE_IS_AT_0_YOU_CANNOT_USE_YOUR_PET(3212),
	// Message: Your pet is starving and will not obey until it gets it's food. Feed your pet!
	YOUR_PET_IS_STARVING_AND_WILL_NOT_OBEY_UNTIL_IT_GETS_ITS_FOOD(3213),
	// Message: $s1 was successfully added to your Contact List.
	S1_WAS_SUCCESSFULLY_ADDED_TO_YOUR_CONTACT_LIST(3214),
	// Message: The name $s1%  doesn't exist. Please try another name.
	THE_NAME_S1__DOESNT_EXIST(3215),
	// Message: The name already exists on the added list.
	THE_NAME_ALREADY_EXISTS_ON_THE_ADDED_LIST(3216),
	// Message: The name is not currently registered.
	THE_NAME_IS_NOT_CURRENTLY_REGISTERED(3217),
	// Message: Do you really wish to delete $s1?
	DO_YOU_REALLY_WISH_TO_DELETE_S1(3218),
	// Message: $s1 was successfully deleted from your Contact List.
	S1_WAS_SUCCESSFULLY_DELETED_FROM_YOUR_CONTACT_LIST(3219),
	// Message: The name deletion was cancelled.
	THE_NAME_DELETION_WAS_CANCELLED(3220),
	// Message: You cannot add your own name.
	YOU_CANNOT_ADD_YOUR_OWN_NAME(3221),
	// Message: The maximum number of names (100) has been reached. You cannot register any more.
	THE_MAXIMUM_NUMBER_OF_NAMES_100_HAS_BEEN_REACHED(3222),
	// Message: The previous name is being registered. Please try again later.
	THE_PREVIOUS_NAME_IS_BEING_REGISTERED(3223),
	// Message: The maximum matches you can participate in 1 week is 70.
	THE_MAXIMUM_MATCHES_YOU_CAN_PARTICIPATE_IN_1_WEEK_IS_70(3224),
	// Message: The total number of matches that can be entered in 1 week is 60 class irrelevant individual matches, 30 specific matches, and 10 team matches.
	THE_TOTAL_NUMBER_OF_MATCHES_THAT_CAN_BE_ENTERED_IN_1_WEEK_IS_60_CLASS_IRRELEVANT_INDIVIDUAL_MATCHES_30_SPECIFIC_MATCHES_AND_10_TEAM_MATCHES(3225),
	// Message: You cannot move while speaking to an NPC. One moment please.
	YOU_CANNOT_MOVE_WHILE_SPEAKING_TO_AN_NPC(3226),
	// Message: The large army of Elmore-Aden began to advance on the Monastery of Silence.
	THE_LARGE_ARMY_OF_ELMOREADEN_BEGAN_TO_ADVANCE_ON_THE_MONASTERY_OF_SILENCE(3227),
	// Message: Anais and his troops fought against them,
	ANAIS_AND_HIS_TROOPS_FOUGHT_AGAINST_THEM(3228),
	// Message: but were all killed in the battle due to Judith's treachery.
	BUT_WERE_ALL_KILLED_IN_THE_BATTLE_DUE_TO_JUDITHS_TREACHERY(3229),
	// Message: Numerous disciples were killed mercilessly,
	NUMEROUS_DISCIPLES_WERE_KILLED_MERCILESSLY(3230),
	// Message: and Solina ended up choosing to be a prisoner of war.
	AND_SOLINA_ENDED_UP_CHOOSING_TO_BE_A_PRISONER_OF_WAR(3231),
	// Message: After she was convicted of treason and heresy and condemned to execution, her disciples were dispersed everywhere and hid themselves.
	AFTER_SHE_WAS_CONVICTED_OF_TREASON_AND_HERESY_AND_CONDEMNED_TO_EXECUTION_HER_DISCIPLES_WERE_DISPERSED_EVERYWHERE_AND_HID_THEMSELVES(3232),
	// Message: Solina's holy relics, the Scepter of Saints and the Book of Saints, which were stored at the monastery, were taken by Judith.
	SOLINAS_HOLY_RELICS_THE_SCEPTER_OF_SAINTS_AND_THE_BOOK_OF_SAINTS_WHICH_WERE_STORED_AT_THE_MONASTERY_WERE_TAKEN_BY_JUDITH(3233),
	// Message: He used them to make himself the Chief of Embryos through the power of Solina.
	HE_USED_THEM_TO_MAKE_HIMSELF_THE_CHIEF_OF_EMBRYOS_THROUGH_THE_POWER_OF_SOLINA(3234),
	// Message: He plotted a conspiracy to fulfill his ambition in the mists of history.
	HE_PLOTTED_A_CONSPIRACY_TO_FULFILL_HIS_AMBITION_IN_THE_MISTS_OF_HISTORY(3235),
	// Message: The vain spirit babbles without cause. Kukukuku...
	THE_VAIN_SPIRIT_BABBLES_WITHOUT_CAUSE(3236),
	// Message: I'll soon make you disappear like them.
	ILL_SOON_MAKE_YOU_DISAPPEAR_LIKE_THEM(3237),
	// Message: Are you Judith van Etina by any chance? I don't believe it...
	ARE_YOU_JUDITH_VAN_ETINA_BY_ANY_CHANCE_I_DONT_BELIEVE_IT(3238),
	// Message: You would be someone who lived hundreds of years ago...
	YOU_WOULD_BE_SOMEONE_WHO_LIVED_HUNDREDS_OF_YEARS_AGO(3239),
	// Message: That's right. Judith van Etina is just a spiteful soul died a long time ago.
	THATS_RIGHT(3240),
	// Message: I am the successor of Embryos, Etis van Etina!
	I_AM_THE_SUCCESSOR_OF_EMBRYOS_ETIS_VAN_ETINA(3241),
	// Message: Remember, woman of the Revelation!!
	REMEMBER_WOMAN_OF_THE_REVELATION(3242),
	// Message: It's too dangerous. Flee!
	ITS_TOO_DANGEROUS(3243),
	// Message: Meteo!!!
	METEO(3244),
	// Message: Kyaahh...!!!!
	KYAAHH(3245),
	// Message: Kuuuuuu...
	KUUUUUU(3246),
	// Message: Kuh... pretty impressive. Could this be a vessel for the gods...?
	KUH(3247),
	// Message: Kukuku... Yes... You're doing well...
	KUKUKU(3248),
	// Message: I don't know what you are talking about...
	I_DONT_KNOW_WHAT_YOU_ARE_TALKING_ABOUT(3249),
	// Message: I will not allow it to happen at your will, Etis van Etina.
	I_WILL_NOT_ALLOW_IT_TO_HAPPEN_AT_YOUR_WILL_ETIS_VAN_ETINA(3250),
	// Message: Kukuku... Well, you babble as much as you want...
	KUKUKU_(3251),
	// Message: Your babbling time now will become precious to you later.
	YOUR_BABBLING_TIME_NOW_WILL_BECOME_PRECIOUS_TO_YOU_LATER(3252),
	// Message: The era of Embryo and myself will soon arrive! Ku ha ha ha ha...
	THE_ERA_OF_EMBRYO_AND_MYSELF_WILL_SOON_ARRIVE_KU_HA_HA_HA_HA(3253),
	// Message: An error has occurred at the arena, and all matches will handled at no cost.
	AN_ERROR_HAS_OCCURRED_AT_THE_ARENA_AND_ALL_MATCHES_WILL_HANDLED_AT_NO_COST(3254),
	// Message: Due to the effect of the Arcane Shield, MP rather than HP received $s1's damage.
	DUE_TO_THE_EFFECT_OF_THE_ARCANE_SHIELD_MP_RATHER_THAN_HP_RECEIVED_S1S_DAMAGE(3255),
	// Message: MP became 0 and the Arcane Shield is disappearing.
	MP_BECAME_0_AND_THE_ARCANE_SHIELD_IS_DISAPPEARING(3256),
	// Message: Kukukuku...
	KUKUKUKU(3257),
	// Message: Ha ah ah ah ah!!
	HA_AH_AH_AH_AH(3258),
	// Message: You have acquired $s1 EXP (Bonus: $s2) and $s3 SP (Bonus: $s4).
	YOU_HAVE_ACQUIRED_S1_EXP_BONUS_S2_AND_S3_SP_BONUS_S4(3259),
	// Message: You cannot use the skill because the servitor has not been summoned.
	YOU_CANNOT_USE_THE_SKILL_BECAUSE_THE_SERVITOR_HAS_NOT_BEEN_SUMMONED(3260),
	// Message: You have $s1 match(es) remaining that you can participate in this week ($s2 1 vs 1 Class matches, $s3 1 vs 1 matches, & $s4 3 vs 3 Team matches).
	YOU_HAVE_S1_MATCHES_REMAINING_THAT_YOU_CAN_PARTICIPATE_IN_THIS_WEEK_S2_1_VS_1_CLASS_MATCHES_S3_1_VS_1_MATCHES__S4_3_VS_3_TEAM_MATCHES(3261),
	// Message: You can proceed only when the inventory weight is below 80 percent and the quantity is below 90 percent.
	YOU_CAN_PROCEED_ONLY_WHEN_THE_INVENTORY_WEIGHT_IS_BELOW_80_PERCENT_AND_THE_QUANTITY_IS_BELOW_90_PERCENT(3262),
	// Message: There are $s2 seconds remaining for $s1's re-use time. It is reset every day at 6:30 AM.
	THERE_ARE_S2_SECONDS_REMAINING_FOR_S1S_REUSE_TIME(3263),
	// Message: There are $s2 minutes $s3 seconds remaining for $s1's re-use time. It is reset every day at 6:30 AM.
	THERE_ARE_S2_MINUTES_S3_SECONDS_REMAINING_FOR_S1S_REUSE_TIME(3264),
	// Message: There are $s2 hours $s3 minutes $s4 seconds remaining for $s1's re-use time. It is reset every day at 6:30 AM.
	THERE_ARE_S2_HOURS_S3_MINUTES_S4_SECONDS_REMAINING_FOR_S1S_REUSE_TIME(3265),
	// Message: The angel Nevit has blessed you from above. You are imbued with full Vitality as well as a Vitality Replenishing effect. And should you die, you will not lose Exp!
	THE_ANGEL_NEVIT_HAS_BLESSED_YOU_FROM_ABOVE(3266),
	// Message: You are starting to feel the effects of Nevit's Blessing.
	YOU_ARE_STARTING_TO_FEEL_THE_EFFECTS_OF_NEVITS_BLESSING(3267),
	// Message: You are further infused with the blessings of Nevit! Continue to battle evil wherever it may lurk.
	YOU_ARE_FURTHER_INFUSED_WITH_THE_BLESSINGS_OF_NEVIT_CONTINUE_TO_BATTLE_EVIL_WHEREVER_IT_MAY_LURK(3268),
	// Message: Nevit's Blessing shines strongly from above. You can almost see his divine aura.
	NEVITS_BLESSING_SHINES_STRONGLY_FROM_ABOVE(3269),
	// Message: $s1 seconds remaining
	S1_SECONDS_REMAINING_(3270),
	// Message: Current Progress: $s1
	CURRENT_PROGRESS_S1(3271),
	// Message: Angel Nevit's Blessing
	ANGEL_NEVITS_BLESSING(3274),
	// Message: Nevit's Blessing has ended. Continue your journey and you will surely meet his favor again sometime soon.
	NEVITS_BLESSING_HAS_ENDED(3275),
	// Message: Nevit's Blessing: $s1
	NEVITS_BLESSING_S1(3277),
	// Message: Free acounts below level 20 cannot shout or engage in buy/sell chatting.
	FREE_ACOUNTS_BELOW_LEVEL_20_CANNOT_SHOUT_OR_ENGAGE_IN_BUYSELL_CHATTING(3282),
	// Message: This account has already received a gil2ft. The gift can only be given once per account.
	THIS_ACCOUNT_HAS_ALREADY_RECEIVED_A_GIFT(3289),
	// Message: This password is incorrect. If you enter the wrong password 5 times in a row, you will be blocked from logging on for 8 hours.\n(Number of attempts: $s1 time(s))
	THIS_PASSWORD_IS_INCORRECT(3315),
	// Message: If you enter the wrong password 5 times in a row, you will be blocked from logging on for 8 hours. The block can be removed after confirming your identity on the homepage.
	IF_YOU_ENTER_THE_WRONG_PASSWORD_5_TIMES_IN_A_ROW_YOU_WILL_BE_BLOCKED_FROM_LOGGING_ON_FOR_8_HOURS(3316),
	// Message: The character's password can only be entered via the mouse.
	THE_CHARACTERS_PASSWORD_CAN_ONLY_BE_ENTERED_VIA_THE_MOUSE(3317),
	// Message: The two entered passwords do not match.\nPlease try again.
	THE_TWO_ENTERED_PASSWORDS_DO_NOT_MATCH(3318),
	// Message: You cannot use a password that only contains one number. Please enter again.
	YOU_CANNOT_USE_A_PASSWORD_THAT_ONLY_CONTAINS_ONE_NUMBER(3319),
	// Message: You cannot use a password that has the same numbers as your account password. Please enter again.
	YOU_CANNOT_USE_A_PASSWORD_THAT_HAS_THE_SAME_NUMBERS_AS_YOUR_ACCOUNT_PASSWORD(3320),
	// Message: You cannot use a password that contains repeated pattern numbers. Please enter again.
	YOU_CANNOT_USE_A_PASSWORD_THAT_CONTAINS_REPEATED_PATTERN_NUMBERS(3321),
	// Message: You have successfully registered the character's password.
	YOU_HAVE_SUCCESSFULLY_REGISTERED_THE_CHARACTERS_PASSWORD(3322),
	// Message: After safely logging in, enter the character's password. The character's password must be entered when a character enters or is deleted. (This is registered separately from the account password.)
	AFTER_SAFELY_LOGGING_IN_ENTER_THE_CHARACTERS_PASSWORD(3323),
	// Message: Note: The number sequence randomly changes each time you log in.
	NOTE_THE_NUMBER_SEQUENCE_RANDOMLY_CHANGES_EACH_TIME_YOU_LOG_IN(3324),
	// Message: After entering the current password, please enter the new password to be used. (This is registered separately from the account password.)
	AFTER_ENTERING_THE_CURRENT_PASSWORD_PLEASE_ENTER_THE_NEW_PASSWORD_TO_BE_USED(3325),
	// Message: The offer can be withdrawn within $s1 day(s) and $s2 hour(s).
	THE_OFFER_CAN_BE_WITHDRAWN_WITHIN_S1_DAYS_AND_S2_HOURS(3326),
	// Message: The offer can be withdrawn within $s1 day(s).
	THE_OFFER_CAN_BE_WITHDRAWN_WITHIN_S1_DAYS(3327),
	// Message: The offer can be withdrawn within $s1 hour(s).
	THE_OFFER_CAN_BE_WITHDRAWN_WITHIN_S1_HOURS(3328),
	// Message: The offer cannot be withdrawn.
	THE_OFFER_CANNOT_BE_WITHDRAWN(3329),
	// Message: Sale Item (can retract offer within $s1 minute(s))
	SALE_ITEM_CAN_RETRACT_OFFER_WITHIN_S1_MINUTES(3330),
	// Message: You cannot use a password that contains continuous numbers. Please enter again.
	YOU_CANNOT_USE_A_PASSWORD_THAT_CONTAINS_CONTINUOUS_NUMBERS(3375),
	// Message: You have entered the wrong password 5 times in a row. This account has been blocked for 8 hours. The time remaining until the block ends is $s1 hour(s). The block can be removed after confirming your identity on the homepage.
	YOU_HAVE_ENTERED_THE_WRONG_PASSWORD_5_TIMES_IN_A_ROW(3376),
	// Message: There was an error in the request.
	THERE_WAS_AN_ERROR_IN_THE_REQUEST(3377),
	// Message: There are currently too many users inquiring about the product inventory. Please try again later.
	THERE_ARE_CURRENTLY_TOO_MANY_USERS_INQUIRING_ABOUT_THE_PRODUCT_INVENTORY(3378),
	// Message: The previous request has not been completed yet. Please wait a little.
	THE_PREVIOUS_REQUEST_HAS_NOT_BEEN_COMPLETED_YET(3379),
	// Message: The product inventory inquiry cannot be completed. Please try again later.
	THE_PRODUCT_INVENTORY_INQUIRY_CANNOT_BE_COMPLETED(3380),
	// Message: The offer on the product has already been retracted.
	THE_OFFER_ON_THE_PRODUCT_HAS_ALREADY_BEEN_RETRACTED(3381),
	// Message: The product has already been received.
	THE_PRODUCT_HAS_ALREADY_BEEN_RECEIVED(3382),
	// Message: The selected product cannot be received on this server.
	THE_SELECTED_PRODUCT_CANNOT_BE_RECEIVED_ON_THIS_SERVER(3383),
	// Message: The selected product cannot be received through this character.
	THE_SELECTED_PRODUCT_CANNOT_BE_RECEIVED_THROUGH_THIS_CHARACTER(3384),
	// Message: Due to system error, the product inventory cannot be used. Please try again later.
	DUE_TO_SYSTEM_ERROR_THE_PRODUCT_INVENTORY_CANNOT_BE_USED(3385),
	// Message: The product cannot be received because the game inventory weight/quantity limit has been exceeded. It can be received only when the inventory's weight and slot count are at less than 80% capacity.
	THE_PRODUCT_CANNOT_BE_RECEIVED_BECAUSE_THE_GAME_INVENTORY_WEIGHTQUANTITY_LIMIT_HAS_BEEN_EXCEEDED(3386),
	// Message: If you receive the selected product item in your own character's inventory, it will be deemed as the same item being used, and the offer retraction will be limited.\n\nDo you still wish to receive the selected item?
	IF_YOU_RECEIVE_THE_SELECTED_PRODUCT_ITEM_IN_YOUR_OWN_CHARACTERS_INVENTORY_IT_WILL_BE_DEEMED_AS_THE_SAME_ITEM_BEING_USED_AND_THE_OFFER_RETRACTION_WILL_BE_LIMITED(3387),
	// Message: $s1Minute
	S1MINUTE(3390),
	// Message: There is an error certifying the character's password. ($s1)
	THERE_IS_AN_ERROR_CERTIFYING_THE_CHARACTERS_PASSWORD(3391),
	// Message: There are currently too many users so the product cannot be received. Please try again later.
	THERE_ARE_CURRENTLY_TOO_MANY_USERS_SO_THE_PRODUCT_CANNOT_BE_RECEIVED(3410),
	// Message: You cannot connect to the current product management server. Please try again later.
	YOU_CANNOT_CONNECT_TO_THE_CURRENT_PRODUCT_MANAGEMENT_SERVER(3411),
	// Message: The product was successfully received. Please check your game inventory.
	THE_PRODUCT_WAS_SUCCESSFULLY_RECEIVED(3412),
	// Message: The product inventory cannot be used during trading, private shop, and workshop setup.
	THE_PRODUCT_INVENTORY_CANNOT_BE_USED_DURING_TRADING_PRIVATE_SHOP_AND_WORKSHOP_SETUP(3413),
	// Message: The password registration does not conform to the policy.
	THE_PASSWORD_REGISTRATION_DOES_NOT_CONFORM_TO_THE_POLICY(3416),
	// Message: The product to be received does not exist in the current product inventory.
	THE_PRODUCT_TO_BE_RECEIVED_DOES_NOT_EXIST_IN_THE_CURRENT_PRODUCT_INVENTORY(3417),
	// Message: The item has been successfully purchased.
	THE_ITEM_HAS_BEEN_SUCCESSFULLY_PURCHASED(6001),
	// Message: The item has failed to be purchased.
	THE_ITEM_HAS_FAILED_TO_BE_PURCHASED(6002),
	// Message: The item you selected cannot be purchased. Unfortunately, the sale period ended.
	THE_ITEM_YOU_SELECTED_CANNOT_BE_PURCHASED(6003),
	// Message: Enchant failed. The enchant skill for the corresponding item will be exactly retained.
	ENCHANT_FAILED(6004),
	// Message: Game points are not enough.
	GAME_POINTS_ARE_NOT_ENOUGH(6005),
	// Message: The item cannot be received because the inventory weight/quantity limit has been exceeded.
	THE_ITEM_CANNOT_BE_RECEIVED_BECAUSE_THE_INVENTORY_WEIGHTQUANTITY_LIMIT_HAS_BEEN_EXCEEDED(6006),
	// Message: Product Purchase Error - The user state is not right.
	PRODUCT_PURCHASE_ERROR__THE_USER_STATE_IS_NOT_RIGHT(6007),
	// Message: Product Purchase Error - The product is not right.
	PRODUCT_PURCHASE_ERROR__THE_PRODUCT_IS_NOT_RIGHT(6008),
	// Message: Product Purchase Error - The item within the product is not right.
	PRODUCT_PURCHASE_ERROR__THE_ITEM_WITHIN_THE_PRODUCT_IS_NOT_RIGHT(6009),
	// Message: The master account of your account has been restricted.
	THE_MASTER_ACCOUNT_OF_YOUR_ACCOUNT_HAS_BEEN_RESTRICTED(6010),
	// Message: You acquired $s1 Exp. and $s2 SP. (As a reward you receive $s3% more Exp.)
	YOU_ACQUIRED_S1_EXP(6011),
	// Message: a blessing that increases Exp. by $1 $2
	A_BLESSING_THAT_INCREASES_EXP(6012),
	// Message: It is not a blessing period. When you reach today's target, you can receive $s1%.
	IT_IS_NOT_A_BLESSING_PERIOD(6013),
	// Message: It is Eva's blessing period. $s1% will be effective until $s2.
	IT_IS_EVAS_BLESSING_PERIOD(6014),
	// Message: It is Eva's blessing period. Until $s1, Jack Sage can gift you with $s2%.
	IT_IS_EVAS_BLESSING_PERIOD_(6015),
	// Message: Progress: Event stage $s1.
	PROGRESS_EVENT_STAGE_S1(6016),
	// Message: Eva's Blessing Stage $s1 has begun.
	EVAS_BLESSING_STAGE_S1_HAS_BEGUN(6017),
	// Message: Eva's Blessing Stage $s1 has ended.
	EVAS_BLESSING_STAGE_S1_HAS_ENDED(6018),
	// Message: You cannot buy the item on this day of the week.
	YOU_CANNOT_BUY_THE_ITEM_ON_THIS_DAY_OF_THE_WEEK(6019),
	// Message: You cannot buy the item at this hour.
	YOU_CANNOT_BUY_THE_ITEM_AT_THIS_HOUR(6020),
	// Message: %s1 reached %s2 consecutive wins in Jack Game.
	S1_REACHED_S2_CONSECUTIVE_WINS_IN_JACK_GAME(6021),
	// Message: $s1 received $s4 $s3 as reward for $s2 consecutive wins.
	S1_RECEIVED_S4_S3_AS_REWARD_FOR_S2_CONSECUTIVE_WINS(6022),
	// Message: World: $s1 consecutive wins ($s2 ppl.)
	WORLD_S1_CONSECUTIVE_WINS_S2_PPL(6023),
	// Message: My Record: $s1 consecutive wins
	MY_RECORD_S1_CONSECUTIVE_WINS(6024),
	// Message: World: Under 4 consecutive wins
	WORLD_UNDER_4_CONSECUTIVE_WINS(6025),
	// Message: My Record: Under 4 consecutive wins
	MY_RECORD_UNDER_4_CONSECUTIVE_WINS(6026),
	// Message: It's Halloween Event period.
	ITS_HALLOWEEN_EVENT_PERIOD(6027),
	// Message: No record over 10 consecutive wins.
	NO_RECORD_OVER_10_CONSECUTIVE_WINS(6028),
	// Message: Please help raise reindeer for Santa's Christmas delivery!
	PLEASE_HELP_RAISE_REINDEER_FOR_SANTAS_CHRISTMAS_DELIVERY(6029),
	// Message: Santa has started delivering the Christmas gifts to Aden!
	SANTA_HAS_STARTED_DELIVERING_THE_CHRISTMAS_GIFTS_TO_ADEN(6030),
	// Message: Santa has completed the deliveries! See you in 1 hour!
	SANTA_HAS_COMPLETED_THE_DELIVERIES_SEE_YOU_IN_1_HOUR(6031),
	// Message: Santa is out delivering the gifts. Merry Christmas!
	SANTA_IS_OUT_DELIVERING_THE_GIFTS(6032),
	// Message: Only the top $s1 appear in the ranking, and only the top $s2 are recorded in My Best Ranking.
	ONLY_THE_TOP_S1_APPEAR_IN_THE_RANKING_AND_ONLY_THE_TOP_S2_ARE_RECORDED_IN_MY_BEST_RANKING(6033),
	// Message: $s1 have/has been initialized.
	S1_HAVEHAS_BEEN_INITIALIZED(6034),
	// Message: When there are many players with the same score, they appear in the order in which they were achieved.
	WHEN_THERE_ARE_MANY_PLAYERS_WITH_THE_SAME_SCORE_THEY_APPEAR_IN_THE_ORDER_IN_WHICH_THEY_WERE_ACHIEVED(6035),
	// Message: Below $s1 points
	BELOW_S1_POINTS(6036),
	// Message: The Lovers' Jubilee has begun!
	THE_LOVERS_JUBILEE_HAS_BEGUN(6037),
	// Message: You can use Evangelist Mark. (/Evangelist on/off is used to toggle)
	YOU_CAN_USE_EVANGELIST_MARK(6038),
	// Message: You have completed the initial level. Congratulations~!
	YOU_HAVE_COMPLETED_THE_INITIAL_LEVEL(6039),
	// Message: Please type "on/off" after the command.
	PLEASE_TYPE_ONOFF_AFTER_THE_COMMAND(6040),
	// Message: This is the April Fools' Day event period.
	THIS_IS_THE_APRIL_FOOLS_DAY_EVENT_PERIOD(6041),
	// Message: The skill was canceled due to insufficient energy.
	THE_SKILL_WAS_CANCELED_DUE_TO_INSUFFICIENT_ENERGY(6042),
	// Message: You cannot replenish energy because you do not meet the requirements.
	YOU_CANNOT_REPLENISH_ENERGY_BECAUSE_YOU_DO_NOT_MEET_THE_REQUIREMENTS(6043),
	// Message: Energy was replenished by $s1.
	ENERGY_WAS_REPLENISHED_BY_S1(6044),
	// Message: $c1 acquired the Special Gift of April Fool's Day.
	C1_ACQUIRED_THE_SPECIAL_GIFT_OF_APRIL_FOOLS_DAY(6045),
	// Message: The premium item for this account was provided. If the premium account is terminated, this item will be deleted.
	THE_PREMIUM_ITEM_FOR_THIS_ACCOUNT_WAS_PROVIDED(6046),
	// Message: The premium item cannot be received because the inventory weight/quantity limit has been exceeded.
	THE_PREMIUM_ITEM_CANNOT_BE_RECEIVED_BECAUSE_THE_INVENTORY_WEIGHTQUANTITY_LIMIT_HAS_BEEN_EXCEEDED(6047),
	// Message: The remium account has been terminated. The provided premium item was deleted.
	THE_REMIUM_ACCOUNT_HAS_BEEN_TERMINATED(6048),
	// Message: $s1% is on the your Ignore List. In order to whisper, they must be removed from your Ignore List. \n\nDo you wish to remove $s1% from your Ignore List?
	S1_IS_ON_THE_YOUR_IGNORE_LIST(6049),
	// Message: If you have a Maestro's Key, you can use it to open the treasure chest.
	IF_YOU_HAVE_A_MAESTROS_KEY_YOU_CAN_USE_IT_TO_OPEN_THE_TREASURE_CHEST(6050),
	// Message: You cannot bookmark this location because you do not have a My Teleport Flag.
	YOU_CANNOT_BOOKMARK_THIS_LOCATION_BECAUSE_YOU_DO_NOT_HAVE_A_MY_TELEPORT_FLAG(6501),
	// Message: My Teleport Flag: $s1
	MY_TELEPORT_FLAG_S1(6502),
	// Message: The evil Thomas D. Turkey has appeared. Please save Santa.
	THE_EVIL_THOMAS_D(6503),
	// Message: You won the battle against Thomas D. Turkey. Santa has been rescued.
	YOU_WON_THE_BATTLE_AGAINST_THOMAS_D(6504),
	// Message: You did not rescue Santa, and Thomas D. Turkey has disappeared.
	YOU_DID_NOT_RESCUE_SANTA_AND_THOMAS_D(6505),
	// Message: Although you can't be certain, the air seems laden with the scent of freshly baked bread.
	ALTHOUGH_YOU_CANT_BE_CERTAIN_THE_AIR_SEEMS_LADEN_WITH_THE_SCENT_OF_FRESHLY_BAKED_BREAD(6506),
	// Message: You feel refreshed. Everything appears clear.
	YOU_FEEL_REFRESHED(6507),
