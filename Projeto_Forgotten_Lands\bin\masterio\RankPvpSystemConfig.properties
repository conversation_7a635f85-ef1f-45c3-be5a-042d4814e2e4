#------------------------------------------------------------------------------------------#
#-----------------------------< Config v3.8.7 for l2j-Frozen >-----------------------------#
#------------------------------------------------------------------------------------------#

#===================================== Rank PvP System ====================================#
# About Legal Kill: 
# When Killer kill Victim and Legal Kill Protection is not activated, then legal kill will rise.

#===================================== Rank PvP System ====================================#
# About Legal Kill: 
# When Killer kill Victim and Legal Kill Protection is not activated, then legal kill will rise.

#------------------------------------------------------------------------------------------#
# 			  								PvP	 	 		   						 	   #
#------------------------------------------------------------------------------------------#
# Enable Rank PvP System
RankPvpSystemEnabled = True

# Enable legal counter in Alt+T window in game. It's mean all kills will be counted by RPS rules.
LegalCounterAltTEnabled = True

# Minimum player level for rise legal kills.
LegalKillMinLvl = 1

# If enabled, kill PK player (player with red nick) will rise kills_legal and kills_today_legal in database. Default true. 
LegalKillForPkKillerEnabled = True

# If enabled, kill unflaged player will rise kills_legal and kills_today_legal in database (otherwise PK mode). Default false. 
LegalKillForInnocentKillEnabled = False

# Set time interval between kills the same victim (in minutes).
# Option is used for Legal Kills & RPC & Rank Points.
# Default: 60
ProtectionTimeReset = 1

# If the same player kill victim many times, then after X times the Legal Kills will not increase (0 - Disabled).
# If LegalKillProtection = 3, then it not rise Legal Kills for kill 4 times the same player.
# INFO: Option used for RPC and Rank Points.
LegalKillProtection = 0

# If the same player kill victim many times, then after X times the Legal Kills will not increase (0 - Disabled).
# X is reseted every day.
# IMPORTANT: LegalKillProtection MUST BE = 0.
# INFO: Option used for RPC and Rank Points.
DailyLegalKillProtection = 0

# If True, then GM characters will be ignored by RPS.
# Option is used when coloring nick and titles too.
# Info: If GM characters are saved in RPS, you should remove them manually from rank_pvp_system tables.
# SQL example: 
# 	DELETE FROM rank_pvp_system WHERE killer_id IN(SELECT charId FROM characters WHERE accesslevel <> USER_ACCESSLEVEL); 
# *USER_ACCESSLEVEL is a access level for users.
# Default: True
GMIgnoreEnabled = False

#------------------------------------------------------------------------------------------#
# 			  	  					RPC Reward & PvP Reward		   						   #
#------------------------------------------------------------------------------------------#
# It gives reward(RPC) for any legal PvP.
# INFO: ProtectionTimeReset is used.
# Enable PvP Reward System (RPC Reward & Reward)
# REQUIRE: RankPvpSystemEnabled = True

# RPC Reward allows earn and exchange RPC points for selected rewards, this RPC are awarded for each PvP.
RpcRewardEnabled = True
# INFO: RPC - is a special currency, player can exchange it for items.
# RPC Reward amount, awarded for each PvP.
RpcRewardAmmount = 1
# Minimum player level to obtain reward.
RpcRewardMinLvl = 1

# PvP Reward awarded for each PvP.
PvpRewardEnabled = True
# Reward item id.
PvpRewardId = 57
# PvP Reward amount, awarded for each PvP.
PvpRewardAmount = 10000
# Minimum player level to obtain reward.
PvpRewardMinLvl = 1

# If enabled, gives reward for kill PK player (player with red nick).
RewardForPkKillerEnabled = True
# If enabled, kill unflaged player will be rewarded (otherwise PK mode). Default false. 
RewardForInnocentKillEnabled = False

# If enabled, gives rewards for kill player by rank. Reward list for each rank defined in database table: rank_pvp_system_rank_reward, is_pvp = true.
# REQUIRE: RanksEnabled = True
RankPvpRewardEnabled = True
# Minimum player level to obtain reward.
RankPvpRewardMinLvl = 1

# If enabled, gives rewards for reach new Rank by player. Reward list for each rank defined in database table: rank_pvp_system_rank_reward, is_level = true.
# REQUIRE: RanksEnabled = True
RankLevelRewardEnabled = True

# If enabled, RP, RPC, PvP Reward and Rank PvP Reward will check Legal Kill conditions.
RewardLegalKillEnabled = True

#------------------------------------------------------------------------------------------#
# 				 					  	 	Ranks 							 		 	   #		
#------------------------------------------------------------------------------------------#
# INFO: ProtectionTimeReset is used.
# INFO: RankNames, 
# 		RankMinPoints, 
# 		RankPointsForKill, 
# 		RankRewardIds, 
# 		RankRewardAmounts 
# 		must have same size and cannot be empty.
# REQUIRE: RankPvpSystemEnabled = true
RanksEnabled = True

# Minimum player level for obtain Rank Points.
RankPointsMinLvl = 1

# Cut off points, earned for kill high rank victims ['rank points for kill']. (should reduce farm of RP on high level victims)
# Example: Killer RP reward is 20, victim RP reward is 50, if killer kill the victim he will obtain 20 points, not 50 points.
RankPointsCutEnabled = True

# Rank names (first element is the higher rank):
# Format: name1,name2,name3
# Example: RankNames = Five Star General,General [Grade 3],General [Grade 2],General, ... ,Apprentice,Recruit
RankNames = Five Star General,General [Grade 3],General [Grade 2],General,Brigadier General,Brigadier [Grade 3],Brigadier [Grade2],Brigadier,Force Colonel,Colonel [Grade 3],Colonel [Grade 2],Colonel,Strike Commander,Commander [Grade 3],Commander [Grade 2],Commander,Field Major,Major [Grade 3],Major [Grade 2],Major,Staff Captain,Captain [Grade 3],Captain [Grade 2],Captain,First Lieutenant,Lieutenant [Grade 3],Lieutenant [Grade 2],Lieutenant,Master Sergeant,Gunnery Sergeant [Grade 3],Gunnery Sergeant [Grade 2],Gunnery Sergeant,Sergeant [Grade 3],Sergeant [Grade 2],Sergeant,Corporal [Grade 2],Corporal,Private [Grade 2],Private,Apprentice [Grade 2],Apprentice,Recruit

# INFO: Be careful with points. MySql database allow BIGINT(18) total rank points.

# Rank require points (first element is points count for obtain 1st rank title, in example: Five Star General):
# Format: count1,count2,count3
# count1 > count2 > count3 > ... >= 0, minimum points for first (lowest) rank must be 0!
# Foramt: RankMinPoints = 10000,1000,400,0
# IMPORTANT: property elements count must be same like in RankNames.
# Example: if player have current points BETWEEN 2,000,000 AND 1,000,000 it gives rank: General [Grade 3].
RankMinPoints = 2000000,1000000,750000,600000,400000,250000,190000,150000,120000,100000,82000,65000,50000,38000,28000,20000,14000,10000,7000,5000,3000,2000,1500,1000,880,760,680,580,500,450,400,360,320,280,240,190,150,120,70,30,10,0

# Example: For Five Star General kills reward is 50 points.
# Format: count1,count2,count3
# All values MUST be greater than 0.
# IMPORTANT: properties count must be same like in RankNames.
RankPointsForKill = 50,48,46,44,42,40,38,36,33,32,31,30,29,28,27,26,25,24,23,22,21,20,19,18,17,16,15,14,13,12,11,10,9,8,7,6,5,4,3,2,1,1

# If enabled RPC points will be awarded for kill Victim with specified rank.
RankRpcEnabled = True
# INFO: RPC - is a special currency, player can exchange it for items.
# RPC amounts for kill player with defined ranks.
# IMPORTANT: properties count must be same like in RankNames.
RankRpcAmount = 42,40,39,38,37,36,35,34,33,32,31,30,29,28,27,26,25,24,23,22,21,20,19,18,17,16,15,14,13,12,11,10,9,8,7,6,5,4,3,2,1,1

# Enable alternative points counting for players.
# If enabled then RankPointsForKill is ignored.
# Example: RankPointsDownAmounts = 100,50,10,1,0
# For first kill awards 100, 2nd kill give 50, ... , 0.
# It can be manipuleted by LegalKillProtection and DailyLegalKillProtection.
# IMPORTANT: Protection X value cannot be less than size of RankPointsDownAmounts. Example: min value for example data is 5.
RankPointsDownCountEnabled = False
RankPointsDownAmounts = 100,50,10,1,0

# Shout current Points info for killer & victim.
RankShoutInfoOnKillEnabled = True

# Shout Bonus Points informations for killer.
# Require: RankShoutInfoOnKillEnabled = True
RankShoutBonusInfoOnKillEnabled = True

# If enabled shows informations about Total Kills on Victim, otherwise show informations about Legal Kills on Victim.
TotalKillsInShoutEnabled = True

# If enabled, gives Rank Points for kill PK player (player with red nick).
#RankRewardForPkKillerEnabled = True

# If enabled, Rank Points for kill unflaged player will be awarded (PK mode). Default false. 
#RankRewardForInnocentKillEnabled = False

# Nick and Title colors for each rank.
# INFO: Do not put RED, PURPLE, or LIGHT RED Nick Colors. This colors are used in game!
# Colors must have HTML format (6 letters (hex)).
# Format: ABCDEF,0103FF, ... ,ABCDEF
# Minimum: 000000, Maximum: FFFFFF.
# IMPORTANT: properties count must be same like in RankNames.
# First color on left is for the highest rank, [5 Star General].
NickColorEnabled = False
NickColors = FF8000,00FF00,0000FF,FF0000,00FF00,FF0000,00FF00,0000FF,FF0000,00FF00,FF0000,00FF00,0000FF,FF0000,00FF00,FF0000,00FF00,0000FF,FF0000,00FF00,FF0000,00FF00,0000FF,FF0000,00FF00,FF0000,00FF00,0000FF,FF0000,00FF00,FF0000,00FF00,0000FF,FF0000,00FF00,FF0000,00FF00,0000FF,FF0000,00FF00,FF0000,00FF00

TitleColorEnabled = False
TitleColors = FF8000,00FF00,0000FF,FF0000,00FF00,FF0000,00FF00,0000FF,FF0000,00FF00,FF0000,00FF00,0000FF,FF0000,00FF00,FF0000,00FF00,0000FF,FF0000,00FF00,FF0000,00FF00,0000FF,FF0000,00FF00,FF0000,00FF00,0000FF,FF0000,00FF00,FF0000,00FF00,0000FF,FF0000,00FF00,FF0000,00FF00,0000FF,FF0000,00FF00,FF0000,00FF00

#------------------------------------------------------------------------------------------#
# 								 	  	 War Kills 						     			   #
#------------------------------------------------------------------------------------------#
# Enable war kills counter
WarKillsEnabled = True

# Ratio for Rank Points when killed a player from War Clan. 
# Add to standard rank points for kill difference between ((points * ratio) - points).
# 2.0 mean 2 times more points for kill a player from war. 
# Ratio must be > 1, if <= 1 points will be not multiplied.
WarRankPointsRatio = 1.2

#------------------------------------------------------------------------------------------#
# 								 		 Combo Kills						     	 	   #
#------------------------------------------------------------------------------------------#
# Combo system allow get combo for killing the victims. 
# Combo will be reseted if character will die, relog, join to event or olympiad.
# Combo system disabled on events and olympiad (option inherited from Rank PvP System).

# Enable combo kill system.
ComboKillEnabled = True

# Shout messages for players, if killer kill more victims.
# Format: combo_kills,message; ... ;combo_kills,message
# List must be ORDERED from lowest to highest by combo_kills.
#
# Message can contain special tags: 
#	 %killer% - it will be replaced by killer name.
#	 %victim% - it will be replaced by victim name.
#	 %combo_level% - it will be replaced by combo level.
#
# Local - shout message to visible players by killer.
# Global - shout message to all players online.
# Info: one of these lists can be empty, and combo_kills can be random numbers, but ordered (example: 1,msg;2,msg;3,msg;6,msg;7,msg;8,msg;10,msg;55,msg;100,msg).
# It can start from 3,msg;6,msg;7, ... then killers with combo level 1 and 2 will be not shouted.
# Checking priority: if combo_kills will be founded on local list, then global list is not checked.
ComboKillLocalAreaMessages = 2,%killer% is a good killer!;3,%killer% is a perfect killer!;4,%killer% have no mercy!;5,%killer% is in killing spree!;6,%killer% assassinate every target!;7,%killer% is killing master!;8,%killer% is a Uber killer!;9,%killer% own the battlefield!
ComboKillGlobalAreaMessages = 10,%killer% is Unstopable!!!

# Alternate combo message method, is not require for each combo level defining the message.
# Just one message for any combo level.
# If enabled shows combo size, example: Player have 5 Combo kills! or Player have 99 Combo kills!
#
# Message can contain special tags: 
#	 %killer% - it will be replaced by killer name.
#	 %victim% - it will be replaced by victim name.
#	 %combo_level% - it will be replaced by combo level.
#
# If ComboKillAltGlobalMessageMinLvl = 10 then if player get combo level >= 10 it will be shout on global chat, otherwise on local chat. 
# If ComboKillAltGlobalMessageMinLvl = 0 it shouts all messages on local chat.
ComboKillAltMessagesEnabled = False
ComboKillAltMessage = %killer% have %combo_level% Combo kills!
ComboKillAltGlobalMessageMinLvl = 10

# If enabled shout messeage about defeated combo killer, if victim combo is greater than selected level.
ComboKillDefeatMessageEnabled = True
# Minimum combo level for show the defeat message. It not mess chat window with player's low combo level.
# Require: ComboKillDefeatMessageEnabled = True
ComboKillDefeatMessageMinComboLvl = 3

# Message can contain special tags: 
#	 %killer% - it will be replaced by killer name.
#	 %combo_level% - it will be replaced by combo level.
#
# ComboKillDefeatMessage = %killer% is defeated! or:
ComboKillDefeatMessage = %killer% is defeated with %combo_level% combo lvl!!!

# Enable protection with Legal Kills, combo will rise if it's legal kill.
ComboKillProtectionWithLegalKillEnabled = True

# Enable protection with No Repeat for kill, combo will rise if victim is not on current combo list.
ComboKillProtectionNoRepeatEnabled = True

# If kill time between previous victim and next victim will be greater than ComboKillReseter, combo will be reseted.
# Time in seconds.
# 0 - disabled.
# Default: 300
ComboKillReseter = 300

# List contains combo kills and rank points ratio added to rank points for kill.
# Format: combo_kills,ratio; ... ;combo_kills,ratio
# List must be ordered from lowest to highest by combo_kills.
# Ratio must be greater than 1.0, otherwise will be ignored.
ComboKillRankPointsRatioEnabled = True
ComboKillRankPointsRatio = 5,1.1;6,1.2;7,1.3;8,1.4;9,1.5;10,1.6

#------------------------------------------------------------------------------------------#
# 			  .pvpinfo command, PvP Status window, Death Status, RPC options 	    	   #
#------------------------------------------------------------------------------------------#
# If enabled allow use .pvpinfo command on targeted player.
PvpInfoCommandEnabled = True

# If enabled change .pvpinfo for /pvpinfo command. (/pvpinfo is recommended)
# IMPORTANT: Is require Client modification file: LineageII/system/commandname-e.dat
# Information how to apply it in: ClientModInfo.txt
PvpInfoUserCommandEnabled = True
# ID of command, it is same id what is in commandname-e.dat.
PvpInfoUserCommandId = 1004

# If enabled shows .pvpinfo window about killer for victim.
PvpInfoCommandShowOnDeathEnabled = True

# Show additional button in PvPStatus window after death. 
# Button allow see more details about killer. HP/MP/CP, equipment (enchant) if enabled. 
# It shows killer status only for KILL MOMENT.
# Option requires PvpInfoCommandShowOnDeathEnabled and PvpInfoCommandEnabled set on True.
DeathManagerDetailsEnabled = True
# Show killer equipment and items enchant level.
DeathManagerShowItemsEnabled = True

# Enable RPC Exchange for items.
# Items list defined in database table: rank_pvp_system_rpc_reward.
# If enabled, button "RPC Exchange" appear in PvP Status Window. 
RpcExchangeEnabled = True

# Additional protection for RPC Get button.
RpcExchangeConfirmBoxEnabled = True

# If enabled shows player's level in PvP Status window (.pvpinfo)
ShowPlayerLevelInPvpInfoEnabled = True

# If enabled shows information about Total Kills and Total Kills Legal in PvP Status window (.pvpinfo)
# If disabled shows only Total Legal Kills.
TotalKillsInPvpInfoEnabled = True

# If enabled shows information about Total Kills and Total Kills Legal On Me in PvP Status window (.pvpinfo)
# If disabled shows only Total Legal Kills On Me.
TotalKillsOnMeInPvpInfoEnabled = True

#------------------------------------------------------------------------------------------#
# 					 	 PvP / Rank / Reward / DM zone option's 						   #
#------------------------------------------------------------------------------------------#
# Zone id's (for Frozen server):
# 	ZONE_PVP = 1;
# 	ZONE_PEACE = 2;
# 	ZONE_SIEGE = 4;
# 	ZONE_MOTHERTREE = 8;
# 	ZONE_CLANHALL = 16;
# 	ZONE_UNUSED = 32;
# 	ZONE_NOLANDING = 64;
# 	ZONE_WATER = 128;
# 	ZONE_JAIL = 256;
# 	ZONE_MONSTERTRACK = 512;
# 	ZONE_SWAMP = 1024;
# 	ZONE_NOSUMMONFRIEND = 2048;
# 	ZONE_OLY = 4096;
# 	ZONE_NOHQ = 8192
# 	ZONE_DANGERAREA = 16384
#
#	*CUSTOM PVP ZONE should be with id = 1 (ZONE_PVP).
# Rank PvP System Anti-Farm options (zones id):

# Allowed zones id's. 
# Format: zoneId,zoneId, ... ,zoneId,zoneId
# Info: If empty, the system works in all zones, otherwise system works in Allowed Zones only (defining restricted zones not required then).
# Example: AllowedZonesIds = 1,7,12 , then system works ONLY in zone with ID = 1 and 7 and 12.
AllowedZonesIds =

# Restricted zones id's (used if AllowedZonesIds = (empty)). 
# Format: zoneId,zoneId, ... ,zoneId,zoneId
# Info: System NOT working in Restricted Zones.
# Default: RestrictedZonesIds = 1,8,9
# Example: AllowedZonesIds = 1,8,9 , then system NOT works in zones with ID'S = 1,8,9.
RestrictedZonesIds = 2,16,256,4096

# INFO: AllowedZonesIds = 1,3,5,9 and RestrictedZonesIds = 1,8,9 
# then system will works only in 1 and 3 and 5 and 9.
# Remember, if you define AllowedZonesIds then system not checking RestrictedZonesIds.

# Death Manager is disabled in this zones.
DeathManagerRestrictedZonesIds = 2,16,256,4096

# Rank Points Bonus Ratio zones id's.
# Add to standard rank points for kill difference between ((points * ratio) - points).
# Ratio must be > 1, if <= 1 points will be not multiplied.
# Format: zoneId,ratio; ... ;zoneId,ratio
# Example: 2,1.5;13,1.2
# It will multiply rank points for Killer by 1.5 in area 2 , if Killer is inside this area.
RankPointsBonusZonesIds =

#------------------------------------------------------------------------------------------#
# 			    						 Anti-Farm 	         					     	   #
#------------------------------------------------------------------------------------------#
# INFO: Options to check farming players 
# (dualbox farming, party farming, clan farming).
# If True protection is activated.
AntiFarmClanAllyEnabled = True
AntiFarmPartyEnabled = True
AntiFarmIpEnabled = False

#------------------------------------------------------------------------------------------#
# 			  						  Community Board        				   	   		   #
#------------------------------------------------------------------------------------------#
# Enable statistics system for community TOP LIST, data like top killers, top RP gatherers will be showed.
# Default: True
TopListEnabled = True

# Set a Time Limit (in days) for system counter. Option is used for update Top Table.
# If some player not killed anybody from X days, he will be ignored on Top List.
# When ignored player kills somebody again he will returns to counting pool in next update of Top Table.
# Disabled: 0
# Default: 7
TopListIgnoreTimeLimit = 7

#------------------------------------------------------------------------------------------#
# 			    						  Database 						            	   #
#------------------------------------------------------------------------------------------#
#----------------- DO NOT CHANGE IT IF YOU DON'T KNOW WHAT ARE YOU DOING! -----------------#

# Interval between PvpTable dump into database (in minutes).
# Value must be greater than 1 or will be overrided for 1.
# Default: 5
PvpTableUpdateInterval = 5

# If enabled, then if RPC System is disabled the players RPC Points will be updated anyway (executed with PvpTableUpdate).
RpcTableForceUpdate = True

# Top Table update time.
# Option is enabled if TopListEnabled = True.
# IMPORTANT: for online 500+ can do lag for few seconds [0s - 3s] when updating.
# Example: TopTableUpdateTimes = 0:00,3:00,6:00,9:00,12:00,15:00,18:00,21:00
# Default: TopTableUpdateTimes = 3:00
TopTableUpdateTimes = 3:00

# If enabled, system will remove permanenlty not active players from PvP Table on server startup.
# Default: False
DatabaseCleanerEnabled = False
# If player not logged into game from X days, his data will be removed from PvP Table PERMANENTLY!
# Option will be useful for big online servers.
# Value must be greater than 0, otherwise DatabaseCleanerEnabled will be changed on False.
# Default: 60
DatabaseCleanerRepeatTime = 60

#------------------------------------------------------------------------------------------#
# 			    						Images & Style	 						       	   #
#------------------------------------------------------------------------------------------#
# If True, then images will be sended from server to client (increases the load on internet), (require copy the 'images' folder into '../gameserver/data' folder).
# If False, then client will retrive images from local directory (require copy the 'RPS_byMasterio.utx' into '../Lineage II/SysTextures' folder).
ServerSideImagesEnabled = False

# Additional image prefix, for reload all images on RPS by Client side.
# Used when ImagesFromServerEnabled = True.
# If you want reload all RPS images just add 1 to previous value.
# Example: If I have this value on 1, then when I run Client and load some images for RPS they will be buffered by Client side.
# If I edited images and I want reset this images on Client side I must rise the value by 1.
# Max prefix value is 500 000. (It gives 500 000 possible reloads)
ImagePrefix = 6

# Buttons style in PvP Status Window and Top List. (static button in Htm file require manual changes)
# Leave empty ButtonFore, ButtonBack for default values of IL chronicle.
ButtonFore = Button_DF_Calculator
ButtonBack = Button_DF_Calculator_Down
ButtonWidth = 65
ButtonHeight = 20
ButtonBigWidth = 100
ButtonBigHeight = 20
#==========================================================================================#