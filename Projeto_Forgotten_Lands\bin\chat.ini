#========================================
# 			  Settings chats			#
#========================================
# Using global chat
#Shout (!)
GlobalShout = False
# Trade (#)
GlobalTradeChat = True
# Range White Game
ChatRange = 1800
# Range regions to shout: 0 - only the current, 1 - current and all neighbouring
ShoutOffset = 1

TradeChats = False

# Limit chat messages along
ChatMessageLimit = 1000

# Seconds every Vote Announcement(You didn't vote today...)
AnnounceVoteDelay = 10800

# Enable logging of chat messages
LogChat = True

# Include filter mat?
ABUSEWORD_BANCHAT = False
# PARTY (3) CLAN (4) ALLIANCE (9) SHOUT (1) TELL (2) GM (5) PETITION_PLAYER (6)
# PETITION_GM (7) TRADE (8) COMMANDCHANNEL_ALL (15) COMMANDCHA NNEL_COMMANDER (16)
# HERO_VOICE (17) (PPO default = 0 (only global))
# Channels to check the mat
ABUSEWORD_BAN_CHANNEL = 0
# Ban length chat for a mate
ABUSEWORD_UNBAN_TIMER = 5
# Do not ban for a mate but simply block messages if True is ignored MAT_BANCHAT
ABUSEWORD_REPLACE = True
# Line, which is replaced by a message that contains profanity
ABUSEWORD_REPLACE_STRING = Vote for Server Guys!!
# Show the command ad no channel
BANCHAT_ANNOUNCE = False
# Declare to the world or to the current region (True = the whole world)
BANCHAT_ANNOUNCE_FOR_ALL_WORLD = False
# Show off-line moderator in ad
BANCHAT_ANNOUNCE_NICK = False

# The maximum level, after which the filter does not include limiting the use of instant messaging in terms of
ChatFilterMinLevel = 0
# Ida chats comma to check
ChatFilterChannels = 1,8
# Mode filter: 1 - redirect to the normal chat, 2 - to display messages about the unavailability of the chat feature
ChatFilterWorkType = 1
# Change the type to chat with a general trading, if the message contains commercial phrases
TradeChatsReplace = False
# The list of trade / phrases to the previous option
TradeWords = ВТТ,ВТС,ВТБ,WTB,WTT,WTS


# Required level to use Trade Chat, Should!
ShoutingInChat = 75

# Required level to use ChatType.ALL!
ChatsRequiredLevel = 81

# Required level to use Private Messages !
PMPlayersInChat = 81

# Should amount of wrote messages per chat be recorded? It is used for proxies
RecordWroteChatMsgsCount = True
