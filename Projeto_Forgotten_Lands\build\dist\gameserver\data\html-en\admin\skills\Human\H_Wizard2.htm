<title>Choose skill to add</title>
<center>
[admin_add_skill 1127 1|Servitor He<PERSON> 1 (Lv 20)]%%
[admin_add_skill 1127 2|Servitor <PERSON> 2 (Lv 20)]%%
[admin_add_skill 1127 3|Servitor <PERSON> 3 (Lv 20)]%%
[admin_add_skill 1127 4|Servitor <PERSON> 4 (Lv 25)]%%
[admin_add_skill 1127 5|<PERSON>vitor <PERSON> 5 (Lv 25)]%%
[admin_add_skill 1127 6|<PERSON>vitor <PERSON> 6 (Lv 25)]%%
[admin_add_skill 1127 7|<PERSON>vitor <PERSON> 7 (Lv 30)]%%
[admin_add_skill 1127 8|Servitor <PERSON> 8 (Lv 30)]%%
[admin_add_skill 1127 9|Servitor <PERSON> 9 (Lv 30)]%%
[admin_add_skill 1127 10|Servitor <PERSON> 10 (Lv 35)]%%
[admin_add_skill 1127 11|Servitor <PERSON> 11 (Lv 35)]%%
[admin_add_skill 1127 12|<PERSON><PERSON><PERSON> 12 (Lv 35)]%%
[admin_add_skill 1144 1|Fast Servitor 1 (Lv 35)]%%
[admin_add_skill 1147 3|Vampiric Touch 3 (Lv 20)]%%
[admin_add_skill 1147 4|Vampiric Touch 4 (Lv 20)]%%
[admin_add_skill 1147 5|Vampiric Touch 5 (Lv 25)]%%
[admin_add_skill 1147 6|Vampiric Touch 6 (Lv 25)]%%
[admin_add_skill 1151 1|Corpse Life Drain 1 (Lv 30)]%%
[admin_add_skill 1151 2|Corpse Life Drain 2 (Lv 35)]%%
[admin_add_skill 1157 1|Body To Mind 1 (Lv 25)]%%
[admin_add_skill 1160 1|Slow 1 (Lv 35)]%%
[admin_add_skill 1164 2|Curse: Weakness 2 (Lv 20)]%%
[admin_add_skill 1164 3|Curse: Weakness 3 (Lv 25)]%%
[admin_add_skill 1164 4|Curse: Weakness 4 (Lv 30)]%%
[admin_add_skill 1164 5|Curse: Weakness 5 (Lv 35)]%%
[admin_add_skill 1167 1|Poisonous Cloud 1 (Lv 25)]%%
[admin_add_skill 1167 2|Poisonous Cloud 2 (Lv 35)]%%
[admin_add_skill 1168 2|Curse:Poison 2 (Lv 20)]%%
[admin_add_skill 1168 3|Curse:Poison 3 (Lv 30)]%%
[admin_add_skill 1172 1|Aura Burn 1 (Lv 20)]%%
[admin_add_skill 1172 2|Aura Burn 2 (Lv 20)]%%
[admin_add_skill 1172 3|Aura Burn 3 (Lv 25)]%%
[admin_add_skill 1172 4|Aura Burn 4 (Lv 25)]%%
[admin_add_skill 1172 5|Aura Burn 5 (Lv 30)]%%
[admin_add_skill 1172 6|Aura Burn 6 (Lv 30)]%%
[admin_add_skill 1172 7|Aura Burn 7 (Lv 35)]%%
[admin_add_skill 1172 8|Aura Burn 8 (Lv 35)]%%
[admin_add_skill 1181 1|Flame Strike 1 (Lv 20)]%%
[admin_add_skill 1181 2|Flame Strike 2 (Lv 25)]%%
[admin_add_skill 1181 3|Flame Strike 3 (Lv 30)]%%
[admin_add_skill 1184 5|Ice Bolt 5 (Lv 20)]%%
[admin_add_skill 1184 6|Ice Bolt 6 (Lv 20)]%%
[admin_add_skill 1220 1|Blaze 1 (Lv 20)]%%
[admin_add_skill 1220 2|Blaze 2 (Lv 20)]%%
[admin_add_skill 1220 3|Blaze 3 (Lv 25)]%%
[admin_add_skill 1220 4|Blaze 4 (Lv 25)]%%
[admin_add_skill 1220 5|Blaze 5 (Lv 30)]%%
[admin_add_skill 1220 6|Blaze 6 (Lv 30)]%%
[admin_add_skill 1220 7|Blaze 7 (Lv 35)]%%
[admin_add_skill 1220 8|Blaze 8 (Lv 35)]%%
[admin_add_skill 1222 1|Curse Chaos 1 (Lv 35)]%%
[admin_add_skill 1225 1|Summon Mew the Cat 1 (Lv 20)]%%
[admin_add_skill 1225 2|Summon Mew the Cat 2 (Lv 25)]%%
[admin_add_skill 1225 3|Summon Mew the Cat 3 (Lv 30)]%%
[admin_add_skill 1225 4|Summon Mew the Cat 4 (Lv 35)]%%
[admin_add_skill 1274 1|Energy Bolt 1 (Lv 20)]%%
[admin_add_skill 1274 2|Energy Bolt 2 (Lv 25)]%%
[admin_add_skill 1274 3|Energy Bolt 3 (Lv 30)]%%
[admin_add_skill 1274 4|Energy Bolt 4 (Lv 35)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index Human/H_Wizard" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
