<html><title>Admin Teleports</title>
<body>
<center>
<table width=260>
<tr><td width=40><button value="Main" action="bypass -h admin_admin" width=40 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td>
<td width=180><center>Hellbound Island</center></td>
<td width=40><button value="Back" action="bypass -h admin_show_html teleports.htm" width=40 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td>
</tr></table>
</center>
<br>
<br>
<center>
<button action="bypass -h admin_move_to -11802 236360 -3271" value="Hellbound Entrance" width=180 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to -8391 242342 -1890" value="Hellbound Quarry" width=180 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to -22432 243491 -3068" value="Enchanted Megaliths" width=180 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to -19715 249356 -3237" value="Hidden Oasis" width=180 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to -27289 253838 -2154" value="Ancient Temple Remnants" width=180 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to -4156 255301 -3139" value="Caravan Encampment" width=180 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 298 235111 -3273" value="Battered Lands" width=180 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 4706 243939 -1922" value="Iron Castle" width=180 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 8902 251884 -2028" value="Steel Citadel Outpost" width=180 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 26606 248354 -2880" value="Anomic Foundry" width=180 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
</center>
</body></html>
