<title>Choose skill to add</title>
<center>
[admin_add_skill 1127 1|Servitor <PERSON> 1 (Lv 20)]%%
[admin_add_skill 1127 2|<PERSON>vitor <PERSON> 2 (Lv 20)]%%
[admin_add_skill 1127 3|Servitor <PERSON> 3 (Lv 20)]%%
[admin_add_skill 1127 4|Servitor <PERSON> 4 (Lv 25)]%%
[admin_add_skill 1127 5|<PERSON>vitor <PERSON> 5 (Lv 25)]%%
[admin_add_skill 1127 6|<PERSON>vitor <PERSON> 6 (Lv 25)]%%
[admin_add_skill 1127 7|<PERSON>vitor <PERSON> 7 (Lv 30)]%%
[admin_add_skill 1127 8|<PERSON>vitor <PERSON> 8 (Lv 30)]%%
[admin_add_skill 1127 9|Servitor <PERSON> 9 (Lv 30)]%%
[admin_add_skill 1127 10|Servitor <PERSON> 10 (Lv 35)]%%
[admin_add_skill 1127 11|Servitor <PERSON> 11 (Lv 35)]%%
[admin_add_skill 1127 12|<PERSON><PERSON><PERSON> 12 (Lv 35)]%%
[admin_add_skill 1126 1|Servitor Recharge 1 (Lv 25)]%%
[admin_add_skill 1126 2|Servitor Recharge 2 (Lv 25)]%%
[admin_add_skill 1126 3|Servitor Recharge 3 (Lv 30)]%%
[admin_add_skill 1126 4|Servitor Recharge 4 (Lv 30)]%%
[admin_add_skill 1126 5|Servitor Recharge 5 (Lv 35)]%%
[admin_add_skill 1126 6|Servitor Recharge 6 (Lv 35)]%%
[admin_add_skill 1069 1|Sleep 1 (Lv 25)]%%
[admin_add_skill 1069 2|Sleep 2 (Lv 25)]%%
[admin_add_skill 1069 3|Sleep 3 (Lv 25)]%%
[admin_add_skill 1069 4|Sleep 4 (Lv 30)]%%
[admin_add_skill 1069 5|Sleep 5 (Lv 30)]%%
[admin_add_skill 1069 6|Sleep 6 (Lv 30)]%%
[admin_add_skill 1069 7|Sleep 7 (Lv 35)]%%
[admin_add_skill 1069 8|Sleep 8 (Lv 35)]%%
[admin_add_skill 1069 9|Sleep 9 (Lv 35)]%%
[admin_add_skill 1160 1|Slow 1 (Lv 35)]%%
[admin_add_skill 1128 1|Summon Shadow 1 (Lv 20)]%%
[admin_add_skill 1128 2|Summon Shadow 2 (Lv 30)]%%
[admin_add_skill 1228 1|Summon Silhouette 1 (Lv 25)]%%
[admin_add_skill 1228 2|Summon Silhouette 2 (Lv 35)]%%
[admin_add_skill 1224 1|Surrender To Poison 1 (Lv 25)]%%
[admin_add_skill 1178 1|Twister 1 (Lv 20)]%%
[admin_add_skill 1178 2|Twister 2 (Lv 20)]%%
[admin_add_skill 1178 3|Twister 3 (Lv 25)]%%
[admin_add_skill 1178 4|Twister 4 (Lv 25)]%%
[admin_add_skill 1178 5|Twister 5 (Lv 30)]%%
[admin_add_skill 1178 6|Twister 6 (Lv 30)]%%
[admin_add_skill 1178 7|Twister 7 (Lv 35)]%%
[admin_add_skill 1178 8|Twister 8 (Lv 35)]%%
[admin_add_skill 1147 3|Vampiric Touch 3 (Lv 20)]%%
[admin_add_skill 1147 4|Vampiric Touch 4 (Lv 20)]%%
[admin_add_skill 1147 5|Vampiric Touch 5 (Lv 25)]%%
[admin_add_skill 1147 6|Vampiric Touch 6 (Lv 25)]%%
[admin_add_skill 249 3|Weapon Mastery 3 (Lv 20)]%%
[admin_add_skill 249 4|Weapon Mastery 4 (Lv 25)]%%
[admin_add_skill 249 5|Weapon Mastery 5 (Lv 25)]%%
[admin_add_skill 249 6|Weapon Mastery 6 (Lv 30)]%%
[admin_add_skill 249 7|Weapon Mastery 7 (Lv 30)]%%
[admin_add_skill 249 8|Weapon Mastery 8 (Lv 35)]%%
[admin_add_skill 249 9|Weapon Mastery 9 (Lv 35)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index DarkElf/DE_Wizard" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
