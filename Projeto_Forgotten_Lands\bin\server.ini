# ===============================
# Network Configuration Server  #
# ===============================
# IP which Bind Game server, * - all possible
GameserverHostname = 127.0.0.1
GameserverPort = 7777
# This is transmitted to the clients connecting from an external network, so it has to be a public IP or resolvable hostname
ExternalHostname = 127.0.0.1
# This is transmitted to the client from the same network, so it has to be a local IP or resolvable host name
InternalHostname = 127.0.0.1

# AdvIpSystem Make more GameServerIDs. Every Ip should be Protected by a DDoS Guard.
# Your Server will have Success with this system Online.
AdvIPSystem = False

R_GUARD = False

# Address \ port LS
LoginPort = 9014
LoginHost = 127.0.0.1
LoginUseCrypt = True
# How to request ID of LS
RequestServerID = 1
# Permission to take a different id, if requested busy
AcceptAlternateID = True

# ====================
# Database Settings  #
# ====================
# Database connection GameServer
Driver = com.mysql.jdbc.Driver
GameURL = *******************************************************************
GameUser  = root
GamePassword  = 6056262

# Database connection LoginServer
LoginURL = *******************************************************************
LoginUser = root
LoginPassword = 6056262

# Maximum number of connections to the database
MaximumDbConnections = 999
# How many seconds after the last activity will update database connection, default is 600 (10 minutes)
# This parameter is important to coordinate with the settings in the MySql server option interactive_timeout (default 28800 (8:00))
MaxIdleConnectionTimeout = 600
# Check interval of inactive connections, default is 60 (1 minute)
# Assuming a stable connection to the database and correct SETUP MaxIdleConnectionTimeout, you can put 1 (not verified)
IdleConnectionTestPeriod = 60

# Auto-save state characters
Autosave = True

# ========================
# Display Options Serer  #
# ========================
# Server Name:
ServerName = freyahf
# Short Server Name, without L2 or Lineage 2 at the beginning.
ShortServerName = freyahf
# Server Sub Name.
# This is required for identifying server in Stream, it is used in HTMLs
# For Example: Sovereign, Ruby
# It can be left empty
ServerSubName = 

# =========================
# Settings name patterns  #
# =========================
CnameTemplate = ([0-9A-Za-z]{2,16})|([0-9\u0410-\u044f-\u4e00-\u9fa5]{2,16})
# Clan name
ClanNameTemplate = ([0-9A-Za-z]{3,16})|([0-9\u0410]{3,16})
# Clan title
ClanTitleTemplate = ([0-9A-Za-z \\p{Punct}]{1,16})|([0-9\u0410 \\p{Punct}]{1,16})
# Alliance name
AllyNameTemplate = ([0-9A-Za-z]{3,16})|([0-9\u0410]{3,16})

NameMaxlen = 32

# Chars with the specified names cannot be created. Splited by ,
ForbiddenCharNames = l0l3d,bella,admin,administrator,GM,GameMaster,L2Saga,L2Mythras,L2Destiny

# ========================
# Vote Reward System
# ========================
EnableVoteReward = False
# Vote Address
# Example: http://website.com/StringTake.php?IP=
# VoteAddress = http://l2top.co/reward/Check.php?id=1450&user=USER&pass=PASSWORD&id2=11396&ip= //http://website.com/StringTake.php?IP=
VoteAddress = 

# ========================
# Display Options Serer  #
# ========================
# NORMAL; RELAX; TEST; NO_LABEL; RESTRICTED; EVENT; FREE
ServerType = NORMAL
# Limit on age
ServerAgeLimit = 0
# Server only for game masters
ServerGMOnly = False
# [] Appears before the name server
ServerBrackets = False
# Its pvp server?
PvPServer = False

# =========================
# Configuration Protocol  #
# =========================
# Minimum and maximum client protocols to access the server
# High Five: Part 4 = 267
MinProtocolRevision = 273
MaxProtocolRevision = 273


# ===================================
# Service to link your IP and Hwid  #
# ===================================
# Allow players to bind to IP Account
# Bind use the command .lock
AllowLockIP = True

# Allow players to bind to account HWid
# Works only in the case when there is a LameGuard/fGuard
# Bind use the command .lock
AllowLockHwid = True

# Bit mask to link your HWid
# 2 - HDD
# 4 - BIOS
# 8 - CPU
# Example: HWIDBan = 10 (reference to HWID will be deemed only HDD and CPU)
# Example: HWIDBan = 14 (reference to HWID will be deemed to HDD, CPU and BIOS)
HwidLockMask = 10


# ======================
# Settings pool sizes  #
# ======================
# Size of the thread pool to execute scheduled tasks, recommended setting: CPU x 4
ScheduledThreadPoolSize =  16
# Size of the thread pool, for the immediate implementation of the tasks recommended value: CPU x 2
ExecutorThreadPoolSize = 8

# Statistics collection launches, create and run-time problems. Reset the file to the server zavresheniyu or on command telnet.
EnableRunnableStats = False

# =======================
# Settings Interchange  #
# =======================
# Downtime mainstream treatment of compounds, the read / write
SelectorSleepTime = 3
# Delay before installation of the Desirable
InterestDelay = 30
# The maximum number of packets to be sent in a single pass
MaxSendPerPass = 32
# Buffer size for reading
ReadBufferSize = 65536
# Size of the buffer to write
WriteBufferSize = 131072
# Pool size for the temporary buffer read / write
BufferPoolSize = 64
# The number of managers to work with the effects of
EffectTaskManagers = 1

# ====================
# Language Settings  #
# ====================
# The following values can be changed by users of the game team. Lang, the default value
# Language dialogue default options ru, en
DefaultLang = en

# Timer to remove chars, 0 removed immediately
DeleteCharAfterDays = 2

# Directory data pack
DatapackRoot =.

# ============================
# Setup the server restarts  #
# ============================
# Daily Auto-Restart on schedule. The restart format chronology.
# *    *    *    *    *  command to be executed
# ┬    ┬    ┬    ┬    ┬
# │    │    │    │    │
# │    │    │    │    │
# │    │    │    │    └───── day of week (0 - 6) (0 or 6 are Sunday to Saturday, or use names)
# │    │    │    └────────── month (1 - 12)
# │    │    └─────────────── day of month (1 - 31)
# │    └──────────────────── hour (0 - 23)
# └───────────────────────── min (0 - 59)
# If the field is empty, the daily Auto-Restart is disabled by default, 5:00
# Every day at hour 05:00 = AutoRestartAt = 0 5 * * *
# Every day at hour 23:00 = AutoRestartAt = 0 23 * * *
# Every Monday at hour 05:00 = AutoRestartAt = 0 5 * * 1
AutoRestartAt = 45 5 * * *

# Should Backup be started during Auto Restart?
BackupDuringAutoRestart = True

# During which hours(don't include hour of Auto Restart),minutes additional backup should be made
# NOTE: Making Backup might completely freeze the server for few seconds
# Syntax: HOUR,MINUTE;HOUR,MINUTE
AdditionalBackupDates = 6,40

# Path to mysqldump file(don't write mysqldump at the end). If server is configurated correctly, this field may be empty
MySqlDumpPath = 

# ========================
# Settings game-masters  #
# ========================
# Hide the status of game masters in /gmlist and Community Board
HideGMStatus = True
# Announce entrance of game masters in the game if he is not in invis
ShowGMLogin = False
# Save game-effects wizards silence, gmspeed, invul etc.
SaveGMEffects = True

# ===================================
# Region setting of the game world  #
# ===================================
# The whole world is divided into regions, the size of the region is the size of the client card, namely 32768x32768, Z range from -32768 to 32767, identification card in the client is given XX_YY.
# To make working with objects on the server, the world is divided into regions, both horizontally and vertically. The size of the region and its closest neighbors is equal to the scope of the player.
# When you set up to remember: the smaller the size of the region, the less strain on the CPU, the less scope player, the less outgoing traffic, but the more memory consumption
# This parameter determines the size of the region, horizontal: 1 << n, with a value of n = 15 - complies with the customer's card size, a value of 12 size is 4096, 11 - 2048
HShift = 11
# This parameter defines the height of the region to the vertical, with a value of 10 - height is 1024
VShift = 11
# Limit the height coordinates for the world, because The world does not use the full range of -32768 to 32767, it can be reduced to -16 384 .. 16383, thus saving memory
MapMinZ = -32768
MapMaxZ = 32767

# Size of the sector areas. Default - 32.
LinearTerritoryCellSize = 32

# ==================
# Settings geodata #
# ==================
# Takes damage from falling
DamageFromFalling = False
# Host to connect to the L2J-GeoEditor
GeoEditorHost = 127.0.0.1

# =========================
# Settings cursed weapon  #
# =========================
# Are they (the default True)
AllowCursedWeapons = True
# Drops to the ground at gun disconnects \ kicked from the server
DropCursedWeaponsOnKick = False

# Maximum number of reflective
MaxReflectionsCount = 300

# Check GameServer ping, response time in seconds
PingServer = True
WaitPingTime = 5

# Call frequency task bypass, in seconds
PurgeTaskFrequency = 60

# Delay replay movement and attack
MovePacketDelay = 100
AttackPacketDelay = 250

# The packets below are heavy and sent way too often to the client.
# Character info for example is sent from each player to the client at every X milliseconds (you set them here).
# If you are on a siege with 100 players around you and you've set the BroadcastCharInfoInterval = 100
# This means that you get 100 heavy packets in 100 milliseconds, which is a lot for a client, like a mini-ddos.
# If players experience graphical lags during sieges or other places where many players roam around, its suggested
# to rise up the intervals so players don't get so flooded with packets that slow-down the client.
# Suggested values: 100ms - 1000ms. Above 1000ms should start to show noticeable rather negative effects on game play.
# Interval packet is sent UserInfo
UserInfoInterval = 100
# Enable sending stats
BroadcastStatsInterval = True
# Interval sending CharInfo
BroadcastCharInfoInterval = 100

# Maximum number of players on the server
MaximumOnlineUsers = 3000
# Auto-delete things lying on the ground, time in seconds, 0 - do not delete; 600 (10 minutes)
AutoDestroyDroppedItemAfter = 14400
# Separate setting for the ejected player or fallen from a PC, the default is 1200 (20 minutes)
AutoDestroyPlayerDroppedItemAfter = 1200
# Include warehouse
AllowWarehouse = True
# Include mail
AllowMail = True
# Time of things to try on the map
WearDelay = 10
# Can players throw things on the ground
AllowDiscardItem = True
# Don't allow players to drop items in town (pace zone)
AllowDiscardItemInTown = False
# Is the water (if the GEODATA necessarily include)
AllowWater = True
# If True, all players have administrator rights (convenient for the test server)
# Template with the privileges are in a file GMAccess.xml, for PlayerID set = "0"
EverybodyHasAdminRights = False
# Permission for all special teams players ( example ///fly)
AllowSpecialCommands = False
# Should we allow entering instances?
AllowEnterInstance = True
# Should we allow Stores?
AllowStores = True
# Allow talk with NPCS
AllowTalkToNpcs = True
# Allow only MoveBackwardToLocation packet?
AllowJustMoving = False
# Allow Tutorial on EnterWorld?
AllowTutorial = True
# Allow HWID Engine
AllowHWIDEngine = True
# Allow logging Skills Statistics
AllowSkillStatsLogger = True
# Allow Items Logging
AllowItemsLogging = True
# Allow Spawn Protection
AllowSpawnProtection = False
# -------------------------------------------------------------
# 2 - all conversations are cached at server
# 1 - The dialogues are cached as treatment
# 0 - cache disabled (for testing)
HtmCacheMode = 0

# The path to generate a static external web server (with / at the end)
# The way to generate a static external web server (with / at the end)
WebServerRoot =./webserver/
# Generation period, 0 - disable
# The period of generation, 0 - off
WebServerDelay = 0

# Setting Angel NevitSystem
AltVitalityNevitUpPoint = 100
AltVitalityNevitPoint = 100

AllowAddonsConfig = True

# ===========================
#        LOG Services       #
# ===========================
Services = True

# ===============================
#       Secondary Password      #
# ===============================
# If True players will be requested to set and enter they secondary password when login
EnableSecondaryPassword = False

# ==================================================================
# Settings to set a password on your character SA (Secondary Auth) #
# Pre-include this option in the client (UseSecondaryAuth = True)  #
# ==================================================================
# Include whether the system SA
SAEnabled = False
# Ban account after a user has exceeded the number of password attempts?
SABanAccEnabled = True
# Enhanced password system, combined with the odd even sure!
SAStrongPass = False
# Maximum number of password attempts
SAMaxAttemps = 5
# Ban length Character for failure password (in minutes)
SABanTime = 480
# Link to the password recovery page
SARecoveryLink = http://website.com/charPassRec.php


# ===========================
#        Email              #
# ===========================
# https://accounts.google.com/displayunlockcaptcha 
MailUser = <EMAIL>
MailPass = sqec xxcx sqqs bdjl
MailSubject = Donation to L2InJustice Report
# DONT FORGET TO ADD %PIN% where you want your TransactionID PIN to be displayed in the message!
MailMessage = Thanks for supporting our server! You can log in game and type .donate $Email and recive your donation ammount! Thank you for your support!

# =====================================
#            Wrong Password           #
# =====================================
# Should normal players receive Message while someone tries to enter their Account, gave wrong password and they are Online?
NormalPlayerReceiveMsgOnWrongAccountPass = True
# What should be Chat Type of the message?
# For example: TELL, HERO, SHOUT
NormalPlayerMsgTypeOnWrongAccount = TELL
# Should normal players receive MAIL while someone tries to enter their Account, gave wrong password and they are OFFLINE?
NormalPlayerMailOnWrongAccountWhileOffline = False
# Should normal players receive MAIL while someone tries to enter their CHARACTER and failed during Secondary Password?
NormalPlayerMailOnWrongSecondaryPassword = True

OnlineUsersPlus = 1
ParalizeOnRaidLevelDiff = True

