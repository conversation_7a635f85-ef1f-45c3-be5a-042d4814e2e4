<title>Manage Events</title>
<center><table width=260 bgcolor=DAFF7F>
<tr>
<td><button value="Main" action="bypass -h admin_admin" width=50 height=20 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Events" action="bypass -h admin_show_html events/events.htm" width=50 height=20 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Chars" action="bypass -h admin_char_manage" width=50 height=20 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="Server" action="bypass -h admin_server admserver.htm" width=50 height=20 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="GM Shop" action="bypass -h admin_gmshop" width=50 height=20 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr></table></center>
<br><br><br>

Select level range:

<a action="bypass -h scripts_events.TvT.TvT:start 1 0">20 - 29</a><br1>
<a action="bypass -h scripts_events.TvT.TvT:start 2 0">30 - 39</a><br1>
<a action="bypass -h scripts_events.TvT.TvT:start 3 0">40 - 51</a><br1>
<a action="bypass -h scripts_events.TvT.TvT:start 4 0">52 - 61</a><br1>
<a action="bypass -h scripts_events.TvT.TvT:start 5 0">62 - 75</a><br1>
<a action="bypass -h l2mv.gameserver.model.events.impl.TvT.TvT:start 6 0">76 - 85</a><br1>
<br>
<a action="bypass -h scripts_events.TvT.TvT:start 1 1">All categories</a><br1>
<br>
<a action="bypass -h scripts_events.TvT.TvT:start -1 0">All levels</a><br1>