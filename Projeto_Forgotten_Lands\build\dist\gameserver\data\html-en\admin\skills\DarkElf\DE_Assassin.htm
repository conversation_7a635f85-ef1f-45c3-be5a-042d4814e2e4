<title>Choose skill to add</title>
<center>
[admin_add_skill 2 1|Confusion 1 (Lv 24)]%%
[admin_add_skill 2 2|Confusion 2 (Lv 28)]%%
[admin_add_skill 2 3|Confusion 3 (Lv 32)]%%
[admin_add_skill 2 4|Confusion 4 (Lv 36)]%%
[admin_add_skill 16 10|Mortal Blow 10 (Lv 20)]%%
[admin_add_skill 16 11|Mortal Blow 11 (Lv 20)]%%
[admin_add_skill 16 12|Mortal Blow 12 (Lv 20)]%%
[admin_add_skill 16 13|Mortal Blow 13 (Lv 24)]%%
[admin_add_skill 16 14|Mortal Blow 14 (Lv 24)]%%
[admin_add_skill 16 15|Mortal Blow 15 (Lv 24)]%%
[admin_add_skill 16 16|Mortal Blow 16 (Lv 28)]%%
[admin_add_skill 16 17|Mortal Blow 17 (Lv 28)]%%
[admin_add_skill 16 18|Mortal Blow 18 (Lv 28)]%%
[admin_add_skill 16 19|Mortal Blow 19 (Lv 32)]%%
[admin_add_skill 16 20|Mortal Blow 20 (Lv 32)]%%
[admin_add_skill 16 21|Mortal Blow 21 (Lv 32)]%%
[admin_add_skill 16 22|Mortal Blow 22 (Lv 36)]%%
[admin_add_skill 16 23|Mortal Blow 23 (Lv 36)]%%
[admin_add_skill 16 24|Mortal Blow 24 (Lv 36)]%%
[admin_add_skill 27 1|Unlock 1 (Lv 20)]%%
[admin_add_skill 27 2|Unlock 2 (Lv 24)]%%
[admin_add_skill 27 3|Unlock 3 (Lv 28)]%%
[admin_add_skill 27 4|Unlock 4 (Lv 32)]%%
[admin_add_skill 27 5|Unlock 5 (Lv 36)]%%
[admin_add_skill 56 10|Power Shot 10 (Lv 20)]%%
[admin_add_skill 56 11|Power Shot 11 (Lv 20)]%%
[admin_add_skill 56 12|Power Shot 12 (Lv 20)]%%
[admin_add_skill 56 13|Power Shot 13 (Lv 24)]%%
[admin_add_skill 56 14|Power Shot 14 (Lv 24)]%%
[admin_add_skill 56 15|Power Shot 15 (Lv 24)]%%
[admin_add_skill 56 16|Power Shot 16 (Lv 28)]%%
[admin_add_skill 56 17|Power Shot 17 (Lv 28)]%%
[admin_add_skill 56 18|Power Shot 18 (Lv 28)]%%
[admin_add_skill 56 19|Power Shot 19 (Lv 32)]%%
[admin_add_skill 56 20|Power Shot 20 (Lv 32)]%%
[admin_add_skill 56 21|Power Shot 21 (Lv 32)]%%
[admin_add_skill 56 22|Power Shot 22 (Lv 36)]%%
[admin_add_skill 56 23|Power Shot 23 (Lv 36)]%%
[admin_add_skill 56 24|Power Shot 24 (Lv 36)]%%
[admin_add_skill 70 3|Drain Health 3 (Lv 20)]%%
[admin_add_skill 70 4|Drain Health 4 (Lv 20)]%%
[admin_add_skill 70 5|Drain Health 5 (Lv 24)]%%
[admin_add_skill 70 6|Drain Health 6 (Lv 24)]%%
[admin_add_skill 70 7|Drain Health 7 (Lv 24)]%%
[admin_add_skill 70 8|Drain Health 8 (Lv 28)]%%
[admin_add_skill 70 9|Drain Health 9 (Lv 28)]%%
[admin_add_skill 70 10|Drain Health 10 (Lv 28)]%%
[admin_add_skill 70 11|Drain Health 11 (Lv 32)]%%
[admin_add_skill 70 12|Drain Health 12 (Lv 32)]%%
[admin_add_skill 70 13|Drain Health 13 (Lv 32)]%%
[admin_add_skill 70 14|Drain Health 14 (Lv 36)]%%
[admin_add_skill 70 15|Drain Health 15 (Lv 36)]%%
[admin_add_skill 70 16|Drain Health 16 (Lv 36)]%%
[admin_add_skill 77 2|Attack Aura 2 (Lv 28)]%%
[admin_add_skill 91 2|Defense Aura 2 (Lv 20)]%%
[admin_add_skill 96 1|Bleed 1 (Lv 24)]%%
[admin_add_skill 96 2|Bleed 2 (Lv 32)]%%
[admin_add_skill 99 1|Rapid Shot 1 (Lv 32)]%%
[admin_add_skill 101 1|Stun Shot 1 (Lv 36)]%%
[admin_add_skill 101 2|Stun Shot 2 (Lv 36)]%%
[admin_add_skill 101 3|Stun Shot 3 (Lv 36)]%%
[admin_add_skill 105 1|Freezing Strike 1 (Lv 36)]%%
[admin_add_skill 105 2|Freezing Strike 2 (Lv 36)]%%
[admin_add_skill 111 1|Ultimate Evasion 1 (Lv 28)]%%
<br>
<button value="Page2" action="bypass -h admin_skill_index DarkElf/DE_Assassin2" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
