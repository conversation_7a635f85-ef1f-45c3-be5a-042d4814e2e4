<html><body><table width=260>
<tr><td width=40>
<button value="Main" action="bypass -h admin_admin" width=40 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df">
</td><td width=180>
<center>Ka<PERSON><PERSON> Female Arbalester</center>
</td><td width=40>
<button value="Back" action="bypass -h admin_show_html skills/Kamael/kamael.htm" width=40 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df">
</td></tr></table>
<img src="sek.cbui355" width=260 height=2><br>
<center><table width=260>
<tr><td>Level:</td><td><edit var="level" width=110 height=15></td></tr>
</table><br>
<font color="b5a6c6"><table width=260>
<tr><td>Binding Trap (max 8)</td><td><button value="Add" action="bypass -h admin_add_skill 518 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Crossbow Mastery (max 45)</td><td><button value="Add" action="bypass -h admin_add_skill 473 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Increase Range (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 486 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Life to Soul (max 5)</td><td><button value="Add" action="bypass -h admin_add_skill 502 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Light Armor Mastery (max 50)</td><td><button value="Add" action="bypass -h admin_add_skill 465 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Magic Immunity (max 55)</td><td><button value="Add" action="bypass -h admin_add_skill 466 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Penetrate Shot (max 15)</td><td><button value="Add" action="bypass -h admin_add_skill 487 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Real Target (max 4)</td><td><button value="Add" action="bypass -h admin_add_skill 522 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Shift Target (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 489 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Soul Mastery (max 23)</td><td><button value="Add" action="bypass -h admin_add_skill 467 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Bleeding Shot (max 34)</td><td><button value="Add" action="bypass -h admin_add_skill 509 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Decoy (max 6)</td><td><button value="Add" action="bypass -h admin_add_skill 525 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Defuse Trap (max 7)</td><td><button value="Add" action="bypass -h admin_add_skill 471 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Rising Shot (max 31)</td><td><button value="Add" action="bypass -h admin_add_skill 508 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Poison Trap (max 6)</td><td><button value="Add" action="bypass -h admin_add_skill 515 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Temptation (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 511 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Quiver of Bolts - Grade B (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 620 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Slow Trap (max 6)</td><td><button value="Add" action="bypass -h admin_add_skill 516 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Fast Shot (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 490 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Flash Trap (max 5)</td><td><button value="Add" action="bypass -h admin_add_skill 517 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Quiver of Bolts - Grade A (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 519 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Sharpshooting (max 8)</td><td><button value="Add" action="bypass -h admin_add_skill 521 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Divine Insperation (max 4)</td><td><button value="Add" action="bypass -h admin_add_skill 1405 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Imbeu Dark Seed (max 7)</td><td><button value="Add" action="bypass -h admin_add_skill 523 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Quiver of Bolts - Grade S (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 520 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
</table></center></font>
</body></html>