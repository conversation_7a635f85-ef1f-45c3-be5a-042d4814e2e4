<title>Choose skill to add</title>
<center>
[admin_add_skill 1069 27|Sleep 27 (Lv 60)]%%
[admin_add_skill 1069 28|Sleep 28 (Lv 60)]%%
[admin_add_skill 1069 29|Sleep 29 (Lv 62)]%%
[admin_add_skill 1069 30|Sleep 30 (Lv 62)]%%
[admin_add_skill 1069 31|Sleep 31 (Lv 64)]%%
[admin_add_skill 1069 32|Sleep 32 (Lv 64)]%%
[admin_add_skill 1069 33|Sleep 33 (Lv 66)]%%
[admin_add_skill 1069 34|Sleep 34 (Lv 66)]%%
[admin_add_skill 1069 35|Sleep 35 (Lv 68)]%%
[admin_add_skill 1069 36|Sleep 36 (Lv 68)]%%
[admin_add_skill 1069 37|Sleep 37 (Lv 70)]%%
[admin_add_skill 1069 38|Sleep 38 (Lv 70)]%%
[admin_add_skill 1069 39|Sleep 39 (Lv 72)]%%
[admin_add_skill 1069 40|Sleep 40 (Lv 72)]%%
[admin_add_skill 1069 41|Sleep 41 (Lv 74)]%%
[admin_add_skill 1069 42|Sleep 42 (Lv 74)]%%
[admin_add_skill 1129 1|Summon Reanimated Man 1 (Lv 44)]%%
[admin_add_skill 1129 2|Summon Reanimated Man 2 (Lv 52)]%%
[admin_add_skill 1129 3|Summon Reanimated Man 3 (Lv 60)]%%
[admin_add_skill 1129 4|Summon Reanimated Man 4 (Lv 64)]%%
[admin_add_skill 1129 5|Summon Reanimated Man 5 (Lv 68)]%%
[admin_add_skill 1129 6|Summon Reanimated Man 6 (Lv 72)]%%
[admin_add_skill 1129 7|Summon Reanimated Man 7 (Lv 74)]%%
[admin_add_skill 1148 1|Death Spike 1 (Lv 44)]%%
[admin_add_skill 1148 2|Death Spike 2 (Lv 48)]%%
[admin_add_skill 1148 3|Death Spike 3 (Lv 52)]%%
[admin_add_skill 1148 4|Death Spike 4 (Lv 56)]%%
[admin_add_skill 1148 5|Death Spike 5 (Lv 58)]%%
[admin_add_skill 1148 6|Death Spike 6 (Lv 60)]%%
[admin_add_skill 1148 7|Death Spike 7 (Lv 62)]%%
[admin_add_skill 1148 8|Death Spike 8 (Lv 64)]%%
[admin_add_skill 1148 9|Death Spike 9 (Lv 66)]%%
[admin_add_skill 1148 10|Death Spike 10 (Lv 68)]%%
[admin_add_skill 1148 11|Death Spike 11 (Lv 70)]%%
[admin_add_skill 1148 12|Death Spike 12 (Lv 72)]%%
[admin_add_skill 1148 13|Death Spike 13 (Lv 74)]%%
[admin_add_skill 1151 3|Corpse Life Drain 3 (Lv 40)]%%
[admin_add_skill 1151 4|Corpse Life Drain 4 (Lv 44)]%%
[admin_add_skill 1151 5|Corpse Life Drain 5 (Lv 48)]%%
[admin_add_skill 1151 6|Corpse Life Drain 6 (Lv 52)]%%
[admin_add_skill 1151 7|Corpse Life Drain 7 (Lv 56)]%%
[admin_add_skill 1151 8|Corpse Life Drain 8 (Lv 58)]%%
[admin_add_skill 1151 9|Corpse Life Drain 9 (Lv 60)]%%
[admin_add_skill 1151 10|Corpse Life Drain 10 (Lv 62)]%%
[admin_add_skill 1151 11|Corpse Life Drain 11 (Lv 64)]%%
[admin_add_skill 1151 12|Corpse Life Drain 12 (Lv 66)]%%
[admin_add_skill 1151 13|Corpse Life Drain 13 (Lv 68)]%%
[admin_add_skill 1151 14|Corpse Life Drain 14 (Lv 70)]%%
[admin_add_skill 1151 15|Corpse Life Drain 15 (Lv 72)]%%
[admin_add_skill 1151 16|Corpse Life Drain 16 (Lv 74)]%%
[admin_add_skill 1154 1|Summon Corrupted Man 1 (Lv 40)]%%
[admin_add_skill 1154 2|Summon Corrupted Man 2 (Lv 48)]%%
[admin_add_skill 1154 3|Summon Corrupted Man 3 (Lv 56)]%%
[admin_add_skill 1154 4|Summon Corrupted Man 4 (Lv 62)]%%
[admin_add_skill 1154 5|Summon Corrupted Man 5 (Lv 66)]%%
[admin_add_skill 1154 6|Summon Corrupted Man 6 (Lv 70)]%%
[admin_add_skill 1155 1|Corpse Burst 1 (Lv 48)]%%
[admin_add_skill 1155 2|Corpse Burst 2 (Lv 48)]%%
[admin_add_skill 1155 3|Corpse Burst 3 (Lv 52)]%%
[admin_add_skill 1155 4|Corpse Burst 4 (Lv 52)]%%
[admin_add_skill 1155 5|Corpse Burst 5 (Lv 56)]%%
[admin_add_skill 1155 6|Corpse Burst 6 (Lv 56)]%%
[admin_add_skill 1155 7|Corpse Burst 7 (Lv 58)]%%
[admin_add_skill 1155 8|Corpse Burst 8 (Lv 60)]%%
[admin_add_skill 1155 9|Corpse Burst 9 (Lv 62)]%%
[admin_add_skill 1155 10|Corpse Burst 10 (Lv 64)]%%
[admin_add_skill 1155 11|Corpse Burst 11 (Lv 66)]%%
[admin_add_skill 1155 12|Corpse Burst 12 (Lv 68)]%%
[admin_add_skill 1155 13|Corpse Burst 13 (Lv 70)]%%
[admin_add_skill 1155 14|Corpse Burst 14 (Lv 72)]%%
[admin_add_skill 1155 15|Corpse Burst 15 (Lv 74)]%%
[admin_add_skill 1156 1|Forget 1 (Lv 44)]%%
[admin_add_skill 1156 2|Forget 2 (Lv 48)]%%
[admin_add_skill 1156 3|Forget 3 (Lv 52)]%%
[admin_add_skill 1156 4|Forget 4 (Lv 56)]%%
[admin_add_skill 1156 5|Forget 5 (Lv 58)]%%
[admin_add_skill 1156 6|Forget 6 (Lv 60)]%%
[admin_add_skill 1156 7|Forget 7 (Lv 62)]%%
[admin_add_skill 1156 8|Forget 8 (Lv 64)]%%
[admin_add_skill 1156 9|Forget 9 (Lv 66)]%%
[admin_add_skill 1156 10|Forget 10 (Lv 68)]%%
[admin_add_skill 1156 11|Forget 11 (Lv 70)]%%
[admin_add_skill 1156 12|Forget 12 (Lv 72)]%%
[admin_add_skill 1156 13|Forget 13 (Lv 74)]%%
[admin_add_skill 1157 2|Body To Mind 2 (Lv 40)]%%
[admin_add_skill 1157 3|Body To Mind 3 (Lv 52)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index Human/H_Necromancer" width=40 height=15><br>
<button value="Page2" action="bypass -h admin_skill_index Human/H_Necromancer2" width=40 height=15><br>
<button value="Page4" action="bypass -h admin_skill_index Human/H_Necromancer4" width=40 height=15><br>
<button value="Page5" action="bypass -h admin_skill_index Human/H_Necromancer5" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
