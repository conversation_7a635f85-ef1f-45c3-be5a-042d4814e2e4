# =================================================
# 				   Ru Vote System
# =================================================
# Should we enable Russian Site Vote System?
EnableRuVoteSystem = False

# What's link to the Votes?
# !!! Use HTTP, not HTTPS
RuVoteLink = http://rf.mmotop.ru/votes/1b21089af10ea20786d638fe4a53d29151341fb8.txt?856161463b24d3c57cf9f1471b5f9a7e

# How often(Milliseconds) should Votes be checked and rewards given?
RuVoteThreadDelay = 300000

# After how long(Milliseconds), vote should appear on the Site?
RuVoteAppearDelay = 7200000

# What hour is on Server Machine, when MMOTop wipes it's votes?
RuVoteWipeHour = 22

# Should same IPs be checked, while trying to receive reward?
RuVoteIPCheck = False
# Should same hwids be checked, while trying to receive reward?
RuVoteHwidCheck = True

# Rewards from Vote Reward System:
# You can have up to 10 different Reward Groups.
# From each Group, maximally 1 Item(x amount of it) can be chosen. If all chances are below 100%, there might be no reward for that group
# Syntax: itemId,minCount,maxCount,chance;itemId,minCount,maxCount,chance etc
# If you have "VoteRewardGroup1 = 57,10,25,100.0" - Item with Id 57(adena) will give(amount 10-25) with 100% Chance
# If you have "VoteRewardGroup1 = 57,10,10,100.0;57,200,200,100.0" - It will either drop 10 or 200 Adena with 100% chance
# If you have "VoteRewardGroup1 = 57,100,100,0.5;57,50,50,100.0" - First it will have 0.5% to give 100 Adena, if it didn't then 50 Adena will be given
# Vote Rune
RuVoteRewardGroup1 = 6673,6,12,100.0
# Misc
RuVoteRewardGroup2 = 6577,1,1,0.03;//!Blessed Enchant Weapon S\
6578,1,1,0.15;//!Blessed Enchant Armor S\
13071,1,1,0.25;//!Red Soul Crystal - Stage 16\
13072,1,1,0.25;//! Blue Soul Crystal - Stage 16\
13073,1,1,0.25;//!Green Soul Crystal - Stage 16\
10480,1,1,0.566;//!Red Soul Crystal - Stage 15\
10481,1,1,0.566;//!Blue Soul Crystal - Stage 15\
10482,0,0,0.566;//!Green Soul Crystal - Stage 15\
14169,1,1,1.25;//!Top Life Stone Level 84\
14168,1,1,2.0;//!High Life Stone Level 84\
13073,1,1,3.333;//!Giant's Codex - Mastery\
959,1,1,3.5;//!Enchant Weapon S\
960,1,1,6.0;//!Enchant Armor S\
6622,1,2,8.0;//!Giant's Codex\
9552,1,1,8.0;//!Fire Crystal\
9553,1,1,8.0;//!Water Crystal\
9556,1,1,8.0;//!Dark Crystal\
9557,1,1,8.0;//!Holy Crystal\
9554,1,1,8.0;//!Earth Crystal\
9555,1,1,8.0;//!Wind Crystal\
9546,1,3,10.0;//!Fire Stone\
9547,1,3,12.0;//!Water Stone\
9548,1,3,14.0;//!Earth Stone\
9549,1,3,16.0;//!Wind Stone\
9550,1,3,18.0;//!Dark Stone\
9551,1,3,100.0//!Holy Stone
# Cool Items
RuVoteRewardGroup3 = 
# You can have up to 10 Groups
RuVoteRewardGroup4 =

# Now Rewards for SMS Vote
RuVoteSmsRewardGroup1 = 6673,15,28,100.0
# Misc
RuVoteSmsRewardGroup2 = 6577,1,1,0.03;//!Blessed Enchant Weapon S\
6578,1,1,0.15;//!Blessed Enchant Armor S\
13071,1,1,0.25;//!Red Soul Crystal - Stage 16\
13072,1,1,0.25;//! Blue Soul Crystal - Stage 16\
13073,1,1,0.25;//!Green Soul Crystal - Stage 16\
10480,1,1,0.566;//!Red Soul Crystal - Stage 15\
10481,1,1,0.566;//!Blue Soul Crystal - Stage 15\
10482,0,0,0.566;//!Green Soul Crystal - Stage 15\
14169,1,1,1.25;//!Top Life Stone Level 84\
14168,1,1,2.0;//!High Life Stone Level 84\
13073,1,1,3.333;//!Giant's Codex - Mastery\
959,1,1,3.5;//!Enchant Weapon S\
960,1,1,6.0;//!Enchant Armor S\
6622,1,2,8.0;//!Giant's Codex\
9552,1,1,8.0;//!Fire Crystal\
9553,1,1,8.0;//!Water Crystal\
9556,1,1,8.0;//!Dark Crystal\
9557,1,1,8.0;//!Holy Crystal\
9554,1,1,8.0;//!Earth Crystal\
9555,1,1,8.0;//!Wind Crystal\
9546,1,3,10.0;//!Fire Stone\
9547,1,3,12.0;//!Water Stone\
9548,1,3,14.0;//!Earth Stone\
9549,1,3,16.0;//!Wind Stone\
9550,1,3,18.0;//!Dark Stone\
9551,1,3,100.0//!Holy Stone
# Cool Items
RuVoteSmsRewardGroup3 = 
# You can have up to 10 Groups
RuVoteSmsRewardGroup4 =


# What should be announcement if player didn't vote?
# To change that, go to data/strings and change value near RussianVote.NotVotedAnnounce in both strings languages!
# If you cannot translate to other languages now, delete whole line of RussianVote.NotVotedAnnounce so only English will be used

# What should be delay(Milliseconds) between Auto Announcements?
# To disable, set value <= 0
RuVoteAnnouncementDelay = 0

# Link to the Vote page in la2.mmotop.ru
# It will be used in htmls and strings
RuVoteLinkToVote = www.bit.ly/votetales
# Link to the Guide of how to vote in la2.mmotop.ru
RuVoteLinkToGuide = www.bit.ly/guidevote

# What should be the message if Voting was successful?
# %nextVoteIn% - will be replaced by number of Hours till next wipe of the votes
RuVoteSuccessMsg = Thank you for voting in La2 MMO Top for L2Cartel, you can vote again in %nextVoteIn% hours and get your next reward!

# What should be the message if Voting has failed?
RuVoteFailedMsg = Your latest vote request for L2Cartel in la2.mmotop.ru has failed! Vote again in www.bit.ly/votetales & press .vote2

# What should be Command to open Panel?
RuVotePanelCommand = vote2

# What should be the message on opening Panel?
RuVotePanelMsg = Thank you for submiting your voting for La2 MMO Top Russian Toplist, our system will check your votes within 1-3hours and deliver your rewards!