<title>Choose skill to add</title>
<center>
[admin_add_skill 1127 1|Servitor Heal 1 (Lv 20)]%%
[admin_add_skill 1127 2|Servitor <PERSON> 2 (Lv 20)]%%
[admin_add_skill 1127 3|Servitor <PERSON> 3 (Lv 20)]%%
[admin_add_skill 1127 4|Servitor <PERSON> 4 (Lv 25)]%%
[admin_add_skill 1127 5|<PERSON>vitor <PERSON> 5 (Lv 25)]%%
[admin_add_skill 1127 6|<PERSON>vitor <PERSON> 6 (Lv 25)]%%
[admin_add_skill 1127 7|<PERSON>vitor <PERSON> 7 (Lv 30)]%%
[admin_add_skill 1127 8|<PERSON>vitor <PERSON> 8 (Lv 30)]%%
[admin_add_skill 1127 9|Servitor <PERSON> 9 (Lv 30)]%%
[admin_add_skill 1127 10|Servitor <PERSON> 10 (Lv 35)]%%
[admin_add_skill 1127 11|Servitor <PERSON> 11 (Lv 35)]%%
[admin_add_skill 1127 12|<PERSON><PERSON><PERSON> 12 (Lv 35)]%%
[admin_add_skill 1145 1|Bright Servitor 1 (Lv 35)]%%
[admin_add_skill 1164 2|Curse: Weakness 2 (Lv 20)]%%
[admin_add_skill 1164 3|Curse: Weakness 3 (Lv 25)]%%
[admin_add_skill 1164 4|Curse: Weakness 4 (Lv 30)]%%
[admin_add_skill 1164 5|Curse: Weakness 5 (Lv 35)]%%
[admin_add_skill 1172 1|Aura Burn 1 (Lv 20)]%%
[admin_add_skill 1172 2|Aura Burn 2 (Lv 20)]%%
[admin_add_skill 1172 3|Aura Burn 3 (Lv 25)]%%
[admin_add_skill 1172 4|Aura Burn 4 (Lv 25)]%%
[admin_add_skill 1172 5|Aura Burn 5 (Lv 30)]%%
[admin_add_skill 1172 6|Aura Burn 6 (Lv 30)]%%
[admin_add_skill 1172 7|Aura Burn 7 (Lv 35)]%%
[admin_add_skill 1172 8|Aura Burn 8 (Lv 35)]%%
[admin_add_skill 1175 1|Aqua Swirl 1 (Lv 20)]%%
[admin_add_skill 1175 2|Aqua Swirl 2 (Lv 20)]%%
[admin_add_skill 1175 3|Aqua Swirl 3 (Lv 25)]%%
[admin_add_skill 1175 4|Aqua Swirl 4 (Lv 25)]%%
[admin_add_skill 1175 5|Aqua Swirl 5 (Lv 30)]%%
[admin_add_skill 1175 6|Aqua Swirl 6 (Lv 30)]%%
[admin_add_skill 1175 7|Aqua Swirl 7 (Lv 35)]%%
[admin_add_skill 1175 8|Aqua Swirl 8 (Lv 35)]%%
[admin_add_skill 1181 1|Flame Strike 1 (Lv 20)]%%
[admin_add_skill 1181 2|Flame Strike 2 (Lv 25)]%%
[admin_add_skill 1181 3|Flame Strike 3 (Lv 30)]%%
[admin_add_skill 1182 1|Resist Aqua 1 (Lv 25)]%%
[admin_add_skill 1184 5|Ice Bolt 5 (Lv 20)]%%
[admin_add_skill 1184 6|Ice Bolt 6 (Lv 20)]%%
[admin_add_skill 1223 1|Surrender To Earth 1 (Lv 35)]%%
[admin_add_skill 1226 1|Summon Unicorn Boxer 1 (Lv 20)]%%
[admin_add_skill 1226 2|Summon Unicorn Boxer 2 (Lv 25)]%%
[admin_add_skill 1226 3|Summon Unicorn Boxer 3 (Lv 30)]%%
[admin_add_skill 1226 4|Summon Unicorn Boxer 4 (Lv 35)]%%
[admin_add_skill 1227 1|Summon Unicorn Mirage 1 (Lv 20)]%%
[admin_add_skill 1227 2|Summon Unicorn Mirage 2 (Lv 25)]%%
[admin_add_skill 1227 3|Summon Unicorn Mirage 3 (Lv 30)]%%
[admin_add_skill 1227 4|Summon Unicorn Mirage 4 (Lv 35)]%%
[admin_add_skill 1264 1|Solar Spark 1 (Lv 25)]%%
[admin_add_skill 1264 2|Solar Spark 2 (Lv 30)]%%
[admin_add_skill 1264 3|Solar Spark 3 (Lv 35)]%%
[admin_add_skill 1274 1|Energy Bolt 1 (Lv 20)]%%
[admin_add_skill 1274 2|Energy Bolt 2 (Lv 25)]%%
[admin_add_skill 1274 3|Energy Bolt 3 (Lv 30)]%%
[admin_add_skill 1274 4|Energy Bolt 4 (Lv 35)]%%

<br>
<button value="Page1" action="bypass -h admin_skill_index Elf/E_Wizard" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
