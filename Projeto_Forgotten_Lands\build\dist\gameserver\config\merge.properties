# =================================================
#               NEW Server Merge Options
# NOTE: EDIT merge.properties in loginserver configs too!
# =================================================
# Should we enable Merging Servers?
EnableMerge = True

# What title should Players from merged server have?
# NOTE: Title cannot be larger than 16 Characters!
MergeNewTitle = From Tales

# Which items should be NOT transferred?
MergeNotTransferItemIds = 
# Amount of what items and by how many, should be multiplied
# Syntax: itemId,percentToDelete;itemId,percentToDelete
# percentToDelete - 0.0 - none, 0.5 - half, 1.0-all
MergeItemsAmountToMultiply = 57,0.3
# Should any item ids be changed to other ids?
# Syntax: originalItemId,newItemId;originalItemId,newItemId
MergeItemsIdsSwap = 

# Should Enchant Level of ALL items above certain level, be decreased?
# For example if you set this value to 8, any enchant level above 8 will be decreased to 8
# Set -1 to disable
MergeItemsMaxEnchant = 10

# Should Active Hero Statuses be merged?
MergeHeroStatus = True

# Clans Merge:
# Should we allow merging Clans?
EnableClansMerge = True

# What should be name of the Server Variable that will be put into database, after Successful Merge?
MergeClansOverVariable = TalesClansMerged

# Server doesn't allow to have 2 same Clan Names.
# To fix this problem, just merged Clans(who had same name as existing clan) will need to have temporarily name(until Clan Leader will change it)
# If Clan Name already exist, we will add 1 char at the end. This char should be normally impossible to be chosen by leader, but possible to be placed in database
# Few char examples: @, #, $, %, ^, &, *, ?
# So if Clan Name already exists and char = @, merged clan name will be: clanName@
MergeClansAddCharOnSameName = *

# Should Clan Levels above certain level, be decreased?
# For example if you set this value to 8, any clan level above 8 will be decreased to 8
# Set 12 to disable
MergeClansMaxLevel = 12

# Should Clan Reputation be transferred?
MergeClansTransferCRP = True

# =================================================
#               OLD server Merge Options
# =================================================
# Is this server merged to other one?
# If so, merge Enter World should be enabled. EnableMerge on the Top of the file should be FALSE
# Player will not be able to do any actions, he will just see Community Board Page
EnableMergeCommunity = False

# =================================================
#                   Database Settings
# =================================================
# Database settings of the GAME SERVER:
# Those should be filled on both servers, pointing to each other
MergeUrl = *******************************************************************
MergeLogin = root
MergePassword = 6056262
# Database settings of the LOGIN SERVER:
# This can be filled only on OLD server
MergeAuthUrl = *******************************************************************
MergeAuthLogin = root
MergeAuthPassword = 6056262

# SQL File to create 2 Tables that should be placed in OLD Server database:
# CREATE TABLE `merge_data` (
#  `old_login` varchar(32) DEFAULT NULL,
#  `new_login` varchar(32) DEFAULT NULL,
#  `email` varchar(45) DEFAULT NULL,
#  `finished` tinyint(1) DEFAULT '0',
#  `old_char_id_1` int(11) DEFAULT NULL,
#  `new_char_name_1` varchar(35) DEFAULT NULL,
#  `old_char_id_2` int(11) DEFAULT NULL,
#  `new_char_name_2` varchar(35) DEFAULT NULL,
#  `old_char_id_3` int(11) DEFAULT NULL,
#  `new_char_name_3` varchar(35) DEFAULT NULL,
#  `old_char_id_4` int(11) DEFAULT NULL,
#  `new_char_name_4` varchar(35) DEFAULT NULL,
#  `old_char_id_5` int(11) DEFAULT NULL,
#  `new_char_name_5` varchar(35) DEFAULT NULL,
#  `old_char_id_6` int(11) DEFAULT NULL,
#  `new_char_name_6` varchar(35) DEFAULT NULL,
#  `old_char_id_7` int(11) DEFAULT NULL,
#  `new_char_name_7` varchar(35) DEFAULT NULL,
#  `old_char_id_8` int(11) DEFAULT NULL,
#  `new_char_name_8` varchar(35) DEFAULT NULL
# ) ENGINE=InnoDB DEFAULT CHARSET=latin1;
#
# CREATE TABLE `merge_data_clan` (
#  `old_clan_id` int(11) NOT NULL,
#  `new_clan_id` int(11) DEFAULT NULL,
#  `old_clan_name` varchar(45) DEFAULT NULL,
#  `new_clan_name` varchar(45) DEFAULT NULL,
#  PRIMARY KEY (`old_clan_id`)
# ) ENGINE=InnoDB DEFAULT CHARSET=latin1;


