# ====================
# Database Settings  #
# ====================
# Database connection
ForumDriver = com.mysql.jdbc.Driver
ForumURL = ***************************
ForumLogin = root
ForumPassword = 6056262
# Maximum number of connections to the database
ForumMaximumDbConnections = 5
# How many seconds after the last activity will zakryvatsya database connection, default is 600 (10 minutes)
# This parameter is important to coordinate with the settings in the mysql server option interactive_timeout (default 28800 (8:00))
ForumMaxIdleConnectionTimeout = 30
# Check interval of inactive connections, default is 60 (1 minute)
# Assuming a stable connection to the database and correct SETUP MaxIdleConnectionTimeout, you can put 1 (not verified)
ForumIdleConnectionTestPeriod = 30

# =========================
#      Other Settings     #
# =========================
# Should forum feature be allowed?
AllowForum = False

# What Voiced commands should open Forum?
# split with ;
ForumCommands = forum

# Should Index Page be shown on Enter World?
EnterWorldShowCommunityIndex = True

# Should forum be shown while logging into game?
ForumOnEnterWorld = False
# What exactly should be shown?
# If Last Post from the certain Board should be shown, write Board Index(ForumBoardType.java)
# If Index, write -1
ForumOnEnterWorldBoardId = 3

# What are Ids of Topics(smf_messages) that can be seen ONLY from real forum?
ForumOnlyForumTopics = 

# What message should be sent, when player is trying to see ONLY Forum Topic?
ForumOnlyForumTopicMsg = This topic can be viewed only from www.lineage2tales.com/forum

# Item Id for Connecting Forum Account with Game
ForumConnectAccountPriceId = 6673
# Price in Polished Crystals for creating Forum Account
ForumConnectAccountPrice = 0

# Minimum number of Posts that existing account should have, to connect it with character
# This MUST be >= 1
ForumAccountMinPosts = 1

# Should writing Posts & Making new Topics be allowed?
ForumAllowPosting = True

# Max Subject length
ForumMaxSubjectLength = 30

# Max Post Message length
ForumMaxPostMessageLength = 900

# What message should be shown, when post is too long?
ForumTooLongPostMsg = Post is too long to be read on In Game Forum. Check it on www.lineage2tales.com/forum

# Delay between Forum Synchronization tasks
# Value in milliseconds
ForumTasksDelay = 7000
# How many threads should be running maximally at the same time?
ForumMaxTasksInSameMoment = 3

# By how many seconds, current online date be cut comparing to forum online?
ForumOnlineDateCut = 60

# Pattern of the correct Subject
# While changing this, take a look at startNewTopic.htm
ForumSubjectPattern = [0-9A-Za-z `~!@#$%^&*()\\-_=\\+\\{\\}\\[\\]|;:'",\\.\\?/\\n\\\\]{3,}

# Pattern of the correct Message
# While changing this, take a look at startNewTopic.htm & startNewPost.htm
ForumMessagePattern = [0-9A-Za-z \\r\\n`~!@#$%^&*()\\-_=\\+\\{\\}\\[\\]|;:'",\\.\\?/\\n\\\\<>]{10,}

# =========================
#         Board Ids     
# =========================
# What IDs have got specified boards in real forum database?
# In SMF, you can find board id in PhpMyAdmin > smf_boards
# Announcements:
ForumBoardIdAnnouncements = 2
# Events
ForumBoardIdEvents = 4
# Changelog:
ForumBoardIdChangelog = 63
# General Discussion:
ForumBoardIdGeneralDiscussion = 112
# Clans and Alliances:
ForumBoardIdClans = 114
# Market Place:
ForumBoardIdMarketPlace = 117
# Bug Tracker:
ForumBoardIdBugTracker = 116
# Suggestions:
ForumBoardIdSuggestions = 113

# =========================
#         Shoutbox     
# =========================
# Should we allow messages in Chat be shown in Forum?
AllowSboutboxFromGame = True
# Should we allow messages from Forum Shoutbox in show in Game?
AllowShoutboxFromForum = False

# Messages from which Chat Types, should be printed on the Forum Shoutbox?
# For Example ALL, SHOUT, TRADE, HERO_VOICE
# Split with ;
ShoutboxFromGameChatTypes = SHOUT

# In which Chat Types, should Message from Forum be printed?
# Message will be sent to all players in all areas
# Split with ;
ShoutboxFromForumChatTypes = SHOUT

# Does player MUST have an forum account to speak in the Chats?
ShoutboxAccountRequired = True

# For which Chats, should above Config + below message work?
# Split with ;
ShoutboxNoAccountChatTypes = SHOUT

# What message should player get while using ShoutboxChatType WITHOUT forum account?
ShoutboxUsageNoAccountMsg = Attach your Forum Account by .forum to unlock the Chat!

# What should be placed before Player Name when he uses Shoutbox from Game?
ShoutboxGamePrefix = 

# What should be placed before Forum member when he uses Shoutbox from Forum?
ShoutboxForumPrefix = [From Forum]

# Whats color name of the Administrator nickname in shoutbox?
# Default: #ff0000
ShoutboxAdminColor = #ff0000

# Pattern for Messages going from Game > Forum
# If Message will not match the pattern, it will just not be show in Forum
ShoutboxGameToForumPattern = [0-9A-Za-z \\r\\n`~!@#$%^&*()\\-_=\\+\\{\\}\\[\\]|;:'",\\.\\?/\\n\\\\<>]{0,}