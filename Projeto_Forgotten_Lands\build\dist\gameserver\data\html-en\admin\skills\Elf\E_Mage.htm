<title>Choose skill to add</title>
<center>
[admin_add_skill 118 1|Magicians Movement 1 (Lv 1)]%%
[admin_add_skill 146 1|Anti Magic 1 (Lv 7)]%%
[admin_add_skill 146 2|Anti Magic 2 (Lv 7)]%%
[admin_add_skill 146 3|Anti Magic 3 (Lv 14)]%%
[admin_add_skill 146 4|Anti Magic 4 (Lv 14)]%%
[admin_add_skill 163 1|Spellcraft 1 (Lv 1)]%%
[admin_add_skill 194 1|Lucky 1 (Lv 1)]%%
[admin_add_skill 214 1|Mana Recovery 1 (Lv 1)]%%
[admin_add_skill 244 1|Armor Mastery 1 (Lv 7)]%%
[admin_add_skill 244 2|Armor Mastery 2 (Lv 14)]%%
[admin_add_skill 244 3|Armor Mastery 3 (Lv 14)]%%
[admin_add_skill 249 1|Weapon Mastery 1 (Lv 7)]%%
[admin_add_skill 249 2|Weapon Mastery 2 (Lv 14)]%%
[admin_add_skill 1011 1|Heal 1 (Lv 7)]%%
[admin_add_skill 1011 2|Heal 2 (Lv 7)]%%
[admin_add_skill 1011 3|Heal 3 (Lv 7)]%%
[admin_add_skill 1011 4|Heal 4 (Lv 14)]%%
[admin_add_skill 1011 5|Heal 5 (Lv 14)]%%
[admin_add_skill 1011 6|Heal 6 (Lv 14)]%%
[admin_add_skill 1012 1|Cure Poison 1 (Lv 7)]%%
[admin_add_skill 1015 1|Battle Heal 1 (Lv 14)]%%
[admin_add_skill 1015 2|Battle Heal 2 (Lv 14)]%%
[admin_add_skill 1015 3|Battle Heal 3 (Lv 14)]%%
[admin_add_skill 1027 1|Group Heal 1 (Lv 14)]%%
[admin_add_skill 1027 2|Group Heal 2 (Lv 14)]%%
[admin_add_skill 1027 3|Group Heal 3 (Lv 14)]%%
[admin_add_skill 1040 1|Shield 1 (Lv 7)]%%
[admin_add_skill 1068 1|Might 1 (Lv 7)]%%
[admin_add_skill 1164 1|Curse: Weakness 1 (Lv 14)]%%
[admin_add_skill 1177 1|Wind Strike 1 (Lv 1)]%%
[admin_add_skill 1177 2|Wind Strike 2 (Lv 7)]%%
[admin_add_skill 1177 3|Wind Strike 3 (Lv 7)]%%
[admin_add_skill 1177 4|Wind Strike 4 (Lv 14)]%%
[admin_add_skill 1177 5|Wind Strike 5 (Lv 14)]%%
[admin_add_skill 1184 1|Ice Bolt 1 (Lv 7)]%%
[admin_add_skill 1184 2|Ice Bolt 2 (Lv 7)]%%
[admin_add_skill 1184 3|Ice Bolt 3 (Lv 14)]%%
[admin_add_skill 1184 4|Ice Bolt 4 (Lv 14)]%%
[admin_add_skill 1206 1|Wind Shackle 1 (Lv 14)]%%
[admin_add_skill 1216 1|Self Heal 1 (Lv 1)]%%
</center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
