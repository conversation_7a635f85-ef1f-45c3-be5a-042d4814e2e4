<title>Choose skill to add</title>
<center>
[admin_add_skill 30 20|Backstab 20 (Lv 58)]%%
[admin_add_skill 30 21|Backstab 21 (Lv 58)]%%
[admin_add_skill 30 22|Backstab 22 (Lv 60)]%%
[admin_add_skill 30 23|Backstab 23 (Lv 60)]%%
[admin_add_skill 30 24|Backstab 24 (Lv 62)]%%
[admin_add_skill 30 25|Backstab 25 (Lv 62)]%%
[admin_add_skill 30 26|Backstab 26 (Lv 64)]%%
[admin_add_skill 30 27|Backstab 27 (Lv 64)]%%
[admin_add_skill 30 28|Backstab 28 (Lv 66)]%%
[admin_add_skill 30 29|Backstab 29 (Lv 66)]%%
[admin_add_skill 30 30|Backstab 30 (Lv 68)]%%
[admin_add_skill 30 31|Backstab 31 (Lv 68)]%%
[admin_add_skill 30 32|Backstab 32 (Lv 70)]%%
[admin_add_skill 30 33|Backstab 33 (Lv 70)]%%
[admin_add_skill 30 34|Backstab 34 (Lv 72)]%%
[admin_add_skill 30 35|Backstab 35 (Lv 72)]%%
[admin_add_skill 30 36|Backstab 36 (Lv 74)]%%
[admin_add_skill 30 37|Backstab 37 (Lv 74)]%%
[admin_add_skill 51 1|Lure 1 (Lv 52)]%%
[admin_add_skill 58 19|Elemental Heal 19 (Lv 40)]%%
[admin_add_skill 58 20|Elemental Heal 20 (Lv 40)]%%
[admin_add_skill 58 21|Elemental Heal 21 (Lv 40)]%%
[admin_add_skill 58 22|Elemental Heal 22 (Lv 43)]%%
[admin_add_skill 58 23|Elemental Heal 23 (Lv 43)]%%
[admin_add_skill 58 24|Elemental Heal 24 (Lv 43)]%%
[admin_add_skill 58 25|Elemental Heal 25 (Lv 46)]%%
[admin_add_skill 58 26|Elemental Heal 26 (Lv 46)]%%
[admin_add_skill 58 27|Elemental Heal 27 (Lv 46)]%%
[admin_add_skill 58 28|Elemental Heal 28 (Lv 49)]%%
[admin_add_skill 58 29|Elemental Heal 29 (Lv 49)]%%
[admin_add_skill 58 30|Elemental Heal 30 (Lv 49)]%%
[admin_add_skill 58 31|Elemental Heal 31 (Lv 52)]%%
[admin_add_skill 58 32|Elemental Heal 32 (Lv 52)]%%
[admin_add_skill 58 33|Elemental Heal 33 (Lv 52)]%%
[admin_add_skill 58 34|Elemental Heal 34 (Lv 55)]%%
[admin_add_skill 58 35|Elemental Heal 35 (Lv 55)]%%
[admin_add_skill 58 36|Elemental Heal 36 (Lv 55)]%%
[admin_add_skill 58 37|Elemental Heal 37 (Lv 58)]%%
[admin_add_skill 58 38|Elemental Heal 38 (Lv 58)]%%
[admin_add_skill 58 39|Elemental Heal 39 (Lv 58)]%%
[admin_add_skill 58 40|Elemental Heal 40 (Lv 60)]%%
[admin_add_skill 58 41|Elemental Heal 41 (Lv 60)]%%
[admin_add_skill 58 42|Elemental Heal 42 (Lv 62)]%%
[admin_add_skill 58 43|Elemental Heal 43 (Lv 62)]%%
[admin_add_skill 58 44|Elemental Heal 44 (Lv 64)]%%
[admin_add_skill 58 45|Elemental Heal 45 (Lv 64)]%%
[admin_add_skill 58 46|Elemental Heal 46 (Lv 66)]%%
[admin_add_skill 58 47|Elemental Heal 47 (Lv 66)]%%
[admin_add_skill 58 48|Elemental Heal 48 (Lv 68)]%%
[admin_add_skill 58 49|Elemental Heal 49 (Lv 68)]%%
[admin_add_skill 58 50|Elemental Heal 50 (Lv 70)]%%
[admin_add_skill 58 51|Elemental Heal 51 (Lv 70)]%%
[admin_add_skill 58 52|Elemental Heal 52 (Lv 72)]%%
[admin_add_skill 58 53|Elemental Heal 53 (Lv 72)]%%
[admin_add_skill 58 54|Elemental Heal 54 (Lv 74)]%%
[admin_add_skill 58 55|Elemental Heal 55 (Lv 74)]%%
[admin_add_skill 60 1|Fake Death 1 (Lv 40)]%%
[admin_add_skill 61 2|Cure Bleeding 2 (Lv 46)]%%
[admin_add_skill 61 3|Cure Bleeding 3 (Lv 62)]%%
[admin_add_skill 96 3|Bleed 3 (Lv 49)]%%
[admin_add_skill 96 4|Bleed 4 (Lv 58)]%%
[admin_add_skill 96 5|Bleed 5 (Lv 66)]%%
[admin_add_skill 96 6|Bleed 6 (Lv 70)]%%
[admin_add_skill 102 2|Entangle 2 (Lv 40)]%%
[admin_add_skill 102 3|Entangle 3 (Lv 43)]%%
[admin_add_skill 102 4|Entangle 4 (Lv 46)]%%
[admin_add_skill 102 5|Entangle 5 (Lv 49)]%%
[admin_add_skill 102 6|Entangle 6 (Lv 52)]%%
[admin_add_skill 102 7|Entangle 7 (Lv 55)]%%
[admin_add_skill 102 8|Entangle 8 (Lv 58)]%%
[admin_add_skill 102 9|Entangle 9 (Lv 60)]%%
[admin_add_skill 102 10|Entangle 10 (Lv 62)]%%
[admin_add_skill 102 11|Entangle 11 (Lv 64)]%%
[admin_add_skill 102 12|Entangle 12 (Lv 66)]%%
[admin_add_skill 102 13|Entangle 13 (Lv 68)]%%
[admin_add_skill 102 14|Entangle 14 (Lv 70)]%%
[admin_add_skill 102 15|Entangle 15 (Lv 72)]%%
[admin_add_skill 102 16|Entangle 16 (Lv 74)]%%
[admin_add_skill 111 2|Ultimate Evasion 2 (Lv 55)]%%
[admin_add_skill 123 1|Spirit Barrier 1 (Lv 40)]%%
[admin_add_skill 123 2|Spirit Barrier 2 (Lv 49)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index Elf/E_PlainsWalker" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index Elf/E_PlainsWalker3" width=40 height=15><br>
<button value="Page4" action="bypass -h admin_skill_index Elf/E_PlainsWalker4" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
