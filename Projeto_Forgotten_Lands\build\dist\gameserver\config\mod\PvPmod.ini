#==================================================
#   PvP Mods - Recommended for PvP Servers		  #
#==================================================

# Each character gets a new "Status nobleman"
# Double-check the set value
# Default: False
NewCharIsNoble = False

# Each character gets a "hero status"
# Double-check the set value
# Default: False
NewCharIsHero = False

# Consumption Shotov enabled (default: True)
ConsumableShot = True
# Consumption Arrows enabled (default: True)
ConsumableArrow = True

#==================================================
#            Attribute is on click 				  #
#==================================================

# The number of attributes to add to the weapon \ armor one stone
# The default is 5 \ 6
att_mod_Weapon = 5
att_mod_Armor = 6

# For the weapon 1st stone gives 20 attributes
# Change to your liking
att_mod_Weapon1 = 20

# Default 150 \ 60
att_mod_max_weapon = 150
att_mod_max_armor = 60

#==================================================
#                  Paint Settings 				  #
#==================================================

# Limit paint
HennaStats = 5

#==================================================
#                  Killing Spree 				  #
#==================================================

# Is killing spree system enabled?
# This system keeps track of the pvp kills that are done without dying, when certain kills are reached it can change the name's color to the player, and also make announcements
# Announcements and colors are not related, each one can have different values or be disabled
KillingSpreeEnabled = True

# Should the killing sprees be announced to all the server
# This will only be sent when a player reach a killing spree breakpoint
# %name% will be replaced with the player's name
# Example: 4,%name% is Dominating!;6,%name% is on Rampage!
# To disable keep it empty.
KillingSpreeAnnouncements = 4,%name% is Dominating!;6,%name% is on Rampage!;8,%name% is on a Killing Spree!;10,%name% is on a Monster Kill!;12,%name% is Unstoppable!;14,%name% is on an Ultra Kill!;16,%name% is Godlike!;18,%name% is Wicked Sick!;20,%name% is on a Ludricrous Kill!;24,%name% is on a Holy Shit!

# Change name color on xx pvp.
# Format: pvpcount,color;pvpcount,color;
# Color picker: http://www.lineage2.es/misc/l2-user_name_color.php
# Example: 100,00FF00;200,CCCCCC;300,888800;400,0000FF;500,880088; etc...
# To disable keep it empty.
KillingSpreeColors = 4,00FFFF;6,FFFF00;8,8E236B;10,FF2400;12,0000FF;14,FF00FF;16,000000;18,545454;20,FF0000

#==================================================
#                  Other Settings 				  #
#==================================================
# 1. Enable \ disable announcement on spawn pe
AnnounceToSpawnRb = True

# Include special spawn new players?
# Default: False
CustomSpawn = False
SpawnX = 83384
SpawnY = 148184
SpawnZ = -3408

# Include \ disable chat messages from NPCs ADEPT (running around in the cities)
ADEPT_ENABLE = true

# Include \ disable spawn trees (city beautification)
SPAWN_CITIES_TREE = false

# Include \ disable spawn NPC Buffer
SPAWN_NPC_BUFFER = false

# Include \ disable spawn cleaners
SPAWN_scrubwoman = true

# Maximum number of people in your party
MaxPartySize = 9



EnebleTitleColorMod = False
TypeTitleColorMod = PvP

CountTitle_1 = 50
TitleColor_1 = FFFFFF

CountTitle_2 = 50
TitleColor_2 = FFFFFF

CountTitle_3 = 50
TitleColor_3 = FFFFFF

CountTitle_4 = 50
TitleColor_4 = FFFFFF

CountTitle_5 = 50
TitleColor_5 = FFFFFF

EnebleNameColorMod = False
TypeNameColorMod = Pk

CountName_1 = 50
NameColor_1 = FFFFFF

CountName_2 = 50
NameColor_2 = FFFFFF

CountName_3 = 50
NameColor_3 = FFFFFF

CountName_4 = 50
NameColor_4 = FFFFFF

CountName_5 = 50
NameColor_5 = FFFFFF