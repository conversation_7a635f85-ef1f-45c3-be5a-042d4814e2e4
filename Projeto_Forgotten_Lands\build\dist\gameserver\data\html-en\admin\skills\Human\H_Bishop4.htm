<title>Choose skill to add</title>
<center>
[admin_add_skill 1069 11|Sleep 11 (Lv 40)]%%
[admin_add_skill 1069 12|Sleep 12 (Lv 40)]%%
[admin_add_skill 1069 13|Sleep 13 (Lv 44)]%%
[admin_add_skill 1069 14|Sleep 14 (Lv 44)]%%
[admin_add_skill 1069 15|Sleep 15 (Lv 44)]%%
[admin_add_skill 1069 16|Sleep 16 (Lv 48)]%%
[admin_add_skill 1069 17|Sleep 17 (Lv 48)]%%
[admin_add_skill 1069 18|Sleep 18 (Lv 48)]%%
[admin_add_skill 1069 19|Sleep 19 (Lv 52)]%%
[admin_add_skill 1069 20|Sleep 20 (Lv 52)]%%
[admin_add_skill 1069 21|Sleep 21 (Lv 52)]%%
[admin_add_skill 1069 22|Sleep 22 (Lv 56)]%%
[admin_add_skill 1069 23|Sleep 23 (Lv 56)]%%
[admin_add_skill 1069 24|Sleep 24 (Lv 56)]%%
[admin_add_skill 1069 25|Sleep 25 (Lv 58)]%%
[admin_add_skill 1069 26|Sleep 26 (Lv 58)]%%
[admin_add_skill 1069 27|Sleep 27 (Lv 60)]%%
[admin_add_skill 1069 28|Sleep 28 (Lv 60)]%%
[admin_add_skill 1069 29|Sleep 29 (Lv 62)]%%
[admin_add_skill 1069 30|Sleep 30 (Lv 62)]%%
[admin_add_skill 1069 31|Sleep 31 (Lv 64)]%%
[admin_add_skill 1069 32|Sleep 32 (Lv 64)]%%
[admin_add_skill 1069 33|Sleep 33 (Lv 66)]%%
[admin_add_skill 1069 34|Sleep 34 (Lv 66)]%%
[admin_add_skill 1069 35|Sleep 35 (Lv 68)]%%
[admin_add_skill 1069 36|Sleep 36 (Lv 68)]%%
[admin_add_skill 1069 37|Sleep 37 (Lv 70)]%%
[admin_add_skill 1069 38|Sleep 38 (Lv 70)]%%
[admin_add_skill 1069 39|Sleep 39 (Lv 72)]%%
[admin_add_skill 1069 40|Sleep 40 (Lv 72)]%%
[admin_add_skill 1069 41|Sleep 41 (Lv 74)]%%
[admin_add_skill 1069 42|Sleep 42 (Lv 74)]%%
[admin_add_skill 1075 2|Peace 2 (Lv 40)]%%
[admin_add_skill 1075 3|Peace 3 (Lv 44)]%%
[admin_add_skill 1075 4|Peace 4 (Lv 48)]%%
[admin_add_skill 1075 5|Peace 5 (Lv 52)]%%
[admin_add_skill 1075 6|Peace 6 (Lv 56)]%%
[admin_add_skill 1075 7|Peace 7 (Lv 58)]%%
[admin_add_skill 1075 8|Peace 8 (Lv 60)]%%
[admin_add_skill 1075 9|Peace 9 (Lv 62)]%%
[admin_add_skill 1075 10|Peace 10 (Lv 64)]%%
[admin_add_skill 1075 11|Peace 11 (Lv 66)]%%
[admin_add_skill 1075 12|Peace 12 (Lv 68)]%%
[admin_add_skill 1075 13|Peace 13 (Lv 70)]%%
[admin_add_skill 1075 14|Peace 14 (Lv 72)]%%
[admin_add_skill 1075 15|Peace 15 (Lv 74)]%%
[admin_add_skill 1217 1|Greater Heal 1 (Lv 40)]%%
[admin_add_skill 1217 2|Greater Heal 2 (Lv 40)]%%
[admin_add_skill 1217 3|Greater Heal 3 (Lv 40)]%%
[admin_add_skill 1217 4|Greater Heal 4 (Lv 44)]%%
[admin_add_skill 1217 5|Greater Heal 5 (Lv 44)]%%
[admin_add_skill 1217 6|Greater Heal 6 (Lv 44)]%%
[admin_add_skill 1217 7|Greater Heal 7 (Lv 48)]%%
[admin_add_skill 1217 8|Greater Heal 8 (Lv 48)]%%
[admin_add_skill 1217 9|Greater Heal 9 (Lv 48)]%%
[admin_add_skill 1217 10|Greater Heal 10 (Lv 52)]%%
[admin_add_skill 1217 11|Greater Heal 11 (Lv 52)]%%
[admin_add_skill 1217 12|Greater Heal 12 (Lv 52)]%%
[admin_add_skill 1217 13|Greater Heal 13 (Lv 56)]%%
[admin_add_skill 1217 14|Greater Heal 14 (Lv 56)]%%
[admin_add_skill 1217 15|Greater Heal 15 (Lv 56)]%%
[admin_add_skill 1217 16|Greater Heal 16 (Lv 58)]%%
[admin_add_skill 1217 17|Greater Heal 17 (Lv 58)]%%
[admin_add_skill 1217 18|Greater Heal 18 (Lv 60)]%%
[admin_add_skill 1217 19|Greater Heal 19 (Lv 60)]%%
[admin_add_skill 1217 20|Greater Heal 20 (Lv 62)]%%
[admin_add_skill 1217 21|Greater Heal 21 (Lv 62)]%%
[admin_add_skill 1217 22|Greater Heal 22 (Lv 64)]%%
[admin_add_skill 1217 23|Greater Heal 23 (Lv 64)]%%
[admin_add_skill 1217 24|Greater Heal 24 (Lv 66)]%%
[admin_add_skill 1217 25|Greater Heal 25 (Lv 66)]%%
[admin_add_skill 1217 26|Greater Heal 26 (Lv 68)]%%
[admin_add_skill 1217 27|Greater Heal 27 (Lv 68)]%%
[admin_add_skill 1217 28|Greater Heal 28 (Lv 70)]%%
[admin_add_skill 1217 29|Greater Heal 29 (Lv 70)]%%
[admin_add_skill 1217 30|Greater Heal 30 (Lv 72)]%%
[admin_add_skill 1217 31|Greater Heal 31 (Lv 72)]%%
[admin_add_skill 1217 32|Greater Heal 32 (Lv 74)]%%
[admin_add_skill 1217 33|Greater Heal 33 (Lv 74)]%%
[admin_add_skill 1218 1|Greater Battle Heal 1 (Lv 40)]%%
[admin_add_skill 1218 2|Greater Battle Heal 2 (Lv 40)]%%
[admin_add_skill 1218 3|Greater Battle Heal 3 (Lv 40)]%%
[admin_add_skill 1218 4|Greater Battle Heal 4 (Lv 44)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index Human/H_Bishop" width=40 height=15><br>
<button value="Page2" action="bypass -h admin_skill_index Human/H_Bishop2" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index Human/H_Bishop3" width=40 height=15><br>
<button value="Page5" action="bypass -h admin_skill_index Human/H_Bishop5" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
