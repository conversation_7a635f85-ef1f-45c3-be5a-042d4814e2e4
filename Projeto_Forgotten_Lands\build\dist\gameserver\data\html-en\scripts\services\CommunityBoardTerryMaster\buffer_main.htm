<html noscrollbar>
<title>Community Board</title>
<body>
	<br>
	<center>
	<table width="770">
		<tr>
			<td>
				<table>
					<tr>
						<td>
							<table width="180" height="490" background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
								<tr>
									<td>
										<table width=180>
											<tr>
												<td>
													<center><img src="forgot.logo" width=143 height=128></center>
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td>
										<table width=180>
											<tr>
												<td>
													<center><img src="L2EssenceCommunity.effect_top" width=167 height=18></center>
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td height=10>
									</td>
								</tr>
								<tr>
									<td>
										<table width=180>
											<tr>
												<td align=center>
													<button value="Home" action="bypass _bbshome" width=177 height=33 back="L2EssenceCommunity.home_btn_over" fore="L2EssenceCommunity.home_btn">
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td>
										<table width=180>
											<tr>
												<td align=center>
													<button value="GM Shop" action="bypass _bbsfile:smallNpcs/gmshop" width=177 height=33 back="L2EssenceCommunity.gmshop_btn_over" fore="L2EssenceCommunity.gmshop_btn">
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td>
										<table width=180>
											<tr>
												<td align=center>
													<button value="Buffer" action="bypass _bbsbuffer" width=177 height=33 back="L2EssenceCommunity.buffer_btn_over" fore="L2EssenceCommunity.buffer_btn">
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td>
										<table width=180>
											<tr>
												<td align=center>
													<button value="Teleport" action="bypass -h _bbsgatekeeper" width=177 height=33 back="L2EssenceCommunity.teleport_btn_over" fore="L2EssenceCommunity.teleport_btn">
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td>
										<table width=180>
											<tr>
												<td align=center>
													<button value="Account Service" action="bypass _bbspage:character" width=177 height=33 back="L2EssenceCommunity.acc_services_btn_over" fore="L2EssenceCommunity.acc_services_btn">
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td>
										<table width=180>
											<tr>
												<td align=center>
													<button value="Donate" action="bypass _bbspage:HowToDonate" width=177 height=33 back="L2EssenceCommunity.itembroker_btn_over" fore="L2EssenceCommunity.itembroker_btn">
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td>
										<table width=180>
											<tr>
												<td align=center>
													<button value="Vote Info" action="bypass _bbspage:voting" width=177 height=33 back="L2EssenceCommunity.rankings_btn_over" fore="L2EssenceCommunity.rankings_btn">
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td>
										<table width=180>
											<tr>
												<td align=center>
													<center><img src="L2EssenceCommunity.effect_bottom" width=167 height=18></center>
												</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td height=10>
									</td>
								</tr>
							</table>
							<br>
						</td>
						<td width=570 valign=top>
							<table border=0 cellpadding=0 cellspacing=0 width=570 height=490 background="L2UI_CT1.Tab_DF_bg">
								<tr>
									<td height=40 align=center valign=top>
										<table border=0 width=560 height=35 cellspacing=1 cellpadding=1>
											<tr>
												<td width=32 align=center valign=center><img src="icon.skill6319" width=28 height=28></td>
												<td width=200 align=left valign=center>
													<font name=hs12 color="556B2F">Community Buffer</font><br>
													<font color=FFFFFF name=__SYSTEMWORLDFONT>Buffing your character and your Pet!</font>
												</td>
												<td width=100 align=center valign=center><font color=556B2F name="hs12">Target:</font></td>
												<td width=40 align=right valign=center>%topbtn%</td>
											</tr>
										</table>
									</td>
								</tr>
								<tr>
									<td height=450 align=center valign=top>
										<table border=0 cellspacing=0 cellpadding=0 width=560>
											<tr>
												<!-- Ready Schemes Column -->
												<td width=180 height=440 align=center valign=top>
													<table border=0 cellspacing=1 cellpadding=1 height=440 width=180>
														<tr>
															<td width=180 height=25 align=center>
																<table>
																	<tr>
																		<td width=170 height=22 align=center background="l2ui_ct1.ListCTRL_DF_Decoration">
																			<font color=556B2F name=hs12>Ready Schemes</font>
																		</td>
																	</tr>
																</table>
															</td>
														</tr>
														<tr>
															<td width=180 height=30 align=center>
																<table border=0 width=180 height=30 cellspacing=1 cellpadding=1>
																	<tr>
																		<td width=35 align=center valign=center>
																			<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="l2ui_ct1.SkillWnd_DF_Icon_Enchant_add_freedom">
																				<tr><td width=32 height=32 align=center valign=center>
																						<button action="bypass _bbsbufferbypass_giveBuffSet mage 0 0" width=30 height=30 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
																					</td>
																				</tr>
																			</table>
																		</td>
																		<td width=140 align=left valign=center>
																			<font name=hs11 color="FFFFFF">Mage Scheme</font>
																		</td>
																	</tr>
																</table>
															</td>
														</tr>
														<tr>
															<td width=180 height=30 align=center>
																<table border=0 width=180 height=30 cellspacing=1 cellpadding=1>
																	<tr>
																		<td width=35 align=center valign=center>
																			<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="l2ui_ct1.SkillWnd_DF_Icon_Enchant_add_attack">
																				<tr><td width=32 height=32 align=center valign=center>
																						<button action="bypass _bbsbufferbypass_giveBuffSet fighter 0 0" width=30 height=30 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
																					</td>
																				</tr>
																			</table>
																		</td>
																		<td width=140 align=left valign=center>
																			<font name=hs11 color="FFFFFF">Fighter Scheme</font>
																		</td>
																	</tr>
																</table>
															</td>
														</tr>
														<tr>
															<td width=180 height=30 align=center>
																<table border=0 width=180 height=30 cellspacing=1 cellpadding=1>
																	<tr>
																		<td width=35 align=center valign=center>
																			<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="l2ui_ct1.SkillWnd_DF_Icon_Enchant_add_dodge">
																				<tr><td width=32 height=32 align=center valign=center>
																						<button action="bypass _bbsbufferbypass_giveBuffSet dagger 0 0" width=30 height=30 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
																					</td>
																				</tr>
																			</table>
																		</td>
																		<td width=140 align=left valign=center>
																			<font name=hs11 color="FFFFFF">Dagger Scheme</font>
																		</td>
																	</tr>
																</table>
															</td>
														</tr>
														<tr>
															<td width=180 height=30 align=center>
																<table border=0 width=180 height=30 cellspacing=1 cellpadding=1>
																	<tr>
																		<td width=35 align=center valign=center>
																			<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="l2ui_ct1.SkillWnd_DF_Icon_Enchant_add_hit">
																				<tr><td width=32 height=32 align=center valign=center>
																						<button action="bypass _bbsbufferbypass_giveBuffSet archer 0 0" width=30 height=30 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
																					</td>
																				</tr>
																			</table>
																		</td>
																		<td width=140 align=left valign=center>
																			<font name=hs11 color="FFFFFF">Archer Scheme</font>
																		</td>
																	</tr>
																</table>
															</td>
														</tr>
														<tr>
															<td width=180 height=30 align=center>
																<table border=0 width=180 height=30 cellspacing=1 cellpadding=1>
																	<tr>
																		<td width=35 align=center valign=center>
																			<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="l2ui_ct1.SkillWnd_DF_Icon_Enchant_add_brave">
																				<tr><td width=32 height=32 align=center valign=center>
																						<button action="bypass _bbsbufferbypass_giveBuffSet support 0 0" width=30 height=30 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
																					</td>
																				</tr>
																			</table>
																		</td>
																		<td width=140 align=left valign=center>
																			<font name=hs11 color="FFFFFF">Support Scheme</font>
																		</td>
																	</tr>
																</table>
															</td>
														</tr>
														<tr>
															<td width=180 height=30 align=center>
																<table border=0 width=180 height=30 cellspacing=1 cellpadding=1>
																	<tr>
																		<td width=35 align=center valign=center>
																			<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="l2ui_ct1.SkillWnd_DF_Icon_Enchant_add_defence">
																				<tr><td width=32 height=32 align=center valign=center>
																						<button action="bypass _bbsbufferbypass_giveBuffSet tank 0 0" width=30 height=30 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
																					</td>
																				</tr>
																			</table>
																		</td>
																		<td width=140 align=left valign=center>
																			<font name=hs11 color="FFFFFF">Tank Scheme</font>
																		</td>
																	</tr>
																</table>
															</td>
														</tr>
													</table>
												</td>
												<!-- Fast Actions & Individual Buffs Column -->
												<td width=180 height=440 align=center valign=top>
													<table border=0 cellspacing=1 cellpadding=1 height=440 width=180>
														<tr>
															<td width=180 height=25 align=center>
																<table>
																	<tr>
																		<td width=170 height=22 align=center background="l2ui_ct1.ListCTRL_DF_Decoration">
																			<font color=556B2F name=hs12>Fast Actions</font>
																		</td>
																	</tr>
																</table>
															</td>
														</tr>
														<tr>
															<td width=180 height=30 align=center>
																<table border=0 width=180 height=30 cellspacing=1 cellpadding=1>
																	<tr>
																		<td width=35 align=center valign=center>
																			<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill0440">
																				<tr><td width=32 height=32 align=center valign=center>
																						<button action="bypass _bbsbufferbypass_heal 0 0 0" width=30 height=30 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
																					</td>
																				</tr>
																			</table>
																		</td>
																		<td width=140 align=left valign=center>
																			<font name=hs11 color="FFFFFF">Heal HP/CP/MP</font>
																		</td>
																	</tr>
																</table>
															</td>
														</tr>
														<tr>
															<td width=180 height=30 align=center>
																<table border=0 width=180 height=30 cellspacing=1 cellpadding=1>
																	<tr>
																		<td width=35 align=center valign=center>
																			<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill1323">
																				<tr><td width=32 height=32 align=center valign=center>
																						<button action="bypass _bbsbufferbypass_giveBuffs 1323 1 noble" width=30 height=30 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
																					</td>
																				</tr>
																			</table>
																		</td>
																		<td width=140 align=left valign=center>
																			<font name=hs11 color="FFFFFF">Noblesse Buff</font>
																		</td>
																	</tr>
																</table>
															</td>
														</tr>
														<tr>
															<td width=180 height=30 align=center>
																<table border=0 width=180 height=30 cellspacing=1 cellpadding=1>
																	<tr>
																		<td width=35 align=center valign=center>
																			<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill1056">
																				<tr><td width=32 height=32 align=center valign=center>
																						<button action="bypass _bbsbufferbypass_removeBuffs 0 0 0" width=30 height=30 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
																					</td>
																				</tr>
																			</table>
																		</td>
																		<td width=140 align=left valign=center>
																			<font name=hs11 color="FFFFFF">Cancel Buffs</font>
																		</td>
																	</tr>
																</table>
															</td>
														</tr>
														<tr>
															<td width=180 height=20 align=center>
																<table>
																	<tr>
																		<td width=170 height=22 align=center background="l2ui_ct1.ListCTRL_DF_Decoration">
																			<font color=556B2F name=hs12>Individual Buffs</font>
																		</td>
																	</tr>
																</table>
															</td>
														</tr>
														<tr>
															<td width=180 height=120 align=center>
																<table border=0 width=180 height=120 cellspacing=1 cellpadding=1>
																	<tr>
																		<td width=92 height=30 align=center>
																			<table border=0 width=92 height=30 cellspacing=1 cellpadding=1>
																				<tr>
																					<td width=28 align=center valign=center>
																						<table border=0 cellspacing=0 cellpadding=0 width=28 height=28 background="Icon.skill1045">
																							<tr><td width=28 height=28 align=center valign=center>
																									<button action="bypass _bbsbufferbypass_redirect view_buffs 0 0" width=30 height=30 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
																								</td>
																							</tr>
																						</table>
																					</td>
																					<td width=60 align=center valign=center>
																						<font name=hs9 color="FFFFFF">Buffs</font>
																					</td>
																				</tr>
																			</table>
																		</td>
																		<td width=92 height=30 align=center>
																			<table border=0 width=92 height=30 cellspacing=1 cellpadding=1>
																				<tr>
																					<td width=60 align=center valign=center>
																						<font name=hs9 color="FFFFFF">Resists</font>
																					</td>
																					<td width=28 align=center valign=center>
																						<table border=0 cellspacing=0 cellpadding=0 width=28 height=28 background="Icon.skill1035">
																							<tr><td width=28 height=28 align=center valign=center>
																									<button action="bypass _bbsbufferbypass_redirect view_resists 0 0" width=30 height=30 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
																								</td>
																							</tr>
																						</table>
																					</td>
																				</tr>
																			</table>
																		</td>
																	</tr>
																	<tr>
																		<td width=92 height=30 align=center>
																			<table border=0 width=92 height=30 cellspacing=1 cellpadding=1>
																				<tr>
																					<td width=28 align=center valign=center>
																						<table border=0 cellspacing=0 cellpadding=0 width=28 height=28 background="Icon.skill1389">
																							<tr><td width=28 height=28 align=center valign=center>
																									<button action="bypass _bbsbufferbypass_redirect view_special 0 0" width=30 height=30 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
																								</td>
																							</tr>
																						</table>
																					</td>
																					<td width=60 align=center valign=center>
																						<font name=hs9 color="FFFFFF">Special</font>
																					</td>
																				</tr>
																			</table>
																		</td>
																		<td width=92 height=30 align=center>
																			<table border=0 width=92 height=30 cellspacing=1 cellpadding=1>
																				<tr>
																					<td width=60 align=center valign=center>
																						<font name=hs9 color="FFFFFF">Dwarf</font>
																					</td>
																					<td width=28 align=center valign=center>
																						<table border=0 cellspacing=0 cellpadding=0 width=28 height=28 background="Icon.skill0825">
																							<tr><td width=28 height=28 align=center valign=center>
																									<button action="bypass _bbsbufferbypass_redirect view_others 0 0" width=30 height=30 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
																								</td>
																							</tr>
																						</table>
																					</td>
																				</tr>
																			</table>
																		</td>
																	</tr>
																	<tr>
																		<td width=92 height=30 align=center>
																			<table border=0 width=92 height=30 cellspacing=1 cellpadding=1>
																				<tr>
																					<td width=28 align=center valign=center>
																						<table border=0 cellspacing=0 cellpadding=0 width=28 height=28 background="Icon.skill0364">
																							<tr><td width=28 height=28 align=center valign=center>
																									<button action="bypass _bbsbufferbypass_redirect view_songs 0 0" width=30 height=30 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
																								</td>
																							</tr>
																						</table>
																					</td>
																					<td width=60 align=center valign=center>
																						<font name=hs9 color="FFFFFF">Songs</font>
																					</td>
																				</tr>
																			</table>
																		</td>
																		<td width=92 height=30 align=center>
																			<table border=0 width=92 height=30 cellspacing=1 cellpadding=1>
																				<tr>
																					<td width=60 align=center valign=center>
																						<font name=hs9 color="FFFFFF">Dances</font>
																					</td>
																					<td width=28 align=center valign=center>
																						<table border=0 cellspacing=0 cellpadding=0 width=28 height=28 background="Icon.skill0310">
																							<tr><td width=28 height=28 align=center valign=center>
																									<button action="bypass _bbsbufferbypass_redirect view_dances 0 0" width=30 height=30 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
																								</td>
																							</tr>
																						</table>
																					</td>
																				</tr>
																			</table>
																		</td>
																	</tr>
																</table>
															</td>
														</tr>
													</table>
												</td>
												<!-- Custom Schemes Column -->
												<td width=195 height=440 align=center valign=top>
													<table border=0 cellspacing=1 cellpadding=1 height=440 width=195>
														<tr>
															<td width=195 height=25 align=center>
																<table>
																	<tr>
																		<td width=185 height=22 align=center background="l2ui_ct1.ListCTRL_DF_Decoration">
																			<font color=556B2F name=hs12>Your Custom Schemes</font>
																		</td>
																	</tr>
																</table>
															</td>
														</tr>
														%schemePart%
													</table>
												</td>
											</tr>
										</table>
									</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
	</center>
</body>
</html>
