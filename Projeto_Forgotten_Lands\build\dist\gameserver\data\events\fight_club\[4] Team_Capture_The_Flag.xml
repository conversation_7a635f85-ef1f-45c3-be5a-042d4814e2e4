<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE list SYSTEM "../events.dtd">
<list>
	<event id="4" name="Capture The Flag" type="FIGHT_CLUB_EVENT" impl="fightclub.CaptureTheFlag">
		<parameter name="icon" value="Icon.etc_bloodpledge_point_i00"/>
		<parameter name="desc" value="Team Event. Try to steal Flag that is placed near enemy spawn location and bring it to your territory. Don't forget to defend your flag, we don't want it to be hold by enemy hands! Event Lasts 10 minutes."/>
		<parameter name="roundRunTime" value="600"/>
		<parameter name="roundEvent" value="false"/>
		<parameter name="isAutoTimed" value="true"/>
		<parameter name="autoTimes" value="02:00,10:00,18:00"/>
		<parameter name="teamed" value="true"/>
		<parameter name="buffer" value="false"/>
		<parameter name="fighterBuffs" value="1204,2;1086,2;1068,3;1045,6;1048,6;1040,3;1062,2;1036,2"/>
		<parameter name="mageBuffs" value="1204,2;1085,3;1059,3;1045,6;1048,6;1040,3;1062,2;1036,2"/>
		<parameter name="rootBetweenRounds" value="true"/>
		<parameter name="excludedClasses" value=""/>
		<parameter name="excludedSkills" value="922"/>
		<parameter name="respawnTime" value="5"/>
		<parameter name="ressAllowed" value="false"/>
		<parameter name="showPersonality" value="true"/>
		<parameter name="badgesKillPlayer" value="0"/>
		<parameter name="badgesKillPet" value="0"/>
		<parameter name="badgesDie" value="0"/>
		<parameter name="badgesCaptureFlag" value="1"/>
		<parameter name="badgesWin" value="6"/>
		<parameter name="badgesLose" value="2"/>
		<parameter name="topKillerReward" value="3"/>
	</event>
</list>