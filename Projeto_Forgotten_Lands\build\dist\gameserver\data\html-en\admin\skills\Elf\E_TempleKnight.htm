<title>Choose skill to add</title>
<center>
[admin_add_skill 316 1|Aegis 1 (Lv 60)]%%
[admin_add_skill 28 13|Aggression 13 (Lv 40)]%%
[admin_add_skill 28 14|Aggression 14 (Lv 40)]%%
[admin_add_skill 28 15|Aggression 15 (Lv 40)]%%
[admin_add_skill 28 16|Aggression 16 (Lv 43)]%%
[admin_add_skill 28 17|Aggression 17 (Lv 43)]%%
[admin_add_skill 28 18|Aggression 18 (Lv 43)]%%
[admin_add_skill 28 19|Aggression 19 (Lv 46)]%%
[admin_add_skill 28 20|Aggression 20 (Lv 46)]%%
[admin_add_skill 28 21|Aggression 21 (Lv 46)]%%
[admin_add_skill 28 22|Aggression 22 (Lv 49)]%%
[admin_add_skill 28 23|Aggression 23 (Lv 49)]%%
[admin_add_skill 28 24|Aggression 24 (Lv 49)]%%
[admin_add_skill 28 25|Aggression 25 (Lv 52)]%%
[admin_add_skill 28 26|Aggression 26 (Lv 52)]%%
[admin_add_skill 28 27|Aggression 27 (Lv 52)]%%
[admin_add_skill 28 28|Aggression 28 (Lv 55)]%%
[admin_add_skill 28 29|Aggression 29 (Lv 55)]%%
[admin_add_skill 28 30|Aggression 30 (Lv 55)]%%
[admin_add_skill 28 31|Aggression 31 (Lv 58)]%%
[admin_add_skill 28 32|Aggression 32 (Lv 58)]%%
[admin_add_skill 28 33|Aggression 33 (Lv 58)]%%
[admin_add_skill 28 34|Aggression 34 (Lv 60)]%%
[admin_add_skill 28 35|Aggression 35 (Lv 60)]%%
[admin_add_skill 28 36|Aggression 36 (Lv 62)]%%
[admin_add_skill 28 37|Aggression 37 (Lv 62)]%%
[admin_add_skill 28 38|Aggression 38 (Lv 64)]%%
[admin_add_skill 28 39|Aggression 39 (Lv 64)]%%
[admin_add_skill 28 40|Aggression 40 (Lv 66)]%%
[admin_add_skill 28 41|Aggression 41 (Lv 66)]%%
[admin_add_skill 28 42|Aggression 42 (Lv 68)]%%
[admin_add_skill 28 43|Aggression 43 (Lv 68)]%%
[admin_add_skill 28 44|Aggression 44 (Lv 70)]%%
[admin_add_skill 28 45|Aggression 45 (Lv 70)]%%
[admin_add_skill 28 46|Aggression 46 (Lv 72)]%%
[admin_add_skill 28 47|Aggression 47 (Lv 72)]%%
[admin_add_skill 28 48|Aggression 48 (Lv 74)]%%
[admin_add_skill 28 49|Aggression 49 (Lv 74)]%%
[admin_add_skill 15 16|Charm 16 (Lv 40)]%%
[admin_add_skill 15 17|Charm 17 (Lv 40)]%%
[admin_add_skill 15 18|Charm 18 (Lv 40)]%%
[admin_add_skill 15 19|Charm 19 (Lv 43)]%%
[admin_add_skill 15 20|Charm 20 (Lv 43)]%%
[admin_add_skill 15 21|Charm 21 (Lv 43)]%%
[admin_add_skill 15 22|Charm 22 (Lv 46)]%%
[admin_add_skill 15 23|Charm 23 (Lv 46)]%%
[admin_add_skill 15 24|Charm 24 (Lv 46)]%%
[admin_add_skill 15 25|Charm 25 (Lv 49)]%%
[admin_add_skill 15 26|Charm 26 (Lv 49)]%%
[admin_add_skill 15 27|Charm 27 (Lv 49)]%%
[admin_add_skill 15 28|Charm 28 (Lv 52)]%%
[admin_add_skill 15 29|Charm 29 (Lv 52)]%%
[admin_add_skill 15 30|Charm 30 (Lv 52)]%%
[admin_add_skill 15 31|Charm 31 (Lv 55)]%%
[admin_add_skill 15 32|Charm 32 (Lv 55)]%%
[admin_add_skill 15 33|Charm 33 (Lv 55)]%%
[admin_add_skill 15 34|Charm 34 (Lv 58)]%%
[admin_add_skill 15 35|Charm 35 (Lv 58)]%%
[admin_add_skill 15 36|Charm 36 (Lv 58)]%%
[admin_add_skill 15 37|Charm 37 (Lv 60)]%%
[admin_add_skill 15 38|Charm 38 (Lv 60)]%%
[admin_add_skill 15 39|Charm 39 (Lv 62)]%%
[admin_add_skill 15 40|Charm 40 (Lv 62)]%%
[admin_add_skill 15 41|Charm 41 (Lv 64)]%%
[admin_add_skill 15 42|Charm 42 (Lv 64)]%%
[admin_add_skill 15 43|Charm 43 (Lv 66)]%%
[admin_add_skill 15 44|Charm 44 (Lv 66)]%%
[admin_add_skill 15 45|Charm 45 (Lv 68)]%%
[admin_add_skill 15 46|Charm 46 (Lv 68)]%%
[admin_add_skill 15 47|Charm 47 (Lv 70)]%%
[admin_add_skill 15 48|Charm 48 (Lv 70)]%%
[admin_add_skill 15 49|Charm 49 (Lv 72)]%%
[admin_add_skill 15 50|Charm 50 (Lv 72)]%%
[admin_add_skill 15 51|Charm 51 (Lv 74)]%%
[admin_add_skill 15 52|Charm 52 (Lv 74)]%%
[admin_add_skill 143 1|Cubic Mastery 1 (Lv 43)]%%
[admin_add_skill 143 2|Cubic Mastery 2 (Lv 55)]%%
[admin_add_skill 61 2|Cure Bleeding 2 (Lv 46)]%%
[admin_add_skill 61 3|Cure Bleeding 3 (Lv 62)]%%
[admin_add_skill 112 3|Deflect Arrow 3 (Lv 43)]%%
[admin_add_skill 112 4|Deflect Arrow 4 (Lv 49)]%%
[admin_add_skill 58 19|Elemental Heal 19 (Lv 40)]%%
[admin_add_skill 58 20|Elemental Heal 20 (Lv 40)]%%
[admin_add_skill 58 21|Elemental Heal 21 (Lv 40)]%%
[admin_add_skill 58 22|Elemental Heal 22 (Lv 43)]%%
[admin_add_skill 58 23|Elemental Heal 23 (Lv 43)]%%
[admin_add_skill 58 24|Elemental Heal 24 (Lv 43)]%%
<br>
<button value="Page2" action="bypass -h admin_skill_index Elf/E_TempleKnight2" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index Elf/E_TempleKnight3" width=40 height=15><br>
<button value="Page4" action="bypass -h admin_skill_index Elf/E_TempleKnight4" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
