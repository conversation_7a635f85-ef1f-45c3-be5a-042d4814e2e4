<title>Choose skill to add</title>
<center>
[admin_add_skill 1069 15|Sleep 15 (Lv 44)]%%
[admin_add_skill 1069 16|Sleep 16 (Lv 48)]%%
[admin_add_skill 1069 17|Sleep 17 (Lv 48)]%%
[admin_add_skill 1069 18|Sleep 18 (Lv 48)]%%
[admin_add_skill 1069 19|Sleep 19 (Lv 52)]%%
[admin_add_skill 1069 20|Sleep 20 (Lv 52)]%%
[admin_add_skill 1069 21|Sleep 21 (Lv 52)]%%
[admin_add_skill 1069 22|Sleep 22 (Lv 56)]%%
[admin_add_skill 1069 23|Sleep 23 (Lv 56)]%%
[admin_add_skill 1069 24|Sleep 24 (Lv 56)]%%
[admin_add_skill 1069 25|Sleep 25 (Lv 58)]%%
[admin_add_skill 1069 26|Sleep 26 (Lv 58)]%%
[admin_add_skill 1069 27|Sleep 27 (Lv 60)]%%
[admin_add_skill 1069 28|Sleep 28 (Lv 60)]%%
[admin_add_skill 1069 29|Sleep 29 (Lv 62)]%%
[admin_add_skill 1069 30|Sleep 30 (Lv 62)]%%
[admin_add_skill 1069 31|Sleep 31 (Lv 64)]%%
[admin_add_skill 1069 32|Sleep 32 (Lv 64)]%%
[admin_add_skill 1069 33|Sleep 33 (Lv 66)]%%
[admin_add_skill 1069 34|Sleep 34 (Lv 66)]%%
[admin_add_skill 1069 35|Sleep 35 (Lv 68)]%%
[admin_add_skill 1069 36|Sleep 36 (Lv 68)]%%
[admin_add_skill 1069 37|Sleep 37 (Lv 70)]%%
[admin_add_skill 1069 38|Sleep 38 (Lv 70)]%%
[admin_add_skill 1069 39|Sleep 39 (Lv 72)]%%
[admin_add_skill 1069 40|Sleep 40 (Lv 72)]%%
[admin_add_skill 1069 41|Sleep 41 (Lv 74)]%%
[admin_add_skill 1069 42|Sleep 42 (Lv 74)]%%
[admin_add_skill 1074 1|Surrender To Wind 1 (Lv 40)]%%
[admin_add_skill 1074 2|Surrender To Wind 2 (Lv 44)]%%
[admin_add_skill 1074 3|Surrender To Wind 3 (Lv 48)]%%
[admin_add_skill 1074 4|Surrender To Wind 4 (Lv 52)]%%
[admin_add_skill 1074 5|Surrender To Wind 5 (Lv 56)]%%
[admin_add_skill 1074 6|Surrender To Wind 6 (Lv 58)]%%
[admin_add_skill 1074 7|Surrender To Wind 7 (Lv 60)]%%
[admin_add_skill 1074 8|Surrender To Wind 8 (Lv 62)]%%
[admin_add_skill 1074 9|Surrender To Wind 9 (Lv 64)]%%
[admin_add_skill 1074 10|Surrender To Wind 10 (Lv 66)]%%
[admin_add_skill 1074 11|Surrender To Wind 11 (Lv 68)]%%
[admin_add_skill 1074 12|Surrender To Wind 12 (Lv 70)]%%
[admin_add_skill 1074 13|Surrender To Wind 13 (Lv 72)]%%
[admin_add_skill 1074 14|Surrender To Wind 14 (Lv 74)]%%
[admin_add_skill 1148 1|Death Spike 1 (Lv 44)]%%
[admin_add_skill 1148 2|Death Spike 2 (Lv 48)]%%
[admin_add_skill 1148 3|Death Spike 3 (Lv 52)]%%
[admin_add_skill 1148 4|Death Spike 4 (Lv 56)]%%
[admin_add_skill 1148 5|Death Spike 5 (Lv 58)]%%
[admin_add_skill 1148 6|Death Spike 6 (Lv 60)]%%
[admin_add_skill 1148 7|Death Spike 7 (Lv 62)]%%
[admin_add_skill 1148 8|Death Spike 8 (Lv 64)]%%
[admin_add_skill 1148 9|Death Spike 9 (Lv 66)]%%
[admin_add_skill 1148 10|Death Spike 10 (Lv 68)]%%
[admin_add_skill 1148 11|Death Spike 11 (Lv 70)]%%
[admin_add_skill 1148 12|Death Spike 12 (Lv 72)]%%
[admin_add_skill 1148 13|Death Spike 13 (Lv 74)]%%
[admin_add_skill 1151 3|Corpse Life Drain 3 (Lv 40)]%%
[admin_add_skill 1151 4|Corpse Life Drain 4 (Lv 44)]%%
[admin_add_skill 1151 5|Corpse Life Drain 5 (Lv 48)]%%
[admin_add_skill 1151 6|Corpse Life Drain 6 (Lv 52)]%%
[admin_add_skill 1151 7|Corpse Life Drain 7 (Lv 56)]%%
[admin_add_skill 1151 8|Corpse Life Drain 8 (Lv 58)]%%
[admin_add_skill 1151 9|Corpse Life Drain 9 (Lv 60)]%%
[admin_add_skill 1151 10|Corpse Life Drain 10 (Lv 62)]%%
[admin_add_skill 1151 11|Corpse Life Drain 11 (Lv 64)]%%
[admin_add_skill 1151 12|Corpse Life Drain 12 (Lv 66)]%%
[admin_add_skill 1151 13|Corpse Life Drain 13 (Lv 68)]%%
[admin_add_skill 1151 14|Corpse Life Drain 14 (Lv 70)]%%
[admin_add_skill 1151 15|Corpse Life Drain 15 (Lv 72)]%%
[admin_add_skill 1151 16|Corpse Life Drain 16 (Lv 74)]%%
[admin_add_skill 1157 2|Body To Mind 2 (Lv 40)]%%
[admin_add_skill 1157 3|Body To Mind 3 (Lv 52)]%%
[admin_add_skill 1157 4|Body To Mind 4 (Lv 58)]%%
[admin_add_skill 1157 5|Body To Mind 5 (Lv 66)]%%
[admin_add_skill 1159 1|Curse Death Link 1 (Lv 52)]%%
[admin_add_skill 1159 2|Curse Death Link 2 (Lv 52)]%%
[admin_add_skill 1159 3|Curse Death Link 3 (Lv 56)]%%
[admin_add_skill 1159 4|Curse Death Link 4 (Lv 56)]%%
[admin_add_skill 1159 5|Curse Death Link 5 (Lv 58)]%%
[admin_add_skill 1159 6|Curse Death Link 6 (Lv 58)]%%
[admin_add_skill 1159 7|Curse Death Link 7 (Lv 60)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index DarkElf/DE_Spellhowler" width=40 height=15><br>
<button value="Page2" action="bypass -h admin_skill_index DarkElf/DE_Spellhowler2" width=40 height=15><br>
<button value="Page4" action="bypass -h admin_skill_index DarkElf/DE_Spellhowler4" width=40 height=15><br>
<button value="Page5" action="bypass -h admin_skill_index DarkElf/DE_Spellhowler5" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
