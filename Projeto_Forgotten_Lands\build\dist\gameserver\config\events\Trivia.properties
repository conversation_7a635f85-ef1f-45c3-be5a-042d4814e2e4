#=====================================================================================
#          			Trivia aka <PERSON>ina Configuration
#=====================================================================================
# Enable Trivia Event
Trivia_Enabled = True

# Remove the question whether, after he was true or false
# (To avoid repetition of questions)
Trivia_Remove_Question = False

# Remove the question whether, if it has not been answered,
# Operates in conjunction with VIKTORINA_REMOVE_QUESTION
Trivia_Remove_Question_No_Answer = False

# Set time when to start the event.
Trivia_Start_Time_Hour = 16
Trivia_Start_Time_Minute = 20

# How many hours will trivia active after start
Trivia_Work_Time = 2

# Time to wait for answer. in sec
Trivia_Time_Answer = 60

# Pause after time for answer end. in sec
Trivia_Time_Pause = 60

# For example, in this config pause between questions will be 2 minutes.
# But only during the first minute you can give an answer to the question
# Lists of awards. REWARD_FERST list of awards to those who answered the first question
# REWARD_OTHER to others who answered correctly.
# Format of the list id, count , chance;id,count,chance......
# Reward only the first who asnwer
Trivia_Reward_First = 57,1,100;57,2,100;

# This will reward others 
Trivia_Reward_Other = 57,1,100;57,2,100;