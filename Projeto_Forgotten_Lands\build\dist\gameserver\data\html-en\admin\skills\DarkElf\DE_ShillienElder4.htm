<title>Choose skill to add</title>
<center>
[admin_add_skill 1206 13|Wind Shackle 13 (Lv 62)]%%
[admin_add_skill 1206 14|Wind Shackle 14 (Lv 64)]%%
[admin_add_skill 1206 15|Wind Shackle 15 (Lv 66)]%%
[admin_add_skill 1206 16|Wind Shackle 16 (Lv 68)]%%
[admin_add_skill 1206 17|Wind Shackle 17 (Lv 70)]%%
[admin_add_skill 1206 18|Wind Shackle 18 (Lv 72)]%%
[admin_add_skill 1206 19|Wind Shackle 19 (Lv 74)]%%
[admin_add_skill 1217 1|Greater Heal 1 (Lv 48)]%%
[admin_add_skill 1217 2|Greater Heal 2 (Lv 48)]%%
[admin_add_skill 1217 3|Greater Heal 3 (Lv 48)]%%
[admin_add_skill 1217 4|Greater Heal 4 (Lv 52)]%%
[admin_add_skill 1217 5|Greater Heal 5 (Lv 52)]%%
[admin_add_skill 1217 6|Greater Heal 6 (Lv 52)]%%
[admin_add_skill 1217 7|Greater Heal 7 (Lv 56)]%%
[admin_add_skill 1217 8|Greater Heal 8 (Lv 56)]%%
[admin_add_skill 1217 9|Greater Heal 9 (Lv 56)]%%
[admin_add_skill 1217 10|Greater Heal 10 (Lv 58)]%%
[admin_add_skill 1217 11|Greater Heal 11 (Lv 58)]%%
[admin_add_skill 1217 12|Greater Heal 12 (Lv 58)]%%
[admin_add_skill 1217 13|Greater Heal 13 (Lv 60)]%%
[admin_add_skill 1217 14|Greater Heal 14 (Lv 60)]%%
[admin_add_skill 1217 15|Greater Heal 15 (Lv 60)]%%
[admin_add_skill 1217 16|Greater Heal 16 (Lv 62)]%%
[admin_add_skill 1217 17|Greater Heal 17 (Lv 62)]%%
[admin_add_skill 1217 18|Greater Heal 18 (Lv 64)]%%
[admin_add_skill 1217 19|Greater Heal 19 (Lv 64)]%%
[admin_add_skill 1217 20|Greater Heal 20 (Lv 66)]%%
[admin_add_skill 1217 21|Greater Heal 21 (Lv 66)]%%
[admin_add_skill 1217 22|Greater Heal 22 (Lv 68)]%%
[admin_add_skill 1217 23|Greater Heal 23 (Lv 68)]%%
[admin_add_skill 1217 24|Greater Heal 24 (Lv 70)]%%
[admin_add_skill 1217 25|Greater Heal 25 (Lv 70)]%%
[admin_add_skill 1217 26|Greater Heal 26 (Lv 72)]%%
[admin_add_skill 1217 27|Greater Heal 27 (Lv 72)]%%
[admin_add_skill 1217 28|Greater Heal 28 (Lv 74)]%%
[admin_add_skill 1217 29|Greater Heal 29 (Lv 74)]%%
[admin_add_skill 1219 1|Greater Group Heal 1 (Lv 48)]%%
[admin_add_skill 1219 2|Greater Group Heal 2 (Lv 48)]%%
[admin_add_skill 1219 3|Greater Group Heal 3 (Lv 48)]%%
[admin_add_skill 1219 4|Greater Group Heal 4 (Lv 52)]%%
[admin_add_skill 1219 5|Greater Group Heal 5 (Lv 52)]%%
[admin_add_skill 1219 6|Greater Group Heal 6 (Lv 52)]%%
[admin_add_skill 1219 7|Greater Group Heal 7 (Lv 56)]%%
[admin_add_skill 1219 8|Greater Group Heal 8 (Lv 56)]%%
[admin_add_skill 1219 9|Greater Group Heal 9 (Lv 56)]%%
[admin_add_skill 1219 10|Greater Group Heal 10 (Lv 58)]%%
[admin_add_skill 1219 11|Greater Group Heal 11 (Lv 58)]%%
[admin_add_skill 1219 12|Greater Group Heal 12 (Lv 58)]%%
[admin_add_skill 1219 13|Greater Group Heal 13 (Lv 60)]%%
[admin_add_skill 1219 14|Greater Group Heal 14 (Lv 60)]%%
[admin_add_skill 1219 15|Greater Group Heal 15 (Lv 60)]%%
[admin_add_skill 1219 16|Greater Group Heal 16 (Lv 62)]%%
[admin_add_skill 1219 17|Greater Group Heal 17 (Lv 62)]%%
[admin_add_skill 1219 18|Greater Group Heal 18 (Lv 64)]%%
[admin_add_skill 1219 19|Greater Group Heal 19 (Lv 64)]%%
[admin_add_skill 1219 20|Greater Group Heal 20 (Lv 66)]%%
[admin_add_skill 1219 21|Greater Group Heal 21 (Lv 66)]%%
[admin_add_skill 1219 22|Greater Group Heal 22 (Lv 68)]%%
[admin_add_skill 1219 23|Greater Group Heal 23 (Lv 68)]%%
[admin_add_skill 1219 24|Greater Group Heal 24 (Lv 70)]%%
[admin_add_skill 1219 25|Greater Group Heal 25 (Lv 70)]%%
[admin_add_skill 1219 26|Greater Group Heal 26 (Lv 72)]%%
[admin_add_skill 1219 27|Greater Group Heal 27 (Lv 72)]%%
[admin_add_skill 1219 28|Greater Group Heal 28 (Lv 74)]%%
[admin_add_skill 1219 29|Greater Group Heal 29 (Lv 74)]%%
[admin_add_skill 1240 1|Guidance 1 (Lv 40)]%%
[admin_add_skill 1240 2|Guidance 2 (Lv 48)]%%
[admin_add_skill 1240 3|Guidance 3 (Lv 56)]%%
[admin_add_skill 1242 1|Death Whisper 1 (Lv 40)]%%
[admin_add_skill 1242 2|Death Whisper 2 (Lv 48)]%%
[admin_add_skill 1242 3|Death Whisper 3 (Lv 56)]%%
[admin_add_skill 1268 2|Vampiric Rage 2 (Lv 44)]%%
[admin_add_skill 1268 3|Vampiric Rage 3 (Lv 58)]%%
[admin_add_skill 1268 4|Vampiric Rage 4 (Lv 72)]%%
[admin_add_skill 1303 1|Wild Magic 1 (Lv 62)]%%
[admin_add_skill 1303 2|Wild Magic 2 (Lv 70)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index DarkElf/DE_ShillienElder" width=40 height=15><br>
<button value="Page2" action="bypass -h admin_skill_index DarkElf/DE_ShillienElder2" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index DarkElf/DE_ShillienElder3" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
