# -----------------------------------------
#  Captcha System 
# -----------------------------------------
# Enable captcha system.
# Its made to be used against clickers and macro users.
# If true will check mobs killed... and etc..
# Default: false
EnableCaptchaSystem = False

# It calculate min/max monsters number then it activate captcha
# Set range for mobs.
CaptchaMinMonstertokill = 1000
CaptchaMaxMonstertokill = 2000

# If player is still in 1 location for the given delay in SECONDS and made atleast this amount of kills, request captcha. Its used to prevent macro abuse.
# Set either of those values to -1 to disable the feature.
# Setting delay to 0 will only count kills from the same location. Setting kills to 0 will account only delay.
CaptchaSameLocationDelay = 0
CaptchaSameLocationMinKills = 5

# Unequip weapon
CaptchaUnequipWeapon = true

# How many attempts player have to enter correct captcha before get punished.
# Default: 3
CaptchaAttempts = 3

# Punishment if the checked player fail on all attempts or does not enter anything for 3 minutes.
# Options : KICK, BANCHAR, JAIL
# Default should be KICK..
CaptchaPunishmentType = KICK

# If the punishment is banchar or jail. it will ban them for .... minutes...
# Default: -1 = permanent
CaptchaPunishmentTime = -1

# Delay between being Tested one more time
# Restarting character is removing that Timer
# Don't Make it less than CaptchaAnswerTime
# Set Value in Seconds
CaptchaDelayBetweenTests = 600

# Delay between using .report
# It is being cancelled, when targeted player was Bot
# Delay is set to Account Name + IP + HWID
# Set Value in Seconds
CaptchaReportDelay = 1800

# Minimum level to try .report on somebody
CaptchaMinLevel = 40