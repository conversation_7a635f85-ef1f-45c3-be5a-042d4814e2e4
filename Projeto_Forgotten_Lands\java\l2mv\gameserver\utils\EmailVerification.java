package l2mv.gameserver.utils;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Properties;
import java.util.Random;

import javax.mail.Message;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import l2mv.commons.dbutils.DbUtils;
import l2mv.gameserver.Config;
import l2mv.gameserver.database.DatabaseFactory;
import l2mv.gameserver.model.Player;

/**
 * Email Verification System
 * <AUTHOR>
 */
public class EmailVerification
{
    private static final Logger _log = LoggerFactory.getLogger(EmailVerification.class);
    
    private static final int CODE_LENGTH = 6;
    private static final long CODE_EXPIRY_TIME = 15 * 60 * 1000; // 15 minutes
    
    /**
     * Generates a random verification code
     */
    public static String generateVerificationCode()
    {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        
        for (int i = 0; i < CODE_LENGTH; i++)
        {
            code.append(random.nextInt(10));
        }
        
        return code.toString();
    }
    
    /**
     * Stores verification code in database with expiry time
     */
    public static void storeVerificationCode(String accountName, String email, String code)
    {
        long expiryTime = System.currentTimeMillis() + CODE_EXPIRY_TIME;
        
        // Store pending email
        AccountEmail.insertAccountData(accountName, "pending_email", email);
        
        // Store verification code
        AccountEmail.insertAccountData(accountName, "email_verification_code", code);
        
        // Store expiry time
        AccountEmail.insertAccountData(accountName, "email_code_expiry", String.valueOf(expiryTime));
        
        _log.info("Verification code stored for account: " + accountName + ", email: " + email);
    }
    
    /**
     * Validates verification code
     */
    public static boolean validateVerificationCode(String accountName, String inputCode)
    {
        String storedCode = AccountEmail.getAccountValue(accountName, "email_verification_code");
        String expiryTimeStr = AccountEmail.getAccountValue(accountName, "email_code_expiry");
        
        if (storedCode == null || expiryTimeStr == null)
        {
            return false;
        }
        
        // Check if code has expired
        long expiryTime = Long.parseLong(expiryTimeStr);
        if (System.currentTimeMillis() > expiryTime)
        {
            clearVerificationData(accountName);
            return false;
        }
        
        // Check if code matches
        return storedCode.equals(inputCode);
    }
    
    /**
     * Confirms email verification and activates email
     */
    public static boolean confirmEmailVerification(String accountName, String inputCode)
    {
        if (!validateVerificationCode(accountName, inputCode))
        {
            return false;
        }
        
        // Get pending email
        String pendingEmail = AccountEmail.getAccountValue(accountName, "pending_email");
        if (pendingEmail == null)
        {
            return false;
        }
        
        // Activate email
        AccountEmail.setEmail(accountName, pendingEmail);
        
        // Mark as verified
        AccountEmail.insertAccountData(accountName, "email_verified", "true");
        
        // Clear verification data
        clearVerificationData(accountName);
        
        _log.info("Email verified and activated for account: " + accountName + ", email: " + pendingEmail);
        return true;
    }
    
    /**
     * Clears verification data from database
     */
    public static void clearVerificationData(String accountName)
    {
        Connection con = null;
        PreparedStatement statement = null;
        
        try
        {
            con = DatabaseFactory.getInstance().getConnection();
            statement = con.prepareStatement("DELETE FROM account_variables WHERE account_name=? AND var IN ('pending_email', 'email_verification_code', 'email_code_expiry')");
            statement.setString(1, accountName);
            statement.executeUpdate();
        }
        catch (Exception e)
        {
            _log.warn("Cannot clear verification data.", e);
        }
        finally
        {
            DbUtils.closeQuietly(con, statement);
        }
    }
    
    /**
     * Sends verification email with code
     */
    public static boolean sendVerificationEmail(String email, String code, String playerName)
    {
        if (Config.MAIL_USER.isEmpty() || Config.MAIL_PASS.isEmpty())
        {
            _log.warn("Email configuration not set. Cannot send verification email.");
            return false;
        }
        
        try
        {
            Properties props = new Properties();
            props.put("mail.smtp.host", "smtp.gmail.com");
            props.put("mail.smtp.socketFactory.port", "465");
            props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.port", "465");
            
            Session session = Session.getInstance(props, new javax.mail.Authenticator() {
                protected javax.mail.PasswordAuthentication getPasswordAuthentication() {
                    return new javax.mail.PasswordAuthentication(Config.MAIL_USER, Config.MAIL_PASS);
                }
            });
            
            Message message = new MimeMessage(session);
            message.setFrom(new InternetAddress(Config.MAIL_USER));
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(email));
            message.setSubject("Email Verification - " + Config.SERVER_NAME);
            
            String emailContent = buildEmailContent(code, playerName);
            message.setContent(emailContent, "text/html; charset=utf-8");
            
            Transport.send(message);
            
            _log.info("Verification email sent to: " + email);
            return true;
        }
        catch (Exception e)
        {
            _log.error("Failed to send verification email to: " + email, e);
            return false;
        }
    }
    
    /**
     * Builds HTML email content
     */
    private static String buildEmailContent(String code, String playerName)
    {
        StringBuilder html = new StringBuilder();
        html.append("<html><body>");
        html.append("<h2>Email Verification - ").append(Config.SERVER_NAME).append("</h2>");
        html.append("<p>Hello ").append(playerName).append(",</p>");
        html.append("<p>Thank you for registering your email address with us.</p>");
        html.append("<p>Your verification code is: <strong style='font-size: 18px; color: #007bff;'>").append(code).append("</strong></p>");
        html.append("<p>Please enter this code in the game to verify your email address.</p>");
        html.append("<p><strong>Important:</strong> This code will expire in 15 minutes.</p>");
        html.append("<p>If you did not request this verification, please ignore this email.</p>");
        html.append("<br>");
        html.append("<p>Best regards,<br>").append(Config.SERVER_NAME).append(" Team</p>");
        html.append("</body></html>");
        
        return html.toString();
    }
    
    /**
     * Checks if email is already verified
     */
    public static boolean isEmailVerified(String accountName)
    {
        String verified = AccountEmail.getAccountValue(accountName, "email_verified");
        return "true".equals(verified);
    }
    
    /**
     * Gets pending email for account
     */
    public static String getPendingEmail(String accountName)
    {
        return AccountEmail.getAccountValue(accountName, "pending_email");
    }
}
