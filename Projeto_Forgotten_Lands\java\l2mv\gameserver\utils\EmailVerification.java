package l2mv.gameserver.utils;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.Random;

import javax.mail.Message;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import l2mv.commons.dbutils.DbUtils;
import l2mv.gameserver.Config;
import l2mv.gameserver.database.DatabaseFactory;
import l2mv.gameserver.donation.DonationReader;

/**
 * Email Verification System
 * <AUTHOR>
 */
public class EmailVerification
{
    private static final Logger _log = LoggerFactory.getLogger(EmailVerification.class);
    
    private static final int CODE_LENGTH = 6;
    private static final long CODE_EXPIRY_TIME = 15 * 60 * 1000; // 15 minutes
    
    /**
     * Generates a random verification code
     */
    public static String generateVerificationCode()
    {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        
        for (int i = 0; i < CODE_LENGTH; i++)
        {
            code.append(random.nextInt(10));
        }
        
        return code.toString();
    }
    
    /**
     * Stores verification code in database with expiry time
     */
    public static void storeVerificationCode(String accountName, String email, String code)
    {
        long expiryTime = System.currentTimeMillis() + CODE_EXPIRY_TIME;
        
        // Store pending email
        AccountEmail.insertAccountData(accountName, "email_pending", email);

        // Store verification code
        AccountEmail.insertAccountData(accountName, "email_code", code);

        // Store expiry time
        AccountEmail.insertAccountData(accountName, "email_expiry", String.valueOf(expiryTime));
        
        _log.info("Verification code stored for account: " + accountName + ", email: " + email);
    }
    
    /**
     * Validates verification code
     */
    public static boolean validateVerificationCode(String accountName, String inputCode)
    {
        String storedCode = AccountEmail.getAccountValue(accountName, "email_code");
        String expiryTimeStr = AccountEmail.getAccountValue(accountName, "email_expiry");
        
        if (storedCode == null || expiryTimeStr == null)
        {
            return false;
        }
        
        // Check if code has expired
        long expiryTime = Long.parseLong(expiryTimeStr);
        if (System.currentTimeMillis() > expiryTime)
        {
            clearVerificationData(accountName);
            return false;
        }
        
        // Check if code matches
        return storedCode.equals(inputCode);
    }
    
    /**
     * Confirms email verification and activates email
     */
    public static boolean confirmEmailVerification(String accountName, String inputCode)
    {
        if (!validateVerificationCode(accountName, inputCode))
        {
            return false;
        }
        
        // Get pending email
        String pendingEmail = AccountEmail.getAccountValue(accountName, "email_pending");
        if (pendingEmail == null)
        {
            return false;
        }
        
        // Activate email
        AccountEmail.setEmail(accountName, pendingEmail);
        
        // Mark as verified
        AccountEmail.insertAccountData(accountName, "email_verified", "true");
        
        // Clear verification data
        clearVerificationData(accountName);
        
        _log.info("Email verified and activated for account: " + accountName + ", email: " + pendingEmail);
        return true;
    }
    
    /**
     * Clears verification data from database
     */
    public static void clearVerificationData(String accountName)
    {
        Connection con = null;
        PreparedStatement statement = null;
        
        try
        {
            con = DatabaseFactory.getInstance().getConnection();
            statement = con.prepareStatement("DELETE FROM account_variables WHERE account_name=? AND var IN ('email_pending', 'email_code', 'email_expiry')");
            statement.setString(1, accountName);
            statement.executeUpdate();
        }
        catch (Exception e)
        {
            _log.warn("Cannot clear verification data.", e);
        }
        finally
        {
            DbUtils.closeQuietly(con, statement);
        }
    }
    
    /**
     * Sends verification email with code using existing donation system
     */
    public static boolean sendVerificationEmail(String email, String code, String playerName)
    {
        _log.info("Checking email configuration - MAIL_USER: '" + Config.MAIL_USER + "', MAIL_PASS: '" + (Config.MAIL_PASS.isEmpty() ? "EMPTY" : "SET") + "'");

        if (Config.MAIL_USER == null || Config.MAIL_USER.isEmpty() || Config.MAIL_PASS == null || Config.MAIL_PASS.isEmpty())
        {
            _log.warn("Email configuration not set. MAIL_USER: '" + Config.MAIL_USER + "', MAIL_PASS: " + (Config.MAIL_PASS == null ? "NULL" : (Config.MAIL_PASS.isEmpty() ? "EMPTY" : "SET")));
            return false;
        }

        try
        {
            // Use the existing donation system's session
            if (DonationReader.getInstance().getSession() == null)
            {
                _log.warn("DonationReader session not available. Email system not initialized.");
                return false;
            }

            Message message = new MimeMessage(DonationReader.getInstance().getSession());
            message.setFrom(new InternetAddress(Config.MAIL_USER));
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(email));
            message.setSubject(Config.MAIL_SUBJECT);

            // Use the configured message template with %PIN% replacement
            String emailContent = Config.MAIL_MESSAGE.replace("%PIN%", code);
            message.setText(emailContent);

            Transport.send(message, Config.MAIL_USER, Config.MAIL_PASS);

            _log.info("Verification email sent to: " + email);
            return true;
        }
        catch (Exception e)
        {
            _log.error("Failed to send verification email to: " + email, e);
            return false;
        }
    }
    

    
    /**
     * Checks if email is already verified
     */
    public static boolean isEmailVerified(String accountName)
    {
        String verified = AccountEmail.getAccountValue(accountName, "email_verified");
        return "true".equals(verified);
    }
    
    /**
     * Gets pending email for account
     */
    public static String getPendingEmail(String accountName)
    {
        return AccountEmail.getAccountValue(accountName, "email_pending");
    }
}
