package l2mv.gameserver.utils;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.Random;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import l2mv.commons.dbutils.DbUtils;
import l2mv.gameserver.Config;
import l2mv.gameserver.database.DatabaseFactory;

/**
 * Email Verification System
 * <AUTHOR>
 */
public class EmailVerification
{
    private static final Logger _log = LoggerFactory.getLogger(EmailVerification.class);
    
    private static final int CODE_LENGTH = 6;
    private static final long CODE_EXPIRY_TIME = 15 * 60 * 1000; // 15 minutes
    
    /**
     * Generates a random verification code
     */
    public static String generateVerificationCode()
    {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        
        for (int i = 0; i < CODE_LENGTH; i++)
        {
            code.append(random.nextInt(10));
        }
        
        return code.toString();
    }
    
    /**
     * Stores verification code in database with expiry time
     */
    public static void storeVerificationCode(String accountName, String email, String code)
    {
        long expiryTime = System.currentTimeMillis() + CODE_EXPIRY_TIME;
        
        // Store pending email
        AccountEmail.insertAccountData(accountName, "email_pending", email);

        // Store verification code
        AccountEmail.insertAccountData(accountName, "email_code", code);

        // Store expiry time
        AccountEmail.insertAccountData(accountName, "email_expiry", String.valueOf(expiryTime));
        
        _log.info("Verification code stored for account: " + accountName + ", email: " + email);
    }
    
    /**
     * Validates verification code
     */
    public static boolean validateVerificationCode(String accountName, String inputCode)
    {
        String storedCode = AccountEmail.getAccountValue(accountName, "email_code");
        String expiryTimeStr = AccountEmail.getAccountValue(accountName, "email_expiry");
        
        if (storedCode == null || expiryTimeStr == null)
        {
            return false;
        }
        
        // Check if code has expired
        long expiryTime = Long.parseLong(expiryTimeStr);
        if (System.currentTimeMillis() > expiryTime)
        {
            clearVerificationData(accountName);
            return false;
        }
        
        // Check if code matches
        return storedCode.equals(inputCode);
    }
    
    /**
     * Confirms email verification and activates email
     */
    public static boolean confirmEmailVerification(String accountName, String inputCode)
    {
        if (!validateVerificationCode(accountName, inputCode))
        {
            return false;
        }
        
        // Get pending email
        String pendingEmail = AccountEmail.getAccountValue(accountName, "email_pending");
        if (pendingEmail == null)
        {
            return false;
        }
        
        // Activate email
        AccountEmail.setEmail(accountName, pendingEmail);
        
        // Mark as verified
        AccountEmail.insertAccountData(accountName, "email_verified", "true");
        
        // Clear verification data
        clearVerificationData(accountName);
        
        _log.info("Email verified and activated for account: " + accountName + ", email: " + pendingEmail);
        return true;
    }
    
    /**
     * Clears verification data from database
     */
    public static void clearVerificationData(String accountName)
    {
        Connection con = null;
        PreparedStatement statement = null;
        
        try
        {
            con = DatabaseFactory.getInstance().getConnection();
            statement = con.prepareStatement("DELETE FROM account_variables WHERE account_name=? AND var IN ('email_pending', 'email_code', 'email_expiry')");
            statement.setString(1, accountName);
            statement.executeUpdate();
        }
        catch (Exception e)
        {
            _log.warn("Cannot clear verification data.", e);
        }
        finally
        {
            DbUtils.closeQuietly(con, statement);
        }
    }
    
    /**
     * Sends verification email with code (simulated for now due to library issues)
     */
    public static boolean sendVerificationEmail(String email, String code, String playerName)
    {
        _log.info("Checking email configuration - MAIL_USER: '" + Config.MAIL_USER + "', MAIL_PASS: '" + (Config.MAIL_PASS.isEmpty() ? "EMPTY" : "SET") + "'");

        if (Config.MAIL_USER == null || Config.MAIL_USER.isEmpty() || Config.MAIL_PASS == null || Config.MAIL_PASS.isEmpty())
        {
            _log.warn("Email configuration not set. MAIL_USER: '" + Config.MAIL_USER + "', MAIL_PASS: " + (Config.MAIL_PASS == null ? "NULL" : (Config.MAIL_PASS.isEmpty() ? "EMPTY" : "SET")));

            // Fallback to console logging
            logVerificationCodeToConsole(email, code, playerName);
            return true;
        }

        try
        {
            return sendEmailViaJavaMail(email, code, playerName);
        }
        catch (Exception e)
        {
            _log.error("Error sending verification email via JavaMail, falling back to console logging", e);

            // Fallback to console logging
            logVerificationCodeToConsole(email, code, playerName);
            return true;
        }
    }

    /**
     * Sends email using raw SMTP without JavaMail dependencies
     */
    private static boolean sendEmailViaJavaMail(String email, String code, String playerName) throws Exception
    {
        try
        {
            return sendEmailViaSMTP(email, code, playerName);
        }
        catch (Exception e)
        {
            _log.error("Failed to send email via SMTP: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Sends email using raw SMTP protocol
     */
    private static boolean sendEmailViaSMTP(String email, String code, String playerName) throws Exception
    {
        java.net.Socket socket = null;
        java.io.PrintWriter out = null;
        java.io.BufferedReader in = null;

        try
        {
            // Connect to Gmail SMTP server
            socket = new java.net.Socket("smtp.gmail.com", 587);
            out = new java.io.PrintWriter(socket.getOutputStream(), true);
            in = new java.io.BufferedReader(new java.io.InputStreamReader(socket.getInputStream()));

            // Read initial response
            String response = in.readLine();
            _log.info("SMTP Initial: " + response);

            // Send EHLO
            out.println("EHLO localhost");
            response = readMultilineResponse(in);
            _log.info("SMTP EHLO: " + response);

            // Start TLS
            out.println("STARTTLS");
            response = in.readLine();
            _log.info("SMTP STARTTLS: " + response);

            // Upgrade to SSL
            javax.net.ssl.SSLSocketFactory factory = (javax.net.ssl.SSLSocketFactory) javax.net.ssl.SSLSocketFactory.getDefault();
            javax.net.ssl.SSLSocket sslSocket = (javax.net.ssl.SSLSocket) factory.createSocket(socket, "smtp.gmail.com", 587, true);
            sslSocket.startHandshake();

            out = new java.io.PrintWriter(sslSocket.getOutputStream(), true);
            in = new java.io.BufferedReader(new java.io.InputStreamReader(sslSocket.getInputStream()));

            // Send EHLO again after TLS
            out.println("EHLO localhost");
            response = readMultilineResponse(in);
            _log.info("SMTP EHLO after TLS: " + response);

            // Authenticate
            out.println("AUTH LOGIN");
            response = in.readLine();
            _log.info("SMTP AUTH LOGIN: " + response);

            // Send username (base64 encoded)
            String encodedUser = java.util.Base64.getEncoder().encodeToString(Config.MAIL_USER.getBytes());
            out.println(encodedUser);
            response = in.readLine();
            _log.info("SMTP Username: " + response);

            // Send password (base64 encoded)
            String encodedPass = java.util.Base64.getEncoder().encodeToString(Config.MAIL_PASS.getBytes());
            out.println(encodedPass);
            response = in.readLine();
            _log.info("SMTP Password: " + response);

            if (!response.startsWith("235"))
            {
                throw new Exception("Authentication failed: " + response);
            }

            // Send email
            out.println("MAIL FROM:<" + Config.MAIL_USER + ">");
            response = in.readLine();
            _log.info("SMTP MAIL FROM: " + response);

            out.println("RCPT TO:<" + email + ">");
            response = in.readLine();
            _log.info("SMTP RCPT TO: " + response);

            out.println("DATA");
            response = in.readLine();
            _log.info("SMTP DATA: " + response);

            // Send email content
            String emailContent = Config.MAIL_MESSAGE.replace("%PIN%", code);
            out.println("From: " + Config.MAIL_USER);
            out.println("To: " + email);
            out.println("Subject: " + Config.MAIL_SUBJECT);
            out.println("Content-Type: text/plain; charset=UTF-8");
            out.println("");
            out.println(emailContent);
            out.println(".");

            response = in.readLine();
            _log.info("SMTP Email sent: " + response);

            out.println("QUIT");
            response = in.readLine();
            _log.info("SMTP QUIT: " + response);

            _log.info("Email sent successfully to: " + email + " for player: " + playerName);
            return true;
        }
        finally
        {
            if (out != null) out.close();
            if (in != null) in.close();
            if (socket != null) socket.close();
        }
    }

    private static String readMultilineResponse(java.io.BufferedReader in) throws Exception
    {
        StringBuilder response = new StringBuilder();
        String line;
        while ((line = in.readLine()) != null)
        {
            response.append(line).append("\n");
            if (line.length() >= 4 && line.charAt(3) == ' ')
            {
                break;
            }
        }
        return response.toString();
    }

    /**
     * Logs verification code to console as fallback
     */
    private static void logVerificationCodeToConsole(String email, String code, String playerName)
    {
        String emailContent = Config.MAIL_MESSAGE.replace("%PIN%", code);

        _log.info("=== EMAIL VERIFICATION CODE ===");
        _log.info("TO: " + email);
        _log.info("PLAYER: " + playerName);
        _log.info("SUBJECT: " + Config.MAIL_SUBJECT);
        _log.info("CODE: " + code);
        _log.info("MESSAGE: " + emailContent);
        _log.info("===============================");

        // Also print to console for easy visibility
        System.out.println("=== EMAIL VERIFICATION CODE ===");
        System.out.println("TO: " + email);
        System.out.println("PLAYER: " + playerName);
        System.out.println("CODE: " + code);
        System.out.println("===============================");
    }

    /**
     * Checks if email is already verified
     */
    public static boolean isEmailVerified(String accountName)
    {
        String verified = AccountEmail.getAccountValue(accountName, "email_verified");
        return "true".equals(verified);
    }
    
    /**
     * Gets pending email for account
     */
    public static String getPendingEmail(String accountName)
    {
        return AccountEmail.getAccountValue(accountName, "email_pending");
    }
}
