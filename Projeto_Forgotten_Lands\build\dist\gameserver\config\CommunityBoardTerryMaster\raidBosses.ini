#======================================================
#               Community Raid Bosses
#======================================================
# Should we enable Raid Bosses Page in Alt+B?
AllowRaidBossesPage = True

# Which boss ids, should we not show on the list?
# Split with ;
RaidBossesNotVisibleIds = 25692;25423;25010;25532;25119;25159;25163;25070;29119;29068;29028;29020;29118;29001;29014

# Should we enable Teleporting to the Bosses?
AllowRaidBossesTeleport = True

# Should teleporting be possible, only when player is in peace zone?
RaidBossesTeleportOnlyFromPeace = True

# Should teleporting be possible only for Premium Users?
RaidBossesTeleportOnlyForPremium = True

# Ids of Bosses that "Teleport To" button should be removed
# Include here only Ids, that are on the list
# Split with ;
RaidBossesDisabledTeleport = 29001;29014;29118;29006;29068;29020;29028

# List of Epic Bosses. Those Bosses will have different color of the name in Community Board
RaidBossesEpics = 29001;29014;29118;29006;29068;29020;29028

# What should be the Price for Teleporting to the Bosses by Community Board?
# Item Id:
RaidBossesTeleportPriceId = 57
# Item Count: 
RaidBossesTeleportPriceCount = 1000000


#======================================================
#               Epic Bosses Page
#======================================================
# Should we allow Epic Bosses page opened by NPC and by ALT+B > Raid Bosses > Epic Bosses ?
AllowEpicBossesPage = True
# Should we allow teleporting to Epic Bosses spots?
# Spots can be changed in the html
AllowEpicBossesTeleport = True
# Should teleporting be possible, only when player is in peace zone?
EpicBossesTeleportOnlyFromPeace = True
# Should teleporting be possible only for Premium Users?
EpicBossesTeleportOnlyForPremium = False

# What should be the Price for Teleporting to the Bosses by Community Board?
# Leave ItemId or Count 0 or -1 to disable
# Item Id:
EpicBossesTeleportPriceId = 57
# Item Count:
EpicBossesTeleportPriceCount = 1000000