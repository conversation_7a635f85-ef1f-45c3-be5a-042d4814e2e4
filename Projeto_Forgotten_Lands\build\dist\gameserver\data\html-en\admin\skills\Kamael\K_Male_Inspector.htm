<html><body><table width=260>
<tr><td width=40>
<button value="Main" action="bypass -h admin_admin" width=40 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df">
</td><td width=180>
<center><PERSON><PERSON><PERSON> Inspector</center>
</td><td width=40>
<button value="Back" action="bypass -h admin_show_html skills/Kamael/kamael.htm" width=40 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df">
</td></tr></table>
<img src="sek.cbui355" width=260 height=2><br>
<center><table width=260>
<tr><td>Level:</td><td><edit var="level" width=110 height=15></td></tr>
</table><br>
<font color="b5a6c6"><table width=260>
<tr><td>Appetite for Destruction (max 3)</td><td><button value="Add" action="bypass -h admin_add_skill 1476 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Life to Soul (max 5)</td><td><button value="Add" action="bypass -h admin_add_skill 502 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Light Armor Mastery (max 50)</td><td><button value="Add" action="bypass -h admin_add_skill 465 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Magic Immunity (max 55)</td><td><button value="Add" action="bypass -h admin_add_skill 466 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Rapier Mastery (max 45)</td><td><button value="Add" action="bypass -h admin_add_skill 474 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Restoration (max 8)</td><td><button value="Add" action="bypass -h admin_add_skill 1487 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Soul Mastery (max 23)</td><td><button value="Add" action="bypass -h admin_add_skill 467 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Triple Thrust (max 37)</td><td><button value="Add" action="bypass -h admin_add_skill 504 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Obilivion (max 7)</td><td><button value="Add" action="bypass -h admin_add_skill 1481 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Vicious Stance (max 20)</td><td><button value="Add" action="bypass -h admin_add_skill 312 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Protection Instinct (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 1478 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Thin Skin (max 7)</td><td><button value="Add" action="bypass -h admin_add_skill 1483 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Shining Edge (max 28)</td><td><button value="Add" action="bypass -h admin_add_skill 505 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Spread Wing (max 25)</td><td><button value="Add" action="bypass -h admin_add_skill 492 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Vampiric Impulse (max 3)</td><td><button value="Add" action="bypass -h admin_add_skill 1477 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Magic Impulse (max 3)</td><td><button value="Add" action="bypass -h admin_add_skill 1479 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Restoration Impact (max 8)</td><td><button value="Add" action="bypass -h admin_add_skill 1488 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Weak Constitution (max 4)</td><td><button value="Add" action="bypass -h admin_add_skill 1482 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Enervation (max 4)</td><td><button value="Add" action="bypass -h admin_add_skill 1484 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Spite (max 3)</td><td><button value="Add" action="bypass -h admin_add_skill 1485 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Mental Impoverish (max 4)</td><td><button value="Add" action="bypass -h admin_add_skill 1486 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Soul Harmony (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 1480 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
</table></center></font>
</body></html>