<title>Choose skill to add</title>
<center>
[admin_add_skill 70 39|Drain Health 39 (Lv 60)]%%
[admin_add_skill 70 40|Drain Health 40 (Lv 62)]%%
[admin_add_skill 70 41|Drain Health 41 (Lv 62)]%%
[admin_add_skill 70 42|Drain Health 42 (Lv 64)]%%
[admin_add_skill 70 43|Drain Health 43 (Lv 64)]%%
[admin_add_skill 70 44|Drain Health 44 (Lv 66)]%%
[admin_add_skill 70 45|Drain Health 45 (Lv 66)]%%
[admin_add_skill 70 46|Drain Health 46 (Lv 68)]%%
[admin_add_skill 70 47|Drain Health 47 (Lv 68)]%%
[admin_add_skill 70 48|Drain Health 48 (Lv 70)]%%
[admin_add_skill 70 49|Drain Health 49 (Lv 70)]%%
[admin_add_skill 70 50|Drain Health 50 (Lv 72)]%%
[admin_add_skill 70 51|Drain Health 51 (Lv 72)]%%
[admin_add_skill 70 52|Drain Health 52 (Lv 74)]%%
[admin_add_skill 70 53|Drain Health 53 (Lv 74)]%%
[admin_add_skill 96 3|Bleed 3 (Lv 49)]%%
[admin_add_skill 96 4|Bleed 4 (Lv 58)]%%
[admin_add_skill 96 5|Bleed 5 (Lv 66)]%%
[admin_add_skill 96 6|Bleed 6 (Lv 70)]%%
[admin_add_skill 105 3|Freezing Strike 3 (Lv 40)]%%
[admin_add_skill 105 4|Freezing Strike 4 (Lv 40)]%%
[admin_add_skill 105 5|Freezing Strike 5 (Lv 43)]%%
[admin_add_skill 105 6|Freezing Strike 6 (Lv 43)]%%
[admin_add_skill 105 7|Freezing Strike 7 (Lv 46)]%%
[admin_add_skill 105 8|Freezing Strike 8 (Lv 46)]%%
[admin_add_skill 105 9|Freezing Strike 9 (Lv 49)]%%
[admin_add_skill 105 10|Freezing Strike 10 (Lv 49)]%%
[admin_add_skill 105 11|Freezing Strike 11 (Lv 52)]%%
[admin_add_skill 105 12|Freezing Strike 12 (Lv 52)]%%
[admin_add_skill 105 13|Freezing Strike 13 (Lv 55)]%%
[admin_add_skill 105 14|Freezing Strike 14 (Lv 55)]%%
[admin_add_skill 105 15|Freezing Strike 15 (Lv 58)]%%
[admin_add_skill 105 16|Freezing Strike 16 (Lv 58)]%%
[admin_add_skill 105 17|Freezing Strike 17 (Lv 60)]%%
[admin_add_skill 105 18|Freezing Strike 18 (Lv 62)]%%
[admin_add_skill 105 19|Freezing Strike 19 (Lv 64)]%%
[admin_add_skill 105 20|Freezing Strike 20 (Lv 66)]%%
[admin_add_skill 105 21|Freezing Strike 21 (Lv 68)]%%
[admin_add_skill 105 22|Freezing Strike 22 (Lv 70)]%%
[admin_add_skill 105 23|Freezing Strike 23 (Lv 72)]%%
[admin_add_skill 105 24|Freezing Strike 24 (Lv 74)]%%
[admin_add_skill 106 1|Veil 1 (Lv 43)]%%
[admin_add_skill 106 2|Veil 2 (Lv 46)]%%
[admin_add_skill 106 3|Veil 3 (Lv 49)]%%
[admin_add_skill 106 4|Veil 4 (Lv 52)]%%
[admin_add_skill 106 5|Veil 5 (Lv 55)]%%
[admin_add_skill 106 6|Veil 6 (Lv 58)]%%
[admin_add_skill 106 7|Veil 7 (Lv 60)]%%
[admin_add_skill 106 8|Veil 8 (Lv 62)]%%
[admin_add_skill 106 9|Veil 9 (Lv 64)]%%
[admin_add_skill 106 10|Veil 10 (Lv 66)]%%
[admin_add_skill 106 11|Veil 11 (Lv 68)]%%
[admin_add_skill 106 12|Veil 12 (Lv 70)]%%
[admin_add_skill 106 13|Veil 13 (Lv 72)]%%
[admin_add_skill 106 14|Veil 14 (Lv 74)]%%
[admin_add_skill 111 2|Ultimate Evasion 2 (Lv 55)]%%
[admin_add_skill 115 3|Power Break 3 (Lv 40)]%%
[admin_add_skill 115 4|Power Break 4 (Lv 43)]%%
[admin_add_skill 115 5|Power Break 5 (Lv 46)]%%
[admin_add_skill 115 6|Power Break 6 (Lv 49)]%%
[admin_add_skill 115 7|Power Break 7 (Lv 52)]%%
[admin_add_skill 115 8|Power Break 8 (Lv 55)]%%
[admin_add_skill 115 9|Power Break 9 (Lv 58)]%%
[admin_add_skill 115 10|Power Break 10 (Lv 60)]%%
[admin_add_skill 115 11|Power Break 11 (Lv 62)]%%
[admin_add_skill 115 12|Power Break 12 (Lv 64)]%%
[admin_add_skill 115 13|Power Break 13 (Lv 66)]%%
[admin_add_skill 115 14|Power Break 14 (Lv 68)]%%
[admin_add_skill 115 15|Power Break 15 (Lv 70)]%%
[admin_add_skill 115 16|Power Break 16 (Lv 72)]%%
[admin_add_skill 115 17|Power Break 17 (Lv 74)]%%
[admin_add_skill 122 1|Hex 1 (Lv 40)]%%
[admin_add_skill 122 2|Hex 2 (Lv 43)]%%
[admin_add_skill 122 3|Hex 3 (Lv 46)]%%
[admin_add_skill 122 4|Hex 4 (Lv 49)]%%
[admin_add_skill 122 5|Hex 5 (Lv 52)]%%
[admin_add_skill 122 6|Hex 6 (Lv 55)]%%
[admin_add_skill 122 7|Hex 7 (Lv 58)]%%
[admin_add_skill 122 8|Hex 8 (Lv 60)]%%
[admin_add_skill 122 9|Hex 9 (Lv 62)]%%
[admin_add_skill 122 10|Hex 10 (Lv 64)]%%
[admin_add_skill 122 11|Hex 11 (Lv 66)]%%
[admin_add_skill 122 12|Hex 12 (Lv 68)]%%
[admin_add_skill 122 13|Hex 13 (Lv 70)]%%
[admin_add_skill 122 14|Hex 14 (Lv 72)]%%
[admin_add_skill 122 15|Hex 15 (Lv 74)]%%
[admin_add_skill 129 2|Poison 2 (Lv 49)]%%
[admin_add_skill 129 3|Poison 3 (Lv 58)]%%
[admin_add_skill 129 4|Poison 4 (Lv 66)]%%
[admin_add_skill 129 5|Poison 5 (Lv 74)]%%
[admin_add_skill 169 2|Quick Step 2 (Lv 43)]%%
[admin_add_skill 171 2|Esprit 2 (Lv 43)]%%
[admin_add_skill 171 3|Esprit 3 (Lv 46)]%%
[admin_add_skill 171 4|Esprit 4 (Lv 49)]%%
[admin_add_skill 171 5|Esprit 5 (Lv 52)]%%
[admin_add_skill 171 6|Esprit 6 (Lv 62)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index DarkElf/DE_AbyssWalker" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index DarkElf/DE_AbyssWalker3" width=40 height=15><br>
<button value="Page4" action="bypass -h admin_skill_index DarkElf/DE_AbyssWalker4" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
