<title>Choose skill to add</title>
<center>
[admin_add_skill 19 1|Double Shot 1 (Lv 40)]%%
[admin_add_skill 19 2|Double Shot 2 (Lv 40)]%%
[admin_add_skill 19 3|Double Shot 3 (Lv 40)]%%
[admin_add_skill 19 4|Double Shot 4 (Lv 43)]%%
[admin_add_skill 19 5|Double Shot 5 (Lv 43)]%%
[admin_add_skill 19 6|Double Shot 6 (Lv 43)]%%
[admin_add_skill 19 7|Double Shot 7 (Lv 46)]%%
[admin_add_skill 19 8|Double Shot 8 (Lv 46)]%%
[admin_add_skill 19 9|Double Shot 9 (Lv 46)]%%
[admin_add_skill 19 10|Double Shot 10 (Lv 49)]%%
[admin_add_skill 19 11|Double Shot 11 (Lv 49)]%%
[admin_add_skill 19 12|Double Shot 12 (Lv 49)]%%
[admin_add_skill 19 13|Double Shot 13 (Lv 52)]%%
[admin_add_skill 19 14|Double Shot 14 (Lv 52)]%%
[admin_add_skill 19 15|Double Shot 15 (Lv 52)]%%
[admin_add_skill 19 16|Double Shot 16 (Lv 55)]%%
[admin_add_skill 19 17|Double Shot 17 (Lv 55)]%%
[admin_add_skill 19 18|Double Shot 18 (Lv 55)]%%
[admin_add_skill 19 19|Double Shot 19 (Lv 58)]%%
[admin_add_skill 19 20|Double Shot 20 (Lv 58)]%%
[admin_add_skill 19 21|Double Shot 21 (Lv 58)]%%
[admin_add_skill 19 22|Double Shot 22 (Lv 60)]%%
[admin_add_skill 19 23|Double Shot 23 (Lv 60)]%%
[admin_add_skill 19 24|Double Shot 24 (Lv 62)]%%
[admin_add_skill 19 25|Double Shot 25 (Lv 62)]%%
[admin_add_skill 19 26|Double Shot 26 (Lv 64)]%%
[admin_add_skill 19 27|Double Shot 27 (Lv 64)]%%
[admin_add_skill 19 28|Double Shot 28 (Lv 66)]%%
[admin_add_skill 19 29|Double Shot 29 (Lv 66)]%%
[admin_add_skill 19 30|Double Shot 30 (Lv 68)]%%
[admin_add_skill 19 31|Double Shot 31 (Lv 68)]%%
[admin_add_skill 19 32|Double Shot 32 (Lv 70)]%%
[admin_add_skill 19 33|Double Shot 33 (Lv 70)]%%
[admin_add_skill 19 34|Double Shot 34 (Lv 72)]%%
[admin_add_skill 19 35|Double Shot 35 (Lv 72)]%%
[admin_add_skill 19 36|Double Shot 36 (Lv 74)]%%
[admin_add_skill 19 37|Double Shot 37 (Lv 74)]%%
[admin_add_skill 24 1|Burst Shot 1 (Lv 46)]%%
[admin_add_skill 24 2|Burst Shot 2 (Lv 46)]%%
[admin_add_skill 24 3|Burst Shot 3 (Lv 46)]%%
[admin_add_skill 24 4|Burst Shot 4 (Lv 49)]%%
[admin_add_skill 24 5|Burst Shot 5 (Lv 49)]%%
[admin_add_skill 24 6|Burst Shot 6 (Lv 49)]%%
[admin_add_skill 24 7|Burst Shot 7 (Lv 52)]%%
[admin_add_skill 24 8|Burst Shot 8 (Lv 52)]%%
[admin_add_skill 24 9|Burst Shot 9 (Lv 52)]%%
[admin_add_skill 24 10|Burst Shot 10 (Lv 55)]%%
[admin_add_skill 24 11|Burst Shot 11 (Lv 55)]%%
[admin_add_skill 24 12|Burst Shot 12 (Lv 55)]%%
[admin_add_skill 24 13|Burst Shot 13 (Lv 58)]%%
[admin_add_skill 24 14|Burst Shot 14 (Lv 58)]%%
[admin_add_skill 24 15|Burst Shot 15 (Lv 58)]%%
[admin_add_skill 24 16|Burst Shot 16 (Lv 60)]%%
[admin_add_skill 24 17|Burst Shot 17 (Lv 60)]%%
[admin_add_skill 24 18|Burst Shot 18 (Lv 62)]%%
[admin_add_skill 24 19|Burst Shot 19 (Lv 62)]%%
[admin_add_skill 24 20|Burst Shot 20 (Lv 64)]%%
[admin_add_skill 24 21|Burst Shot 21 (Lv 64)]%%
[admin_add_skill 24 22|Burst Shot 22 (Lv 66)]%%
[admin_add_skill 24 23|Burst Shot 23 (Lv 66)]%%
[admin_add_skill 24 24|Burst Shot 24 (Lv 68)]%%
[admin_add_skill 24 25|Burst Shot 25 (Lv 68)]%%
[admin_add_skill 24 26|Burst Shot 26 (Lv 70)]%%
[admin_add_skill 24 27|Burst Shot 27 (Lv 70)]%%
[admin_add_skill 24 28|Burst Shot 28 (Lv 72)]%%
[admin_add_skill 24 29|Burst Shot 29 (Lv 72)]%%
[admin_add_skill 24 30|Burst Shot 30 (Lv 74)]%%
[admin_add_skill 24 31|Burst Shot 31 (Lv 74)]%%
[admin_add_skill 99 2|Rapid Shot 2 (Lv 55)]%%
[admin_add_skill 101 4|Stun Shot 4 (Lv 40)]%%
[admin_add_skill 101 5|Stun Shot 5 (Lv 40)]%%
[admin_add_skill 101 6|Stun Shot 6 (Lv 40)]%%
[admin_add_skill 101 7|Stun Shot 7 (Lv 43)]%%
[admin_add_skill 101 8|Stun Shot 8 (Lv 43)]%%
[admin_add_skill 101 9|Stun Shot 9 (Lv 43)]%%
[admin_add_skill 101 10|Stun Shot 10 (Lv 46)]%%
[admin_add_skill 101 11|Stun Shot 11 (Lv 46)]%%
[admin_add_skill 101 12|Stun Shot 12 (Lv 46)]%%
<br>
<button value="Page2" action="bypass -h admin_skill_index Human/H_Hawkeye2" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index Human/H_Hawkeye3" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
