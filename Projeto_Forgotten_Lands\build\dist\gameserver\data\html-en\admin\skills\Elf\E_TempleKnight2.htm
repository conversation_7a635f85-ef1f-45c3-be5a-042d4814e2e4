<title>Choose skill to add</title>
<center>
[admin_add_skill 58 25|Elemental Heal 25 (Lv 46)]%%
[admin_add_skill 58 26|Elemental Heal 26 (Lv 46)]%%
[admin_add_skill 58 27|Elemental Heal 27 (Lv 46)]%%
[admin_add_skill 58 28|Elemental Heal 28 (Lv 49)]%%
[admin_add_skill 58 29|Elemental Heal 29 (Lv 49)]%%
[admin_add_skill 58 30|Elemental Heal 30 (Lv 49)]%%
[admin_add_skill 58 31|Elemental Heal 31 (Lv 52)]%%
[admin_add_skill 58 32|Elemental Heal 32 (Lv 52)]%%
[admin_add_skill 58 33|Elemental Heal 33 (Lv 52)]%%
[admin_add_skill 58 34|Elemental Heal 34 (Lv 55)]%%
[admin_add_skill 58 35|Elemental Heal 35 (Lv 55)]%%
[admin_add_skill 58 36|Elemental Heal 36 (Lv 55)]%%
[admin_add_skill 58 37|Elemental Heal 37 (Lv 58)]%%
[admin_add_skill 58 38|Elemental Heal 38 (Lv 58)]%%
[admin_add_skill 58 39|Elemental Heal 39 (Lv 58)]%%
[admin_add_skill 58 40|Elemental Heal 40 (Lv 60)]%%
[admin_add_skill 58 41|Elemental Heal 41 (Lv 60)]%%
[admin_add_skill 58 42|Elemental Heal 42 (Lv 62)]%%
[admin_add_skill 58 43|Elemental Heal 43 (Lv 62)]%%
[admin_add_skill 58 44|Elemental Heal 44 (Lv 64)]%%
[admin_add_skill 58 45|Elemental Heal 45 (Lv 64)]%%
[admin_add_skill 58 46|Elemental Heal 46 (Lv 66)]%%
[admin_add_skill 58 47|Elemental Heal 47 (Lv 66)]%%
[admin_add_skill 58 48|Elemental Heal 48 (Lv 68)]%%
[admin_add_skill 58 49|Elemental Heal 49 (Lv 68)]%%
[admin_add_skill 58 50|Elemental Heal 50 (Lv 70)]%%
[admin_add_skill 58 51|Elemental Heal 51 (Lv 70)]%%
[admin_add_skill 58 52|Elemental Heal 52 (Lv 72)]%%
[admin_add_skill 58 53|Elemental Heal 53 (Lv 72)]%%
[admin_add_skill 58 54|Elemental Heal 54 (Lv 74)]%%
[admin_add_skill 58 55|Elemental Heal 55 (Lv 74)]%%
[admin_add_skill 239 2|Expertise C 2 (Lv 40)]%%
[admin_add_skill 239 3|Expertise B 3 (Lv 52)]%%
[admin_add_skill 239 4|Expertise A 4 (Lv 60)]%%
[admin_add_skill 239 5|Expertise S 5 (Lv 75)]%%
[admin_add_skill 102 2|Entangle 2 (Lv 40)]%%
[admin_add_skill 102 3|Entangle 3 (Lv 43)]%%
[admin_add_skill 102 4|Entangle 4 (Lv 46)]%%
[admin_add_skill 102 5|Entangle 5 (Lv 49)]%%
[admin_add_skill 102 6|Entangle 6 (Lv 52)]%%
[admin_add_skill 102 7|Entangle 7 (Lv 55)]%%
[admin_add_skill 102 8|Entangle 8 (Lv 58)]%%
[admin_add_skill 102 9|Entangle 9 (Lv 60)]%%
[admin_add_skill 102 10|Entangle 10 (Lv 62)]%%
[admin_add_skill 102 11|Entangle 11 (Lv 64)]%%
[admin_add_skill 102 12|Entangle 12 (Lv 66)]%%
[admin_add_skill 102 13|Entangle 13 (Lv 68)]%%
[admin_add_skill 102 14|Entangle 14 (Lv 70)]%%
[admin_add_skill 102 15|Entangle 15 (Lv 72)]%%
[admin_add_skill 102 16|Entangle 16 (Lv 74)]%%
[admin_add_skill 291 1|Final Fortress 1 (Lv 52)]%%
[admin_add_skill 291 2|Final Fortress 2 (Lv 55)]%%
[admin_add_skill 291 3|Final Fortress 3 (Lv 58)]%%
[admin_add_skill 291 4|Final Fortress 4 (Lv 60)]%%
[admin_add_skill 291 5|Final Fortress 5 (Lv 62)]%%
[admin_add_skill 291 6|Final Fortress 6 (Lv 64)]%%
[admin_add_skill 291 7|Final Fortress 7 (Lv 66)]%%
[admin_add_skill 291 8|Final Fortress 8 (Lv 68)]%%
[admin_add_skill 291 9|Final Fortress 9 (Lv 70)]%%
[admin_add_skill 291 10|Final Fortress 10 (Lv 72)]%%
[admin_add_skill 291 11|Final Fortress 11 (Lv 74)]%%
[admin_add_skill 191 2|Focus Mind 2 (Lv 43)]%%
[admin_add_skill 191 3|Focus Mind 3 (Lv 49)]%%
[admin_add_skill 191 4|Focus Mind 4 (Lv 55)]%%
[admin_add_skill 191 5|Focus Mind 5 (Lv 64)]%%
[admin_add_skill 191 6|Focus Mind 6 (Lv 72)]%%
[admin_add_skill 288 1|Guard Stance 1 (Lv 43)]%%
[admin_add_skill 288 2|Guard Stance 2 (Lv 52)]%%
[admin_add_skill 288 3|Guard Stance 3 (Lv 62)]%%
[admin_add_skill 288 4|Guard Stance 4 (Lv 70)]%%
[admin_add_skill 18 1|Hate Aura 1 (Lv 40)]%%
[admin_add_skill 18 2|Hate Aura 2 (Lv 40)]%%
[admin_add_skill 18 3|Hate Aura 3 (Lv 40)]%%
[admin_add_skill 18 4|Hate Aura 4 (Lv 43)]%%
[admin_add_skill 18 5|Hate Aura 5 (Lv 43)]%%
[admin_add_skill 18 6|Hate Aura 6 (Lv 43)]%%
[admin_add_skill 18 7|Hate Aura 7 (Lv 46)]%%
[admin_add_skill 18 8|Hate Aura 8 (Lv 46)]%%
[admin_add_skill 18 9|Hate Aura 9 (Lv 46)]%%
[admin_add_skill 18 10|Hate Aura 10 (Lv 49)]%%
[admin_add_skill 18 11|Hate Aura 11 (Lv 49)]%%
[admin_add_skill 18 12|Hate Aura 12 (Lv 49)]%%
[admin_add_skill 18 13|Hate Aura 13 (Lv 52)]%%
[admin_add_skill 18 14|Hate Aura 14 (Lv 52)]%%
[admin_add_skill 18 15|Hate Aura 15 (Lv 52)]%%
[admin_add_skill 18 16|Hate Aura 16 (Lv 55)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index Elf/E_TempleKnight" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index Elf/E_TempleKnight3" width=40 height=15><br>
<button value="Page4" action="bypass -h admin_skill_index Elf/E_TempleKnight4" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
