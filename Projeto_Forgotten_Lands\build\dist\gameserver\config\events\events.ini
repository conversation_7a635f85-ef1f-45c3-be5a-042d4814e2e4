# ================================================= 
# 					  Properties 				  #
#					 Event Configs                #
# ================================================= 
#               Retail and Custom Events          #
# =================================================

# Coffer of Shadows Event
EnableCofferShadowEvent = True
# Rtg on price chests (the default for x1 - 50k)
CofferOfShadowsPriceRate = 1.
# Rtg on the awards, the real number (it was equivalent RateDropItems)
CofferOfShadowsRewardRate = 1.

# GM Hunter Event
# 57,10,1000,100 - ID , MINCOUNT, MAXCOUNT , CHANCE split with ;
GmHunterRewards = 57,10,1000,100;
# Boost the gm-char on event start.
#GmHunterSetSpeed = 200
GmHunterSetPdefence = 5000
GmHunterSetMdefence = 5000
GmHunterSetHP = 50000
GmHunterSetCP = 50000

# Master Of Enchaning
EnableMasterEnchantingEvent = True
# Chance sharpening Staff of Master Yogi
MasterYogiEnchantChance = 56
# Maximum sharpening Staff of Master Yogi
MasterYogiEnchantMaxWeapon = 18
# Safe sharpening Staff of Master Yogi
MasterYogiSafeEnchant = 4

# April Fool's 
EnableAprilFoolsDayEvent = False
# Vesper Herb drop rate (default 50 - 5%)
AprilFollsDropChance = 50

# GvG (Group VS Group) 
# Remove all of the effects before event
GvGDisableEffect = True

# TheFallHarvest
EnableFallHarvestEvent = True
# Drop rate of pollen
TFH_POLLEN_CHANCE = 20.

# Glittering medal
EnableGlitteringMedalEvent = True
# Simple drop rate medal
MEDAL_CHANCE = 1.
# Schans drop glittering medalek
GLITTMEDAL_CHANCE = 0.05

# L2-Day Letter Collection Event
EnableLetterCollectionEvent = True
# Drop Chance letters, double, default = 1.
L2DAY_LETTER_CHANCE = 1.

# Change of Heart
EnableHearthEvent = True
# Drop rate hearts, double, default 5. = 5%
EVENT_CHANGE_OF_HEART_CHANCE = 5.

# Jobs .gettask mobid
# Example: .gettask 32210   
BountyHuntersEnabled = True

# Saving Snowman
EnableSavingSnowmanEvent = True
# Price of qualifying for x1 recommended price 50k adena.
SavingSnowmanLoteryPrice = 50000
# The likelihood of santa sleigh (integer)
# Default 2/1000 - Santa appears on every 500m mob.
# 1000 - 100% chance
SavingSnowmanRewarderChance = 2

# March 8
EnableMarch8Event = True
# Drop rate of parts to craft (default 10 = 1%)
March8DropChance = 10.
# Reith cost prescriptions. Default: 50k adena for x1
March8PriceRate = 1.

# Trick Of Transmutation
EnableTrickOfTransEvent = True
# Key drop rate from mobs (the default is 10. = 1%)
TRICK_OF_TRANS_CHANCE = 10.

# Summer Meleons Event
EnableSummerMeleonsEvent = True

# PC Caffe Exchange Points Event
EnablePCCafeExchangeEvent = True

# Vitality Gift Event
EnableVitalityGiftEvent = True

# Freya Event
EnableFreyaEvent = True

# Christmas Event
EnableChrismasEvent = True

# The Flow Horror Event
EnableFlowHorrorEvent = True

# Configuration script CustomDropItems
AllowCustomDropItems = True
# ID item is added to the drop list mobs
# Format 1234; 5678, etc.
CDItemsId = 6392, 6393
CDItemsCountDropMin = 1, 1
CDItemsCountDropMax = 1, 1

# Drop rate Custom Items
CustomDropItemsChance = 5.,0.5
# Enable IGRAC levels?
CDItemsAllowMinMaxPlayerLvl = false
# The minimum level that will start dropping an item (only works if CDItemsAllowMinMaxPlayerLvl = true)
CDItemsMinPlayerLvl = 20
# The maximum level that will start dropping an item (only works if CDItemsAllowMinMaxPlayerLvl = true)
CDItemsMaxPlayerLvl = 85
# Enable level mobs?
CDItemsAllowMinMaxMobLvl = false
# The minimum level from which to start dropping an item (only works if CDItemsAllowMinMaxMobLvl = true)
CDItemsMinMobLvl = 20
# The maximum level from which to start dropping an item (only works if CDItemsAllowMinMaxMobLvl = true)
CDItemsMaxMobLvl = 80
# Include drop only RB? (When CDItemsAllowOnlyRbDrops = true, with regular mobs will not fall)
CDItemsAllowOnlyRbDrops = false

# ===============================
#		Random Boss Event		#
# ===============================
# Set true if you want enable RandomBoss Event
EnableRandomBossEvent = True
# Set RandomBoss ID
RandomBossID = 25126
# RandomBoss Spawn interval in minutes
RandomBossTime = 60
# RandomBoss Spawn Location X
RandomBossSpawnX = 85928
# RandomBoss Spawn Location Y
RandomBossSpawnY = 257880
# RandomBoss Spawn Location Z
RandomBossSpawnZ = -11664

# ===============================
#		Fight Club      		#
# ===============================
# Allow Fight Club
AllowFightClub = True
# Should only 1 player per HWID be able to join Event at the time?
FightClubHwidCheck = True
# Which Event should be disabled?
# Counter will stop on Event was Started message
# Default: -1
FightClubNotAllowedEvent = -1

FightClubEqualizeRooms = True

# ===============================
#		Santa Event      		#
# ===============================
# Should we allow Christmas Santa Event?
# Tree ornaments will drop from items
# Santa will appear in Giran
# Santa will trade ornaments for Hats and Christmas Tree
AllowSantaEvent = True

# Multiplier of Ornaments Drop Chance
# Default: 1.0
SantaItemsChanceMult = 3.0