# ===========================================
#        Buffer Settings (first option)     #
# ===========================================
# Include Buffer
BBSPVPBufferEnabled = false
# Buff when choosing a pet buff 1st
BBSPVPBufferOneBuffPet = false
# Allow or Buffer for sieges
BBSPVPBufferAllowOnSiege = false
# Allow or Buffer in instances and epic bosses
BBSPVPBufferAllowInInstance = false
# Allow or Buffer able PvP flag.
BBSPVPBufferAllowOnPvP = false
# Setup time buffs. In seconds. 2 hours = 7200
BBSPVPBufferTime = 7200
# ID for an item that produce buff. By default, <PERSON>a (ID 57)
BBSPVPBufferPriceItem = 57
# Price per buff - Set to 0 for free buffs
BBSPVPBufferPriceOne = 0
# Max number of buffs on the page in Buffer. Updated to match new limits.
BBSPVPBufferMaxPerPage = 36
# Max number of buffs to save a set of buffs. Updated to match new limits.
BBSPVPBufferMaxPerSet = 36
# Time through which the rebaff avtobaffom. In seconds.
BBSPVPBufferTaskDelay = 14400
# Minimum level to use char Buffer
BBSPVPBufferMinLvl = 1
# Maximum Chara for Buffer
BBSPVPBufferMaxLvl = 99


# ===========================================
# Settings Buffer (second option)		    #
# ===========================================
# Allow Buffer?
AllowBBSBuffer = True
# Fixed Buffs
# Allowed buffs
BuffIDs = 1214

# Enable the recovery stats
BuffAllowHeal = True
# Stat recovery function works only in peace zone
BuffHealIsInPeace = True
# Activate the reset effects
BuffAllowCancel = True

# Enable manual editing sets
# If off, then when the set once saved
# Buffs imposed on the player, and when you click on the set, he immediately puts them
BuffManualEditSets = True

# of sets for one character.
# Maximum buff sets for one player.
MaximumSetsPerChar = 8

# for the subject consumed a buff.
# Item ID for one buff.
BuffItemId = 57
# The number of items consumed by a buff.
# Amount of item ID for one buff - Set to 0 for free buffs.
BuffItemCount = 0

# The minimum level for the free buff
# Minimum level for free buffs.
FreeBuffLevel = 40

# The new value for the duration of the buff (usually).
# Default: 0 (the value of which is written in the skill)
# New time period for buffs in sec. (Regular skills)
# Default: 0 (that means that the skill duration will be on default as in xml)
BuffTime = 7200
# Multiplier duration buff (Conventional)
# Buff time multiplier (regular buffs)
BuffTimeMod = 1

# The new value for the duration of the buff (song / dance).
# Default: 0 (the value of which is written in the skill)
# New time period for buffs in sec. (Songs / dances)
# Default: 0 (that means that the skill duration will be on default as in xml)
BuffTimeMusic = 7200
# Multiplier duration buff (song / dance)
# Buff time multiplier (songs / dances)
BuffTimeModMusic = 1

# The new value for the duration of the buff (COV, POF, Chant of victory, Magnus ...).
# Default: 0 (the value of which is written in the skill)
# New time period for buffs in sec. (Speacial skills, COV, POF, Chant of victory)
# Default: 0 (that means that the skill duration will be on default as in xml)
BuffTimeSpecial = 7200
# Multiplier duration buff (COV, POF, Chant of victory, Magnus ...)
# Buff time multiplier (special skills, COV, POF, Chant of victory etc)
BuffTimeModSpecial = 1

# Display buffs
# The number of rows buffs
BuffsPageRows = 9
# Maximum buffs in one set.
# Note, all value in this column are multiplied by BuffsPageRows, if you put the "4" and BuffsPageRows equals 9, max buffs will be "36", etc.
# Maximum buffs in one buff set.
# Pay attention, this configuration is always multiplyed by "9", if you set "4" the max buffs will be "36" etc.
MaxBuffsPerSet = 4

# ===========================================================
# Settings bafery prohibit the use under certain conditions #
# ===========================================================
# Allow if in a state of death spell?
# Enable if char is dead?
AllowWhenDead = false
# Allow if the spell is not at rest (fights, uses action)
# Enable if char is not in peace (attaking, making an action)
AllowWhenInAction = false
# Allow if the charms on the Olympic Games / Olympic Games in the area?
# Enable if char is in olympiad?
AllowWhenInOlly = false
# Allow if the spell is traveling?
# Enable if char is flying?
AllowWhenInFly = false
# Allow if the spell on the ship?
# Enable if char is in veichle?
AllowWhenInVeichle = false
# Allow if the spell is on the strider or other animals?
# Enable is char is mounted?
AllowWhenMounted = false
# Allow if the spell can not move? (Paralysis, slip, overweight, etc.)
# Enable if char cannot move? (Paralyzed, sleep, overweight etc)
AllowWhenCantMove = false
# Allow if the spell is a trade?
# Enable if char is in trade?
AllowWhenInTrade = false
# Allow if spell fish?
# Enable if char is fishing?
AllowWhenFishing = false
# Allow if the spell is not able to understand (started logout, teleports)?
# Enable if char is in temporarry state? (Logout started, teleport)
AllowWhenInTemp = false
# Allow if a spell duel?
# Enable if char is in duel?
AllowWhenInDuel = false
# Allow if you have cursed weapon?
# Enable if char is possesed by cursed weapon?
AllowWhenUseCursed = false
# Allow if charms PC?
# Enable if char is PK?
AllowWhenIsPk = false
# Allow only clan leaders?
# Enable only to clan leaders?
AllowOnlyToClanLeader = false
# Allow only Nobles?
# Enable only to noble
AllowOnlyToNoble = false
# Allow if the spell is involved in the fight for teritory or in the Battle of the castle (SIEGE EVENT)?
# Enable if char is on siege or on teritorry siege active zone (SIEGE EVENT)
AllowUseInTWPlayer = false
# Allow only in a peaceful area?
# Enable only in peace zone?
AllowUseOnlyInPeace = false

# ===========================================================
# 							Buff Sets 						#
# ===========================================================
BuffSetMage = 1397,3; 1182,3; 1189,3; 1191,3; 1392,3; 1393,3; 1352,1; 1353,1; 1354,1; 1259,4; 1035,4; 1078,6; 1461,1; 1303,2; 830,1; 1383,3; 1062,2; 4703,13; 1085,3; 1501,1; 1500,1; 1499,1; 1504,1; 1413,1; 1323,1; 363,1; 308,1; 530,1; 349,1; 268,1; 267,1; 304,1; 264,1; 365,1; 273,1; 276,1;
BuffSetFighter = 192,1
BuffSetDagger = 192,1
BuffSetSupport = 192,1
BuffSetTank = 192,1