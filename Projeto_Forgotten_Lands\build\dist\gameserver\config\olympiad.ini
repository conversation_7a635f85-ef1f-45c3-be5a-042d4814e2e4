#==============================================
# 				Olympiad Settings			  #
#==============================================
EnableOlympiad = True
EnableOlympiadSpectating = True

# Olympiad Manager Shout Just one message 
# Official = False
OlyManagerShoutJustOneMessage = True

# Should Opponent Name & Class be shown to the fighters just after teleportation to Olympiad Stadium?
OlympiadShowOpponentPersonality = True

#==============================================#
#             ANTI FEED OPTION                 #
#==============================================#
# Don't Allow Clan Members to fight VS same Clan
# Official = False

# Maximum number in Stadion Preparation (standard: 160)
OlympiadStadiasCount = 160

# Olympiad Start Time in Military hours Default 8pm (20)
AltOlyStartTime = 18
# Olympiad Start Time for Min's, Default 00 so at the start of the hour.
AltOlyMin = 00

# Olympiad Compeition Period, Default 6 hours.
# (If set different, should be increment by 10mins)
AltOlyCPeriod = 21600000

# Olympiad Weekly Period, Default: 604800000
AltOlyWPeriod = 604800000

# Olympiad Validation Period, Default 12 Hours.
# Default: 43200000
AltOlyVPeriod = 43200000

# If you change the config make a request to the database: DELETE FROM `server_variables` WHERE `name` = 'Olympiad_End';
# Example (every two weeks):
# Default = 1 and AltOlyDateEndWeekly = 0
AltOlyDateEndMonthly = 1,15
# If AltOlyDateEndWeekly is not 0, then it will be used over AltOlyDateEndMonthly, and olympiad will end every week on a certain week day
# Sunday=1, Monday=2, Tuesday=3, Wednesday=4, Thursday=5, Friday=6, Saturday=7, Disabled=0 will use AltOlyDateEndMonthly instead
AltOlyDateEndWeekly = 7

ClassGameMin = 11
NonClassGameMin = 2
TeamGameMin = 16

# Number used for displaying amount of registered participants, messages "Fewer than ..." or "More than ...".
# 0 for displaying digits instead of text phrase (old style).
# Default: 100
AltOlyRegistrationDisplayNumber = 100

# Olympiad battle winner reward.
# Default: 13722
AltOlyBattleRewItem = 13722

# Default: 50
AltOlyClassedRewItemCount = 50

# Default: 40
AltOlyNonClassedRewItemCount = 40

# Default: 50
AltOlyTeamRewItemCount = 50

# ItemId for this reward.
# Default: 13722
AltOlyCompRewItem = 13722

# Rate to exchange points to reward item.
# Default: 1000
AltOlyGPPerPoint = 1000

# Noblesse points awarded to Heros.
# Default: 200
AltOlyHeroPoints = 200

# Noblesse points awarded to Rank 1 members.
# Default: 100
AltOlyRank1Points = 100

# Noblesse points awarded to Rank 2 members.
# Default: 75
AltOlyRank2Points = 75

# Noblesse points awarded to Rank 3 members.
# Default: 55
AltOlyRank3Points = 55

# Noblesse points awarded to Rank 4 members.
# Default: 40
AltOlyRank4Points = 40

# Noblesse points awarded to Rank 5 members.
# Default: 30
AltOlyRank5Points = 30

# Maximum fighting gallop can be made
GameMaxLimit = 100
# Limit the fighting in class
GameClassesCountLimit = 30
# Limit fighting outside class
GameNoClassesCountLimit = 100
# Limit fighting team
GameTeamCountLimit = 10

# The number of fights per month required for tokens
OlympiadBattlesForReward = 15

# The number of points issued on receipt nubla and earlier this month
OlympiadPointsDefault = 50
# The number of points given out in a week
OlympiadPointsWeekly = 25

# Show /olympiadstat in the old version (only you)
OlympiadOldStyleStat = False

# Block Gulf twinks at the Olympics (check for IP)
OlympiadPlayerIp = True

# Время ожидания до порта на арену, использовать только значения стандартного ряда (120,60,30,15,5,4,3,2,1)
# Default: 120
AltOlyWaitTime = 30
# Время ожидания порта в город после боя, использовать только значения стандартного ряда (20,10,5,4,3,2,1)
# Default: 20
AltOlyPortBackTime = 20

# Config for maximum grinding equip to fight in the Olympics
# False to disable
OlyEnchantLimit = true
OlyEnchantLimitWeapon = 6
OlyEnchantLimitArmor = 6
OlyEnchantLimitJewel = 6

# Clear peretochennye things?
# Works only when the OlyEnchantLimit!
OlympiadUnEquipBadEnchantItem = True

# Time to teleport to the stadium
OlympiadBeginTime = 30