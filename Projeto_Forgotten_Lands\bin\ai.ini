AllowNpcAIs = True

# The number of managers for the problems AI
AiTaskManagers = 1

# Tick size (in milliseconds), must be a multiple of 250.
# Tick size in attack mode
AiTaskDelay = 150
# Tick size in active
AiTaskActiveDelay = 150

# Block active tasks. Need to debug or to dramatically reduce the load.
BlockActiveTasks = False

# Instead of returning home on foot, use the quick teleport
AlwaysTeleportHome = false

# A random effect sharpening weapons from monsters by default.
# For the best visual effect is recommended to set the value (0-16).
MonstersWeaponEnchantMin = 0
MonstersWeaponEnchantMax = 0
MonstersWeaponEnchantChance = 0

# Include random movement of monsters
RndWalk = True

# Frequency of random movement: from 0 to 100
RndWalkRate = 85

# The maximum distance at which NPC may depart from the spawn point
MaxDriftRange = 100

# Frequency of random animations: from 0 to 100
RndAnimationRate = 95

# Check frequency of aggression (in milliseconds)
AggroCheckInterval = 250

# The time that aggressive mobs will not show aggression to the player after teleport (in milliseconds)
NonAggroTimeOnTeleport = 15000

# The maximum range at which mobs are no longer pursue the aggressor and replaced spawn.
# For mobs in closed pomesheniyah and danzhenah:
MaxPursueUndergoundRange = 2000
# For the mobs on the surface:
MaxPursueRange = 4000
# For raids:
MaxPursueRangeRaid = 5000