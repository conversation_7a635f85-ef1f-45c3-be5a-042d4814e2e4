<title>Choose skill to add</title>
<center>
[admin_add_skill 147 29|Magic Resistance 29 (Lv 52)]%%
[admin_add_skill 147 30|Magic Resistance 30 (Lv 55)]%%
[admin_add_skill 147 31|Magic Resistance 31 (Lv 55)]%%
[admin_add_skill 147 32|Magic Resistance 32 (Lv 55)]%%
[admin_add_skill 147 33|Magic Resistance 33 (Lv 58)]%%
[admin_add_skill 147 34|Magic Resistance 34 (Lv 58)]%%
[admin_add_skill 147 35|Magic Resistance 35 (Lv 58)]%%
[admin_add_skill 147 36|Magic Resistance 36 (Lv 60)]%%
[admin_add_skill 147 37|Magic Resistance 37 (Lv 60)]%%
[admin_add_skill 147 38|Magic Resistance 38 (Lv 62)]%%
[admin_add_skill 147 39|Magic Resistance 39 (Lv 62)]%%
[admin_add_skill 147 40|Magic Resistance 40 (Lv 64)]%%
[admin_add_skill 147 41|Magic Resistance 41 (Lv 64)]%%
[admin_add_skill 147 42|Magic Resistance 42 (Lv 66)]%%
[admin_add_skill 147 43|Magic Resistance 43 (Lv 66)]%%
[admin_add_skill 147 44|Magic Resistance 44 (Lv 68)]%%
[admin_add_skill 147 45|Magic Resistance 45 (Lv 68)]%%
[admin_add_skill 147 46|Magic Resistance 46 (Lv 70)]%%
[admin_add_skill 147 47|Magic Resistance 47 (Lv 70)]%%
[admin_add_skill 147 48|Magic Resistance 48 (Lv 72)]%%
[admin_add_skill 147 49|Magic Resistance 49 (Lv 72)]%%
[admin_add_skill 147 50|Magic Resistance 50 (Lv 74)]%%
[admin_add_skill 147 51|Magic Resistance 51 (Lv 74)]%%
[admin_add_skill 191 2|Focus Mind 2 (Lv 43)]%%
[admin_add_skill 191 3|Focus Mind 3 (Lv 49)]%%
[admin_add_skill 191 4|Focus Mind 4 (Lv 55)]%%
[admin_add_skill 191 5|Focus Mind 5 (Lv 64)]%%
[admin_add_skill 191 6|Focus Mind 6 (Lv 72)]%%
[admin_add_skill 223 13|Sting 13 (Lv 40)]%%
[admin_add_skill 223 14|Sting 14 (Lv 40)]%%
[admin_add_skill 223 15|Sting 15 (Lv 40)]%%
[admin_add_skill 223 16|Sting 16 (Lv 43)]%%
[admin_add_skill 223 17|Sting 17 (Lv 43)]%%
[admin_add_skill 223 18|Sting 18 (Lv 43)]%%
[admin_add_skill 223 19|Sting 19 (Lv 46)]%%
[admin_add_skill 223 20|Sting 20 (Lv 46)]%%
[admin_add_skill 223 21|Sting 21 (Lv 46)]%%
[admin_add_skill 223 22|Sting 22 (Lv 49)]%%
[admin_add_skill 223 23|Sting 23 (Lv 49)]%%
[admin_add_skill 223 24|Sting 24 (Lv 49)]%%
[admin_add_skill 223 25|Sting 25 (Lv 52)]%%
[admin_add_skill 223 26|Sting 26 (Lv 52)]%%
[admin_add_skill 223 27|Sting 27 (Lv 52)]%%
[admin_add_skill 223 28|Sting 28 (Lv 55)]%%
[admin_add_skill 223 29|Sting 29 (Lv 55)]%%
[admin_add_skill 223 30|Sting 30 (Lv 55)]%%
[admin_add_skill 223 31|Sting 31 (Lv 58)]%%
[admin_add_skill 223 32|Sting 32 (Lv 58)]%%
[admin_add_skill 223 33|Sting 33 (Lv 58)]%%
[admin_add_skill 223 34|Sting 34 (Lv 60)]%%
[admin_add_skill 223 35|Sting 35 (Lv 60)]%%
[admin_add_skill 223 36|Sting 36 (Lv 62)]%%
[admin_add_skill 223 37|Sting 37 (Lv 62)]%%
[admin_add_skill 223 38|Sting 38 (Lv 64)]%%
[admin_add_skill 223 39|Sting 39 (Lv 64)]%%
[admin_add_skill 223 40|Sting 40 (Lv 66)]%%
[admin_add_skill 223 41|Sting 41 (Lv 66)]%%
[admin_add_skill 223 42|Sting 42 (Lv 68)]%%
[admin_add_skill 223 43|Sting 43 (Lv 68)]%%
[admin_add_skill 223 44|Sting 44 (Lv 70)]%%
[admin_add_skill 223 45|Sting 45 (Lv 70)]%%
[admin_add_skill 223 46|Sting 46 (Lv 72)]%%
[admin_add_skill 223 47|Sting 47 (Lv 72)]%%
[admin_add_skill 223 48|Sting 48 (Lv 74)]%%
[admin_add_skill 223 49|Sting 49 (Lv 74)]%%
[admin_add_skill 239 2|Expertise C 2 (Lv 40)]%%
[admin_add_skill 239 3|Expertise B 3 (Lv 52)]%%
[admin_add_skill 239 4|Expertise A 4 (Lv 60)]%%
[admin_add_skill 239 5|Expertise S 5 (Lv 75)]%%
[admin_add_skill 271 1|Dance of Warrior 1 (Lv 55)]%%
[admin_add_skill 272 1|Dance of Inspiration 1 (Lv 46)]%%
[admin_add_skill 273 1|Dance of Mystic 1 (Lv 49)]%%
[admin_add_skill 274 1|Dance of Fire 1 (Lv 40)]%%
[admin_add_skill 275 1|Dance of Fury 1 (Lv 58)]%%
[admin_add_skill 276 1|Dance of Concentration 1 (Lv 52)]%%
[admin_add_skill 277 1|Dance of Light 1 (Lv 43)]%%
[admin_add_skill 307 1|Dance of Aqua Guard 1 (Lv 70)]%%
[admin_add_skill 309 1|Dance of Earth Guard 1 (Lv 62)]%%
[admin_add_skill 310 1|Dance of Vampire 1 (Lv 74)]%%
[admin_add_skill 311 1|Dance of Protection 1 (Lv 66)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index DarkElf/DE_BladeDancer" width=40 height=15><br>
<button value="Page2" action="bypass -h admin_skill_index DarkElf/DE_BladeDancer2" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
