# ---------------------------------------------------------------------------
#    					Buff Store System
# ---------------------------------------------------------------------------

# Enable / Disable Sell Buff System
BuffStoreEnabled = True

# If enabled then the buffer will consume his own mp for each buff sold
BuffStoreMpEnabled = True

# Multiplier to control the mp consumed for each buff sold
# 1.0 is 100% of the original mp consume. Smaller value is less consume
BuffStoreMpConsumeMultiplier = 1.0

# Enable specific skill item consume. Example: Skill <PERSON>t of Victory using Spirit Ore.
BuffStoreItemConsumeEnabled = False

# Allowed class ids that can put their buffs on sale
# Format: class ids separated by commas
BuffStoreAllowedClassList = 15,16,97,17,98,29,30,105,42,43,112,50,116,51,115,100,21,107,34,94,103

# Forbidden skill list, that cannot be put on sale
# Put all the skill ids separated by commas
BuffStoreForbiddenSkillList = 765,989,1507,1410,1411,764,914,988,327,1323,1325,1326,1327,1531,1427
# Title Color of the offline Buff Store
BuffStoreTitleColor = F9966B
# Name Color of the offline Buff Store when it goes offline
BuffStoreOfflineNameColor = FBB117
# Name Color of the store when its online
BuffStoreNameColor = FFFFFF
