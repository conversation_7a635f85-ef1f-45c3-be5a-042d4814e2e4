# ================================================= 
# 					  Settings 					  #
#				  Other properties                #
# ================================================= 
# Vote Announce Msg
VoteMsg = You didn't vote! Vote in all banners and after type .getreward in game!
# Of Adena, giving the character when it is created
StartingAdena = 100

# Enable Special Tutorial Help Player on Creating Character?
# Popup Htmls from Level 6 to level up more faster.
EnableSpecialTutorial = True

# Start level, with which the player starts the game on the server.
# Default: 0 - off
StartingLvL = 0

#Sales of all item is  1 on the Aden
SellAllItemsFree = false

# Speed Swimming. The default is 50
SwimingSpeedTemplate = 50

# - When the player on 9 more than the level of the monster, the drop rate is divided by 3
# - After 9 levels difference between a player and a dark blue mob, drop rate is reduced by 9% per level
# (9lvls diff = -9%; 10lvls diff = -18%; 11lvls diff = 27%; ...)
# Pometka1: These rules is distributed as a normal drop, and the Spoil
# Pometka2: These rules are ignored when Server Rates drop like Aden
UseDeepBlueDropRules = True
DeepBlueDropMaxDiff = 85
DeepBlueDropRaidMaxDiff = 2

# Limit to the number of slots in your inventory (note that the client will show a maximum of 80 slots)
MaximumSlotsForNoDwarf = 120
MaximumSlotsForDwarf = 200
MaximumSlotsForGMPlayer = 250
MaximumSlotsForQuests = 100

# The number of slots in the vault
BaseWarehouseSlotsForNoDwarf = 100
BaseWarehouseSlotsForDwarf = 120
MaximumWarehouseSlotsForClan = 200

# Limit freight slot
MaximumFreightSlots = 10

#Chance % of successful enchant
EnchantChance = 45
EnchantChanceArmor = 45
EnchantChanceAccessory = 56
# Crystal scrolls may have another chance
EnchantChanceCrystal = 50
EnchantChanceCrystalArmor = 50
EnchantChanceCrystalAccessory = 50
EnchantChanceCrystalArmorOlf = 50
# Chance enchant Bless point
EnchantChanceBless = 50
EnchantChanceArmorBless = 50
EnchantChanceAccessoryBless = 56

# Alternative Enchant System
# Default: false
UseAltEnchant = True

# Alternative Enchant System for Olf T-Shirt
# Default: false
EnableOlfTShirtEnchant = True

# Alternative Enchant System
# In order to work must enable: UseAltEnchant = True
# Here you should to set enchant value for every enchant level, last value will be max enchant level.
EnchantWeaponFighter = 100,100,100,100,75,60,60,40,35,35,35,30,25,25,25,15
EnchantWeaponFighterCrystal = 100,100,100,100,75,60,60,40,35,35,35,30,25,25,25,15
EnchantWeaponFighterBlessed = 100,100,100,100,75,60,60,40,35,35,35,30,25,25,25,15
EnchantArmor = 100,100,100,100,75,60,60,40,35,35,35,30,25,25,25,15
EnchantArmorCrystal = 100,100,100,100,75,60,60,40,35,35,35,30,25,25,25,15
EnchantArmorBlessed = 100,100,100,100,75,60,60,40,35,35,35,30,25,25,25,15
EnchantJewelry = 100,100,100,100,75,60,60,40,35,35,35,30,25,25,25,15
EnchantJewelryCrystal = 100,100,100,100,75,60,60,40,35,35,35,30,25,25,25,15
EnchantJewelryBlessed = 100,100,100,100,75,60,60,40,35,35,35,30,25,25,25,15
# Enchanting for Olf T-Shirt
# Requires EnableOlfTShirtEnchant Enabled
EnchantOlfTShirtChances = 100,100,100,60,45,40,35,40,10,5

# The Maximum safe enchant
SafeEnchantCommon = 4

# The maximum safe for enchant FullBody armour.
SafeEnchantFullBody = 4

# Maximum enchant for Divine Scrolls
EnchantMaxDivineScrollWeapon = 16
EnchantMaxDivineScrollArmor = 16
EnchantMaxDivineScrollJewelry = 16

# Maximum enchant
EnchantMaxWeapon = 16
EnchantMaxArmor = 16
EnchantMaxJewelry = 16
EnchantMaxOlfTShirt = 6

# If enchant Blessed-scrolling bad, how much enchant to drop?
# Default: 0 (off)

EnchantMax = 20
SafeEnchant = 0

# Limit of bonus HP, at over-enchant Armour. (Official: 10)
ArmorOverEnchantHPBonusLimit = 10
# Show a success animation \ Crush Grinding (Default = false)
ShowEnchantEffectResult = true

# Chance successful enchant stones Attribute
EnchantAttributeChance = 35
# Chance successful enchant Attribute Crystals
EnchantAttributeCrystalChance = 25

# When set to true - accelerates regeneration after long sitting character
RegenSitWait = True

# Giving the players skill return (5 minutes)
UnstuckSkill = True

# Of HP, MP and CP is restored after death.
# Re-spawn a percentage of the maximum
# "1" - to leave as much as it was, <0> - 1 unit. (Only HP)
RespawnRestoreCP = -1
RespawnRestoreHP = 65
RespawnRestoreMP = -1

# Maximum number of slots for Private Store (sale / purchase) for the gnomes and all other races.
#Off-like 5 slots for the dwarves, and other races to 4
MaxPvtStoreSlotsDwarf = 5
MaxPvtStoreSlotsOther = 4
MaxPvtManufactureSlots = 20

# Package SendStatus in the "Priv. Store Chars" send all merchants or only offline
SendStatusTradeJustOffline = false
# Multiplier of traders in the package SendStatus
SendStatusTradeMod = 1.
# Traders off to the general online?
ShowOfflineTradeInOnline = False

# Colors nicks
# BBGGRR
GMNameColour = 00FF00
NormalNameColour = FFFFFF
ClanleaderNameColour = FFFFFF

# Include aura hero for GM's?
GMHeroAura = false

# Show welcome.htm when logging in?
ShowHTMLWelcome = True

# Technical parameter, if the players take off when viewing long multisell - decrease (increase traffic and overhead)
MultisellPageSize = 40

# Announce a location Mammon
AnnounceMammonSpawn = True

# Maximum number of attachments in the AA in the seven seals.
# Official = 1000000
MaxPlayerContribution = 1000000

# When enabled it protects the player from debuff chaining. This means that the character is protected against the same debuff for the time that the debuff lasts
# Retail is false, but for balancing is good to be true
DebuffProtectionSystem = False

# If false players wont be able to enchant their skills when they are outside a peace zone
# Retail: True
AllowSkillEnchantingOutsidePeaceZone = True


# ================================
# 		    Prime Shop			 #
#	       item-mall.xml		 #
# ================================
# Which item is the ID that will be considered in lieu of glasses premium account
# -1 = Premium Points
GamePointItemId = -1


# ================================================= 
# 			      Captcha System				  #
# ================================================= 
# Should we allow Captcha System?
# It is run by .report command
AllowCaptcha = False

# How many seconds player should have to answer Captcha
CaptchaAnswerTime = 20

# Jail Time for wrong answer or no answer at all
# Set Value in Seconds
CaptchaJailTime = 120

# Maximum chance count for put incorrect captcha.
CaptchaCount = 2

# Punishment data.
CaptchaPunishment = JAIL:450,JAIL:1500,JAIL:5000,BAN:-100

# ================================================= 
# 				        Etc 					  #
# ================================================= 
# Need Optimisation is unbalanced
# Enable Achievement System
EnableAchievements = True

# =================================================
# 			      Item Logs     				  #
# =================================================
# Should we enable Item Logs that players can see?
EnablePlayerItemLogs = True

# Should Item Logs be saved in Database? This feature allows to everythinb be saved on db and recovered on start
# If false, items only go to normal logs and are session based, meaning that after restart the server you wont see your previous logs anymore
# This suppose heavy db usage when saving everything, but can be usefull, the PlayerItemLogsMaxTime config sets for how long db logs will be mantained
PlayerItemLogsSavedInDB = False

# For how many milliseconds Log will be visible to player?
# 48 hours = 172800000
PlayerItemLogsMaxTime = 172800000

# =================================================
# 			     Buff Return System
# =================================================
# This system allowed to Give Back lost buffs caused by Cancellation, Steal Essence
# This system completely doesn't exist on retail
# After how many seconds, buffs should be returned ON OLYMPIAD?
# Set -1 to Disable
BuffReturnOlympiadTime = -1
# After how many seconds, buffs should be returned ON AUTO EVENTS?
BuffReturnAutoEventsTime = -1
# After how many seconds, buffs should be returned ANYWHERE ELSE?
BuffReturnNormalLocationsTime = -1

# =================================================
# 				Macro Reuse Bug
# =================================================
# In retail there are 2 bugs using /useskill macro.
# It is caused because the client has delay per skill, aswell as the server, but there is some missynchronization between both.
# First one allows to bypass 1.2 secs of reuse time while the second one resets the skill reuse after the skill is enchanted.
# Default: False (unless you want bugs from retail)
AllowMacrosReuseBug = False
AllowMacrosEnchantBug = false

EnableEmotions = True
