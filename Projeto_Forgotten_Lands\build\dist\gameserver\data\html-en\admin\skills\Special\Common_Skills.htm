<table width=260>
<tr><td width=40>
<button value="Main" action="bypass -h admin_admin" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF">
</td><td width=180>
<center>Common Skills Menu</center>
</td><td width=40>
<button value="Back" action="bypass -h admin_show_html skills.htm" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF">
</td></tr></table>
<img src="sek.cbui355" width=260 height=2><br>
<center><table width=260>
<tr><td>Level:</td><td><edit var="level" width=110 height=15></td></tr>
</table><br>
<font color="676767"><table width=260>
<tr><td>Common Skills:</td></tr>
<tr><td>Lucky (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 194 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Create Common Item (max 9)</td><td><button value="Add" action="bypass -h admin_add_skill 1320 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Common Craft (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 1322 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td><br></td></tr>
<tr><td>Common mage skills:</td></tr>
<tr><td>Magicians Movement (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 118 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Spellcraft (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 163 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Mana Recovery (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 214 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td><br></td></tr>
<tr><td>Fishing skills:</td></tr>
<tr><td>Fishing (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 1312 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Pumping (max 27)</td><td><button value="Add" action="bypass -h admin_add_skill 1313 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Reeling (max 27)</td><td><button value="Add" action="bypass -h admin_add_skill 1314 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Fishing Expertise (max 27)</td><td><button value="Add" action="bypass -h admin_add_skill 1315 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td><br></td></tr>
<tr><td>Reward skills:</td></tr>
<tr><td>Expand Dwarven Craft (max 8)</td><td><button value="Add" action="bypass -h admin_add_skill 1368 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Expand Common Craft (max 8)</td><td><button value="Add" action="bypass -h admin_add_skill 1369 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Expand Trade (max 8)</td><td><button value="Add" action="bypass -h admin_add_skill 1370 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Expand Storage (max 8)</td><td><button value="Add" action="bypass -h admin_add_skill 1371 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Expand Inventory (max 8)</td><td><button value="Add" action="bypass -h admin_add_skill 1372 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td><br></td></tr>
<tr><td>Event skills:</td></tr>
<tr><td>Heart Shot (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 3261 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Heart Shot (HoT) (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 3260 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
<tr><td>Double Heart Shot (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 3262 $level" width=55 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr>
</table></font></center>
