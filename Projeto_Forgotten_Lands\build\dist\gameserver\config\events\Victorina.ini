# ================================================= 
#                Quiz Event Settings 	     	  #
# =================================================
# Includes a quiz if true
Victorina_Enabled = True
# Remove a question after he was true or false
# (To avoid repetition of questions)
<PERSON><PERSON>_Remove_Question = False
# Remove the question whether, if it no one answered,
# Works in conjunction with VIKTORINA_REMOVE_QUESTION
Victorina_Remove_Question_No_Answer = False
# Start time of the quiz if we indicate that the start will be 16
# 16.00, variruetsya only hours, not minutes
Victorina_Start_Time_Hour = 01

Victorina_Start_Time_Minute = 14
# Vitkoriny time, specify in hours
<PERSON>ina_Work_Time = 2
# The time within which to respond to
# Question in seconds.
Victorina_Time_Answer = 60
# At the announcement of the results to the following question in seconds
<PERSON><PERSON>_Time_Pause = 60
# For example, in this config pause between questions will be 2 minutes.
# But only for the first minute you can answer the question
# List of awards. REWARD_FERST list of rewards for those who answered the first question
# REWARD_OTHER to others who answered correctly.
# List format id, colichestvo, chance; id, colichestvo, chance; id, colichestvo, chance;
<PERSON>ina_Reward_Ferst = 57,1,100; 57,2,100;
Victorina_Reward_Other = 57,1,100; 57,2,100;