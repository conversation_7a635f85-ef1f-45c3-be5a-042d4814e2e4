<title>Choose skill to add</title>
<center>
[admin_add_skill 10 1|Summon Storm Cubic 1 (Lv 40)]%%
[admin_add_skill 10 2|Summon Storm Cubic 2 (Lv 44)]%%
[admin_add_skill 10 3|Summon Storm Cubic 3 (Lv 52)]%%
[admin_add_skill 10 4|Summon Storm Cubic 4 (Lv 58)]%%
[admin_add_skill 10 5|Summon Storm Cubic 5 (Lv 62)]%%
[admin_add_skill 10 6|Summon Storm Cubic 6 (Lv 66)]%%
[admin_add_skill 10 7|Summon Storm Cubic 7 (Lv 70)]%%
[admin_add_skill 10 8|Summon Storm Cubic 8 (Lv 74)]%%
[admin_add_skill 143 1|Cubic Mastery 1 (Lv 44)]%%
[admin_add_skill 143 2|Cubic Mastery 2 (Lv 56)]%%
[admin_add_skill 146 13|Anti Magic 13 (Lv 40)]%%
[admin_add_skill 146 14|Anti Magic 14 (Lv 40)]%%
[admin_add_skill 146 15|Anti Magic 15 (Lv 40)]%%
[admin_add_skill 146 16|Anti Magic 16 (Lv 44)]%%
[admin_add_skill 146 17|Anti Magic 17 (Lv 44)]%%
[admin_add_skill 146 18|Anti Magic 18 (Lv 44)]%%
[admin_add_skill 146 19|Anti Magic 19 (Lv 48)]%%
[admin_add_skill 146 20|Anti Magic 20 (Lv 48)]%%
[admin_add_skill 146 21|Anti Magic 21 (Lv 48)]%%
[admin_add_skill 146 22|Anti Magic 22 (Lv 52)]%%
[admin_add_skill 146 23|Anti Magic 23 (Lv 52)]%%
[admin_add_skill 146 24|Anti Magic 24 (Lv 52)]%%
[admin_add_skill 146 25|Anti Magic 25 (Lv 56)]%%
[admin_add_skill 146 26|Anti Magic 26 (Lv 56)]%%
[admin_add_skill 146 27|Anti Magic 27 (Lv 56)]%%
[admin_add_skill 146 28|Anti Magic 28 (Lv 58)]%%
[admin_add_skill 146 29|Anti Magic 29 (Lv 58)]%%
[admin_add_skill 146 30|Anti Magic 30 (Lv 60)]%%
[admin_add_skill 146 31|Anti Magic 31 (Lv 60)]%%
[admin_add_skill 146 32|Anti Magic 32 (Lv 62)]%%
[admin_add_skill 146 33|Anti Magic 33 (Lv 62)]%%
[admin_add_skill 146 34|Anti Magic 34 (Lv 64)]%%
[admin_add_skill 146 35|Anti Magic 35 (Lv 64)]%%
[admin_add_skill 146 36|Anti Magic 36 (Lv 66)]%%
[admin_add_skill 146 37|Anti Magic 37 (Lv 66)]%%
[admin_add_skill 146 38|Anti Magic 38 (Lv 68)]%%
[admin_add_skill 146 39|Anti Magic 39 (Lv 68)]%%
[admin_add_skill 146 40|Anti Magic 40 (Lv 70)]%%
[admin_add_skill 146 41|Anti Magic 41 (Lv 70)]%%
[admin_add_skill 146 42|Anti Magic 42 (Lv 72)]%%
[admin_add_skill 146 43|Anti Magic 43 (Lv 72)]%%
[admin_add_skill 146 44|Anti Magic 44 (Lv 74)]%%
[admin_add_skill 146 45|Anti Magic 45 (Lv 74)]%%
[admin_add_skill 164 3|Quick Recovery 3 (Lv 48)]%%
[admin_add_skill 212 2|Fast HP Recovery 2 (Lv 44)]%%
[admin_add_skill 212 3|Fast HP Recovery 3 (Lv 52)]%%
[admin_add_skill 212 4|Fast HP Recovery 4 (Lv 58)]%%
[admin_add_skill 212 5|Fast HP Recovery 5 (Lv 64)]%%
[admin_add_skill 212 6|Fast HP Recovery 6 (Lv 74)]%%
[admin_add_skill 213 3|Boost Mana 3 (Lv 40)]%%
[admin_add_skill 213 4|Boost Mana 4 (Lv 48)]%%
[admin_add_skill 213 5|Boost Mana 5 (Lv 56)]%%
[admin_add_skill 213 6|Boost Mana 6 (Lv 60)]%%
[admin_add_skill 213 7|Boost Mana 7 (Lv 66)]%%
[admin_add_skill 213 8|Boost Mana 8 (Lv 72)]%%
[admin_add_skill 228 2|Fast Spell Casting 2 (Lv 40)]%%
[admin_add_skill 228 3|Fast Spell Casting 3 (Lv 56)]%%
[admin_add_skill 229 3|Fast Mana Recovery 3 (Lv 44)]%%
[admin_add_skill 229 4|Fast Mana Recovery 4 (Lv 52)]%%
[admin_add_skill 229 5|Fast Mana Recovery 5 (Lv 60)]%%
[admin_add_skill 229 6|Fast Mana Recovery 6 (Lv 68)]%%
[admin_add_skill 229 7|Fast Mana Recovery 7 (Lv 74)]%%
[admin_add_skill 234 9|Robe Mastery 9 (Lv 40)]%%
[admin_add_skill 234 10|Robe Mastery 10 (Lv 40)]%%
[admin_add_skill 234 11|Robe Mastery 11 (Lv 40)]%%
[admin_add_skill 234 12|Robe Mastery 12 (Lv 44)]%%
[admin_add_skill 234 13|Robe Mastery 13 (Lv 44)]%%
[admin_add_skill 234 14|Robe Mastery 14 (Lv 44)]%%
[admin_add_skill 234 15|Robe Mastery 15 (Lv 48)]%%
[admin_add_skill 234 16|Robe Mastery 16 (Lv 48)]%%
[admin_add_skill 234 17|Robe Mastery 17 (Lv 48)]%%
[admin_add_skill 234 18|Robe Mastery 18 (Lv 52)]%%
[admin_add_skill 234 19|Robe Mastery 19 (Lv 52)]%%
[admin_add_skill 234 20|Robe Mastery 20 (Lv 52)]%%
<br>
<button value="Page2" action="bypass -h admin_skill_index Human/H_Warlock2" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index Human/H_Warlock3" width=40 height=15><br>
<button value="Page4" action="bypass -h admin_skill_index Human/H_Warlock4" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
