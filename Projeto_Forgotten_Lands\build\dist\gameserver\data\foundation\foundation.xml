<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE list SYSTEM "foundation.dtd">
<list>
	<foundation simple="9422" found="11557"/>	<!-- Change: 'Dynasty Helmet' to 'Dynasty Helmet' -->
	<foundation simple="9421" found="11512"/>	<!-- Change: 'Dynasty Gaiter' to 'Dynasty Gaiters' -->
	<foundation simple="9423" found="11513"/>	<!-- Change: 'Dynasty Gauntlet -> Heavy Armor' to 'Dynasty Gauntlet' -->
	<foundation simple="9424" found="11526"/>	<!-- Change: 'Dynasty Boots -> Heavy Armor' to 'Dynasty Boots' -->
	<foundation simple="9441" found="11532"/>	<!-- Change: 'Dynasty Shield' to 'Dynasty Shield' -->
	<foundation simple="9429" found="11525"/>	<!-- Change: 'Dynasty Leather Helmet' to 'Dynasty Leather Helmet' -->
	<foundation simple="9428" found="11516"/>	<!-- Change: 'Dynasty Leather Leggings' to 'Dynasty Leather Leggings' -->
	<foundation simple="9430" found="11515"/>	<!-- Change: 'Dynasty Leather Gloves -> Light Armor' to 'Dynasty Leather Gloves' -->
	<foundation simple="9431" found="11524"/>	<!-- Change: 'Dynasty Leather Boots -> Light Armor' to 'Dynasty Leather Boots' -->
	<foundation simple="9438" found="11539"/>	<!-- Change: 'Dynasty Circlet' to 'Dynasty Circlet' -->
	<foundation simple="9437" found="11558"/>	<!-- Change: 'Dynasty Stockings' to 'Dynasty Stockings' -->
	<foundation simple="9439" found="11514"/>	<!-- Change: 'Dynasty Gloves -> Robe' to 'Dynasty Gloves' -->
	<foundation simple="9440" found="11533"/>	<!-- Change: 'Dynasty Shoes -> Robe' to 'Dynasty Shoes' -->
	<foundation simple="12812" found="10119"/>	<!-- Change: 'Dynasty Sigil' to 'Dynasty Sigil' -->
	<foundation simple="15606" found="16289"/>	<!-- Change: 'Moirai Helmet' to 'Moirai Helmet' -->
	<foundation simple="15612" found="16295"/>	<!-- Change: 'Moirai Gaiter' to 'Moirai Gaiter' -->
	<foundation simple="15615" found="16298"/>	<!-- Change: 'Moirai Gauntlet' to 'Moirai Gauntlet' -->
	<foundation simple="15618" found="16301"/>	<!-- Change: 'Moirai Boots' to 'Moirai Boots' -->
	<foundation simple="15621" found="16304"/>	<!-- Change: 'Moirai Shield' to 'Moirai Shield' -->
	<foundation simple="15607" found="16290"/>	<!-- Change: 'Moirai Leather Helmet' to 'Moirai Leather Helmet' -->
	<foundation simple="15613" found="16296"/>	<!-- Change: 'Moirai Leather Legging' to 'Moirai Leather Legging' -->
	<foundation simple="15616" found="16299"/>	<!-- Change: 'Moirai Leather Gloves' to 'Moirai Leather Gloves' -->
	<foundation simple="15619" found="16302"/>	<!-- Change: 'Moirai Leather Boots' to 'Moirai Leather Boots' -->
	<foundation simple="15608" found="16291"/>	<!-- Change: 'Moirai Circlet' to 'Moirai Circlet' -->
	<foundation simple="15614" found="16297"/>	<!-- Change: 'Moirai Stockings' to 'Moirai Stockings' -->
	<foundation simple="15617" found="16300"/>	<!-- Change: 'Moirai Gloves' to 'Moirai Gloves' -->
	<foundation simple="15620" found="16303"/>	<!-- Change: 'Moirai Shoes' to 'Moirai Shoes' -->
	<foundation simple="15622" found="16305"/>	<!-- Change: 'Moirai Sigil' to 'Moirai Sigil' -->
	<foundation simple="13137" found="16306"/>	<!-- Change: 'Vesper Helmet' to 'Vesper Helmet' -->
	<foundation simple="13438" found="16312"/>	<!-- Change: 'Vesper Gaiters' to 'Vesper Gaiter' -->
	<foundation simple="13439" found="16315"/>	<!-- Change: 'Vesper Gauntlet' to 'Vesper Gauntlet' -->
	<foundation simple="13440" found="16318"/>	<!-- Change: 'Vesper Boots' to 'Vesper Boots' -->
	<foundation simple="13471" found="16321"/>	<!-- Change: 'Vesper Shield' to 'Vesper Shield' -->
	<foundation simple="13138" found="16307"/>	<!-- Change: 'Vesper Leather Helmet' to 'Vesper Leather Helmet' -->
	<foundation simple="13441" found="16313"/>	<!-- Change: 'Vesper Leather Leggings' to 'Vesper Leather Legging' -->
	<foundation simple="13442" found="16316"/>	<!-- Change: 'Vesper Leather Gloves' to 'Vesper Leather Gloves' -->
	<foundation simple="13443" found="16319"/>	<!-- Change: 'Vesper Leather Boots' to 'Vesper Leather Boots' -->
	<foundation simple="13139" found="16308"/>	<!-- Change: 'Vesper Circlet' to 'Vesper Circlet' -->
	<foundation simple="13444" found="16314"/>	<!-- Change: 'Vesper Stockings' to 'Vesper Stockings' -->
	<foundation simple="13445" found="16317"/>	<!-- Change: 'Vesper Gloves' to 'Vesper Gloves' -->
	<foundation simple="13446" found="16320"/>	<!-- Change: 'Vesper Shoes' to 'Vesper Shoes' -->
	<foundation simple="12813" found="16322"/>	<!-- Change: 'Vesper Sigil' to 'Vesper Sigil' -->
	<foundation simple="15724" found="16379"/>	<!-- Change: 'Moirai Earring' to 'Moirai Earring' -->
	<foundation simple="15724" found="16379"/>	<!-- Change: 'Moirai Earring' to 'Moirai Earring' -->
	<foundation simple="15725" found="16380"/>	<!-- Change: 'Moirai Necklace' to 'Moirai Necklace' -->
	<foundation simple="15723" found="16378"/>	<!-- Change: 'Moirai Ring' to 'Moirai Ring' -->
	<foundation simple="15723" found="16378"/>	<!-- Change: 'Moirai Ring' to 'Moirai Ring' -->
	<foundation simple="14163" found="16376"/>	<!-- Change: 'Vesper Earring' to 'Vesper Earring' -->
	<foundation simple="14163" found="16376"/>	<!-- Change: 'Vesper Earring' to 'Vesper Earring' -->
	<foundation simple="14164" found="16377"/>	<!-- Change: 'Vesper Necklace' to 'Vesper Necklace' -->
	<foundation simple="14165" found="16375"/>	<!-- Change: 'Vesper Ring' to 'Vesper Ring' -->
	<foundation simple="14165" found="16375"/>	<!-- Change: 'Vesper Ring' to 'Vesper Ring' -->
	<foundation simple="13883" found="14527"/>	<!-- Change: 'Icarus Dual Daggers' to 'Icarus Dual Daggers - Confusion' -->
	<foundation simple="10415" found="11300"/>	<!-- Change: 'Icarus Dual Sword' to 'Icarus Dual Sword - Destruction' -->
	<foundation simple="10216" found="11301"/>	<!-- Change: 'Icarus Disperser -> Critical Damage' to 'Icarus Disperser - Confusion -> Critical Damage' -->
	<foundation simple="10448" found="11304"/>	<!-- Change: 'Icarus Disperser -> Critical Damage' to 'Icarus Disperser - Confusion -> Critical Damage' -->
	<foundation simple="10447" found="11303"/>	<!-- Change: 'Icarus Disperser -> Evasion' to 'Icarus Disperser - Confusion -> Evasion' -->
	<foundation simple="10446" found="11302"/>	<!-- Change: 'Icarus Disperser -> Focus' to 'Icarus Disperser - Confusion -> Focus' -->
	<foundation simple="10222" found="11345"/>	<!-- Change: 'Icarus Hall -> Acumen' to 'Icarus Hall - Hail -> Acumen' -->
	<foundation simple="10457" found="11348"/>	<!-- Change: 'Icarus Hall -> Acumen' to 'Icarus Hall - Hail -> Acumen' -->
	<foundation simple="10456" found="11347"/>	<!-- Change: 'Icarus Hall -> Conversion' to 'Icarus Hall - Hail -> Conversion' -->
	<foundation simple="10455" found="11346"/>	<!-- Change: 'Icarus Hall -> Mana Up' to 'Icarus Hall - Hail -> Mana Up' -->
	<foundation simple="10220" found="11333"/>	<!-- Change: 'Icarus Hammer -> Anger' to 'Icarus Hammer - Earth -> Anger' -->
	<foundation simple="10452" found="11334"/>	<!-- Change: 'Icarus Hammer -> Anger' to 'Icarus Hammer - Earth -> Anger' -->
	<foundation simple="10453" found="11335"/>	<!-- Change: 'Icarus Hammer -> Health' to 'Icarus Hammer - Earth -> Health' -->
	<foundation simple="10454" found="11336"/>	<!-- Change: 'Icarus Hammer -> Rsk. Focus' to 'Icarus Hammer - Earth -> Rsk. Focus' -->
	<foundation simple="10221" found="11337"/>	<!-- Change: 'Icarus Hand -> Focus' to 'Icarus Hand - Destruction -> Focus' -->
	<foundation simple="10459" found="11339"/>	<!-- Change: 'Icarus Hand -> Focus' to 'Icarus Hand - Destruction -> Focus' -->
	<foundation simple="10460" found="11340"/>	<!-- Change: 'Icarus Hand -> Haste' to 'Icarus Hand - Destruction -> Haste' -->
	<foundation simple="10458" found="11338"/>	<!-- Change: 'Icarus Hand -> Rsk. Evasion' to 'Icarus Hand - Destruction -> Rsk. Evasion' -->
	<foundation simple="10218" found="11341"/>	<!-- Change: 'Icarus Heavy Arms -> Focus' to 'Icarus Heavy Arms - Lightning -> Focus' -->
	<foundation simple="10437" found="11342"/>	<!-- Change: 'Icarus Heavy Arms -> Focus' to 'Icarus Heavy Arms - Lightning -> Focus' -->
	<foundation simple="10438" found="11343"/>	<!-- Change: 'Icarus Heavy Arms -> Health' to 'Icarus Heavy Arms - Lightning -> Health' -->
	<foundation simple="10439" found="11344"/>	<!-- Change: 'Icarus Heavy Arms -> Light' to 'Icarus Heavy Arms - Lightning -> Light' -->
	<foundation simple="10215" found="11305"/>	<!-- Change: 'Icarus Sawsword -> Focus' to 'Icarus Sawsword - Destruction -> Focus' -->
	<foundation simple="10434" found="11306"/>	<!-- Change: 'Icarus Sawsword -> Focus' to 'Icarus Sawsword - Destruction -> Focus' -->
	<foundation simple="10435" found="11307"/>	<!-- Change: 'Icarus Sawsword -> Health' to 'Icarus Sawsword - Destruction -> Health' -->
	<foundation simple="10436" found="11308"/>	<!-- Change: 'Icarus Sawsword -> Light' to 'Icarus Sawsword - Destruction -> Light' -->
	<foundation simple="10226" found="11309"/>	<!-- Change: 'Icarus Shooter -> Cheap Shot' to 'Icarus Shooter - Concentration -> Cheap Shot' -->
	<foundation simple="10467" found="11310"/>	<!-- Change: 'Icarus Shooter -> Cheap Shot' to 'Icarus Shooter - Concentration -> Cheap Shot' -->
	<foundation simple="10469" found="11312"/>	<!-- Change: 'Icarus Shooter -> Focus' to 'Icarus Shooter - Concentration -> Focus' -->
	<foundation simple="10468" found="11311"/>	<!-- Change: 'Icarus Shooter -> Guidance' to 'Icarus Shooter - Concentration -> Guidance' -->
	<foundation simple="10217" found="11317"/>	<!-- Change: 'Icarus Spirit -> Acumen' to 'Icarus Spirit - Nature -> Acumen' -->
	<foundation simple="10440" found="11318"/>	<!-- Change: 'Icarus Spirit -> Acumen' to 'Icarus Spirit - Nature -> Acumen' -->
	<foundation simple="10442" found="11320"/>	<!-- Change: 'Icarus Spirit -> Conversion' to 'Icarus Spirit - Nature -> Conversion' -->
	<foundation simple="10441" found="11319"/>	<!-- Change: 'Icarus Spirit -> Mana Up' to 'Icarus Spirit - Nature -> Mana Up' -->
	<foundation simple="10223" found="11321"/>	<!-- Change: 'Icarus Spitter -> Cheap Shot' to 'Icarus Spitter - Concentration -> Cheap Shot' -->
	<foundation simple="10443" found="11322"/>	<!-- Change: 'Icarus Spitter -> Cheap Shot' to 'Icarus Spitter - Concentration -> Cheap Shot' -->
	<foundation simple="10445" found="11324"/>	<!-- Change: 'Icarus Spitter -> Focus' to 'Icarus Spitter - Concentration -> Focus' -->
	<foundation simple="10444" found="11323"/>	<!-- Change: 'Icarus Spitter -> Guidance' to 'Icarus Spitter - Concentration -> Guidance' -->
	<foundation simple="10224" found="11313"/>	<!-- Change: 'Icarus Stinger  -> Focus' to 'Icarus Stinger - Destruction -> Focus' -->
	<foundation simple="10461" found="11314"/>	<!-- Change: 'Icarus Stinger  -> Focus' to 'Icarus Stinger - Destruction -> Focus' -->
	<foundation simple="10462" found="11315"/>	<!-- Change: 'Icarus Stinger  -> Health' to 'Icarus Stinger - Destruction -> Health' -->
	<foundation simple="10463" found="11316"/>	<!-- Change: 'Icarus Stinger  -> Light' to 'Icarus Stinger - Destruction -> Light' -->
	<foundation simple="10219" found="11329"/>	<!-- Change: 'Icarus Trident -> Anger' to 'Icarus Trident - Thunder -> Anger' -->
	<foundation simple="10449" found="11330"/>	<!-- Change: 'Icarus Trident -> Anger' to 'Icarus Trident - Thunder -> Anger' -->
	<foundation simple="10450" found="11331"/>	<!-- Change: 'Icarus Trident -> Critical Stun' to 'Icarus Trident - Thunder -> Critical Stun' -->
	<foundation simple="10451" found="11332"/>	<!-- Change: 'Icarus Trident -> Light' to 'Icarus Trident - Thunder -> Light' -->
	<foundation simple="10225" found="11325"/>	<!-- Change: 'Icarus Wingblade -> Focus' to 'Icarus Wingblade - Lightning -> Focus' -->
	<foundation simple="10464" found="11326"/>	<!-- Change: 'Icarus Wingblade -> Focus' to 'Icarus Wingblade - Lightning -> Focus' -->
	<foundation simple="10465" found="11327"/>	<!-- Change: 'Icarus Wingblade -> Health' to 'Icarus Wingblade - Lightning -> Health' -->
	<foundation simple="10466" found="11328"/>	<!-- Change: 'Icarus Wingblade -> Light' to 'Icarus Wingblade - Lightning -> Light' -->
	<foundation simple="52" found="16150"/> 	<!-- Change: 'Vesper Dual Sword' to 'Vesper Dual Sword - Destruction' -->
	<foundation simple="13884" found="16148"/>	<!-- Change: 'Vesper Dual Daggers' to 'Vesper Dual Daggers - Gale' -->
	<foundation simple="13463" found="16048"/>	<!-- Change: 'Vesper Avenger  to 'Vesper Avenger - Landslide  -->
	<foundation simple="14137" found="16075"/>	<!-- Change: 'Vesper Avenger -> Health' to 'Vesper Avenger - Landslide -> Health' -->
	<foundation simple="14136" found="16074"/>	<!-- Change: 'Vesper Avenger -> HP Drain' to 'Vesper Avenger - Landslide -> HP Drain' -->
	<foundation simple="14138" found="16076"/>	<!-- Change: 'Vesper Avenger -> HP Regeneration' to 'Vesper Avenger - Landslide -> HP Regeneration' -->
	<foundation simple="13459" found="16044"/>	<!-- Change: 'Vesper Buster' to 'Vesper Buster - Cleverness' -->
	<foundation simple="14125" found="16063"/>	<!-- Change: 'Vesper Buster -> Acumen' to 'Vesper Buster - Cleverness -> Acumen' -->
	<foundation simple="14126" found="16064"/>	<!-- Change: 'Vesper Buster -> Magic Hold' to 'Vesper Buster - Cleverness -> Magic Hold' -->
	<foundation simple="14124" found="16062"/>	<!-- Change: 'Vesper Buster -> Mana Up' to 'Vesper Buster - Cleverness -> Mana Up' -->
	<foundation simple="13465" found="16050"/>	<!-- Change: 'Vesper Caster -> Acumen' to 'Vesper Caster - Cleverness -> Acumen' -->
	<foundation simple="14142" found="16080"/>	<!-- Change: 'Vesper Caster -> Acumen' to 'Vesper Caster - Cleverness -> Acumen' -->
	<foundation simple="14144" found="16082"/>	<!-- Change: 'Vesper Caster -> Mana Up' to 'Vesper Caster - Cleverness -> Mana Up' -->
	<foundation simple="14143" found="16081"/>	<!-- Change: 'Vesper Caster -> MP Regeneration' to 'Vesper Caster - Cleverness -> MP Regeneration' -->
	<foundation simple="13457" found="16042"/>	<!-- Change: 'Vesper Cutter -> Focus' to 'Vesper Cutter - Thunder -> Focus' -->
	<foundation simple="14120" found="16058"/>	<!-- Change: 'Vesper Cutter -> Focus' to 'Vesper Cutter - Thunder -> Focus' -->
	<foundation simple="14118" found="16056"/>	<!-- Change: 'Vesper Cutter -> Haste' to 'Vesper Cutter - Thunder -> Haste' -->
	<foundation simple="14119" found="16057"/>	<!-- Change: 'Vesper Cutter -> Health' to 'Vesper Cutter - Thunder -> Health' -->
	<foundation simple="13461" found="16046"/>	<!-- Change: 'Vesper Fighter -> Critical Stun' to 'Vesper Fighter - Gale -> Crt. Stun' -->
	<foundation simple="14132" found="16070"/>	<!-- Change: 'Vesper Fighter -> Critical Stun' to 'Vesper Fighter - Gale -> Crt. Stun' -->
	<foundation simple="14130" found="16068"/>	<!-- Change: 'Vesper Fighter -> Focus' to 'Vesper Fighter - Gale -> Focus' -->
	<foundation simple="14131" found="16069"/>	<!-- Change: 'Vesper Fighter -> Health' to 'Vesper Fighter - Gale -> Health' -->
	<foundation simple="13470" found="16055"/>	<!-- Change: 'Vesper Nagan -> Focus' to 'Vesper Nagan - Gale -> Focus' -->
	<foundation simple="14159" found="16097"/>	<!-- Change: 'Vesper Nagan -> Focus' to 'Vesper Nagan - Gale -> Focus' -->
	<foundation simple="14157" found="16095"/>	<!-- Change: 'Vesper Nagan -> Haste' to 'Vesper Nagan - Gale -> Haste' -->
	<foundation simple="14158" found="16096"/>	<!-- Change: 'Vesper Nagan -> Health' to 'Vesper Nagan - Gale -> Health' -->
	<foundation simple="13468" found="16053"/>	<!-- Change: 'Vesper Pincer -> Focus' to 'Vesper Pincer - Thunder -> Focus' -->
	<foundation simple="14153" found="16091"/>	<!-- Change: 'Vesper Pincer -> Focus' to 'Vesper Pincer - Thunder -> Focus' -->
	<foundation simple="14151" found="16089"/>	<!-- Change: 'Vesper Pincer -> Haste' to 'Vesper Pincer - Thunder -> Haste' -->
	<foundation simple="14152" found="16090"/>	<!-- Change: 'Vesper Pincer -> Health' to 'Vesper Pincer - Thunder -> Health' -->
	<foundation simple="13464" found="16049"/>	<!-- Change: 'Vesper Retributer -> Health' to 'Vesper Retributer - Gale -> Health' -->
	<foundation simple="14140" found="16078"/>	<!-- Change: 'Vesper Retributer -> Health' to 'Vesper Retributer - Gale -> Health' -->
	<foundation simple="14141" found="16079"/>	<!-- Change: 'Vesper Retributer -> HP Drain' to 'Vesper Retributer - Gale -> HP Drain' -->
	<foundation simple="14139" found="16077"/>	<!-- Change: 'Vesper Retributer -> HP Regeneration' to 'Vesper Retributer - Gale -> HP Regeneration' -->
	<foundation simple="13460" found="16045"/>	<!-- Change: 'Vesper Shaper -> Critical Damage' to 'Vesper Shaper - Gale -> Crt. Damage' -->
	<foundation simple="14127" found="16065"/>	<!-- Change: 'Vesper Shaper -> Critical Damage' to 'Vesper Shaper - Gale -> Crt. Damage' -->
	<foundation simple="14129" found="16067"/>	<!-- Change: 'Vesper Shaper -> Haste' to 'Vesper Shaper - Gale -> Haste' -->
	<foundation simple="14128" found="16066"/>	<!-- Change: 'Vesper Shaper -> HP Drain' to 'Vesper Shaper - Gale -> HP Drain' -->
	<foundation simple="13469" found="16054"/>	<!-- Change: 'Vesper Sheutjeh -> Cheap Shot' to 'Vesper Schutze - Clairvoyance -> Cheap Shot' -->
	<foundation simple="14154" found="16092"/>	<!-- Change: 'Vesper Sheutjeh -> Cheap Shot' to 'Vesper Schutze - Clairvoyance -> Cheap Shot' -->
	<foundation simple="14156" found="16094"/>	<!-- Change: 'Vesper Sheutjeh -> Critical Slow' to 'Vesper Schutze - Clairvoyance -> Crt. Slow' -->
	<foundation simple="14155" found="16093"/>	<!-- Change: 'Vesper Sheutjeh -> Focus' to 'Vesper Schutze - Clairvoyance -> Focus' -->
	<foundation simple="13466" found="16051"/>	<!-- Change: 'Vesper Singer -> Empower' to 'Vesper Singer - Tidal Wave -> Empower' -->
	<foundation simple="14145" found="16083"/>	<!-- Change: 'Vesper Singer -> Empower' to 'Vesper Singer - Tidal Wave -> Empower' -->
	<foundation simple="14147" found="16085"/>	<!-- Change: 'Vesper Singer -> Magic Hold' to 'Vesper Singer - Tidal Wave -> Magic Hold' -->
	<foundation simple="14146" found="16084"/>	<!-- Change: 'Vesper Singer -> MP Regeneration' to 'Vesper Singer - Tidal Wave -> MP Regeneration' -->
	<foundation simple="13458" found="16043"/>	<!-- Change: 'Vesper Slasher -> Focus' to 'Vesper Slasher - Gale -> Focus' -->
	<foundation simple="14123" found="16061"/>	<!-- Change: 'Vesper Slasher -> Focus' to 'Vesper Slasher - Gale -> Focus' -->
	<foundation simple="14121" found="16059"/>	<!-- Change: 'Vesper Slasher -> Haste' to 'Vesper Slasher - Gale -> Haste' -->
	<foundation simple="14122" found="16060"/>	<!-- Change: 'Vesper Slasher -> Health' to 'Vesper Slasher - Gale -> Health' -->
	<foundation simple="13462" found="16047"/>	<!-- Change: 'Vesper Stormer -> Guidance' to 'Vesper Stormer - Thunder -> Guidance' -->
	<foundation simple="14134" found="16072"/>	<!-- Change: 'Vesper Stormer -> Guidance' to 'Vesper Stormer - Thunder -> Guidance' -->
	<foundation simple="14135" found="16073"/>	<!-- Change: 'Vesper Stormer -> Haste' to 'Vesper Stormer - Thunder -> Haste' -->
	<foundation simple="14133" found="16071"/>	<!-- Change: 'Vesper Stormer -> Health' to 'Vesper Stormer - Thunder -> Health' -->
	<foundation simple="13467" found="16052"/>	<!-- Change: 'Vesper Thrower -> Cheap Shot' to 'Vesper Thrower - Clairvoyance -> Cheap Shot' -->
	<foundation simple="14148" found="16086"/>	<!-- Change: 'Vesper Thrower -> Cheap Shot' to 'Vesper Thrower - Clairvoyance -> Cheap Shot' -->
	<foundation simple="14149" found="16087"/>	<!-- Change: 'Vesper Thrower -> Focus' to 'Vesper Thrower - Clairvoyance -> Focus' -->
	<foundation simple="14150" found="16088"/>	<!-- Change: 'Vesper Thrower -> Critical Slow' to 'Vesper Thrower - Clairvoyance -> Crt. Slow' -->
	<foundation simple="14463" found="16134"/>	<!-- Change: 'Vesper Cutter {PvP}' to 'Vesper Cutter- Thunder {PvP}' -->
	<foundation simple="14480" found="16181"/>	<!-- Change: 'Vesper Cutter {PvP} -> Focus' to 'Vesper Cutter- Thunder {PvP}' -->
	<foundation simple="14478" found="16179"/>	<!-- Change: 'Vesper Cutter {PvP} -> Haste' to 'Vesper Cutter- Thunder {PvP}' -->
	<foundation simple="14479" found="16180"/>	<!-- Change: 'Vesper Cutter {PvP} -> Health' to 'Vesper Cutter- Thunder {PvP}' -->
	<foundation simple="14363" found="14417"/>	<!-- Change: 'Icarus Sawsword {PvP}' to 'Icarus Sawsword - Destruction {PvP}' -->
	<foundation simple="14376" found="14418"/>	<!-- Change: 'Icarus Sawsword {PvP} -> Focus' to 'Icarus Sawsword - Destruction {PvP} -> Focus' -->
	<foundation simple="14377" found="14419"/>	<!-- Change: 'Icarus Sawsword {PvP} -> Health' to 'Icarus Sawsword - Destruction {PvP} -> Health' -->
	<foundation simple="14378" found="14420"/>	<!-- Change: 'Icarus Sawsword {PvP} -> Light' to 'Icarus Sawsword - Destruction {PvP} -> Light' -->
	<foundation simple="14464" found="16135"/>	<!-- Change: 'Vesper Slasher {PvP}' to 'Vesper Slasher- Gail {PvP}' -->
	<foundation simple="14483" found="16184"/>	<!-- Change: 'Vesper Slasher {PvP} -> Focus' to 'Vesper Slasher- Gail {PvP}' -->
	<foundation simple="14481" found="16182"/>	<!-- Change: 'Vesper Slasher {PvP} -> Haste' to 'Vesper Slasher- Gail {PvP}' -->
	<foundation simple="14482" found="16183"/>	<!-- Change: 'Vesper Slasher {PvP} -> Health' to 'Vesper Slasher- Gail {PvP}' -->
	<foundation simple="14366" found="14453"/>	<!-- Change: 'Icarus Heavy Arms {PvP}' to 'Icarus Heavy Arms - Lightning {PvP}' -->
	<foundation simple="14379" found="14454"/>	<!-- Change: 'Icarus Heavy Arms {PvP} -> Focus' to 'Icarus Heavy Arms - Lightning {PvP} -> Focus' -->
	<foundation simple="14380" found="14455"/>	<!-- Change: 'Icarus Heavy Arms {PvP} -> Health' to 'Icarus Heavy Arms - Lightning {PvP} -> Health' -->
	<foundation simple="14381" found="14456"/>	<!-- Change: 'Icarus Heavy Arms {PvP} -> Light' to 'Icarus Heavy Arms - Lightning {PvP} -> Light' -->
	<foundation simple="14368" found="14445"/>	<!-- Change: 'Icarus Hammer {PvP}' to 'Icarus Hammer - Earth {PvP}' -->
	<foundation simple="14394" found="14446"/>	<!-- Change: 'Icarus Hammer {PvP} -> Anger' to 'Icarus Hammer - Earth {PvP} -> Anger' -->
	<foundation simple="14395" found="14447"/>	<!-- Change: 'Icarus Hammer {PvP} -> Health' to 'Icarus Hammer - Earth {PvP} -> Health' -->
	<foundation simple="14396" found="14448"/>	<!-- Change: 'Icarus Hammer {PvP} -> Rsk. Focus' to 'Icarus Hammer - Earth {PvP} -> Rsk. Focus' -->
	<foundation simple="14469" found="16140"/>	<!-- Change: 'Vesper Avenger {PvP}' to 'Vesper Avenger - Landslide {PvP}' -->
	<foundation simple="14497" found="16198"/>	<!-- Change: 'Vesper Avenger {PvP} -> Health' to 'Vesper Avenger - Landslide {PvP}' -->
	<foundation simple="14496" found="16197"/>	<!-- Change: 'Vesper Avenger {PvP} -> HP Drain' to 'Vesper Avenger - Landslide {PvP}' -->
	<foundation simple="14498" found="16199"/>	<!-- Change: 'Vesper Avenger {PvP} -> HP Regeneration' to 'Vesper Avenger - Landslide {PvP}' -->
	<foundation simple="14470" found="16141"/>	<!-- Change: 'Vesper Retributer {PvP}' to 'Vesper Retributer - Gail {PvP}' -->
	<foundation simple="14500" found="16201"/>	<!-- Change: 'Vesper Retributer {PvP} -> Health' to 'Vesper Retributer - Gail {PvP}' -->
	<foundation simple="14501" found="16202"/>	<!-- Change: 'Vesper Retributer {PvP} -> HP Drain' to 'Vesper Retributer - Gail {PvP}' -->
	<foundation simple="14499" found="16200"/>	<!-- Change: 'Vesper Retributer {PvP} -> HP Regeneration' to 'Vesper Retributer - Gail {PvP}' -->
	<foundation simple="14364" found="14413"/>	<!-- Change: 'Icarus Disperser {PvP}' to 'Icarus Disperser - Confusion {PvP}' -->
	<foundation simple="14390" found="14416"/>	<!-- Change: 'Icarus Disperser {PvP} -> Critical Damage' to 'Icarus Disperser - Confusion {PvP} -> Critical Damage' -->
	<foundation simple="14389" found="14415"/>	<!-- Change: 'Icarus Disperser {PvP} -> Evasion' to 'Icarus Disperser - Confusion {PvP} -> Evasion' -->
	<foundation simple="14388" found="14414"/>	<!-- Change: 'Icarus Disperser {PvP} -> Focus' to 'Icarus Disperser - Confusion {PvP} -> Focus' -->
	<foundation simple="14466" found="16137"/>	<!-- Change: 'Vesper Shaper {PvP}' to 'Vesper Shaper - Gail {PvP}' -->
	<foundation simple="14487" found="16188"/>	<!-- Change: 'Vesper Shaper {PvP} -> Crt. Damage' to 'Vesper Shaper - Gail {PvP}' -->
	<foundation simple="14489" found="16190"/>	<!-- Change: 'Vesper Shaper {PvP} -> Haste' to 'Vesper Shaper - Gail {PvP}' -->
	<foundation simple="14488" found="16189"/>	<!-- Change: 'Vesper Shaper {PvP} -> HP Drain' to 'Vesper Shaper - Gail {PvP}' -->
	<foundation simple="14371" found="14433"/>	<!-- Change: 'Icarus Spitter {PvP}' to 'Icarus Spitter - Concentration {PvP}' -->
	<foundation simple="14385" found="14434"/>	<!-- Change: 'Icarus Spitter {PvP} -> Cheap Shot' to 'Icarus Spitter - Concentration {PvP} -> Cheap Shot' -->
	<foundation simple="14387" found="14436"/>	<!-- Change: 'Icarus Spitter {PvP} -> Focus' to 'Icarus Spitter - Concentration {PvP} -> Focus' -->
	<foundation simple="14386" found="14435"/>	<!-- Change: 'Icarus Spitter {PvP} -> Guidance' to 'Icarus Spitter - Concentration {PvP} -> Guidance' -->
	<foundation simple="14473" found="16144"/>	<!-- Change: 'Vesper Thrower {PvP}' to 'Vesper Thrower - Clairvoyance {PvP}' -->
	<foundation simple="14508" found="16209"/>	<!-- Change: 'Vesper Thrower {PvP} -> Cheap Shot' to 'Vesper Thrower - Clairvoyance {PvP}' -->
	<foundation simple="14510" found="16211"/>	<!-- Change: 'Vesper Thrower {PvP} -> Crt. Slow' to 'Vesper Thrower - Clairvoyance {PvP}' -->
	<foundation simple="14509" found="16210"/>	<!-- Change: 'Vesper Thrower {PvP} -> Focus' to 'Vesper Thrower - Clairvoyance {PvP}' -->
	<foundation simple="14369" found="14449"/>	<!-- Change: 'Icarus Hand {PvP}' to 'Icarus Hand - Destruction {PvP}' -->
	<foundation simple="14401" found="14451"/>	<!-- Change: 'Icarus Hand {PvP} -> Focus' to 'Icarus Hand - Destruction {PvP} -> Focus' -->
	<foundation simple="14402" found="14452"/>	<!-- Change: 'Icarus Hand {PvP} -> Haste' to 'Icarus Hand - Destruction {PvP} -> Haste' -->
	<foundation simple="14400" found="14450"/>	<!-- Change: 'Icarus Hand {PvP} -> Rsk. Evasion' to 'Icarus Hand - Destruction {PvP} -> Rsk. Evasion' -->
	<foundation simple="14467" found="16138"/>	<!-- Change: 'Vesper Fighter {PvP}' to 'Vesper Fighter - Gail {PvP}' -->
	<foundation simple="14492" found="16193"/>	<!-- Change: 'Vesper Fighter {PvP} -> Crt. Stun' to 'Vesper Fighter - Gail {PvP}' -->
	<foundation simple="14490" found="16191"/>	<!-- Change: 'Vesper Fighter {PvP} -> Focus' to 'Vesper Fighter - Gail {PvP}' -->
	<foundation simple="14491" found="16192"/>	<!-- Change: 'Vesper Fighter {PvP} -> Health' to 'Vesper Fighter - Gail {PvP}' -->
	<foundation simple="14375" found="14412"/>	<!-- Change: 'Icarus Dual Sword {PvP}' to 'Icarus Dual Sword - Destruction {PvP}' -->
	<foundation simple="14462" found="16151"/>	<!-- Change: 'Vesper Dual Sword {PvP}' to 'Vesper Dual Sword - Destruction {PvP}' -->
	<foundation simple="14461" found="14529"/>	<!-- Change: 'Icarus Dual Daggers {PvP}' to 'Icarus Dual Daggers - Confusion {PvP}' -->
	<foundation simple="14477" found="16149"/>	<!-- Change: 'Vesper Dual Daggers {PvP}' to 'Vesper Dual Daggers - Gale {PvP}' -->
	<foundation simple="14367" found="14441"/>	<!-- Change: 'Icarus Trident {PvP}' to 'Icarus Trident - Thunder {PvP}' -->
	<foundation simple="14391" found="14442"/>	<!-- Change: 'Icarus Trident {PvP} -> Anger' to 'Icarus Trident - Thunder {PvP} -> Anger' -->
	<foundation simple="14392" found="14443"/>	<!-- Change: 'Icarus Trident {PvP} -> Critical Stun' to 'Icarus Trident - Thunder {PvP} -> Critical Stun' -->
	<foundation simple="14393" found="14444"/>	<!-- Change: 'Icarus Trident {PvP} -> Light' to 'Icarus Trident - Thunder {PvP} -> Light' -->
	<foundation simple="14468" found="16139"/>	<!-- Change: 'Vesper Stormer {PvP}' to 'Vesper Stormer - Thunder {PvP}' -->
	<foundation simple="14494" found="16195"/>	<!-- Change: 'Vesper Stormer {PvP} -> Guidance' to 'Vesper Stormer - Thunder {PvP}' -->
	<foundation simple="14495" found="16196"/>	<!-- Change: 'Vesper Stormer {PvP} -> Haste' to 'Vesper Stormer - Thunder {PvP}' -->
	<foundation simple="14493" found="16194"/>	<!-- Change: 'Vesper Stormer {PvP} -> Health' to 'Vesper Stormer - Thunder {PvP}' -->
	<foundation simple="14370" found="14457"/>	<!-- Change: 'Icarus Hall {PvP}' to 'Icarus Hall - Hail {PvP}' -->
	<foundation simple="14399" found="14460"/>	<!-- Change: 'Icarus Hall {PvP} -> Acumen' to 'Icarus Hall - Hail {PvP} -> Acumen' -->
	<foundation simple="14398" found="14459"/>	<!-- Change: 'Icarus Hall {PvP} -> Conversion' to 'Icarus Hall - Hail {PvP} -> Conversion' -->
	<foundation simple="14397" found="14458"/>	<!-- Change: 'Icarus Hall {PvP} -> Mana Up' to 'Icarus Hall - Hail {PvP} -> Mana Up' -->
	<foundation simple="14365" found="14429"/>	<!-- Change: 'Icarus Spirit {PvP}' to 'Icarus Spirit - Nature {PvP}' -->
	<foundation simple="14382" found="14430"/>	<!-- Change: 'Icarus Spirit {PvP} -> Acumen' to 'Icarus Spirit - Nature {PvP} -> Acumen' -->
	<foundation simple="14384" found="14432"/>	<!-- Change: 'Icarus Spirit {PvP} -> Conversion' to 'Icarus Spirit - Nature {PvP} -> Conversion' -->
	<foundation simple="14383" found="14431"/>	<!-- Change: 'Icarus Spirit {PvP} -> Mana Up' to 'Icarus Spirit - Nature {PvP} -> Mana Up' -->
	<foundation simple="14472" found="16143"/>	<!-- Change: 'Vesper Singer {PvP}' to 'Vesper Singer - Tidal Wave {PvP}' -->
	<foundation simple="14505" found="16206"/>	<!-- Change: 'Vesper Singer {PvP} -> Empower' to 'Vesper Singer - Tidal Wave {PvP}' -->
	<foundation simple="14507" found="16208"/>	<!-- Change: 'Vesper Singer {PvP} -> Magic Hold' to 'Vesper Singer - Tidal Wave {PvP}' -->
	<foundation simple="14506" found="16207"/>	<!-- Change: 'Vesper Singer {PvP} -> MP Regeneration' to 'Vesper Singer - Tidal Wave {PvP}' -->
	<foundation simple="14372" found="14425"/>	<!-- Change: 'Icarus Stinger {PvP}' to 'Icarus Stinger - Destruction {PvP}' -->
	<foundation simple="14403" found="14426"/>	<!-- Change: 'Icarus Stinger {PvP} -> Focus' to 'Icarus Stinger - Destruction {PvP} -> Focus' -->
	<foundation simple="14404" found="14427"/>	<!-- Change: 'Icarus Stinger {PvP} -> Health' to 'Icarus Stinger - Destruction {PvP} -> Health' -->
	<foundation simple="14405" found="14428"/>	<!-- Change: 'Icarus Stinger {PvP} -> Light' to 'Icarus Stinger - Destruction {PvP} -> Light' -->
	<foundation simple="14474" found="16145"/>	<!-- Change: 'Vesper Pincer {PvP}' to 'Vesper Pincer- Thunder {PvP}' -->
	<foundation simple="14513" found="16214"/>	<!-- Change: 'Vesper Pincer {PvP} -> Focus' to 'Vesper Pincer- Thunder {PvP}' -->
	<foundation simple="14511" found="16212"/>	<!-- Change: 'Vesper Pincer {PvP} -> Haste' to 'Vesper Pincer- Thunder {PvP}' -->
	<foundation simple="14512" found="16213"/>	<!-- Change: 'Vesper Pincer {PvP} -> Health' to 'Vesper Pincer- Thunder {PvP}' -->
	<foundation simple="14373" found="14437"/>	<!-- Change: 'Icarus Wingblade {PvP}' to 'Icarus Wingblade - Lightning {PvP}' -->
	<foundation simple="14406" found="14438"/>	<!-- Change: 'Icarus Wingblade {PvP} -> Focus' to 'Icarus Wingblade - Lightning {PvP} -> Focus' -->
	<foundation simple="14407" found="14439"/>	<!-- Change: 'Icarus Wingblade {PvP} -> Health' to 'Icarus Wingblade - Lightning {PvP} -> Health' -->
	<foundation simple="14408" found="14440"/>	<!-- Change: 'Icarus Wingblade {PvP} -> Light' to 'Icarus Wingblade - Lightning {PvP} -> Light' -->
	<foundation simple="14476" found="16147"/>	<!-- Change: 'Vesper Nagan {PvP}' to 'Vesper Nagan - Gail {PvP}' -->
	<foundation simple="14519" found="16220"/>	<!-- Change: 'Vesper Nagan {PvP} -> Focus' to 'Vesper Nagan - Gail {PvP}' -->
	<foundation simple="14517" found="16218"/>	<!-- Change: 'Vesper Nagan {PvP} -> Haste' to 'Vesper Nagan - Gail {PvP}' -->
	<foundation simple="14518" found="16219"/>	<!-- Change: 'Vesper Nagan {PvP} -> Health' to 'Vesper Nagan - Gail {PvP}' -->
	<foundation simple="14374" found="14421"/>	<!-- Change: 'Icarus Shooter {PvP}' to 'Icarus Shooter - Concentration {PvP}' -->
	<foundation simple="14409" found="14422"/>	<!-- Change: 'Icarus Shooter {PvP} -> Cheap Shot' to 'Icarus Shooter - Concentration {PvP} -> Cheap Shot' -->
	<foundation simple="14411" found="14424"/>	<!-- Change: 'Icarus Shooter {PvP} -> Focus' to 'Icarus Shooter - Concentration {PvP} -> Focus' -->
	<foundation simple="14410" found="14423"/>	<!-- Change: 'Icarus Shooter {PvP} -> Guidance' to 'Icarus Shooter - Concentration {PvP} -> Guidance' -->
	<foundation simple="14475" found="16146"/>	<!-- Change: 'Vesper Sheutjeh {PvP}' to 'Vesper Schutze - Clairvoyance {PvP}' -->
	<foundation simple="14514" found="16215"/>	<!-- Change: 'Vesper Sheutjeh {PvP} -> Cheap Shot' to 'Vesper Schutze - Clairvoyance {PvP}' -->
	<foundation simple="14516" found="16217"/>	<!-- Change: 'Vesper Sheutjeh {PvP} -> Crt. Slow' to 'Vesper Schutze - Clairvoyance {PvP}' -->
	<foundation simple="14515" found="16216"/>	<!-- Change: 'Vesper Sheutjeh {PvP} -> Focus' to 'Vesper Schutze - Clairvoyance {PvP}' -->
	<foundation simple="13140" found="16837"/>
	<foundation simple="13448" found="16843"/>
	<foundation simple="13449" found="16846"/>
	<foundation simple="13450" found="16849"/>
	<foundation simple="13141" found="16838"/>
	<foundation simple="13451" found="16844"/>
	<foundation simple="13452" found="16847"/>
	<foundation simple="13453" found="16850"/>
	<foundation simple="13142" found="16839"/>
	<foundation simple="13454" found="16845"/>
	<foundation simple="13455" found="16848"/>
	<foundation simple="13456" found="16851"/>
</list>