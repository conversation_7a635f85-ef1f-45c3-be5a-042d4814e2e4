<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE list SYSTEM "../events.dtd">
<list>
	<event id="7" name="Protect The King" type="FIGHT_CLUB_EVENT" impl="fightclub.ProtectTheKing">
		<parameter name="icon" value="L2UI_CH3.fishskill"/>
		<parameter name="desc" value="Team Event. One of the City Defenders is selected to be the King. If he will get killed, attackers will take control over the City! Team who will be defending the City when Event ends, will claim the Reward!"/>
		<parameter name="roundRunTime" value="600"/>
        <parameter name="roundEvent" value="false"/>
        <parameter name="isAutoTimed" value="true"/>
        <parameter name="autoTimes" value="05:00,13:00,21:00"/>
        <parameter name="teamed" value="true"/>
		<parameter name="buffer" value="true"/>
		<parameter name="fighterBuffs" value="1204,2;1086,2;1068,3;1045,6;1048,6;1040,3;1062,2;1036,2"/>
		<parameter name="mageBuffs" value="1204,2;1085,3;1059,3;1045,6;1048,6;1040,3;1062,2;1036,2"/>
		<parameter name="rootBetweenRounds" value="true"/>
		<parameter name="excludedClasses" value=""/>
		<parameter name="respawnTime" value="5"/>
		<parameter name="respawnTime_DEFENDER" value="15"/>
		<parameter name="ressAllowed" value="false"/>
		<parameter name="showPersonality" value="true"/>
		<parameter name="badgesKillPlayer" value="0"/>
		<parameter name="badgesKillPet" value="0"/>
		<parameter name="badgesDie" value="0"/>
		<parameter name="badgesWin" value="6"/>
		<parameter name="badgesLose" value="2"/>
		<parameter name="topKillerReward" value="3"/>

		<parameter name="waitSecondsAfterKingDeath" value="5"/>
		<parameter name="kingNameColor" value="00fff6"/>
		<parameter name="announceKingName" value="False"/>
	</event>
</list>