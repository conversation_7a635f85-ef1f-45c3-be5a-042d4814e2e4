<title>Choose skill to add</title>
<center>
[admin_add_skill 15 16|Charm 16 (Lv 40)]%%
[admin_add_skill 15 17|Charm 17 (Lv 40)]%%
[admin_add_skill 15 18|Charm 18 (Lv 40)]%%
[admin_add_skill 15 19|Charm 19 (Lv 43)]%%
[admin_add_skill 15 20|Charm 20 (Lv 43)]%%
[admin_add_skill 15 21|Charm 21 (Lv 43)]%%
[admin_add_skill 15 22|Charm 22 (Lv 46)]%%
[admin_add_skill 15 23|Charm 23 (Lv 46)]%%
[admin_add_skill 15 24|Charm 24 (Lv 46)]%%
[admin_add_skill 15 25|Charm 25 (Lv 49)]%%
[admin_add_skill 15 26|Charm 26 (Lv 49)]%%
[admin_add_skill 15 27|Charm 27 (Lv 49)]%%
[admin_add_skill 15 28|Charm 28 (Lv 52)]%%
[admin_add_skill 15 29|Charm 29 (Lv 52)]%%
[admin_add_skill 15 30|Charm 30 (Lv 52)]%%
[admin_add_skill 15 31|Charm 31 (Lv 55)]%%
[admin_add_skill 15 32|Charm 32 (Lv 55)]%%
[admin_add_skill 15 33|Charm 33 (Lv 55)]%%
[admin_add_skill 15 34|Charm 34 (Lv 58)]%%
[admin_add_skill 15 35|Charm 35 (Lv 58)]%%
[admin_add_skill 15 36|Charm 36 (Lv 58)]%%
[admin_add_skill 15 37|Charm 37 (Lv 60)]%%
[admin_add_skill 15 38|Charm 38 (Lv 60)]%%
[admin_add_skill 15 39|Charm 39 (Lv 62)]%%
[admin_add_skill 15 40|Charm 40 (Lv 62)]%%
[admin_add_skill 15 41|Charm 41 (Lv 64)]%%
[admin_add_skill 15 42|Charm 42 (Lv 64)]%%
[admin_add_skill 15 43|Charm 43 (Lv 66)]%%
[admin_add_skill 15 44|Charm 44 (Lv 66)]%%
[admin_add_skill 15 45|Charm 45 (Lv 68)]%%
[admin_add_skill 15 46|Charm 46 (Lv 68)]%%
[admin_add_skill 15 47|Charm 47 (Lv 70)]%%
[admin_add_skill 15 48|Charm 48 (Lv 70)]%%
[admin_add_skill 15 49|Charm 49 (Lv 72)]%%
[admin_add_skill 15 50|Charm 50 (Lv 72)]%%
[admin_add_skill 15 51|Charm 51 (Lv 74)]%%
[admin_add_skill 15 52|Charm 52 (Lv 74)]%%
[admin_add_skill 21 2|Poison Recovery 2 (Lv 40)]%%
[admin_add_skill 21 3|Poison Recovery 3 (Lv 60)]%%
[admin_add_skill 58 19|Elemental Heal 19 (Lv 40)]%%
[admin_add_skill 58 20|Elemental Heal 20 (Lv 40)]%%
[admin_add_skill 58 21|Elemental Heal 21 (Lv 40)]%%
[admin_add_skill 58 22|Elemental Heal 22 (Lv 43)]%%
[admin_add_skill 58 23|Elemental Heal 23 (Lv 43)]%%
[admin_add_skill 58 24|Elemental Heal 24 (Lv 43)]%%
[admin_add_skill 58 25|Elemental Heal 25 (Lv 46)]%%
[admin_add_skill 58 26|Elemental Heal 26 (Lv 46)]%%
[admin_add_skill 58 27|Elemental Heal 27 (Lv 46)]%%
[admin_add_skill 58 28|Elemental Heal 28 (Lv 49)]%%
[admin_add_skill 58 29|Elemental Heal 29 (Lv 49)]%%
[admin_add_skill 58 30|Elemental Heal 30 (Lv 49)]%%
[admin_add_skill 58 31|Elemental Heal 31 (Lv 52)]%%
[admin_add_skill 58 32|Elemental Heal 32 (Lv 52)]%%
[admin_add_skill 58 33|Elemental Heal 33 (Lv 52)]%%
[admin_add_skill 58 34|Elemental Heal 34 (Lv 55)]%%
[admin_add_skill 58 35|Elemental Heal 35 (Lv 55)]%%
[admin_add_skill 58 36|Elemental Heal 36 (Lv 55)]%%
[admin_add_skill 58 37|Elemental Heal 37 (Lv 58)]%%
[admin_add_skill 58 38|Elemental Heal 38 (Lv 58)]%%
[admin_add_skill 58 39|Elemental Heal 39 (Lv 58)]%%
[admin_add_skill 58 40|Elemental Heal 40 (Lv 60)]%%
[admin_add_skill 58 41|Elemental Heal 41 (Lv 60)]%%
[admin_add_skill 58 42|Elemental Heal 42 (Lv 62)]%%
[admin_add_skill 58 43|Elemental Heal 43 (Lv 62)]%%
[admin_add_skill 58 44|Elemental Heal 44 (Lv 64)]%%
[admin_add_skill 58 45|Elemental Heal 45 (Lv 64)]%%
<br>
<button value="Page2" action="bypass -h admin_skill_index Elf/E_SwordSinger2" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index Elf/E_SwordSinger3" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
