<title>Choose skill to add</title>
<center>
[admin_add_skill 1048 4|Bless the Soul 4 (Lv 56)]%%
[admin_add_skill 1048 5|Bless the Soul 5 (Lv 62)]%%
[admin_add_skill 1048 6|Bless the Soul 6 (Lv 70)]%%
[admin_add_skill 1050 1|Return 1 (Lv 40)]%%
[admin_add_skill 1050 2|Return 2 (Lv 56)]%%
[admin_add_skill 1062 2|Berserker Spirit 2 (Lv 52)]%%
[admin_add_skill 1068 3|Might 3 (Lv 40)]%%
[admin_add_skill 1073 2|Kiss of Eva 2 (Lv 52)]%%
[admin_add_skill 1077 2|Focus 2 (Lv 44)]%%
[admin_add_skill 1077 3|Focus 3 (Lv 52)]%%
[admin_add_skill 1078 3|Greater Concentration 3 (Lv 44)]%%
[admin_add_skill 1078 4|Greater Concentration 4 (Lv 52)]%%
[admin_add_skill 1078 5|Greater Concentration 5 (Lv 60)]%%
[admin_add_skill 1078 6|Greater Concentration 6 (Lv 68)]%%
[admin_add_skill 1085 3|Greater Acumen 3 (Lv 48)]%%
[admin_add_skill 1086 1|Haste 1 (Lv 44)]%%
[admin_add_skill 1086 2|Haste 2 (Lv 52)]%%
[admin_add_skill 1182 1|Resist Aqua 1 (Lv 58)]%%
[admin_add_skill 1182 2|Resist Aqua 2 (Lv 62)]%%
[admin_add_skill 1182 3|Resist Aqua 3 (Lv 66)]%%
[admin_add_skill 1189 1|Resist Wind 1 (Lv 58)]%%
[admin_add_skill 1189 2|Resist Wind 2 (Lv 62)]%%
[admin_add_skill 1189 3|Resist Wind 3 (Lv 66)]%%
[admin_add_skill 1191 2|Resist Fire 2 (Lv 40)]%%
[admin_add_skill 1191 3|Resist Fire 3 (Lv 44)]%%
[admin_add_skill 1201 10|Dryad Root 10 (Lv 40)]%%
[admin_add_skill 1201 11|Dryad Root 11 (Lv 40)]%%
[admin_add_skill 1201 12|Dryad Root 12 (Lv 40)]%%
[admin_add_skill 1201 13|Dryad Root 13 (Lv 44)]%%
[admin_add_skill 1201 14|Dryad Root 14 (Lv 44)]%%
[admin_add_skill 1201 15|Dryad Root 15 (Lv 44)]%%
[admin_add_skill 1201 16|Dryad Root 16 (Lv 48)]%%
[admin_add_skill 1201 17|Dryad Root 17 (Lv 48)]%%
[admin_add_skill 1201 18|Dryad Root 18 (Lv 48)]%%
[admin_add_skill 1201 19|Dryad Root 19 (Lv 52)]%%
[admin_add_skill 1201 20|Dryad Root 20 (Lv 52)]%%
[admin_add_skill 1201 21|Dryad Root 21 (Lv 52)]%%
[admin_add_skill 1201 22|Dryad Root 22 (Lv 56)]%%
[admin_add_skill 1201 23|Dryad Root 23 (Lv 56)]%%
[admin_add_skill 1201 24|Dryad Root 24 (Lv 56)]%%
[admin_add_skill 1201 25|Dryad Root 25 (Lv 58)]%%
[admin_add_skill 1201 26|Dryad Root 26 (Lv 60)]%%
[admin_add_skill 1201 27|Dryad Root 27 (Lv 62)]%%
[admin_add_skill 1201 28|Dryad Root 28 (Lv 64)]%%
[admin_add_skill 1201 29|Dryad Root 29 (Lv 66)]%%
[admin_add_skill 1201 30|Dryad Root 30 (Lv 68)]%%
[admin_add_skill 1201 31|Dryad Root 31 (Lv 70)]%%
[admin_add_skill 1201 32|Dryad Root 32 (Lv 72)]%%
[admin_add_skill 1201 33|Dryad Root 33 (Lv 74)]%%
[admin_add_skill 1240 1|Guidance 1 (Lv 40)]%%
[admin_add_skill 1240 2|Guidance 2 (Lv 48)]%%
[admin_add_skill 1240 3|Guidance 3 (Lv 56)]%%
[admin_add_skill 1242 1|Death Whisper 1 (Lv 40)]%%
[admin_add_skill 1242 2|Death Whisper 2 (Lv 48)]%%
[admin_add_skill 1242 3|Death Whisper 3 (Lv 56)]%%
[admin_add_skill 1243 1|Bless Shield 1 (Lv 40)]%%
[admin_add_skill 1243 2|Bless Shield 2 (Lv 48)]%%
[admin_add_skill 1243 3|Bless Shield 3 (Lv 56)]%%
[admin_add_skill 1272 1|Word of Fear 1 (Lv 44)]%%
[admin_add_skill 1272 2|Word of Fear 2 (Lv 48)]%%
[admin_add_skill 1272 3|Word of Fear 3 (Lv 52)]%%
[admin_add_skill 1272 4|Word of Fear 4 (Lv 56)]%%
[admin_add_skill 1272 5|Word of Fear 5 (Lv 58)]%%
[admin_add_skill 1272 6|Word of Fear 6 (Lv 60)]%%
[admin_add_skill 1272 7|Word of Fear 7 (Lv 62)]%%
[admin_add_skill 1272 8|Word of Fear 8 (Lv 64)]%%
[admin_add_skill 1272 9|Word of Fear 9 (Lv 66)]%%
[admin_add_skill 1272 10|Word of Fear 10 (Lv 68)]%%
[admin_add_skill 1272 11|Word of Fear 11 (Lv 70)]%%
[admin_add_skill 1272 12|Word of Fear 12 (Lv 72)]%%
[admin_add_skill 1272 13|Word of Fear 13 (Lv 74)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index Human/H_Prophet" width=40 height=15><br>
<button value="Page2" action="bypass -h admin_skill_index Human/H_Prophet2" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index Human/H_Prophet3" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
