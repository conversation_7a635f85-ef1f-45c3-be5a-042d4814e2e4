<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE list SYSTEM "events.dtd">
<list>
	<event id="111" name="Archaic Fortress" type="SIEGE_EVENT" impl="FortressSiege">
		<on_init>
			<init name="upgradeable_doors" />
			<init name="commander_doors" />
			<init name="enter_doors" />
			<init name="siege_zones" />
			<init name="flag_zones" />
			<spawn name="flag_pole" />
			
			<spawn name="peace_commanders" />
		</on_init>
		<on_start>
			<despawn name="peace_commanders" />
			<!--Спавним гвардов-->
			<spawn name="default_guards" />
			<spawn name="guards_live_with_c_center" />
			<spawn name="siege_commanders" />
			<if name="old_owner">
				<spawn name="owner_flagdef"/>
				<spawn name="mercenary" />
			</if>
			<else>
				<spawn name="noowner_flagdef" />
			</else>
			<if name="reinforce_1">
				<spawn name="reinforce_1" />
			</if>
			<if name="reinforce_2">
				<spawn name="reinforce_2" />
			</if>
			<if name="dwarvens">
				<spawn name="dwarvens" />
			</if>
			<!--Обновляем двери-->
			<refresh name="upgradeable_doors" />
			<refresh name="commander_doors" />
			<active name="siege_zones" />
			<teleport_players id="attackers"/>
			<teleport_players id="spectators"/>
		</on_start>
		<on_stop>
			<if name="old_owner">
				<despawn name="owner_flagdef"/>
				<despawn name="mercenary" />
			</if>
			<else>
				<despawn name="noowner_flagdef" />
			</else>
			<despawn name="reinforce_1"/>
			<despawn name="reinforce_2"/>
			<despawn name="dwarvens"/>
			<despawn name="default_guards" />
			<despawn name="guards_live_with_c_center" />
			<despawn name="siege_commanders" />

			<start name="registration" />
			<!--Капитаны и менчант-->
			<spawn name="peace_commanders" />
			
			<!--Обновляем двери-->
			<refresh name="upgradeable_doors" />
			<refresh name="commander_doors" />
			<teleport_players id="spectators"/>
			<deactive name="siege_zones" />
		</on_stop>
		<on_time>
			<on time="-3600">
				<announce val="3600" />
			</on>
			<on time="-1800">
				<announce val="1800" />
			</on>
			<on time="-600"> <!--10 min to siege-->
				<announce val="600" />
				<stop name="registration" />
				<despawn name="merchant"/>
			</on>
			<on time="-5">
				<announce val="5" />
			</on>
			<on time="-4">
				<announce val="4" />
			</on>
			<on time="-3">
				<announce val="3" />
			</on>
			<on time="-2">
				<announce val="2" />
			</on>
			<on time="-1">
				<announce val="1" />
			</on>
			<on time="0">
				<start name="event" />
				<play_sound type="SOUND" range="-1" sound="NS20_F" />
			</on>
			<on time="98">
				<play_sound type="SOUND" range="-1" sound="NB02_F" />
			</on>
			<on time="200">
				<play_sound type="SOUND" range="-1" sound="NS19_S01" />
			</on>
			<on time="300">
				<give_item id="-300" count="31" />
			</on>
			<on time="310">
				<play_sound type="SOUND" range="-1" sound="NS12_F" />
			</on>
			<on time="403">
				<play_sound type="SOUND" range="-1" sound="NS19_F" />
			</on>
			<on time="512">
				<play_sound type="SOUND" range="-1" sound="NS18_S01" />
			</on>
			<on time="596">
				<play_sound type="SOUND" range="-1" sound="NS02_F" />
			</on>
			<on time="600">
				<give_item id="-300" count="31" />
			</on>
			<on time="691">
				<play_sound type="SOUND" range="-1" sound="NS12_S01" />
			</on>
			<on time="785">
				<play_sound type="SOUND" range="-1" sound="NS18_F" />
			</on>
			<on time="883">
				<play_sound type="SOUND" range="-1" sound="NS02_S01" />
			</on>
			<on time="900">
				<give_item id="-300" count="31" />
			</on>
			<on time="978">
				<play_sound type="SOUND" range="-1" sound="NS20_F" />
			</on>
			<on time="1076">
				<play_sound type="SOUND" range="-1" sound="NB02_F" />
			</on>
			<on time="1178">
				<play_sound type="SOUND" range="-1" sound="NS19_S01" />
			</on>
			<on time="1200">
				<give_item id="-300" count="31" />
			</on>
			<on time="1288">
				<play_sound type="SOUND" range="-1" sound="NS12_F" />
			</on>
			<on time="1381">
				<play_sound type="SOUND" range="-1" sound="NS19_F" />
			</on>
			<on time="1490">
				<play_sound type="SOUND" range="-1" sound="NS18_S01" />
			</on>
			<on time="1500">
				<give_item id="-300" count="31" />
			</on>
			<on time="1574">
				<play_sound type="SOUND" range="-1" sound="NS02_F" />
			</on>
			<on time="1669">
				<play_sound type="SOUND" range="-1" sound="NS12_S01" />
			</on>
			<on time="1763">
				<play_sound type="SOUND" range="-1" sound="NS18_F" />
			</on>
			<on time="1800">
				<give_item id="-300" count="31" />
			</on>
			<on time="1861">
				<play_sound type="SOUND" range="-1" sound="NS02_S01" />
			</on>
			<on time="1956">
				<play_sound type="SOUND" range="-1" sound="NS20_F" />
			</on>
			<on time="2054">
				<play_sound type="SOUND" range="-1" sound="NB02_F" />
			</on>
			<on time="2100">
				<give_item id="-300" count="31" />
			</on>
			<on time="2156">
				<play_sound type="SOUND" range="-1" sound="NS19_S01" />
			</on>
			<on time="2266">
				<play_sound type="SOUND" range="-1" sound="NS12_F" />
			</on>
			<on time="2359">
				<play_sound type="SOUND" range="-1" sound="NS19_F" />
			</on>
			<on time="2400">
				<give_item id="-300" count="31" />
			</on>
			<on time="2468">
				<play_sound type="SOUND" range="-1" sound="NS18_S01" />
			</on>
			<on time="2552">
				<play_sound type="SOUND" range="-1" sound="NS02_F" />
			</on>
			<on time="2647">
				<play_sound type="SOUND" range="-1" sound="NS12_S01" />
			</on>
			<on time="2700">
				<give_item id="-300" count="31" />
			</on>
			<on time="2741">
				<play_sound type="SOUND" range="-1" sound="NS18_F" />
			</on>
			<on time="2839">
				<play_sound type="SOUND" range="-1" sound="NS02_S01" />
			</on>
			<on time="2934">
				<play_sound type="SOUND" range="-1" sound="NS20_F" />
			</on>
			<on time="3000">
				<give_item id="-300" count="31" />
			</on>
			<on time="3032">
				<play_sound type="SOUND" range="-1" sound="NB02_F" />
			</on>
			<on time="3134">
				<play_sound type="SOUND" range="-1" sound="NS19_S01" />
			</on>
			<on time="3227">
				<play_sound type="SOUND" range="-1" sound="NS12_F" />
			</on>
			<on time="3300">
				<give_item id="-300" count="31" />
			</on>
			<on time="3320">
				<play_sound type="SOUND" range="-1" sound="NS19_F" />
			</on>
			<on time="3429">
				<play_sound type="SOUND" range="-1" sound="NS18_S01" />
			</on>
			<on time="3513">
				<play_sound type="SOUND" range="-1" sound="NS02_F" />
			</on>
			<on time="3600">
				<stop name="event" />
			</on>
		</on_time>
		<objects name="merchant">
			<spawn_ex name="archaic_fortress_peace_merchant" />
		</objects>
		<objects name="flag_pole">
			<static_object id="23130500" />
		</objects>
		<objects name="combat_flags">
			<combat_flag x="109206" y="-141229" z="-2826" />
			<combat_flag x="109224" y="-141146" z="-2826" />
			<combat_flag x="109251" y="-141073" z="-2826" />
		</objects>
		<objects name="peace_commanders">
			<spawn_ex name="archaic_fortress_peace_commanders" />
		</objects>
		<objects name="siege_commanders">
			<spawn_ex name="archaic_fortress_siege_commanders" />
		</objects>
		<objects name="envoy">
			<spawn_ex name="archaic_fortress_envoy" />
		</objects>
		<objects name="default_guards"> <!--дефольтные гварды спавнятся при любых условиях-->
			<spawn_ex name="archaic_fortress_default_guards" />
		</objects>
		<objects name="guards_live_with_c_center"> <!--гварды которые будут стоят пока не вворвутся в командый центр-->
			<spawn_ex name="archaic_fortress_live_with_c_center" />
		</objects>
		<objects name="noowner_flagdef"> <!--гварды охраняемые Флагполе, когда нету овнера-->
			<spawn_ex name="archaic_fortress_noowner_flagdef" />
		</objects>
		<objects name="owner_flagdef"> <!--гварды охраняемые Флагполе, когда есть овнера-->
			<spawn_ex name="archaic_fortress_owner_flagdef" />
		</objects>
		<objects name="dwarvens">
			<spawn_ex name="archaic_fortress_gunner" />
		</objects>
		<objects name="reinforce_1">
			<spawn_ex name="archaic_fortress_reinforce_1" />
		</objects>
		<objects name="reinforce_2">
			<spawn_ex name="archaic_fortress_reinforce_2" />
		</objects>
		<objects name="enter_doors">
			<door id="23130001" />
			<door id="23130006" />
		</objects>
		<objects name="upgradeable_doors">
			<door id="23130001" />
			<door id="23130006" />
			<door id="23130007" />
			<door id="23130008" />
		</objects>
		<objects name="commander_doors">
			<door id="23130002" />
			<door id="23130003" />
			<door id="23130004" />
			<door id="23130005" />
		</objects>
		<objects name="mercenary_points">
			<point x="114123" y="-143108" z="-3232" />
			<point x="113620" y="-142609" z="-3248" />
			<point x="113136" y="-142130" z="-3248" />
			<point x="112738" y="-141717" z="-3232" />
			<point x="112610" y="-141142" z="-3144" />
			<point x="112318" y="-140607" z="-3048" />
			<point x="111949" y="-140966" z="-2960" />
			<point x="111602" y="-141763" z="-2944" />
			<point x="110756" y="-141563" z="-2952" />
			<point x="110203" y="-141434" z="-2952" />
		</objects>
		<objects name="mercenary">
			<spawn_ex name="archaic_fortress_mercenary" />
		</objects>
		<objects name="siege_zones">
			<zone name="[schutt_fort_siege]" />
		</objects>
		<objects name="flag_zones">
			<zone name="[schutt_fort_hq]" />
		</objects>
	</event>
</list>