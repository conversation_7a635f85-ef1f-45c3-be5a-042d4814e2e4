<title>Choose skill to add</title>
<center>
[admin_add_skill 147 46|Magic Resistance 46 (Lv 70)]%%
[admin_add_skill 147 47|Magic Resistance 47 (Lv 70)]%%
[admin_add_skill 147 48|Magic Resistance 48 (Lv 72)]%%
[admin_add_skill 147 49|Magic Resistance 49 (Lv 72)]%%
[admin_add_skill 147 50|Magic Resistance 50 (Lv 74)]%%
[admin_add_skill 147 51|Magic Resistance 51 (Lv 74)]%%
[admin_add_skill 191 2|Focus Mind 2 (Lv 43)]%%
[admin_add_skill 191 3|Focus Mind 3 (Lv 49)]%%
[admin_add_skill 191 4|Focus Mind 4 (Lv 55)]%%
[admin_add_skill 191 5|Focus Mind 5 (Lv 64)]%%
[admin_add_skill 191 6|Focus Mind 6 (Lv 72)]%%
[admin_add_skill 196 1|Holy Blade 1 (Lv 43)]%%
[admin_add_skill 217 9|Sword Blunt Mastery 9 (Lv 40)]%%
[admin_add_skill 217 10|Sword Blunt Mastery 10 (Lv 40)]%%
[admin_add_skill 217 11|Sword Blunt Mastery 11 (Lv 40)]%%
[admin_add_skill 217 12|Sword Blunt Mastery 12 (Lv 43)]%%
[admin_add_skill 217 13|Sword Blunt Mastery 13 (Lv 43)]%%
[admin_add_skill 217 14|Sword Blunt Mastery 14 (Lv 43)]%%
[admin_add_skill 217 15|Sword Blunt Mastery 15 (Lv 46)]%%
[admin_add_skill 217 16|Sword Blunt Mastery 16 (Lv 46)]%%
[admin_add_skill 217 17|Sword Blunt Mastery 17 (Lv 46)]%%
[admin_add_skill 217 18|Sword Blunt Mastery 18 (Lv 49)]%%
[admin_add_skill 217 19|Sword Blunt Mastery 19 (Lv 49)]%%
[admin_add_skill 217 20|Sword Blunt Mastery 20 (Lv 49)]%%
[admin_add_skill 217 21|Sword Blunt Mastery 21 (Lv 52)]%%
[admin_add_skill 217 22|Sword Blunt Mastery 22 (Lv 52)]%%
[admin_add_skill 217 23|Sword Blunt Mastery 23 (Lv 52)]%%
[admin_add_skill 217 24|Sword Blunt Mastery 24 (Lv 55)]%%
[admin_add_skill 217 25|Sword Blunt Mastery 25 (Lv 55)]%%
[admin_add_skill 217 26|Sword Blunt Mastery 26 (Lv 55)]%%
[admin_add_skill 217 27|Sword Blunt Mastery 27 (Lv 58)]%%
[admin_add_skill 217 28|Sword Blunt Mastery 28 (Lv 58)]%%
[admin_add_skill 217 29|Sword Blunt Mastery 29 (Lv 58)]%%
[admin_add_skill 217 30|Sword Blunt Mastery 30 (Lv 60)]%%
[admin_add_skill 217 31|Sword Blunt Mastery 31 (Lv 60)]%%
[admin_add_skill 217 32|Sword Blunt Mastery 32 (Lv 62)]%%
[admin_add_skill 217 33|Sword Blunt Mastery 33 (Lv 62)]%%
[admin_add_skill 217 34|Sword Blunt Mastery 34 (Lv 64)]%%
[admin_add_skill 217 35|Sword Blunt Mastery 35 (Lv 64)]%%
[admin_add_skill 217 36|Sword Blunt Mastery 36 (Lv 66)]%%
[admin_add_skill 217 37|Sword Blunt Mastery 37 (Lv 66)]%%
[admin_add_skill 217 38|Sword Blunt Mastery 38 (Lv 68)]%%
[admin_add_skill 217 39|Sword Blunt Mastery 39 (Lv 68)]%%
[admin_add_skill 217 40|Sword Blunt Mastery 40 (Lv 70)]%%
[admin_add_skill 217 41|Sword Blunt Mastery 41 (Lv 70)]%%
[admin_add_skill 217 42|Sword Blunt Mastery 42 (Lv 72)]%%
[admin_add_skill 217 43|Sword Blunt Mastery 43 (Lv 72)]%%
[admin_add_skill 217 44|Sword Blunt Mastery 44 (Lv 74)]%%
[admin_add_skill 217 45|Sword Blunt Mastery 45 (Lv 74)]%%
[admin_add_skill 230 2|Sprint 2 (Lv 52)]%%
[admin_add_skill 239 2|Expertise C 2 (Lv 40)]%%
[admin_add_skill 239 3|Expertise B 3 (Lv 52)]%%
[admin_add_skill 239 4|Expertise A 4 (Lv 60)]%%
[admin_add_skill 239 5|Expertise S 5 (Lv 75)]%%
[admin_add_skill 264 1|Song of Earth 1 (Lv 55)]%%
[admin_add_skill 265 1|Song of Life 1 (Lv 52)]%%
[admin_add_skill 266 1|Song of Water 1 (Lv 58)]%%
[admin_add_skill 267 1|Song of Warding 1 (Lv 40)]%%
[admin_add_skill 268 1|Song of Wind 1 (Lv 46)]%%
[admin_add_skill 269 1|Song of Hunter 1 (Lv 49)]%%
[admin_add_skill 270 1|Song of Invocation 1 (Lv 43)]%%
[admin_add_skill 304 1|Song of Vitality 1 (Lv 66)]%%
[admin_add_skill 305 1|Song of Vengeance 1 (Lv 74)]%%
[admin_add_skill 306 1|Song of Flame Guard 1 (Lv 62)]%%
[admin_add_skill 308 1|Song of Storm Guard 1 (Lv 70)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index Elf/E_SwordSinger" width=40 height=15><br>
<button value="Page2" action="bypass -h admin_skill_index Elf/E_SwordSinger2" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
