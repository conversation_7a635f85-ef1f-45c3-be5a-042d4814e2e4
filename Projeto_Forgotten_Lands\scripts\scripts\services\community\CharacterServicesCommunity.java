package scripts.services.community;

import java.util.StringTokenizer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import l2mv.commons.util.Rnd;
import l2mv.gameserver.Config;
import l2mv.gameserver.ConfigHolder;
import l2mv.gameserver.data.htm.HtmCache;
import l2mv.gameserver.handler.bbs.CommunityBoardManager;
import l2mv.gameserver.handler.bbs.ICommunityBoardHandler;
import l2mv.gameserver.model.Player;
import l2mv.gameserver.network.serverpackets.ShowBoard;
import l2mv.gameserver.scripts.Functions;
import l2mv.gameserver.scripts.ScriptFile;
import l2mv.gameserver.templates.item.ItemTemplate;
import l2mv.gameserver.data.xml.holder.ItemHolder;
import l2mv.gameserver.utils.Util;
import l2mv.gameserver.utils.EmailVerification;

public class CharacterServicesCommunity extends Functions implements ScriptFile, ICommunityBoardHandler
{
    static final Logger _log = LoggerFactory.getLogger(CharacterServicesCommunity.class);

    @Override
    public void onLoad()
    {
        _log.info("=== CharacterServicesCommunity onLoad called ===");
        _log.info("COMMUNITYBOARD_ENABLED: " + Config.COMMUNITYBOARD_ENABLED);
        _log.info("EnableMergeCommunity: " + ConfigHolder.getBool("EnableMergeCommunity"));

        if (Config.COMMUNITYBOARD_ENABLED && !ConfigHolder.getBool("EnableMergeCommunity"))
        {
            _log.info("CharacterServicesCommunity: Character Services Community loaded.");
            CommunityBoardManager.getInstance().registerHandler(this);
            _log.info("CharacterServicesCommunity: Handler registered successfully.");
        }
        else
        {
            _log.info("CharacterServicesCommunity: NOT loaded - conditions not met.");
        }
    }

    @Override
    public void onReload()
    {
        if (Config.COMMUNITYBOARD_ENABLED && !ConfigHolder.getBool("EnableMergeCommunity"))
        {
            CommunityBoardManager.getInstance().removeHandler(this);
        }
    }

    @Override
    public void onShutdown()
    {
    }

    @Override
    public String[] getBypassCommands()
    {
        return new String[]
        {
            "_bbscharservices;titlecolor",
            "_bbscharservices;namecolor",
            "_bbscharservices;hairchange",
            "_bbscharservices;changesex",
            "_bbscharservices;secondarypass",
            "_bbscharservices;anchor",
            "_bbscharservices;registeremail",
            "_bbscharservices;verifyemail"
        };
    }

    @Override
    public void onBypassCommand(Player player, String bypass)
    {
        _log.info("onBypassCommand called - bypass: " + bypass);

        StringTokenizer st = new StringTokenizer(bypass, ";");
        String cmd = st.nextToken();
        String action = st.hasMoreTokens() ? st.nextToken() : "";

        _log.info("cmd: " + cmd + ", action: " + action);

        if ("_bbscharservices".equals(cmd))
        {
            if ("titlecolor".equals(action))
            {
                int page = 1;
                if (st.hasMoreTokens()) {
                    try {
                        page = Integer.parseInt(st.nextToken());
                    } catch (NumberFormatException e) {
                        page = 1;
                    }
                }
                showTitleColorPage(player, page);
            }
            else if ("namecolor".equals(action))
            {
                int page = 1;
                if (st.hasMoreTokens()) {
                    try {
                        page = Integer.parseInt(st.nextToken());
                    } catch (NumberFormatException e) {
                        page = 1;
                    }
                }
                showNameColorPage(player, page);
            }
            else if ("hairchange".equals(action))
            {
                showHairChangePage(player);
            }
            else if ("changesex".equals(action))
            {
                showChangeSexPage(player);
            }
            else if ("secondarypass".equals(action))
            {
                showSecondaryPassPage(player);
            }
            else if ("anchor".equals(action))
            {
                showAnchorPage(player);
            }
            else if ("registeremail".equals(action))
            {
                if (st.hasMoreTokens())
                {
                    String subAction = st.nextToken();
                    if ("submit".equals(subAction))
                    {
                        processRegisterEmail(player, st);
                        return;
                    }
                }
                showRegisterEmailPage(player);
            }
            else if ("verifyemail".equals(action))
            {
                if (st.hasMoreTokens())
                {
                    String subAction = st.nextToken();
                    if ("submit".equals(subAction))
                    {
                        // Process verification code submission via bypass
                        if (st.hasMoreTokens())
                        {
                            String verificationCode = st.nextToken();
                            _log.info("Processing email verification via bypass - code: '" + verificationCode + "'");
                            processEmailVerification(player, verificationCode);
                        }
                        else
                        {
                            _log.warn("No verification code provided in bypass");
                            showVerifyEmailPage(player);
                        }
                        return;
                    }
                    else if ("resend".equals(subAction))
                    {
                        resendVerificationCode(player);
                        return;
                    }
                    else if ("cancel".equals(subAction))
                    {
                        cancelEmailVerification(player);
                        return;
                    }
                    else if ("testconfig".equals(subAction))
                    {
                        player.sendMessage("MAIL_USER: '" + Config.MAIL_USER + "'");
                        player.sendMessage("MAIL_PASS: '" + (Config.MAIL_PASS.isEmpty() ? "EMPTY" : "SET (" + Config.MAIL_PASS.length() + " chars)") + "'");
                        player.sendMessage("MAIL_SUBJECT: '" + Config.MAIL_SUBJECT + "'");
                        return;
                    }
                }
                showVerifyEmailPage(player);
            }
        }
    }

    private void processEmailVerification(Player player, String verificationCode)
    {
        _log.info("Processing email verification - code: '" + verificationCode + "', length: " + (verificationCode != null ? verificationCode.length() : "null"));

        if (verificationCode == null || verificationCode.length() != 6)
        {
            player.sendMessage("Please enter a valid 6-digit verification code.");
            showVerifyEmailPage(player);
            return;
        }

        if (EmailVerification.confirmEmailVerification(player.getAccountName(), verificationCode))
        {
            String pendingEmail = EmailVerification.getPendingEmail(player.getAccountName());
            player.sendMessage("Email address verified and registered successfully: " + pendingEmail);
            onBypassCommand(player, "_bbspage:character");
        }
        else
        {
            player.sendMessage("Invalid or expired verification code. Please try again.");
            showVerifyEmailPage(player);
        }
    }

    private void showTitleColorPage(Player player, int page)
    {
        String html = HtmCache.getInstance().getNotNull(Config.BBS_HOME_DIR + "pages/services/titlecolor.htm", player);

        // Generate color list using pagination
        StringBuilder list = new StringBuilder();
        String[] allColors = Config.SERVICES_CHANGE_Title_COLOR_LIST;

        // Pagination: 12 colors per page
        int colorsPerPage = 12;
        int startIndex = (page - 1) * colorsPerPage;
        int endIndex = Math.min(startIndex + colorsPerPage, allColors.length);

        String[] colors = new String[endIndex - startIndex];
        System.arraycopy(allColors, startIndex, colors, 0, colors.length);

        int rowIndex = 0;
        for (String color : colors)
        {
            ItemTemplate item = ItemHolder.getInstance().getTemplate(Config.SERVICES_CHANGE_Title_COLOR_ITEM);
            String itemName = item != null ? item.getName() : "Festival Coin";
            String cost = String.valueOf(Config.SERVICES_CHANGE_Title_COLOR_PRICE);

            // Convert BGR to RGB for display (original colors are in BGR format)
            String displayColor = color.length() == 6 ?
                color.substring(4, 6) + color.substring(2, 4) + color.substring(0, 2) : color;

            // Estrutura igual ao Events: cada linha em uma tabela separada
            String titleText = player.getTitle() != null && !player.getTitle().isEmpty() ? player.getTitle() : player.getName();

            // Cores alternadas em TODAS as linhas: cinza escuro e cinza médio
            String bgColor = (rowIndex % 2 == 0) ? " bgcolor=\"111111\"" : " bgcolor=\"1a1a1a\"";

            list.append("<table width=540").append(bgColor).append(">");
            list.append("<tr>");
            list.append("<td width=540 align=center>");
            list.append("<table width=540 height=20>");
            list.append("<tr>");
            list.append("<td align=center>");
            list.append("<table>");
            list.append("<tr>");
            list.append("<td width=180 valign=top><font color=\"").append(displayColor).append("\">").append(titleText).append("</font></td>");
            list.append("<td width=180 align=\"center\" valign=top>").append(itemName).append(" x").append(cost).append("</td>");
            list.append("<td width=180 align=\"center\" valign=top><button value=\"Buy\" action=\"bypass -h scripts_services.TitleColor:change ").append(color).append("\" width=80 height=20 back=\"L2UI_CT1.Button_DF\" fore=\"L2UI_CT1.Button_DF\"></td>");
            list.append("</tr>");
            list.append("</table>");
            list.append("</td>");
            list.append("</tr>");
            list.append("</table>");
            list.append("</td>");
            list.append("</tr>");
            list.append("</table>");

            rowIndex++;
        }

        // Generate navigation buttons
        StringBuilder navigation = new StringBuilder();
        int totalPages = (int) Math.ceil((double) allColors.length / colorsPerPage);

        if (page > 1) {
            navigation.append("<button value=\"Previous\" action=\"bypass -h _bbscharservices;titlecolor;").append(page - 1).append("\" width=80 height=20 back=\"L2UI_CT1.Button_DF\" fore=\"L2UI_CT1.Button_DF\">");
        }

        navigation.append(" Page ").append(page).append("/").append(totalPages).append(" ");

        if (page < totalPages) {
            navigation.append("<button value=\"Next\" action=\"bypass -h _bbscharservices;titlecolor;").append(page + 1).append("\" width=80 height=20 back=\"L2UI_CT1.Button_DF\" fore=\"L2UI_CT1.Button_DF\">");
        }

        // Add Back to Account Services button
        navigation.append("<br><br><button value=\"Back\" action=\"bypass _bbspage:character\" width=120 height=25 back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\">");

        html = html.replace("{list}", list.toString());
        html = html.replace("{navigation}", navigation.toString());

        ShowBoard.separateAndSend(html, player);
    }

    private void showNameColorPage(Player player, int page)
    {
        String html = HtmCache.getInstance().getNotNull(Config.BBS_HOME_DIR + "pages/services/namecolor.htm", player);

        // Generate color list using pagination
        StringBuilder list = new StringBuilder();
        String[] allColors = Config.SERVICES_CHANGE_NICK_COLOR_LIST;

        // Pagination: 12 colors per page
        int colorsPerPage = 12;
        int startIndex = (page - 1) * colorsPerPage;
        int endIndex = Math.min(startIndex + colorsPerPage, allColors.length);

        String[] colors = new String[endIndex - startIndex];
        System.arraycopy(allColors, startIndex, colors, 0, colors.length);

        int rowIndex = 0;
        for (String color : colors)
        {
            ItemTemplate item = ItemHolder.getInstance().getTemplate(Config.SERVICES_CHANGE_NICK_COLOR_ITEM);
            String itemName = item != null ? item.getName() : "Festival Coin";
            String cost = String.valueOf(Config.SERVICES_CHANGE_NICK_COLOR_PRICE);

            // Convert BGR to RGB for display (original colors are in BGR format)
            String displayColor = color.length() == 6 ?
                color.substring(4, 6) + color.substring(2, 4) + color.substring(0, 2) : color;

            // Estrutura igual ao Events: cada linha em uma tabela separada
            // Cores alternadas em TODAS as linhas: cinza escuro e cinza médio
            String bgColor = (rowIndex % 2 == 0) ? " bgcolor=\"111111\"" : " bgcolor=\"1a1a1a\"";

            list.append("<table width=540").append(bgColor).append(">");
            list.append("<tr>");
            list.append("<td width=540 align=center>");
            list.append("<table width=540 height=20>");
            list.append("<tr>");
            list.append("<td align=center>");
            list.append("<table>");
            list.append("<tr>");
            list.append("<td width=180 valign=top><font color=\"").append(displayColor).append("\">").append(player.getName()).append("</font></td>");
            list.append("<td width=180 align=\"center\" valign=top>").append(itemName).append(" x").append(cost).append("</td>");
            list.append("<td width=180 align=\"center\" valign=top><button value=\"Buy\" action=\"bypass -h scripts_services.NickColor:change ").append(color).append("\" width=80 height=20 back=\"L2UI_CT1.Button_DF\" fore=\"L2UI_CT1.Button_DF\"></td>");
            list.append("</tr>");
            list.append("</table>");
            list.append("</td>");
            list.append("</tr>");
            list.append("</table>");
            list.append("</td>");
            list.append("</tr>");
            list.append("</table>");

            rowIndex++;
        }

        // Generate navigation buttons
        StringBuilder navigation = new StringBuilder();
        int totalPages = (int) Math.ceil((double) allColors.length / colorsPerPage);

        if (page > 1) {
            navigation.append("<button value=\"Previous\" action=\"bypass -h _bbscharservices;namecolor;").append(page - 1).append("\" width=80 height=20 back=\"L2UI_CT1.Button_DF\" fore=\"L2UI_CT1.Button_DF\">");
        }

        navigation.append(" Page ").append(page).append("/").append(totalPages).append(" ");

        if (page < totalPages) {
            navigation.append("<button value=\"Next\" action=\"bypass -h _bbscharservices;namecolor;").append(page + 1).append("\" width=80 height=20 back=\"L2UI_CT1.Button_DF\" fore=\"L2UI_CT1.Button_DF\">");
        }

        // Add Back to Account Services button
        navigation.append("<br><br><button value=\"Back\" action=\"bypass _bbspage:character\" width=120 height=25 back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\">");

        html = html.replace("{list}", list.toString());
        html = html.replace("{navigation}", navigation.toString());

        ShowBoard.separateAndSend(html, player);
    }

    private void showHairChangePage(Player player)
    {
        String html = HtmCache.getInstance().getNotNull(Config.BBS_HOME_DIR + "pages/services/hairchange.htm", player);

        // Generate hair style list using the same logic as the working show() method
        StringBuilder list = new StringBuilder();
        String itemName = "Adena";
        String cost = "100";

        // Hair style arrays - only showing available styles (A, B, C, D, E)
        int[] Male = {1, 1, 1, 1, 1};
        int[] Female = {1, 1, 1, 1, 1};

        for (int i = 0; i < 5; i++)
        {
            // Use the SAME logic as the working show() method
            boolean canUse = (player.getHairStyle() != i) && (player.getSex() == 0 ? Male[i] != 0 : Female[i] != 0);

            String styleName = getHairStyleName(i);
            String styleColor = canUse ? "99CC00" : "CC3333";

            // Alternating background colors like Title/Name Color pages
            String bgColor = (i % 2 == 0) ? " bgcolor=\"111111\"" : " bgcolor=\"1a1a1a\"";

            list.append("<table width=540").append(bgColor).append(">");
            list.append("<tr>");
            list.append("<td width=180 align=center><font color=\"").append(styleColor).append("\">").append(styleName).append("</font></td>");

            if (canUse)
            {
                list.append("<td width=180 align=center>").append(itemName).append(" x").append(cost).append("</td>");
                list.append("<td width=180 align=center><button value=\"Buy\" action=\"bypass _bbsscripts:services.HairChange:change ").append(i).append("\" width=80 height=20 back=\"L2UI_CT1.Button_DF\" fore=\"L2UI_CT1.Button_DF\"></td>");
            }
            else
            {
                String reason = (player.getHairStyle() == i) ? "Current Style" : "Not Available";
                list.append("<td width=180 align=center><font color=\"CC3333\">").append(reason).append("</font></td>");
                list.append("<td width=180 align=center><font color=\"CC3333\">-</font></td>");
            }

            list.append("</tr>");
            list.append("</table>");
        }

        // Navigation (Back button only)
        String navigation = "<button value=\"Back\" action=\"bypass _bbspage:character\" width=120 height=25 back=\"L2UI_CT1.Button_DF\" fore=\"L2UI_CT1.Button_DF\">";

        html = html.replace("{list}", list.toString());
        html = html.replace("{navigation}", navigation);

        ShowBoard.separateAndSend(html, player);
    }

    private void showChangeSexPage(Player player)
    {
        String html = HtmCache.getInstance().getNotNull(Config.BBS_HOME_DIR + "pages/Donate/changesex.htm", player);
        
        ItemTemplate item = ItemHolder.getInstance().getTemplate(Config.SERVICES_CHANGE_SEX_ITEM);
        String itemName = item != null ? item.getName() : "Unknown Item";
        String cost = Util.formatAdena(Config.SERVICES_CHANGE_SEX_PRICE);
        
        html = html.replace("%item%", itemName);
        html = html.replace("%cost%", cost);
        html = html.replace("%playerName%", player.getName());
        html = html.replace("%playersex%", player.getSex() == 1 ? "Female" : "Male");
        
        // Replace the button action to return to Community Board
        html = html.replace("bypass -h scripts_services.Rename:changesex", "bypass _bbsscripts:services.Rename:changesex;_bbspage:character");
        
        ShowBoard.separateAndSend(html, player);
    }



    private void showSecondaryPassPage(Player player)
    {
        String html = HtmCache.getInstance().getNotNull(Config.BBS_HOME_DIR + "pages/services/secondarypass.htm", player);
        ShowBoard.separateAndSend(html, player);
    }

    private void showAnchorPage(Player player)
    {
        String html = HtmCache.getInstance().getNotNull(Config.BBS_HOME_DIR + "pages/services/anchor.htm", player);

        // Get real hardware lock information
        String currentIP = player.getIP();
        String hwidLock = player.getHwidLock();

        String hwidStatus = (hwidLock != null && !hwidLock.isEmpty()) ? "Locked" : "Not Locked";
        String displayIP = (currentIP != null && !currentIP.isEmpty() && !currentIP.equals("NOT_CONNECTED")) ? currentIP : "Not Available";

        html = html.replace("{hwid_status}", hwidStatus);
        html = html.replace("{hwid}", displayIP);

        ShowBoard.separateAndSend(html, player);
    }

    private void processRegisterEmail(Player player, StringTokenizer st)
    {
        if (!st.hasMoreTokens())
        {
            player.sendMessage("Please provide email address.");
            showRegisterEmailPage(player);
            return;
        }

        String email = st.nextToken();
        String confirmEmail = st.hasMoreTokens() ? st.nextToken() : "";

        // Validate email format
        if (!isValidEmail(email))
        {
            player.sendMessage("Invalid email format. Please use format: <EMAIL>");
            showRegisterEmailPage(player);
            return;
        }

        // Check if emails match
        if (!email.equalsIgnoreCase(confirmEmail))
        {
            player.sendMessage("Email addresses do not match!");
            showRegisterEmailPage(player);
            return;
        }

        // Check if email is already registered
        String currentEmail = l2mv.gameserver.utils.AccountEmail.getEmail(player);
        if (currentEmail != null && !currentEmail.isEmpty())
        {
            player.sendMessage("Email updated from: " + currentEmail + " to: " + email);
        }
        else
        {
            player.sendMessage("Email successfully registered: " + email);
        }

        // Save email to account
        l2mv.gameserver.utils.AccountEmail.setEmail(player, email);

        // Return to Account Service page
        String html = HtmCache.getInstance().getNotNull(Config.BBS_HOME_DIR + "pages/character.htm", player);
        ShowBoard.separateAndSend(html, player);
    }

    private boolean isValidEmail(String email)
    {
        if (email == null || email.isEmpty())
        {
            return false;
        }

        return email.contains("@") && email.contains(".") && email.length() >= 5 && email.length() <= 50;
    }

    private void showRegisterEmailPage(Player player)
    {
        String html = HtmCache.getInstance().getNotNull(Config.BBS_HOME_DIR + "pages/services/registeremail.htm", player);

        // Check if player already has email registered
        String currentEmail = l2mv.gameserver.utils.AccountEmail.getEmail(player);
        if (currentEmail != null && !currentEmail.isEmpty())
        {
            if (EmailVerification.isEmailVerified(player.getAccountName()))
            {
                html = html.replace("Register your Email Address:", "Current Email: <font color=\"00FF00\">" + currentEmail + "</font> <font color=\"LEVEL\">[VERIFIED]</font><br>Update your Email Address:");
            }
            else
            {
                html = html.replace("Register your Email Address:", "Current Email: <font color=\"FFFF00\">" + currentEmail + "</font> <font color=\"FF6600\">[NOT VERIFIED]</font><br>Update your Email Address:");
            }
        }

        // Check if there's a pending verification
        String pendingEmail = EmailVerification.getPendingEmail(player.getAccountName());
        if (pendingEmail != null)
        {
            html = html.replace("Register your Email Address:", "Pending Verification: <font color=\"FFFF00\">" + pendingEmail + "</font><br><button value=\"Continue Verification\" action=\"bypass _bbscharservices;verifyemail\" width=150 height=20 back=\"L2UI_CT1.Button_DF\" fore=\"L2UI_CT1.Button_DF\"><br><br>Or register a new email:");
        }

        ShowBoard.separateAndSend(html, player);
    }

    private String getHairStyleName(int hairStyle)
    {
        switch (hairStyle)
        {
            case 0: return "Style A";
            case 1: return "Style B";
            case 2: return "Style C";
            case 3: return "Style D";
            case 4: return "Style E";
            case 5: return "Style F";
            case 6: return "Style G";
            default: return "Unknown";
        }
    }

    @Override
    public void onWriteCommand(Player player, String bypass, String arg1, String arg2, String arg3, String arg4, String arg5)
    {
        _log.info("=== onWriteCommand called ===");
        _log.info("Player: " + (player != null ? player.getName() : "null"));
        _log.info("Bypass: '" + bypass + "'");
        _log.info("arg1: '" + arg1 + "'");
        _log.info("arg2: '" + arg2 + "'");
        _log.info("arg3: '" + arg3 + "'");
        _log.info("arg4: '" + arg4 + "'");
        _log.info("arg5: '" + arg5 + "'");

        if (bypass.contains("registeremail;submit"))
        {
            // Arguments: arg1=0, arg2=0, arg3=email, arg4=confirmemail
            String email = arg3;
            String confirmEmail = arg4;

            _log.info("Processing email registration - email: " + email + ", confirmEmail: " + confirmEmail);

            // Validate email format
            if (!isValidEmail(email))
            {
                player.sendMessage("Invalid email format. Please use format: <EMAIL>");
                showRegisterEmailPage(player);
                return;
            }

            // Check if emails match
            if (!email.equals(confirmEmail))
            {
                player.sendMessage("Email addresses do not match. Please try again.");
                showRegisterEmailPage(player);
                return;
            }

            // Generate verification code and send email
            String verificationCode = EmailVerification.generateVerificationCode();
            EmailVerification.storeVerificationCode(player.getAccountName(), email, verificationCode);

            _log.info("About to send email - Config.MAIL_USER: '" + Config.MAIL_USER + "', Config.MAIL_PASS: '" + (Config.MAIL_PASS.isEmpty() ? "EMPTY" : "SET") + "'");

            if (EmailVerification.sendVerificationEmail(email, verificationCode, player.getName()))
            {
                player.sendMessage("A verification code has been generated for: " + email);
                player.sendMessage("TEMPORARY: Check the server console for your verification code.");
                player.sendMessage("(Email sending will be enabled once libraries are properly configured)");
                showVerifyEmailPage(player);
            }
            else
            {
                player.sendMessage("Failed to generate verification code. Please try again.");
                showRegisterEmailPage(player);
            }
        }
        else if (bypass.contains("verifyemail;submit"))
        {
            // Arguments: arg1=0, arg2=0, arg3=verificationcode
            String verificationCode = arg3;
            processEmailVerification(player, verificationCode);
        }
        else
        {
            _log.info("onWriteCommand - bypass not handled: " + bypass);
        }
    }

    private void showVerifyEmailPage(Player player)
    {
        String html = HtmCache.getInstance().getNotNull(Config.BBS_HOME_DIR + "pages/services/verifyemail.htm", player);

        // Get pending email to display
        String pendingEmail = EmailVerification.getPendingEmail(player.getAccountName());
        if (pendingEmail != null)
        {
            html = html.replace("%pending_email%", pendingEmail);
        }
        else
        {
            html = html.replace("%pending_email%", "Unknown");
        }

        ShowBoard.separateAndSend(html, player);
    }

    private void resendVerificationCode(Player player)
    {
        String pendingEmail = EmailVerification.getPendingEmail(player.getAccountName());
        if (pendingEmail == null)
        {
            player.sendMessage("No pending email verification found.");
            onBypassCommand(player, "_bbspage:character");
            return;
        }

        // Generate new verification code
        String verificationCode = EmailVerification.generateVerificationCode();
        EmailVerification.storeVerificationCode(player.getAccountName(), pendingEmail, verificationCode);

        if (EmailVerification.sendVerificationEmail(pendingEmail, verificationCode, player.getName()))
        {
            player.sendMessage("A new verification code has been sent to: " + pendingEmail);
            showVerifyEmailPage(player);
        }
        else
        {
            player.sendMessage("Failed to send verification email. Please try again later.");
            showVerifyEmailPage(player);
        }
    }

    private void cancelEmailVerification(Player player)
    {
        EmailVerification.clearVerificationData(player.getAccountName());
        player.sendMessage("Email verification cancelled.");
        onBypassCommand(player, "_bbspage:character");
    }
}
