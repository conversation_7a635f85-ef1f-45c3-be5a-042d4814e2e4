#=====================================================================================
#							Settings forge in the community
#=====================================================================================
# Allow Forge System ?
Allow = True

#=====================================================================================
#						Settings augmentation in the community (not transferred)
#=====================================================================================
# The subject, which will be taken as payment for augmentation. (Low,Mid,High,Top,Jewels)
AugmentItems = 37000, 37000, 37000, 37000, 37000

# The number of items subject to augmentation. (Low,Mid,High,Top,Jewels)
AugmentCount = 1, 3, 6, 10, 24

#=====================================================================================
#						Settings foundation in the community
#=====================================================================================
# Item Id used for exchanging foundation items
FoundationItem = 37000

# Prices for the foundation of each item grade (NG, D, C, B, A, S, S80, S84)
FoundationPriceArmor = 40, 40, 40, 40, 40, 40, 40, 40
FoundationPriceWeapon = 100, 100, 100, 100, 100, 100, 100, 100
FoundationPriceJewel = 40, 40, 40, 40, 40, 40, 40, 40

#=====================================================================================
#							Enchanting settings in the community
#=====================================================================================
# The subject, which will be taken as payment for enchanting.
Item = 37000

# Maximum possible Enchanting weapons. (Syntax: Weapon,Armor,Jewels)
MaxEnchant = 6, 6, 6

# The level at which you can sharpen weapons.
WValue = 1, 2, 3, 4, 5, 6

# The level at which you can shape the armor.
AValue = 1, 2, 3, 4, 5, 6

# The level on which to sharpen jewelry.
JValue = 1, 2, 3, 4, 5, 6

# The number of items for sharpening weapons at a certain level.
WPrice= 5, 15, 30, 50, 70, 100

# The number of items for sharpening armor on some level.
APrice = 1, 5, 10, 15, 20, 25

# The number of items for sharpening jewelry at a certain level.
JPrice = 1, 5, 10, 15, 20, 25

#=====================================================================================
#						Settings attribution in the community
#=====================================================================================
# Enabling sharpening attribute PvP items.
AtributePvP = True

#  Max Attribute to insert in Armor and Weapon
MaxAAttribute = 120
MaxWAttribute = 300

# Type the level you want to get for each attributing
AtributeArmorValue = 120
AtributeWeaponValue = 300

# Type the level you want to get for each attributing
PriceForAtributeArmor = 13
PriceForAtributeWeapon = 100

# Terms of sharpening attribute certain grade.
AtributeGrade = NG:NO;D:NO;C:NO;B:NO;A:NO;S:NO;S80:ON;S84:ON
