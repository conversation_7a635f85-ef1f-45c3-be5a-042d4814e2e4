<title>Choose skill to add</title>
<center>
[admin_add_skill 24 4|<PERSON><PERSON><PERSON> Shot 4 (Lv 49)]%%
[admin_add_skill 24 5|<PERSON><PERSON><PERSON> Shot 5 (Lv 49)]%%
[admin_add_skill 24 6|<PERSON><PERSON><PERSON> Shot 6 (Lv 49)]%%
[admin_add_skill 24 7|<PERSON><PERSON><PERSON> Shot 7 (Lv 52)]%%
[admin_add_skill 24 8|Burst Shot 8 (Lv 52)]%%
[admin_add_skill 24 9|Burst Shot 9 (Lv 52)]%%
[admin_add_skill 24 10|Burst Shot 10 (Lv 55)]%%
[admin_add_skill 24 11|Burs<PERSON> Shot 11 (Lv 55)]%%
[admin_add_skill 24 12|<PERSON>urs<PERSON> Shot 12 (Lv 55)]%%
[admin_add_skill 24 13|B<PERSON><PERSON> Shot 13 (Lv 58)]%%
[admin_add_skill 24 14|Burst Shot 14 (Lv 58)]%%
[admin_add_skill 24 15|Burs<PERSON> Shot 15 (Lv 58)]%%
[admin_add_skill 24 16|<PERSON><PERSON><PERSON> Shot 16 (Lv 60)]%%
[admin_add_skill 24 17|<PERSON><PERSON><PERSON> <PERSON> 17 (Lv 60)]%%
[admin_add_skill 24 18|<PERSON><PERSON><PERSON> Shot 18 (Lv 62)]%%
[admin_add_skill 24 19|<PERSON><PERSON><PERSON> Shot 19 (Lv 62)]%%
[admin_add_skill 24 20|Burst Shot 20 (Lv 64)]%%
[admin_add_skill 24 21|Burst Shot 21 (Lv 64)]%%
[admin_add_skill 24 22|Burst Shot 22 (Lv 66)]%%
[admin_add_skill 24 23|Burst Shot 23 (Lv 66)]%%
[admin_add_skill 24 24|Burst Shot 24 (Lv 68)]%%
[admin_add_skill 24 25|Burst Shot 25 (Lv 68)]%%
[admin_add_skill 24 26|Burst Shot 26 (Lv 70)]%%
[admin_add_skill 24 27|Burst Shot 27 (Lv 70)]%%
[admin_add_skill 24 28|Burst Shot 28 (Lv 72)]%%
[admin_add_skill 24 29|Burst Shot 29 (Lv 72)]%%
[admin_add_skill 24 30|Burst Shot 30 (Lv 74)]%%
[admin_add_skill 24 31|Burst Shot 31 (Lv 74)]%%
[admin_add_skill 58 19|Elemental Heal 19 (Lv 40)]%%
[admin_add_skill 58 20|Elemental Heal 20 (Lv 40)]%%
[admin_add_skill 58 21|Elemental Heal 21 (Lv 40)]%%
[admin_add_skill 58 22|Elemental Heal 22 (Lv 43)]%%
[admin_add_skill 58 23|Elemental Heal 23 (Lv 43)]%%
[admin_add_skill 58 24|Elemental Heal 24 (Lv 43)]%%
[admin_add_skill 58 25|Elemental Heal 25 (Lv 46)]%%
[admin_add_skill 58 26|Elemental Heal 26 (Lv 46)]%%
[admin_add_skill 58 27|Elemental Heal 27 (Lv 46)]%%
[admin_add_skill 58 28|Elemental Heal 28 (Lv 49)]%%
[admin_add_skill 58 29|Elemental Heal 29 (Lv 49)]%%
[admin_add_skill 58 30|Elemental Heal 30 (Lv 49)]%%
[admin_add_skill 58 31|Elemental Heal 31 (Lv 52)]%%
[admin_add_skill 58 32|Elemental Heal 32 (Lv 52)]%%
[admin_add_skill 58 33|Elemental Heal 33 (Lv 52)]%%
[admin_add_skill 58 34|Elemental Heal 34 (Lv 55)]%%
[admin_add_skill 58 35|Elemental Heal 35 (Lv 55)]%%
[admin_add_skill 58 36|Elemental Heal 36 (Lv 55)]%%
[admin_add_skill 58 37|Elemental Heal 37 (Lv 58)]%%
[admin_add_skill 58 38|Elemental Heal 38 (Lv 58)]%%
[admin_add_skill 58 39|Elemental Heal 39 (Lv 58)]%%
[admin_add_skill 58 40|Elemental Heal 40 (Lv 60)]%%
[admin_add_skill 58 41|Elemental Heal 41 (Lv 60)]%%
[admin_add_skill 58 42|Elemental Heal 42 (Lv 62)]%%
[admin_add_skill 58 43|Elemental Heal 43 (Lv 62)]%%
[admin_add_skill 58 44|Elemental Heal 44 (Lv 64)]%%
[admin_add_skill 58 45|Elemental Heal 45 (Lv 64)]%%
[admin_add_skill 58 46|Elemental Heal 46 (Lv 66)]%%
[admin_add_skill 58 47|Elemental Heal 47 (Lv 66)]%%
[admin_add_skill 58 48|Elemental Heal 48 (Lv 68)]%%
[admin_add_skill 58 49|Elemental Heal 49 (Lv 68)]%%
[admin_add_skill 58 50|Elemental Heal 50 (Lv 70)]%%
[admin_add_skill 58 51|Elemental Heal 51 (Lv 70)]%%
[admin_add_skill 58 52|Elemental Heal 52 (Lv 72)]%%
[admin_add_skill 58 53|Elemental Heal 53 (Lv 72)]%%
[admin_add_skill 58 54|Elemental Heal 54 (Lv 74)]%%
[admin_add_skill 58 55|Elemental Heal 55 (Lv 74)]%%
[admin_add_skill 61 2|Cure Bleeding 2 (Lv 46)]%%
[admin_add_skill 61 3|Cure Bleeding 3 (Lv 62)]%%
[admin_add_skill 99 2|Rapid Shot 2 (Lv 55)]%%
[admin_add_skill 101 4|Stun Shot 4 (Lv 40)]%%
[admin_add_skill 101 5|Stun Shot 5 (Lv 40)]%%
[admin_add_skill 101 6|Stun Shot 6 (Lv 40)]%%
[admin_add_skill 101 7|Stun Shot 7 (Lv 43)]%%
[admin_add_skill 101 8|Stun Shot 8 (Lv 43)]%%
[admin_add_skill 101 9|Stun Shot 9 (Lv 43)]%%
[admin_add_skill 101 10|Stun Shot 10 (Lv 46)]%%
[admin_add_skill 101 11|Stun Shot 11 (Lv 46)]%%
[admin_add_skill 101 12|Stun Shot 12 (Lv 46)]%%
[admin_add_skill 101 13|Stun Shot 13 (Lv 49)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index Elf/E_SilverRanger" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index Elf/E_SilverRanger3" width=40 height=15><br>
<button value="Page4" action="bypass -h admin_skill_index Elf/E_SilverRanger4" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
