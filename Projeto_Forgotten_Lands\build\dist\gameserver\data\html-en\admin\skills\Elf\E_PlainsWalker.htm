<title>Choose skill to add</title>
<center>
[admin_add_skill 12 1|Switch 1 (Lv 43)]%%
[admin_add_skill 12 2|Switch 2 (Lv 46)]%%
[admin_add_skill 12 3|Switch 3 (Lv 49)]%%
[admin_add_skill 12 4|Switch 4 (Lv 52)]%%
[admin_add_skill 12 5|Switch 5 (Lv 55)]%%
[admin_add_skill 12 6|Switch 6 (Lv 58)]%%
[admin_add_skill 12 7|Switch 7 (Lv 60)]%%
[admin_add_skill 12 8|Switch 8 (Lv 62)]%%
[admin_add_skill 12 9|Switch 9 (Lv 64)]%%
[admin_add_skill 12 10|Switch 10 (Lv 66)]%%
[admin_add_skill 12 11|Switch 11 (Lv 68)]%%
[admin_add_skill 12 12|Switch 12 (Lv 70)]%%
[admin_add_skill 12 13|Switch 13 (Lv 72)]%%
[admin_add_skill 12 14|Switch 14 (Lv 74)]%%
[admin_add_skill 15 16|Charm 16 (Lv 40)]%%
[admin_add_skill 15 17|Charm 17 (Lv 40)]%%
[admin_add_skill 15 18|Charm 18 (Lv 40)]%%
[admin_add_skill 15 19|Charm 19 (Lv 43)]%%
[admin_add_skill 15 20|Charm 20 (Lv 43)]%%
[admin_add_skill 15 21|Charm 21 (Lv 43)]%%
[admin_add_skill 15 22|Charm 22 (Lv 46)]%%
[admin_add_skill 15 23|Charm 23 (Lv 46)]%%
[admin_add_skill 15 24|Charm 24 (Lv 46)]%%
[admin_add_skill 15 25|Charm 25 (Lv 49)]%%
[admin_add_skill 15 26|Charm 26 (Lv 49)]%%
[admin_add_skill 15 27|Charm 27 (Lv 49)]%%
[admin_add_skill 15 28|Charm 28 (Lv 52)]%%
[admin_add_skill 15 29|Charm 29 (Lv 52)]%%
[admin_add_skill 15 30|Charm 30 (Lv 52)]%%
[admin_add_skill 15 31|Charm 31 (Lv 55)]%%
[admin_add_skill 15 32|Charm 32 (Lv 55)]%%
[admin_add_skill 15 33|Charm 33 (Lv 55)]%%
[admin_add_skill 15 34|Charm 34 (Lv 58)]%%
[admin_add_skill 15 35|Charm 35 (Lv 58)]%%
[admin_add_skill 15 36|Charm 36 (Lv 58)]%%
[admin_add_skill 15 37|Charm 37 (Lv 60)]%%
[admin_add_skill 15 38|Charm 38 (Lv 60)]%%
[admin_add_skill 15 39|Charm 39 (Lv 62)]%%
[admin_add_skill 15 40|Charm 40 (Lv 62)]%%
[admin_add_skill 15 41|Charm 41 (Lv 64)]%%
[admin_add_skill 15 42|Charm 42 (Lv 64)]%%
[admin_add_skill 15 43|Charm 43 (Lv 66)]%%
[admin_add_skill 15 44|Charm 44 (Lv 66)]%%
[admin_add_skill 15 45|Charm 45 (Lv 68)]%%
[admin_add_skill 15 46|Charm 46 (Lv 68)]%%
[admin_add_skill 15 47|Charm 47 (Lv 70)]%%
[admin_add_skill 15 48|Charm 48 (Lv 70)]%%
[admin_add_skill 15 49|Charm 49 (Lv 72)]%%
[admin_add_skill 15 50|Charm 50 (Lv 72)]%%
[admin_add_skill 15 51|Charm 51 (Lv 74)]%%
[admin_add_skill 15 52|Charm 52 (Lv 74)]%%
[admin_add_skill 21 2|Poison Recovery 2 (Lv 40)]%%
[admin_add_skill 21 3|Poison Recovery 3 (Lv 60)]%%
[admin_add_skill 27 6|Unlock 6 (Lv 40)]%%
[admin_add_skill 27 7|Unlock 7 (Lv 43)]%%
[admin_add_skill 27 8|Unlock 8 (Lv 46)]%%
[admin_add_skill 27 9|Unlock 9 (Lv 52)]%%
[admin_add_skill 27 10|Unlock 10 (Lv 55)]%%
[admin_add_skill 27 11|Unlock 11 (Lv 60)]%%
[admin_add_skill 27 12|Unlock 12 (Lv 64)]%%
[admin_add_skill 27 13|Unlock 13 (Lv 68)]%%
[admin_add_skill 27 14|Unlock 14 (Lv 72)]%%
[admin_add_skill 30 1|Backstab 1 (Lv 40)]%%
[admin_add_skill 30 2|Backstab 2 (Lv 40)]%%
[admin_add_skill 30 3|Backstab 3 (Lv 40)]%%
[admin_add_skill 30 4|Backstab 4 (Lv 43)]%%
[admin_add_skill 30 5|Backstab 5 (Lv 43)]%%
[admin_add_skill 30 6|Backstab 6 (Lv 43)]%%
[admin_add_skill 30 7|Backstab 7 (Lv 46)]%%
[admin_add_skill 30 8|Backstab 8 (Lv 46)]%%
[admin_add_skill 30 9|Backstab 9 (Lv 46)]%%
[admin_add_skill 30 10|Backstab 10 (Lv 49)]%%
[admin_add_skill 30 11|Backstab 11 (Lv 49)]%%
[admin_add_skill 30 12|Backstab 12 (Lv 49)]%%
[admin_add_skill 30 13|Backstab 13 (Lv 52)]%%
[admin_add_skill 30 14|Backstab 14 (Lv 52)]%%
[admin_add_skill 30 15|Backstab 15 (Lv 52)]%%
[admin_add_skill 30 16|Backstab 16 (Lv 55)]%%
[admin_add_skill 30 17|Backstab 17 (Lv 55)]%%
[admin_add_skill 30 18|Backstab 18 (Lv 55)]%%
[admin_add_skill 30 19|Backstab 19 (Lv 58)]%%
<br>
<button value="Page2" action="bypass -h admin_skill_index Elf/E_PlainsWalker2" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index Elf/E_PlainsWalker3" width=40 height=15><br>
<button value="Page4" action="bypass -h admin_skill_index Elf/E_PlainsWalker4" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
