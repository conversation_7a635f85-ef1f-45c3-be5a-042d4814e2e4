<title>Choose skill to add</title>
<center>
[admin_add_skill 146 5|Anti Magic 5 (Lv 20)]%%
[admin_add_skill 146 6|Anti Magic 6 (Lv 20)]%%
[admin_add_skill 146 7|Anti Magic 7 (Lv 25)]%%
[admin_add_skill 146 8|Anti Magic 8 (Lv 25)]%%
[admin_add_skill 146 9|Anti Magic 9 (Lv 30)]%%
[admin_add_skill 146 10|Anti Magic 10 (Lv 30)]%%
[admin_add_skill 146 11|Anti Magic 11 (Lv 35)]%%
[admin_add_skill 146 12|Anti Magic 12 (Lv 35)]%%
[admin_add_skill 164 1|Quick Recovery 1 (Lv 20)]%%
[admin_add_skill 164 2|Quick Recovery 2 (Lv 30)]%%
[admin_add_skill 212 1|Fast HP Recovery 1 (Lv 35)]%%
[admin_add_skill 213 1|Boost Mana 1 (Lv 20)]%%
[admin_add_skill 213 2|Boost Mana 2 (Lv 30)]%%
[admin_add_skill 228 1|Fast Spell Casting 1 (Lv 25)]%%
[admin_add_skill 229 1|Fast Mana Recovery 1 (Lv 25)]%%
[admin_add_skill 229 2|Fast Mana Recovery 2 (Lv 35)]%%
[admin_add_skill 235 1|Robe Mastery 1 (Lv 20)]%%
[admin_add_skill 235 2|Robe Mastery 2 (Lv 20)]%%
[admin_add_skill 235 3|Robe Mastery 3 (Lv 25)]%%
[admin_add_skill 235 4|Robe Mastery 4 (Lv 25)]%%
[admin_add_skill 235 5|Robe Mastery 5 (Lv 30)]%%
[admin_add_skill 235 6|Robe Mastery 6 (Lv 30)]%%
[admin_add_skill 235 7|Robe Mastery 7 (Lv 35)]%%
[admin_add_skill 235 8|Robe Mastery 8 (Lv 35)]%%
[admin_add_skill 236 1|Light Armor Mastery 1 (Lv 20)]%%
[admin_add_skill 236 2|Light Armor Mastery 2 (Lv 20)]%%
[admin_add_skill 236 3|Light Armor Mastery 3 (Lv 25)]%%
[admin_add_skill 236 4|Light Armor Mastery 4 (Lv 25)]%%
[admin_add_skill 236 5|Light Armor Mastery 5 (Lv 30)]%%
[admin_add_skill 236 6|Light Armor Mastery 6 (Lv 30)]%%
[admin_add_skill 236 7|Light Armor Mastery 7 (Lv 35)]%%
[admin_add_skill 236 8|Light Armor Mastery 8 (Lv 35)]%%
[admin_add_skill 239 1|Expertise D 1 (Lv 20)]%%
[admin_add_skill 249 3|Weapon Mastery 3 (Lv 20)]%%
[admin_add_skill 249 4|Weapon Mastery 4 (Lv 25)]%%
[admin_add_skill 249 5|Weapon Mastery 5 (Lv 25)]%%
[admin_add_skill 249 6|Weapon Mastery 6 (Lv 30)]%%
[admin_add_skill 249 7|Weapon Mastery 7 (Lv 30)]%%
[admin_add_skill 249 8|Weapon Mastery 8 (Lv 35)]%%
[admin_add_skill 249 9|Weapon Mastery 9 (Lv 35)]%%
[admin_add_skill 1011 7|Heal 7 (Lv 20)]%%
[admin_add_skill 1011 8|Heal 8 (Lv 20)]%%
[admin_add_skill 1011 9|Heal 9 (Lv 20)]%%
[admin_add_skill 1011 10|Heal 10 (Lv 25)]%%
[admin_add_skill 1011 11|Heal 11 (Lv 25)]%%
[admin_add_skill 1011 12|Heal 12 (Lv 25)]%%
[admin_add_skill 1011 13|Heal 13 (Lv 30)]%%
[admin_add_skill 1011 14|Heal 14 (Lv 30)]%%
[admin_add_skill 1011 15|Heal 15 (Lv 30)]%%
[admin_add_skill 1011 16|Heal 16 (Lv 35)]%%
[admin_add_skill 1011 17|Heal 17 (Lv 35)]%%
[admin_add_skill 1011 18|Heal 18 (Lv 35)]%%
[admin_add_skill 1012 2|Cure Poison 2 (Lv 35)]%%
[admin_add_skill 1013 1|Recharge 1 (Lv 30)]%%
[admin_add_skill 1013 2|Recharge 2 (Lv 30)]%%
[admin_add_skill 1013 3|Recharge 3 (Lv 35)]%%
[admin_add_skill 1013 4|Recharge 4 (Lv 35)]%%
[admin_add_skill 1015 4|Battle Heal 4 (Lv 20)]%%
[admin_add_skill 1015 5|Battle Heal 5 (Lv 20)]%%
[admin_add_skill 1015 6|Battle Heal 6 (Lv 20)]%%
[admin_add_skill 1015 7|Battle Heal 7 (Lv 25)]%%
[admin_add_skill 1015 8|Battle Heal 8 (Lv 25)]%%
[admin_add_skill 1015 9|Battle Heal 9 (Lv 25)]%%
[admin_add_skill 1015 10|Battle Heal 10 (Lv 30)]%%
[admin_add_skill 1015 11|Battle Heal 11 (Lv 30)]%%
[admin_add_skill 1015 12|Battle Heal 12 (Lv 30)]%%
[admin_add_skill 1015 13|Battle Heal 13 (Lv 35)]%%
[admin_add_skill 1015 14|Battle Heal 14 (Lv 35)]%%
[admin_add_skill 1015 15|Battle Heal 15 (Lv 35)]%%
<br>
<button value="Page2" action="bypass -h admin_skill_index Elf/E_Oracle2" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
