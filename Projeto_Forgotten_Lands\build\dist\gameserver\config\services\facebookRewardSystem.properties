# ==================================================
#               Facebook Reward System             #
# ================================================== 
# Should we allow Facebook Reward System?
# Without this command nothing will work
AllowFacebookRewardSystem = False

# ==================================================
#          Connecting char with FB Profile         #
# ================================================== 
# What minimum level player should have, to connect to facebook profile?
FacebookConnectMinLevel = 76

# During registration, if player have facebook name with NOT ONLY english letters, we need to find out his facebook by writing character name in comment
# This config chooses syntax showing how exactly comment should look like
# For example if you write here %originalComment% "%charName% - then we will identify him by comment like "This server rocks! "Vampir"
# You can make a lot of possibilities, like %charName%: %originalComment%, or %originalComment% pm me(%charName%) in game
# Replacements are: %originalComment% and %charName%
FacebookRegistrationNicknameSyntax = %originalComment% "%charName%

# During Registration, should wrote comment be just the same as requested?
FacebookRegistrationOnlyExactComment = False

# ==================================================
#          		 Completing Tasks                  #
# ================================================== 
# What's Time Limit(in Seconds) for completing single task?
# 60 - 1 Minute. 300 - 5 minutes. 600 - 10 minutes. 900 - 15 minutes. 1800 - 30 minutes.
FacebookTimeLimit = 900

# How often(Millis) Thread which check Time Limit, should do its job?
FacebookTimeLimitThreadDelay = 5000

# Delay(in Seconds) between completing Task and starting Next one.
# This delay is made, so player will not Like, Comment and Share everything in same moment
# It is better if he will do next action after a while, so chance that his friends will know about L2Tales is bigger
# 1 Hour = 3600. 3 Hours = 10800. 6 Hours = 21600. 12 Hours = 43200
FacebookDelayBetweenTasks = 10800

# For how long(Seconds) COMMENT/POST content should not be possible to be used again after being chosen by a player?
# It should NOT be smaller than FacebookTimeLimit value.
# It should also not be too long, since there is limited amount of comments on our list
FacebookCommentContentReuse = 1800

# ==================================================
#              		    Rewards  			       #
# ==================================================
# What reward should be given for LIKE on official post with GIVEN TASK?
# Syntax: itemId,itemCount;itemId,itemCount
FacebookRewardLikeTask = 6673,2
# What reward should be given for LIKE on official post with NO GIVEN TASK?
# Syntax: itemId,itemCount;itemId,itemCount
FacebookRewardLikeNoTask = 6673,1

# What reward should be given for COMMENT on official post with GIVEN TASK?
# Syntax: itemId,itemCount;itemId,itemCount
FacebookRewardCommentTask = 6673,6;40000,1
# What reward should be given for COMMENT on official post with NO GIVEN TASK?
# Syntax: itemId,itemCount;itemId,itemCount
FacebookRewardCommentNoTask = 6673,3;40000,1

# ==================================================
#          		    Announcement                   #
# ==================================================
# Should we allow Facebook Auto Announcement for Player NOT YET CONNECTED to facebook profile?
# Announcement Message can be found in string folder under address: Facebook.AutoAnnounce.NotConnected
FacebookAllowAutoAnnouncementNotConnected = True
# How often(Seconds) this announcement should be shown to players NOT YET CONNECTED to facebook profiles?
FacebookAutoAnnouncementNotConnectedDelay = 420

# Should we allow Facebook Auto Announcement for Player ALREADY CONNECTED to facebook who have NEW TASK waiting?
# Announcement Message can be found in string folder under address: Facebook.AutoAnnounce.NextTask
FacebookAllowAutoAnnouncementConnected = True
# How often(Seconds) this announcement should be shown to players ALREADY CONNECTED to facebook profiles?
FacebookAutoAnnouncementConnectedDelay = 240

# Should we allow Facebook Auto Announcement for Player WITH NEGATIVE POINTS?
# Announcement Message can be found in string folder under address: Facebook.AutoAnnounce.NegativePoints
FacebookAllowAutoAnnounceNegativePoints = True
# How often(Seconds) this announcement should be shown to players WITH NEGATIVE POINTS?
FacebookAutoAnnouncementNegativePointsDelay = 60

# ==================================================
#          Extracting Data from Facebook           #
# ================================================== 
# Every how many Milliseconds since last extraction, should server look for next Comments, Likes, Posts etc?
# NOTE: Do not set it to very low number
FacebookExtractionDelay = 3000

# How many of last posts should be valid for Rewarding & checking if anyone removed his Action(Like/Share/Comment)?
FacebookValidPostsCount = 3

# Whats page name that is used for links in the browser
# For example "VercettiOfficial". It can be found by going to the page and looking at the link("https://www.facebook.com/VercettiOfficial/?fref=photo")
FacebookPageName = VercettiOfficial

# Active Page token:
FacebookToken = CAANZCGNYc8YIBAEdXQeSoSQPt7Fg6tTjQ3jIxZBNUqsZAXMTc7zSuk7BWJZAVnW9PlmpN9MmSKGcnNPbNZA8DLzU4kx0iLZCjL3gWqHbRcwm9ShidSJlZABz54GklkZBhKIcwwIZBewrZCizpZA8UZCUtF7OpOjZBZBGr9HOXEULhQl8Sy0nUOzOd0NzsT5vfEh7P1OQOrVlOyzUX9RaZAkZB4AA5AvI