<title>Choose skill to add</title>
<center>
[admin_add_skill 1013 5|Recharge 5 (Lv 40)]%%
[admin_add_skill 1013 6|Recharge 6 (Lv 40)]%%
[admin_add_skill 1013 7|Recharge 7 (Lv 44)]%%
[admin_add_skill 1013 8|Recharge 8 (Lv 44)]%%
[admin_add_skill 1013 9|Recharge 9 (Lv 48)]%%
[admin_add_skill 1013 10|Recharge 10 (Lv 48)]%%
[admin_add_skill 1013 11|Recharge 11 (Lv 52)]%%
[admin_add_skill 1013 12|Recharge 12 (Lv 52)]%%
[admin_add_skill 1013 13|Recharge 13 (Lv 56)]%%
[admin_add_skill 1013 14|Recharge 14 (Lv 56)]%%
[admin_add_skill 1013 15|Recharge 15 (Lv 58)]%%
[admin_add_skill 1013 16|Recharge 16 (Lv 58)]%%
[admin_add_skill 1013 17|Recharge 17 (Lv 60)]%%
[admin_add_skill 1013 18|Recharge 18 (Lv 60)]%%
[admin_add_skill 1013 19|Recharge 19 (Lv 62)]%%
[admin_add_skill 1013 20|Recharge 20 (Lv 62)]%%
[admin_add_skill 1013 21|Recharge 21 (Lv 64)]%%
[admin_add_skill 1013 22|Recharge 22 (Lv 64)]%%
[admin_add_skill 1013 23|Recharge 23 (Lv 66)]%%
[admin_add_skill 1013 24|Recharge 24 (Lv 66)]%%
[admin_add_skill 1013 25|Recharge 25 (Lv 68)]%%
[admin_add_skill 1013 26|Recharge 26 (Lv 68)]%%
[admin_add_skill 1013 27|Recharge 27 (Lv 70)]%%
[admin_add_skill 1013 28|Recharge 28 (Lv 70)]%%
[admin_add_skill 1013 29|Recharge 29 (Lv 72)]%%
[admin_add_skill 1013 30|Recharge 30 (Lv 72)]%%
[admin_add_skill 1013 31|Recharge 31 (Lv 74)]%%
[admin_add_skill 1013 32|Recharge 32 (Lv 74)]%%
[admin_add_skill 1018 1|Purify 1 (Lv 44)]%%
[admin_add_skill 1018 2|Purify 2 (Lv 52)]%%
[admin_add_skill 1018 3|Purify 3 (Lv 62)]%%
[admin_add_skill 1035 2|Mental Shield 2 (Lv 40)]%%
[admin_add_skill 1035 3|Mental Aegis 3 (Lv 48)]%%
[admin_add_skill 1035 4|Mental Aegis 4 (Lv 56)]%%
[admin_add_skill 1040 3|Shield 3 (Lv 44)]%%
[admin_add_skill 1059 2|Empower 2 (Lv 44)]%%
[admin_add_skill 1059 3|Greater Empower 3 (Lv 52)]%%
[admin_add_skill 1068 3|Might 3 (Lv 40)]%%
[admin_add_skill 1073 2|Kiss of Eva 2 (Lv 52)]%%
[admin_add_skill 1077 2|Focus 2 (Lv 44)]%%
[admin_add_skill 1077 3|Focus 3 (Lv 52)]%%
[admin_add_skill 1078 3|Greater Concentration 3 (Lv 44)]%%
[admin_add_skill 1078 4|Greater Concentration 4 (Lv 52)]%%
[admin_add_skill 1078 5|Greater Concentration 5 (Lv 60)]%%
[admin_add_skill 1078 6|Greater Concentration 6 (Lv 68)]%%
[admin_add_skill 1189 2|Resist Wind 2 (Lv 40)]%%
[admin_add_skill 1189 3|Resist Wind 3 (Lv 44)]%%
[admin_add_skill 1201 10|Dryad Root 10 (Lv 40)]%%
[admin_add_skill 1201 11|Dryad Root 11 (Lv 40)]%%
[admin_add_skill 1201 12|Dryad Root 12 (Lv 40)]%%
[admin_add_skill 1201 13|Dryad Root 13 (Lv 44)]%%
[admin_add_skill 1201 14|Dryad Root 14 (Lv 44)]%%
[admin_add_skill 1201 15|Dryad Root 15 (Lv 44)]%%
[admin_add_skill 1201 16|Dryad Root 16 (Lv 48)]%%
[admin_add_skill 1201 17|Dryad Root 17 (Lv 48)]%%
[admin_add_skill 1201 18|Dryad Root 18 (Lv 48)]%%
[admin_add_skill 1201 19|Dryad Root 19 (Lv 52)]%%
[admin_add_skill 1201 20|Dryad Root 20 (Lv 52)]%%
[admin_add_skill 1201 21|Dryad Root 21 (Lv 52)]%%
[admin_add_skill 1201 22|Dryad Root 22 (Lv 56)]%%
[admin_add_skill 1201 23|Dryad Root 23 (Lv 56)]%%
[admin_add_skill 1201 24|Dryad Root 24 (Lv 56)]%%
[admin_add_skill 1201 25|Dryad Root 25 (Lv 58)]%%
[admin_add_skill 1201 26|Dryad Root 26 (Lv 60)]%%
[admin_add_skill 1201 27|Dryad Root 27 (Lv 62)]%%
[admin_add_skill 1201 28|Dryad Root 28 (Lv 64)]%%
[admin_add_skill 1201 29|Dryad Root 29 (Lv 66)]%%
[admin_add_skill 1201 30|Dryad Root 30 (Lv 68)]%%
[admin_add_skill 1201 31|Dryad Root 31 (Lv 70)]%%
[admin_add_skill 1201 32|Dryad Root 32 (Lv 72)]%%
[admin_add_skill 1201 33|Dryad Root 33 (Lv 74)]%%
[admin_add_skill 1206 6|Wind Shackle 6 (Lv 40)]%%
[admin_add_skill 1206 7|Wind Shackle 7 (Lv 44)]%%
[admin_add_skill 1206 8|Wind Shackle 8 (Lv 48)]%%
[admin_add_skill 1206 9|Wind Shackle 9 (Lv 52)]%%
[admin_add_skill 1206 10|Wind Shackle 10 (Lv 56)]%%
[admin_add_skill 1206 11|Wind Shackle 11 (Lv 58)]%%
[admin_add_skill 1206 12|Wind Shackle 12 (Lv 60)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index DarkElf/DE_ShillienElder" width=40 height=15><br>
<button value="Page2" action="bypass -h admin_skill_index DarkElf/DE_ShillienElder2" width=40 height=15><br>
<button value="Page4" action="bypass -h admin_skill_index DarkElf/DE_ShillienElder4" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
