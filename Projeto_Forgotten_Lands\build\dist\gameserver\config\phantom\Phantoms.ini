# Enable Phantom players
# WARNING! It can be enabled through GM commands, below settings must be appropreately set.
PhantomPlayersEnabled = True

# In which account will all phantom players be stored.
PhantomPlayersAccount = PhantomPlayerAI

# Disable phantom actions on init, move etc...
# Default: false
DisablePhantomActions = False

# Disable global respawn of phantom players.
# There is admin commands related to this.. 
# Default : False
DisablePhantomRespawn = False

# Which classes are phantoms banned from using.
#                       WL CL BS PP ES Or EE PS SO SE WC
PhantomBannedClassIds = 14,15,16,17,28,29,30,41,42,43,52

# Which sets are phantoms banned from using.
#                     Dy Dyn Dyn Vsp VsN Vsp VsN Vsp VsN Ele Ele Ele
PhantomBannedSetIds = 141,142,143

# The max grade of items phantoms can use.
# 0: No-Grade 
# 1: D Grade
# 2: C Grade
# 3: B Grade
# 4: A Grade
# 5: S Grade
# 6: S80 Grade
# 7: S84 Grade
PhantomMaxWeaponGrade = 7
PhantomMaxArmorGrade = 7
PhantomMaxJewelGrade = 7

# Chance to enchant weap.
# Default: 0
PhantomChanceEnchantWeap = 0

# Max number of enchant that weap can get.
# Work as this Rnd.get(3, Config)....
# Default : 4
PhantomMaxEnchantWeap = 6

# Allow to setup custom titles from a list for new phantom.
# If true will set nobless + custom title on % chance.
# Default: False
AllowSetupCustomTitles = True

# Chance to Set Title + Noble on new phantom player.
# Default: 30
ChanceToSetTitle = 30

# Max drift range for Phantom, if phantom distance to npc is higher then this number phantom will skip it...
# to disable it set high number 
# Default: 1000000 (all kind of npc's)
MaxDriftRangeForNpc = 10000

# How many phantom players are allowed to be in the game.
# Multiple instances of spawners can be achieved through GM commands, but this is a global limit.
# All spawners will wait to spawn until the phantoms count goes to lower than maximum.
PhantomMaxPlayers = 3000

# The maximum phantom players that will be spawned by the spawner.
# If set to match the max players for example, the spawner will keep spawning until the count is reached and after that it will stop spawning.
# Resulting in all phantoms slowly leaving the game due to their lifetime until there are none. Set higher numbers than max phantoms to keep phantoms active.
# If the current spawned phantoms exceeds the maximum allowed, the spawner will wait until there is a free spot and then continue counting and spawning.
PhantomSpawnMax = 3000

# How many SECONDS should we wait until another phantom is spawned.
PhantomSpawnDelay = 1

# How many MINUTES can a phantom be in the world until it logs out.
PhantomMaxLifetime = 720

# Phantom clans
# Keep empty to disable. Split Clan id's with ;
# Example: 2103120;3120312030;31203010
PhantomClans = 268491280;268485918;268485555


#-------------------------------------------------------------
# Roaming in town settings
#-------------------------------------------------------------

# Add NPC's that will be allowed to walk into it.
PhantomRoamingNpcs = 37001,37010,37004,32864,400,30115,32323,31688,37015,31126,30300,32783,32615,32347,31049,31860

# How many times should phantoms check the following NPCs before they stop checking them.
PhantomRoamingMaxWhChecks = 3
PhantomRoamingMaxWhChecksDwarf = 8
PhantomRoamingMaxShopChecks = 2
PhantomRoamingMaxShopChecksDwarf = 5
PhantomRoamingMaxNpcChecks = 6

# Delay in seconds when roaming.
PhantomRoamingMinWhDelay = 120
PhantomRoamingMaxWhDelay = 120
PhantomRoamingMinShopDelay = 60
PhantomRoamingMaxShopDelay = 180
PhantomRoamingMinNpcDelay = 60
PhantomRoamingMaxNpcDelay = 120
PhantomRoamingMinPrivatestoreDelay = 20
PhantomRoamingMaxPrivatestoreDelay = 120
PhantomRoamingMinFreeroamDelay = 60
PhantomRoamingMaxFreeroamDelay = 120

# Debug phantom players
# Default: False
DebugPhantoms = False