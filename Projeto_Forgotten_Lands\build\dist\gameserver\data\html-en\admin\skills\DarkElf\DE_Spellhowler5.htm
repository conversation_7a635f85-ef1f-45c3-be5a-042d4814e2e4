<title>Choose skill to add</title>
<center>
[admin_add_skill 1234 1|Vampiric Claw 1 (Lv 40)]%%
[admin_add_skill 1234 2|Vampiric Claw 2 (Lv 40)]%%
[admin_add_skill 1234 3|Vampiric Claw 3 (Lv 44)]%%
[admin_add_skill 1234 4|Vampiric Claw 4 (Lv 44)]%%
[admin_add_skill 1234 5|Vampiric Claw 5 (Lv 48)]%%
[admin_add_skill 1234 6|Vampiric Claw 6 (Lv 48)]%%
[admin_add_skill 1234 7|Vampiric Claw 7 (Lv 52)]%%
[admin_add_skill 1234 8|Vampiric Claw 8 (Lv 52)]%%
[admin_add_skill 1234 9|Vampiric Claw 9 (Lv 56)]%%
[admin_add_skill 1234 10|Vampiric Claw 10 (Lv 56)]%%
[admin_add_skill 1234 11|Vampiric Claw 11 (Lv 58)]%%
[admin_add_skill 1234 12|Vampiric Claw 12 (Lv 58)]%%
[admin_add_skill 1234 13|Vampiric Claw 13 (Lv 60)]%%
[admin_add_skill 1234 14|Vampiric Claw 14 (Lv 60)]%%
[admin_add_skill 1234 15|Vampiric Claw 15 (Lv 62)]%%
[admin_add_skill 1234 16|Vampiric Claw 16 (Lv 62)]%%
[admin_add_skill 1234 17|Vampiric Claw 17 (Lv 64)]%%
[admin_add_skill 1234 18|Vampiric Claw 18 (Lv 64)]%%
[admin_add_skill 1234 19|Vampiric Claw 19 (Lv 66)]%%
[admin_add_skill 1234 20|Vampiric Claw 20 (Lv 66)]%%
[admin_add_skill 1234 21|Vampiric Claw 21 (Lv 68)]%%
[admin_add_skill 1234 22|Vampiric Claw 22 (Lv 68)]%%
[admin_add_skill 1234 23|Vampiric Claw 23 (Lv 70)]%%
[admin_add_skill 1234 24|Vampiric Claw 24 (Lv 70)]%%
[admin_add_skill 1234 25|Vampiric Claw 25 (Lv 72)]%%
[admin_add_skill 1234 26|Vampiric Claw 26 (Lv 72)]%%
[admin_add_skill 1234 27|Vampiric Claw 27 (Lv 74)]%%
[admin_add_skill 1234 28|Vampiric Claw 28 (Lv 74)]%%
[admin_add_skill 1239 1|Hurricane 1 (Lv 40)]%%
[admin_add_skill 1239 2|Hurricane 2 (Lv 40)]%%
[admin_add_skill 1239 3|Hurricane 3 (Lv 44)]%%
[admin_add_skill 1239 4|Hurricane 4 (Lv 44)]%%
[admin_add_skill 1239 5|Hurricane 5 (Lv 48)]%%
[admin_add_skill 1239 6|Hurricane 6 (Lv 48)]%%
[admin_add_skill 1239 7|Hurricane 7 (Lv 52)]%%
[admin_add_skill 1239 8|Hurricane 8 (Lv 52)]%%
[admin_add_skill 1239 9|Hurricane 9 (Lv 56)]%%
[admin_add_skill 1239 10|Hurricane 10 (Lv 56)]%%
[admin_add_skill 1239 11|Hurricane 11 (Lv 58)]%%
[admin_add_skill 1239 12|Hurricane 12 (Lv 58)]%%
[admin_add_skill 1239 13|Hurricane 13 (Lv 60)]%%
[admin_add_skill 1239 14|Hurricane 14 (Lv 60)]%%
[admin_add_skill 1239 15|Hurricane 15 (Lv 62)]%%
[admin_add_skill 1239 16|Hurricane 16 (Lv 62)]%%
[admin_add_skill 1239 17|Hurricane 17 (Lv 64)]%%
[admin_add_skill 1239 18|Hurricane 18 (Lv 64)]%%
[admin_add_skill 1239 19|Hurricane 19 (Lv 66)]%%
[admin_add_skill 1239 20|Hurricane 20 (Lv 66)]%%
[admin_add_skill 1239 21|Hurricane 21 (Lv 68)]%%
[admin_add_skill 1239 22|Hurricane 22 (Lv 68)]%%
[admin_add_skill 1239 23|Hurricane 23 (Lv 70)]%%
[admin_add_skill 1239 24|Hurricane 24 (Lv 70)]%%
[admin_add_skill 1239 25|Hurricane 25 (Lv 72)]%%
[admin_add_skill 1239 26|Hurricane 26 (Lv 72)]%%
[admin_add_skill 1239 27|Hurricane 27 (Lv 74)]%%
[admin_add_skill 1239 28|Hurricane 28 (Lv 74)]%%
[admin_add_skill 1267 1|Shadow Flare 1 (Lv 40)]%%
[admin_add_skill 1267 2|Shadow Flare 2 (Lv 44)]%%
[admin_add_skill 1267 3|Shadow Flare 3 (Lv 48)]%%
[admin_add_skill 1267 4|Shadow Flare 4 (Lv 52)]%%
[admin_add_skill 1267 5|Shadow Flare 5 (Lv 56)]%%
[admin_add_skill 1267 6|Shadow Flare 6 (Lv 58)]%%
[admin_add_skill 1267 7|Shadow Flare 7 (Lv 60)]%%
[admin_add_skill 1267 8|Shadow Flare 8 (Lv 62)]%%
[admin_add_skill 1267 9|Shadow Flare 9 (Lv 64)]%%
[admin_add_skill 1267 10|Shadow Flare 10 (Lv 66)]%%
[admin_add_skill 1267 11|Shadow Flare 11 (Lv 68)]%%
[admin_add_skill 1267 12|Shadow Flare 12 (Lv 70)]%%
[admin_add_skill 1267 13|Shadow Flare 13 (Lv 72)]%%
[admin_add_skill 1267 14|Shadow Flare 14 (Lv 74)]%%
[admin_add_skill 1287 1|Seed of Wind 1 (Lv 66)]%%
[admin_add_skill 1288 1|Aura Symphony 1 (Lv 68)]%%
[admin_add_skill 1291 1|Demon Wind 1 (Lv 70)]%%
[admin_add_skill 1294 1|Elemental Storm 1 (Lv 72)]%%
[admin_add_skill 1297 1|Clear Mind 1 (Lv 40)]%%
[admin_add_skill 1297 2|Clear Mind 2 (Lv 48)]%%
[admin_add_skill 1297 3|Clear Mind 3 (Lv 56)]%%
[admin_add_skill 1297 4|Clear Mind 4 (Lv 62)]%%
[admin_add_skill 1297 5|Clear Mind 5 (Lv 68)]%%
[admin_add_skill 1297 6|Clear Mind 6 (Lv 74)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index DarkElf/DE_Spellhowler" width=40 height=15><br>
<button value="Page2" action="bypass -h admin_skill_index DarkElf/DE_Spellhowler2" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index DarkElf/DE_Spellhowler3" width=40 height=15><br>
<button value="Page4" action="bypass -h admin_skill_index DarkElf/DE_Spellhowler4" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
