<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/" debug="false">
	<appender name="CONSOLE" class="org.apache.log4j.ConsoleAppender">
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{HH:mm:ss}] %5p %m%n"/>
		</layout>
	</appender>
	<appender name="FILE" class="org.apache.log4j.FileAppender">
		<param name="File" value="log/java.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss:SSS}] %5p %c{2}: %m%n"/>
		</layout>
	</appender>
	<appender name="ENTER_WORLD" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/enterWorld/players.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n" />
		</layout>
	</appender>
	<appender name="DONATIONS" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/donations.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n"/>
		</layout>
	</appender>
	<appender name="GAME" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/game.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n"/>
		</layout>
	</appender>
	<appender name="CHAT" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/chat/allChatTypes.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n"/>
		</layout>
	</appender>
	<appender name="CHAT_PM" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/chat/chat_privateMessages.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n"/>
		</layout>
	</appender>
	<appender name="CHAT_SHOUT" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/chat/chat_shout.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n"/>
		</layout>
	</appender>
	<appender name="CHAT_TRADE" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/chat/chat_trade.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n"/>
		</layout>
	</appender>
	<appender name="MAILS" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/chat/mails.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n"/>
		</layout>
	</appender>
	<appender name="PRIVATE_STORE_MESSAGES" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/chat/storeMessages.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n"/>
		</layout>
	</appender>
	<appender name="ILLEGAL_ACTIVITY" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/illegalActivity.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n"/>
		</layout>
	</appender>
	<appender name="VOTES" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/features/votes.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n"/>
		</layout>
	</appender>
	<appender name="STREAM" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/features/stream.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n"/>
		</layout>
	</appender>
	<appender name="FACEBOOK" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/features/facebook.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n"/>
		</layout>
	</appender>
	<appender name="TOURNAMENT" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/features/tournament.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n"/>
		</layout>
	</appender>
	<appender name="EVENTS" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/events.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n"/>
		</layout>
	</appender>
	<appender name="ITEM" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/items/allItems.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd-a"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n"/>
		</layout>
	</appender>
	<appender name="GM_ITEM" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/items/gmItems.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n"/>
		</layout>
	</appender>
	<appender name="ITEM_DONATION_COINS" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/items/donationCoins.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n"/>
		</layout>
	</appender>
	<appender name="GMACTIONS" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/gmActions/realGMs.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n"/>
		</layout>
	</appender>
	<appender name="HIDDEN_GM_ACTIONS" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="log/gmActions/hiddenGMs.log"/>
		<param name="Append" value="true"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss}] %m%n"/>
		</layout>
	</appender>
	<appender name="DEBUG" class="org.apache.log4j.FileAppender">
		<param name="File" value="log/debug.log"/>
		<param name="Append" value="false"/>
		<param name="DatePattern" value="'.'yyyy-MM-dd"/>
		<layout class="org.apache.log4j.EnhancedPatternLayout">
			<param name="ConversionPattern" value="[%d{dd.MM.yy HH:mm:ss:SSS}] %m%n"/>
		</layout>
	</appender>
	<appender name="FILEASYNC" class="org.apache.log4j.AsyncAppender">
		<param name="Blocking" value="false"/>
		<appender-ref ref="FILE"/>
	</appender>
	<appender name="GAMEASYNC" class="org.apache.log4j.AsyncAppender">
		<param name="Blocking" value="false"/>
		<param name="BufferSize" value="512"/>
		<appender-ref ref="GAME"/>
	</appender>
	<appender name="CHATASYNC" class="org.apache.log4j.AsyncAppender">
		<param name="Blocking" value="false"/>
		<param name="BufferSize" value="256"/>
		<appender-ref ref="CHAT"/>
	</appender>
	<appender name="CHAT_PM_ASYNC" class="org.apache.log4j.AsyncAppender">
		<param name="Blocking" value="false"/>
		<param name="BufferSize" value="128"/>
		<appender-ref ref="CHAT_PM"/>
	</appender>
	<appender name="CHAT_SHOUT_ASYNC" class="org.apache.log4j.AsyncAppender">
		<param name="Blocking" value="false"/>
		<param name="BufferSize" value="128"/>
		<appender-ref ref="CHAT_SHOUT"/>
	</appender>
	<appender name="CHAT_TRADE_ASYNC" class="org.apache.log4j.AsyncAppender">
		<param name="Blocking" value="false"/>
		<param name="BufferSize" value="128"/>
		<appender-ref ref="CHAT_TRADE"/>
	</appender>
	<appender name="EVENTSASYNC" class="org.apache.log4j.AsyncAppender">
		<param name="Blocking" value="false"/>
		<param name="BufferSize" value="256"/>
		<appender-ref ref="EVENTS"/>
	</appender>
	<appender name="ITEMASYNC" class="org.apache.log4j.AsyncAppender">
		<param name="Blocking" value="false"/>
		<param name="BufferSize" value="512"/>
		<appender-ref ref="ITEM"/>
	</appender>
	<appender name="GMITEMASYNC" class="org.apache.log4j.AsyncAppender">
		<param name="Blocking" value="false"/>
		<param name="BufferSize" value="512"/>
		<appender-ref ref="GM_ITEM"/>
	</appender>
	<logger name="enterWorld" additivity="false">
		<appender-ref ref="ENTER_WORLD" />
	</logger>
	<logger name="game" additivity="false">
		<appender-ref ref="GAMEASYNC"/>
	</logger>
	<logger name="chat" additivity="false">
		<appender-ref ref="CHATASYNC"/>
	</logger>
	<logger name="chatPM" additivity="false">
		<appender-ref ref="CHAT_PM_ASYNC"/>
	</logger>
	<logger name="chatShout" additivity="false">
		<appender-ref ref="CHAT_SHOUT_ASYNC"/>
	</logger>
	<logger name="chatTrade" additivity="false">
		<appender-ref ref="CHAT_TRADE_ASYNC"/>
	</logger>
	<logger name="mails" additivity="false">
		<appender-ref ref="MAILS"/>
	</logger>
	<logger name="privateStoreMessages" additivity="false">
		<appender-ref ref="PRIVATE_STORE_MESSAGES"/>
	</logger>
	<logger name="illegalActivity" additivity="false">
		<appender-ref ref="ILLEGAL_ACTIVITY"/>
	</logger>
	<logger name="donations" additivity="false">
		<appender-ref ref="DONATIONS"/>
	</logger>
	<logger name="votes" additivity="false">
		<appender-ref ref="VOTES"/>
	</logger>
	<logger name="stream" additivity="false">
		<appender-ref ref="STREAM"/>
	</logger>
	<logger name="facebook" additivity="false">
		<appender-ref ref="FACEBOOK"/>
	</logger>
	<logger name="tournament" additivity="false">
		<appender-ref ref="TOURNAMENT"/>
	</logger>
	<logger name="events" additivity="false">
		<appender-ref ref="EVENTSASYNC"/>
	</logger>
	<logger name="item" additivity="false">
		<appender-ref ref="ITEMASYNC"/>
	</logger>
	<logger name="gmItem" additivity="false">
		<appender-ref ref="GMITEMASYNC"/>
	</logger>
	<logger name="itemDonationCoins" additivity="false">
		<appender-ref ref="ITEM_DONATION_COINS"/>
	</logger>
	<logger name="gmactions" additivity="false">
		<appender-ref ref="GMACTIONS"/>
	</logger>
	<logger name="hiddenGMActions" additivity="false">
		<appender-ref ref="HIDDEN_GM_ACTIONS"/>
	</logger>
	<logger name="debug" additivity="false">
		<level value="debug"/>
		<appender-ref ref="DEBUG"/>
	</logger>
	<logger name="org.hibernate">
		<level value="WARN"/>
	</logger>
	<root>
		<level value="info"/>
		<appender-ref ref="CONSOLE"/>
		<appender-ref ref="FILEASYNC"/>
	</root>
</log4j:configuration>
