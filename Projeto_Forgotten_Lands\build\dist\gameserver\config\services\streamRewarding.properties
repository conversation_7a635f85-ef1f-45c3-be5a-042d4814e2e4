# ==================================================
#               Stream Rewarding System            #
# ================================================== 

# ========================
# Display Options Serer  #
# ========================
# Server Name:
ServerName = Forgotten Lands

# Server Sub Name.
# This is required for identifying server in Stream, it is used in HTMLs
# For Example: Sovereign, Ruby
# It can be left empty
ServerSubName = 


# Should we allow Streaming System?
# Without this command nothing will work
AllowStreamingSystem = False
# Should all activities regarding Stream be logged in logs file?
AllowStreamLogs = False

# Twitch Client ID requested by the new twitch api system, to identify the user
# Needs to be created from twitch
TwitchClientId = xxxxxx

# How often(<PERSON><PERSON>), twitch should be checked if:
# - New Stream Appeared
# - Stream disappeared
# - Viewers count changed
# NOTE: DON'T SET THIS VALUE TO VERY LOW VALUE, EVEN IF STREAMING SYSTEM IS OFFLINE!
StreamCheckTwitchDelay = 60000

# What URL should be sent to Twitch, to get the records of active streams?
# While writing this Config, API with guide could be found here: https://github.com/justintv/Twitch-API/blob/master/v3_resources/search.md
# Spaces should be replaced with '%20'. It's best to put the link to the browser and see how it will be converted
TwitchActiveStreamsURL = https://api.twitch.tv/kraken/search/streams?q=apocalyps&limit=100
# Is Stream Game Name present in above URL?
# Accepted Answers: true/false
TwitchActiveStreamsURLWithGameName = False

# What should be exact(case sensitive) name of the Game that streamers should have?
# Leave it empty, to disable
TwitchCorrectStreamGame = Lineage II: The Chaotic Chronicle
# What texts(like Tales, L2) should Channel Title contain? NOT case sensitive
# Syntax: partOfTheName,partOfTheName;partOfTheName,partOfTheName,partOfTheName
# For example: L2,Apocalyps;lineage 2,Apocalyps;lineage2,Apocalyps
# In above example, name MUST contain 'L2' & 'Apocalyos' or 'Lineage 2' & 'Apocalyps' or 'lineage2' & 'Apocalyps'
# So if Name is l2tales it will pass. www.lineage2tales.com will pass, Lineage   Tales will NOT pass
# Leave it empty to disable
# After editing, edit also data/html-bypasses/npc/stream.htm
TwitchCorrectStreamTitlesContainArray = Forgotten Lands

# ==================================================
#                  Streaming Panel                 #
# ==================================================
# Should we allow accessing Stream Panel?
AllowStreamPanel = True

# What Voiced Commands, should allow accessing to Stream Panel?
# Set this value from data/html-bypasses/npc/stream.htm
# Take a look on value 'voicedCommands". They are case sensitive, split with ';', reloadable only during server restart

# Should Player Id who connected to the Stream, be saved in database so it will remain after Server Restart?
StreamConnectionSavedInDB = True

# Does Connection to Twitch Channel requires Approval by Admin?
# ABOVE CONFIG MUST BE TRUE, IF THIS HAS TO WORK!
# Admin Command to approve: //streams
StreamConnectionNeedsApproval = False

# Does stream table exists in same database as other GameServer tales?
StreamTableInGameServerDB = True
# IF StreamTableInGameServerDB=False, fill this data:
StreamTableDBURL = *******************************************************************
StreamTableDBLogin = root
StreamTableDBPassword = 6056262

# ==================================================
#                       Rewarding                  #
# ================================================== 
# Should we allow Rewarding for Stream Channels?
AllowRewardingStreamers = True

# After how many seconds, player should get rewarded for streaming?
# 1 Hour = 3600. 3 Hours = 10800. 5 Hours = 18000
StreamingSecondsToReward = 7200

# How many seconds should be MAXIMALLY rewarded per day?
StreamingMaxRewardedSecondsPerDay = 28800
# At what hour, should Total Stream Times be cleared?
# Minute and seconds will be set to 0
StreamingTotalRewardedTimeClearHour = 7

# What rewards streamer should get after reached Time of the Reward & enough viewers?
# Syntax: minViewers,itemId,itemCount;minViewers,itemId,itemCount;minViewers,itemId,itemCount
# If you want 1 FA since 0 viewers, 2 FA since 3 viewers: 0,6673,1;1,6673,2
# Streamer will only get reward for 1 count of min Streamers. For example if he has 5 streamers in above example, he will get only 2 FA, not 3
# While changing the Rewards, don't forget to change HTML: command/stream/mainNotStreamer.htm
StreamingRewards = 3,6673,6;0,6673,2

# Should Reward bonus be increased, when Player is not attached to the Stream or he is offline
StreamIncreaseRewardWhileOffline = False

# ==================================================
#                AFK Checking System               #
# ================================================== 
# Should we allow AFK checking system?
# This system checks if Streamer is AFK while streaming, if he is then reward is not given during AFK time.
# NOTE: To make this system works fine, StreamIncreaseRewardWhileOffline needs to be set to FALSE
AllowStreamingAFKSystem = True

# After how many seconds of being AFK, should players see messages and not receive rewards?
StreamingAFKSystemSecondsToAFK = 60
# When player is AFK, every how many seconds should he get messages that he is AFK?
# Put -1 to disable those messages
StreamingAFKSystemDelayBetweenMsgs = 10
# What should be Chat Type of the message about being AFK?
# Possibilities: TELL, PARTY, CLAN, ANNOUNCEMENT, COMMANDCHANNEL_ALL, COMMANDCHANNEL_COMMANDER, HERO_VOICE, CRITICAL_ANNOUNCE, BATTLEFIELD
# Content of the message can be found in data/string under address Twitch.AFKMessage
StreamingAFKSystemMsgType = COMMANDCHANNEL_ALL