<html><title>Admin Teleports</title>
<body>
<center>
<table width=260>
<tr><td width=40><button value="Main" action="bypass -h admin_admin" width=40 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td>
<td width=180><center>Raid Boss 60-69</center></td>
<td width=40><button value="Back" action="bypass -h admin_show_html teleports.htm" width=40 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td>
</tr></table>
</center>
<br>
<br>
<center>
<button action="bypass -h admin_move_to 102800 -126000 -2500" value="Ice Fairy Sirra (60)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 76787 245775 -10376" value="The 3rd Underwater Guardian (60)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 167152 53120 -4148" value="Guardian of the Statue of Giant Karum (60)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 170320 42640 -4832" value="Taik High Prefect Arak (60)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 115072 112272 -3018" value="Lord Ishka (60)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 113600 47120 -4640" value="Fairy Queen Timiniel (60)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 104240 -3664 -3392" value="Roaring Lord Kastor (62)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 173880 -11412 -2880" value="Ghost of the Well Lidia (63)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 88532 245798 -10376" value="Eva's Guardian Millenu (65)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 117232 -9476 -3320" value="Rahha (65)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 170656 85184 -2000" value="Fierce Tiger King Angel (65)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 41966 215417 -3728" value="Demon Kurikups (65)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 120080 111248 -3047" value="Ancient Weird Drake (65)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 170048 -24896 -3440" value="Gargoyle Lord Tiphon (65)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 113232 17456 -4384" value="Enmity Ghost Ramdal (65)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 168288 28368 -3632" value="Shilen's Priest Hisilrome (65)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 93296 -75104 -1824" value="Demon's Agent Falston (66)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 155000 85400 -3200" value="Abyss Brukunt (67)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 144400 -28192 -1920" value="Kernon's Faithful Servant Kelone (67)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 143265 110044 -3944" value="Bloody Priest Rudelto (69)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
<button action="bypass -h admin_move_to 185800 -26500 -2000" value="Spirit of Andras, The Betrayer (69)" width=240 height=21 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"><br1>
</center></body></html>