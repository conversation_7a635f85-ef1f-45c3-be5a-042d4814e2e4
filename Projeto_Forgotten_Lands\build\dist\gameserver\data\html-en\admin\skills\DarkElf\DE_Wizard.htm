<title>Choose skill to add</title>
<center>
[admin_add_skill 146 5|Anti Magic 5 (Lv 20)]%%
[admin_add_skill 146 6|Anti Magic 6 (Lv 20)]%%
[admin_add_skill 146 7|Anti Magic 7 (Lv 25)]%%
[admin_add_skill 146 8|Anti Magic 8 (Lv 25)]%%
[admin_add_skill 146 9|Anti Magic 9 (Lv 30)]%%
[admin_add_skill 146 10|Anti Magic 10 (Lv 30)]%%
[admin_add_skill 146 11|Anti Magic 11 (Lv 35)]%%
[admin_add_skill 146 12|Anti Magic 12 (Lv 35)]%%
[admin_add_skill 1172 1|Aura Burn 1 (Lv 20)]%%
[admin_add_skill 1172 2|Aura Burn 2 (Lv 20)]%%
[admin_add_skill 1172 3|Aura Burn 3 (Lv 25)]%%
[admin_add_skill 1172 4|Aura Burn 4 (Lv 25)]%%
[admin_add_skill 1172 5|Aura Burn 5 (Lv 30)]%%
[admin_add_skill 1172 6|Aura Burn 6 (Lv 30)]%%
[admin_add_skill 1172 7|Aura Burn 7 (Lv 35)]%%
[admin_add_skill 1172 8|Aura Burn 8 (Lv 35)]%%
[admin_add_skill 1157 1|Body To Mind 1 (Lv 25)]%%
[admin_add_skill 213 1|Boost Mana 1 (Lv 20)]%%
[admin_add_skill 213 2|Boost Mana 2 (Lv 30)]%%
[admin_add_skill 1078 1|Concentration 1 (Lv 20)]%%
[admin_add_skill 1078 2|Concentration 2 (Lv 30)]%%
[admin_add_skill 1151 1|Corpse Life Drain 1 (Lv 30)]%%
[admin_add_skill 1151 2|Corpse Life Drain 2 (Lv 35)]%%
[admin_add_skill 1222 1|Curse Chaos 1 (Lv 35)]%%
[admin_add_skill 1168 2|Curse:Poison 2 (Lv 20)]%%
[admin_add_skill 1168 3|Curse:Poison 3 (Lv 30)]%%
[admin_add_skill 239 1|Expertise D 1 (Lv 20)]%%
[admin_add_skill 212 1|Fast HP Recovery 1 (Lv 35)]%%
[admin_add_skill 229 1|Fast Mana Recovery 1 (Lv 25)]%%
[admin_add_skill 229 2|Fast Mana Recovery 2 (Lv 35)]%%
[admin_add_skill 228 1|Fast Spell Casting 1 (Lv 25)]%%
[admin_add_skill 1181 1|Flame Strike 1 (Lv 20)]%%
[admin_add_skill 1181 2|Flame Strike 2 (Lv 25)]%%
[admin_add_skill 1181 3|Flame Strike 3 (Lv 30)]%%
[admin_add_skill 285 1|Higher Mana Gain 1 (Lv 20)]%%
[admin_add_skill 285 2|Higher Mana Gain 2 (Lv 20)]%%
[admin_add_skill 285 3|Higher Mana Gain 3 (Lv 25)]%%
[admin_add_skill 285 4|Higher Mana Gain 4 (Lv 25)]%%
[admin_add_skill 285 5|Higher Mana Gain 5 (Lv 30)]%%
[admin_add_skill 285 6|Higher Mana Gain 6 (Lv 30)]%%
[admin_add_skill 285 7|Higher Mana Gain 7 (Lv 35)]%%
[admin_add_skill 285 8|Higher Mana Gain 8 (Lv 35)]%%
[admin_add_skill 1184 5|Ice Bolt 5 (Lv 20)]%%
[admin_add_skill 1184 6|Ice Bolt 6 (Lv 20)]%%
[admin_add_skill 1146 1|Mighty Servitor 1 (Lv 35)]%%
[admin_add_skill 1167 1|Poisonous Cloud 1 (Lv 25)]%%
[admin_add_skill 1167 2|Poisonous Cloud 2 (Lv 35)]%%
[admin_add_skill 164 1|Quick Recovery 1 (Lv 20)]%%
[admin_add_skill 164 2|Quick Recovery 2 (Lv 30)]%%
[admin_add_skill 234 1|Robe Mastery 1 (Lv 20)]%%
[admin_add_skill 234 2|Robe Mastery 2 (Lv 20)]%%
[admin_add_skill 234 3|Robe Mastery 3 (Lv 25)]%%
[admin_add_skill 234 4|Robe Mastery 4 (Lv 25)]%%
[admin_add_skill 234 5|Robe Mastery 5 (Lv 30)]%%
[admin_add_skill 234 6|Robe Mastery 6 (Lv 30)]%%
[admin_add_skill 234 7|Robe Mastery 7 (Lv 35)]%%
[admin_add_skill 234 8|Robe Mastery 8 (Lv 35)]%%
<br>
<button value="Page2" action="bypass -h admin_skill_index DarkElf/DE_Wizard2" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
