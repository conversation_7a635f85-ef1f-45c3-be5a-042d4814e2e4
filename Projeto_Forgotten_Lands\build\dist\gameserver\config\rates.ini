#============================================
# 		   Settings Server Rates			#
#============================================
# Rate XP and SP
RateXp = 25.
RateSp = 25.
# Rate drop Adena
RateDropAdena = 15.
# Rate drop things
RateDropItems = 1.

# Rate chance of loot items
RateChanceGroupDropItems = 1.

# Rate drop rate of things
RateChanceDropItems = 1.
# Rate drop rate much things
RateChanceDropWAA = 1.
# Rate drop rate Knight-Epaulette
RateChanceDropEpolets = 1.
# Rate Herb drop rate
RateChanceDropHerbs = 0.1
# Rate chance spoils
RateChanceSpoil = 1.
# Rate spoils much chance of things
RateChanceSpoilWAA = 1.

# Block rate value by the number of scrolls
NoRateEnchantScroll = True

# Block rate value by the number of Herb
NoRateHerbs = True

# Block rate value by the number of ATT
NoRateAtt = False

# Block rate value by the number of drugs
NoRateLifeStone = False

# Block rate value by the number of key materials
NoRateKeyMaterial = False

# Block rate value by the number of prescriptions
NoRateRecipes = True

# Block rate value by the number of Forgotten Scroll
NoRateForgottenScroll = True

# Rate spoil
RateDropSpoil = 10.
# Rate fishing
RateFishDropCount = 4.
# Third-party modifier for Common things, RateDropItems also case
RateDropCommonItems = 1.

# Block rate value by the number of drops with RB above objects with a limited
# If true the next value is not working
# Is always 1.
NoRateRaidBoss = False

RateRaidBoss = 5.

# Block drop rate value by the number of objects with a limited higher
# If true the next value is not working
# Is always 1.
NoRateSiegeGuard = False

RateSiegeGuard = 10.

# Rate quest rewards
RateQuestsReward = 10.
# Rate to drop quest items
RateQuestsDrop = 2.

# The Real / services that increase the rates the rates the party is calculated as the average of the lot (False) or the participant with the lowest tick rate (True).
RatePartyMin = False
# Rate scoring confidence Hellbound
RateHellboundConfidence = 5.
# Rate clan reputation
RateClanRepScore = 1.
# Rate Manor
RateManor = 25
# These etc items are not Rated
NoRateItemIds = 10170,21712,10314,16025,16026,10296,9628,9629,9630,9546,9547,9548,9549,9550,9551,9625,9626,9627,6622,17248,17249,9552,9553,9554,9555,9556,9557,21195,8761,8762,9576,10486,14169,16163,16167,8751,8752,9575,10485,14168,16162,16166,9912,6673,6577,6578
# Block rate value by the number of equip
NoRateEquipment = true

# If the absolute value of reputation points in the calculation of more than option, not to apply the rates the (balancing)
RateClanRepScoreMaxAffected = 2
# Rate spawn mobs
RateMobSpawn = 1
# Minimum level for action RateMobSpawn
RateMobMinLevel = 1
# The maximum level for action RateMobSpawn
RateMobMaxLevel = 100

RateDropRecipes = 1. 

RateDropKeyMaterial = 1.

RateDropLifeStone = 1.

RateDropAtt = 1.

RateDropHerbs = 1.

RateDropEnchantScroll = 1.

RateDropChampion = 1.
ChampionDropOnlyAdena = False
RateSpoilChampion = 1.
RateChampionDropAdena = 1.
