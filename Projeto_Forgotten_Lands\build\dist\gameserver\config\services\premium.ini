# Rate improve service
# Allows for time to raise rates the account on all charms
# 0 - service is disabled
# 1 - used for one account, all GS-s, within the AC and
# 2 - used for one account, under the current GS-a
RateBonusType = 2

# Load Premium Account on enter game?
PremiumHTML = True
# When your Premium is over announce you in screen message.
PremiumDone = True
# Every time you log in server announce you how much time you have till your premium left.
PremiumInfo = True

# Id of the item given by the premium party system
PartyGift = 1

# If the id is different from 0, every new char created will be given the indicated premium service for the default time
# Default: 0 (disabled)
AltNewCharPremiumId = 0

#Charge PA at character creation
# 0 - off
# 1 .. - Day
RateBonusCreateChar = 0

# All of the following values ​​can be specified in the list,
# Number of elements must be equal!
RateBonusPrice = 1500, 250
RateBonusItem = 37000, 37000

# This is a multiplier to the base rate, coupling to apply to Rates on EPC / SP / Adena / Drop / Spoil
RateBonusValue = 2.5;2.0

# Duration in days
RateBonusTime = 30;2

# Rates to tune. Final bonus = RateBonusValue * RateBonus (XP)
RateBonusXp = 1.
RateBonusSp = 1.
RateBonusAdena = 1.
RateBonusItems = 1.
RateBonusSpoil = 1.

# Enable auto-fit only for players from the PA.
# Operate with AutoLoot and AutoLootIndividual
AutoLootPA = False

#% Chance of successful grinding for PA
EnchantChancePA = 68
EnchantChanceArmorPA = 52
EnchantChanceAccessoryPA = 54

# Chance sharpening Bless point
EnchantChanceBlessPA = 68
EnchantChanceArmorBlessPA = 52
EnchantChanceAccessoryBlessPA = 54

# Chance sharpening Crystal point
EnchantChanceCrystalPA = 68
EnchantChanceCrystalArmorPA = 52
EnchantChanceCrystalAccessoryPA = 54

# Alternative system of sharpening
# Default: False
UseAltEnchantPA = False

# The sharpening
# For sharpening values above the maximum stated in this config, the level will be the last value
EnchantWeaponFighterPA = 100,100,100,70,70,70,70,70,70,70,70,70,70,70,70,35,35,35,35,35
EnchantWeaponFighterCrystalPA = 100,100,100,70,70,70,70,70,70,70,70,70,70,70,70,35,35,35,35,35
EnchantWeaponFighterBlessedPA = 100,100,100,70,70,70,70,70,70,70,70,70,70,70,70,35,35,35,35,35
EnchantArmorPA = 100,100,100,66,33,25,20,16,14,12,11,10,9,8,8,7,7,6,6,6
EnchantArmorCrystalPA = 100,100,100,66,33,25,20,16,14,12,11,10,9,8,8,7,7,6,6,6
EnchantArmorBlessedPA = 100,100,100,66,33,25,20,16,14,12,11,10,9,8,8,7,7,6,6,6
EnchantJewelryPA = 100,100,100,66,33,25,20,16,14,12,11,10,9,8,8,7,7,6,6,6
EnchantJewelryCrystalPA = 100,100,100,66,33,25,20,16,14,12,11,10,9,8,8,7,7,6,6,6
EnchantJewelryBlessedPA = 100,100,100,66,33,25,20,16,14,12,11,10,9,8,8,7,7,6,6,6