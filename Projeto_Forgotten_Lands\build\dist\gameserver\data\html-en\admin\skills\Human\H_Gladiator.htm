<title>Choose skill to add</title>
<center>
[admin_add_skill 1 1|Triple Slash 1 (Lv 40)]%%
[admin_add_skill 1 2|Triple Slash 2 (Lv 40)]%%
[admin_add_skill 1 3|Triple Slash 3 (Lv 40)]%%
[admin_add_skill 1 4|Triple Slash 4 (Lv 43)]%%
[admin_add_skill 1 5|Triple Slash 5 (Lv 43)]%%
[admin_add_skill 1 6|Triple Slash 6 (Lv 43)]%%
[admin_add_skill 1 7|Triple Slash 7 (Lv 46)]%%
[admin_add_skill 1 8|Triple Slash 8 (Lv 46)]%%
[admin_add_skill 1 9|Triple Slash 9 (Lv 46)]%%
[admin_add_skill 1 10|Triple Slash 10 (Lv 49)]%%
[admin_add_skill 1 11|Triple Slash 11 (Lv 49)]%%
[admin_add_skill 1 12|Triple Slash 12 (Lv 49)]%%
[admin_add_skill 1 13|Triple Slash 13 (Lv 52)]%%
[admin_add_skill 1 14|Triple Slash 14 (Lv 52)]%%
[admin_add_skill 1 15|Triple Slash 15 (Lv 52)]%%
[admin_add_skill 1 16|Triple Slash 16 (Lv 55)]%%
[admin_add_skill 1 17|Triple Slash 17 (Lv 55)]%%
[admin_add_skill 1 18|Triple Slash 18 (Lv 55)]%%
[admin_add_skill 1 19|Triple Slash 19 (Lv 58)]%%
[admin_add_skill 1 20|Triple Slash 20 (Lv 58)]%%
[admin_add_skill 1 21|Triple Slash 21 (Lv 58)]%%
[admin_add_skill 1 22|Triple Slash 22 (Lv 60)]%%
[admin_add_skill 1 23|Triple Slash 23 (Lv 60)]%%
[admin_add_skill 1 24|Triple Slash 24 (Lv 62)]%%
[admin_add_skill 1 25|Triple Slash 25 (Lv 62)]%%
[admin_add_skill 1 26|Triple Slash 26 (Lv 64)]%%
[admin_add_skill 1 27|Triple Slash 27 (Lv 64)]%%
[admin_add_skill 1 28|Triple Slash 28 (Lv 66)]%%
[admin_add_skill 1 29|Triple Slash 29 (Lv 66)]%%
[admin_add_skill 1 30|Triple Slash 30 (Lv 68)]%%
[admin_add_skill 1 31|Triple Slash 31 (Lv 68)]%%
[admin_add_skill 1 32|Triple Slash 32 (Lv 70)]%%
[admin_add_skill 1 33|Triple Slash 33 (Lv 70)]%%
[admin_add_skill 1 34|Triple Slash 34 (Lv 72)]%%
[admin_add_skill 1 35|Triple Slash 35 (Lv 72)]%%
[admin_add_skill 1 36|Triple Slash 36 (Lv 74)]%%
[admin_add_skill 1 37|Triple Slash 37 (Lv 74)]%%
[admin_add_skill 5 1|Double Sonic Slash 1 (Lv 46)]%%
[admin_add_skill 5 2|Double Sonic Slash 2 (Lv 46)]%%
[admin_add_skill 5 3|Double Sonic Slash 3 (Lv 46)]%%
[admin_add_skill 5 4|Double Sonic Slash 4 (Lv 49)]%%
[admin_add_skill 5 5|Double Sonic Slash 5 (Lv 49)]%%
[admin_add_skill 5 6|Double Sonic Slash 6 (Lv 49)]%%
[admin_add_skill 5 7|Double Sonic Slash 7 (Lv 52)]%%
[admin_add_skill 5 8|Double Sonic Slash 8 (Lv 52)]%%
[admin_add_skill 5 9|Double Sonic Slash 9 (Lv 52)]%%
[admin_add_skill 5 10|Double Sonic Slash 10 (Lv 55)]%%
[admin_add_skill 5 11|Double Sonic Slash 11 (Lv 55)]%%
[admin_add_skill 5 12|Double Sonic Slash 12 (Lv 55)]%%
[admin_add_skill 5 13|Double Sonic Slash 13 (Lv 58)]%%
[admin_add_skill 5 14|Double Sonic Slash 14 (Lv 58)]%%
[admin_add_skill 5 15|Double Sonic Slash 15 (Lv 58)]%%
[admin_add_skill 5 16|Double Sonic Slash 16 (Lv 60)]%%
[admin_add_skill 5 17|Double Sonic Slash 17 (Lv 60)]%%
[admin_add_skill 5 18|Double Sonic Slash 18 (Lv 62)]%%
[admin_add_skill 5 19|Double Sonic Slash 19 (Lv 62)]%%
[admin_add_skill 5 20|Double Sonic Slash 20 (Lv 64)]%%
[admin_add_skill 5 21|Double Sonic Slash 21 (Lv 64)]%%
[admin_add_skill 5 22|Double Sonic Slash 22 (Lv 66)]%%
[admin_add_skill 5 23|Double Sonic Slash 23 (Lv 66)]%%
[admin_add_skill 5 24|Double Sonic Slash 24 (Lv 68)]%%
[admin_add_skill 5 25|Double Sonic Slash 25 (Lv 68)]%%
[admin_add_skill 5 26|Double Sonic Slash 26 (Lv 70)]%%
[admin_add_skill 5 27|Double Sonic Slash 27 (Lv 70)]%%
[admin_add_skill 5 28|Double Sonic Slash 28 (Lv 72)]%%
[admin_add_skill 5 29|Double Sonic Slash 29 (Lv 72)]%%
[admin_add_skill 5 30|Double Sonic Slash 30 (Lv 74)]%%
[admin_add_skill 5 31|Double Sonic Slash 31 (Lv 74)]%%
[admin_add_skill 6 1|Sonic Blaster 1 (Lv 40)]%%
[admin_add_skill 6 2|Sonic Blaster 2 (Lv 40)]%%
[admin_add_skill 6 3|Sonic Blaster 3 (Lv 40)]%%
[admin_add_skill 6 4|Sonic Blaster 4 (Lv 43)]%%
[admin_add_skill 6 5|Sonic Blaster 5 (Lv 43)]%%
[admin_add_skill 6 6|Sonic Blaster 6 (Lv 43)]%%
[admin_add_skill 6 7|Sonic Blaster 7 (Lv 46)]%%
[admin_add_skill 6 8|Sonic Blaster 8 (Lv 46)]%%
[admin_add_skill 6 9|Sonic Blaster 9 (Lv 46)]%%
[admin_add_skill 6 10|Sonic Blaster 10 (Lv 49)]%%
<br>
<button value="Page2" action="bypass -h admin_skill_index Human/H_Gladiator2" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index Human/H_Gladiator3" width=40 height=15><br>
<button value="Page4" action="bypass -h admin_skill_index Human/H_Gladiator4" width=40 height=15><br>
<button value="Page5" action="bypass -h admin_skill_index Human/H_Gladiator5" width=40 height=15><br>
<button value="Page6" action="bypass -h admin_skill_index Human/H_Gladiator6" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
