<title>Choose skill to add</title>
<center>
[admin_add_skill 1069 36|Sleep 36 (Lv 68)]%%
[admin_add_skill 1069 37|Sleep 37 (Lv 70)]%%
[admin_add_skill 1069 38|Sleep 38 (Lv 70)]%%
[admin_add_skill 1069 39|Sleep 39 (Lv 72)]%%
[admin_add_skill 1069 40|Sleep 40 (Lv 72)]%%
[admin_add_skill 1069 41|Sleep 41 (Lv 74)]%%
[admin_add_skill 1069 42|Sleep 42 (Lv 74)]%%
[admin_add_skill 1073 2|Kiss of Eva 2 (Lv 52)]%%
[admin_add_skill 1078 3|Greater Concentration 3 (Lv 44)]%%
[admin_add_skill 1078 4|Greater Concentration 4 (Lv 52)]%%
[admin_add_skill 1078 5|Greater Concentration 5 (Lv 60)]%%
[admin_add_skill 1078 6|Greater Concentration 6 (Lv 68)]%%
[admin_add_skill 1087 2|Agility 2 (Lv 44)]%%
[admin_add_skill 1087 3|Agility 3 (Lv 52)]%%
[admin_add_skill 1206 6|Wind Shackle 6 (Lv 40)]%%
[admin_add_skill 1206 7|Wind Shackle 7 (Lv 44)]%%
[admin_add_skill 1206 8|Wind Shackle 8 (Lv 48)]%%
[admin_add_skill 1206 9|Wind Shackle 9 (Lv 52)]%%
[admin_add_skill 1206 10|Wind Shackle 10 (Lv 56)]%%
[admin_add_skill 1206 11|Wind Shackle 11 (Lv 58)]%%
[admin_add_skill 1206 12|Wind Shackle 12 (Lv 60)]%%
[admin_add_skill 1206 13|Wind Shackle 13 (Lv 62)]%%
[admin_add_skill 1206 14|Wind Shackle 14 (Lv 64)]%%
[admin_add_skill 1206 15|Wind Shackle 15 (Lv 66)]%%
[admin_add_skill 1206 16|Wind Shackle 16 (Lv 68)]%%
[admin_add_skill 1206 17|Wind Shackle 17 (Lv 70)]%%
[admin_add_skill 1206 18|Wind Shackle 18 (Lv 72)]%%
[admin_add_skill 1206 19|Wind Shackle 19 (Lv 74)]%%
[admin_add_skill 1217 1|Greater Heal 1 (Lv 48)]%%
[admin_add_skill 1217 2|Greater Heal 2 (Lv 48)]%%
[admin_add_skill 1217 3|Greater Heal 3 (Lv 48)]%%
[admin_add_skill 1217 4|Greater Heal 4 (Lv 52)]%%
[admin_add_skill 1217 5|Greater Heal 5 (Lv 52)]%%
[admin_add_skill 1217 6|Greater Heal 6 (Lv 52)]%%
[admin_add_skill 1217 7|Greater Heal 7 (Lv 56)]%%
[admin_add_skill 1217 8|Greater Heal 8 (Lv 56)]%%
[admin_add_skill 1217 9|Greater Heal 9 (Lv 56)]%%
[admin_add_skill 1217 10|Greater Heal 10 (Lv 58)]%%
[admin_add_skill 1217 11|Greater Heal 11 (Lv 58)]%%
[admin_add_skill 1217 12|Greater Heal 12 (Lv 58)]%%
[admin_add_skill 1217 13|Greater Heal 13 (Lv 60)]%%
[admin_add_skill 1217 14|Greater Heal 14 (Lv 60)]%%
[admin_add_skill 1217 15|Greater Heal 15 (Lv 60)]%%
[admin_add_skill 1217 16|Greater Heal 16 (Lv 62)]%%
[admin_add_skill 1217 17|Greater Heal 17 (Lv 62)]%%
[admin_add_skill 1217 18|Greater Heal 18 (Lv 64)]%%
[admin_add_skill 1217 19|Greater Heal 19 (Lv 64)]%%
[admin_add_skill 1217 20|Greater Heal 20 (Lv 66)]%%
[admin_add_skill 1217 21|Greater Heal 21 (Lv 66)]%%
[admin_add_skill 1217 22|Greater Heal 22 (Lv 68)]%%
[admin_add_skill 1217 23|Greater Heal 23 (Lv 68)]%%
[admin_add_skill 1217 24|Greater Heal 24 (Lv 70)]%%
[admin_add_skill 1217 25|Greater Heal 25 (Lv 70)]%%
[admin_add_skill 1217 26|Greater Heal 26 (Lv 72)]%%
[admin_add_skill 1217 27|Greater Heal 27 (Lv 72)]%%
[admin_add_skill 1217 28|Greater Heal 28 (Lv 74)]%%
[admin_add_skill 1217 29|Greater Heal 29 (Lv 74)]%%
[admin_add_skill 1243 1|Bless Shield 1 (Lv 40)]%%
[admin_add_skill 1243 2|Bless Shield 2 (Lv 48)]%%
[admin_add_skill 1243 3|Bless Shield 3 (Lv 56)]%%
[admin_add_skill 1243 4|Bless Shield 4 (Lv 62)]%%
[admin_add_skill 1243 5|Bless Shield 5 (Lv 66)]%%
[admin_add_skill 1243 6|Bless Shield 6 (Lv 70)]%%
[admin_add_skill 1255 1|Party Recall 1 (Lv 48)]%%
[admin_add_skill 1255 2|Party Recall 2 (Lv 56)]%%
[admin_add_skill 1257 2|Decrease Weight 2 (Lv 44)]%%
[admin_add_skill 1257 3|Decrease Weight 3 (Lv 52)]%%
[admin_add_skill 1259 1|Resist Shock 1 (Lv 40)]%%
[admin_add_skill 1259 2|Resist Shock 2 (Lv 52)]%%
[admin_add_skill 1259 3|Resist Shock 3 (Lv 64)]%%
[admin_add_skill 1259 4|Resist Shock 4 (Lv 72)]%%
[admin_add_skill 1273 1|Serenade of Eva 1 (Lv 44)]%%
[admin_add_skill 1273 2|Serenade of Eva 2 (Lv 48)]%%
[admin_add_skill 1273 3|Serenade of Eva 3 (Lv 52)]%%
[admin_add_skill 1273 4|Serenade of Eva 4 (Lv 56)]%%
[admin_add_skill 1273 5|Serenade of Eva 5 (Lv 58)]%%
[admin_add_skill 1273 6|Serenade of Eva 6 (Lv 60)]%%
[admin_add_skill 1273 7|Serenade of Eva 7 (Lv 62)]%%
[admin_add_skill 1273 8|Serenade of Eva 8 (Lv 64)]%%
[admin_add_skill 1273 9|Serenade of Eva 9 (Lv 66)]%%
[admin_add_skill 1273 10|Serenade of Eva 10 (Lv 68)]%%
[admin_add_skill 1273 11|Serenade of Eva 11 (Lv 70)]%%
[admin_add_skill 1273 12|Serenade of Eva 12 (Lv 72)]%%
[admin_add_skill 1273 13|Serenade of Eva 13 (Lv 74)]%%
[admin_add_skill 1303 1|Wild Magic 1 (Lv 62)]%%
[admin_add_skill 1303 2|Wild Magic 2 (Lv 70)]%%
[admin_add_skill 1304 1|Advanced Block 1 (Lv 58)]%%
[admin_add_skill 1304 2|Advanced Block 2 (Lv 66)]%%
[admin_add_skill 1304 3|Advanced Block 3 (Lv 72)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index Elf/E_Elder" width=40 height=15><br>
<button value="Page2" action="bypass -h admin_skill_index Elf/E_Elder2" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index Elf/E_Elder3" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
