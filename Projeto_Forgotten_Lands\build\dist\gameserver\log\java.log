[28.06.25 00:00:01:165]  INFO instancemanager.SpawnManager: SpawnManager: spawned 443 npc; spawns: 121; group: DAY
[28.06.25 00:00:11:904]  INFO olympiad.CompEndTask: Olympiad System: Olympiad Game Ended
[28.06.25 00:00:11:905]  INFO olympiad.Olympiad: Olympiad System: Competition Period Starts in 0 days, 18 hours and 0 mins.
[28.06.25 00:00:11:905]  INFO olympiad.Olympiad: Olympiad System: Event starts/started: Sat Jun 28 18:00:11 BRT 2025
[28.06.25 00:01:59:107]  INFO olympiad.OlympiadEndTask: Olympiad System: Starting Validation period. Time to end validation:719
[28.06.25 00:06:00:812]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, before:0 toRemove:0 after:0 to Change:0
[28.06.25 00:06:00:813]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, Before: 0 Final:0
[28.06.25 00:06:00:814]  INFO fightclubmanager.FightClubEventManager: Last Hero: Removing room because it doesnt have enough players
[28.06.25 00:18:12:774]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_dead_npc
[28.06.25 00:18:12:777]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_aden
[28.06.25 00:18:12:778]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: bloodaltar_boss_darkelf
[28.06.25 00:18:12:781]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_dion
[28.06.25 00:18:12:783]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_dwarw
[28.06.25 00:18:12:785]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_giran
[28.06.25 00:18:12:787]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_gludin
[28.06.25 00:18:12:789]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_gludio
[28.06.25 00:18:12:790]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_goddart
[28.06.25 00:18:12:792]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_heine
[28.06.25 00:18:12:793]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: bloodaltar_boss_orc
[28.06.25 00:18:12:794]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_oren
[28.06.25 00:18:12:795]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: bloodaltar_boss_schutgart
[28.06.25 00:48:05:354]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[28.06.25 00:48:12:794]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_alive_npc
[28.06.25 00:50:05:032]  INFO threading.RunnableImpl: FourSepulchersManager: Entry time: Sat Jun 28 00:55:05 BRT 2025
[28.06.25 01:06:00:810]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, before:0 toRemove:0 after:0 to Change:0
[28.06.25 01:06:00:810]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, Before: 0 Final:0
[28.06.25 01:06:00:811]  INFO fightclubmanager.FightClubEventManager: Team VS Team: Removing room because it doesnt have enough players
[28.06.25 01:28:27:873]  INFO gameserver.GameServer: ============================================================================
[28.06.25 01:28:27:874]  INFO gameserver.GameServer: Nome: .................................................................... GAME SERVER
[28.06.25 01:28:27:874]  INFO gameserver.GameServer: Project Revision: ........................................................ LII [MultVerso]
[28.06.25 01:28:27:874]  INFO gameserver.GameServer: Update: .................................................................. High Five: Part 5
[28.06.25 01:28:27:874]  INFO gameserver.GameServer: Numero da Revision:....................................................... 2.0
[28.06.25 01:28:27:874]  INFO gameserver.GameServer: Build Revision: .......................................................... MultVerso
[28.06.25 01:28:27:875]  INFO gameserver.GameServer: Build date: .............................................................. 2025.06.28 01:25
[28.06.25 01:28:27:875]  INFO gameserver.GameServer: Compiler version: ........................................................ 17.0.15+9-LTS-241 (Oracle Corporation)
[28.06.25 01:28:27:875]  INFO gameserver.GameServer: ============================================================================
[28.06.25 01:28:27:917]  INFO gameserver.Config: Loaded 6 trade words.
[28.06.25 01:28:27:927]  INFO gameserver.Config: Abuse: Loaded 15 abuse words.
[28.06.25 01:28:27:981]  INFO gameserver.Config: Loaded 313 Configs!
[28.06.25 01:28:27:988]  INFO gameserver.GameServer: IP EXTERNO =============================127.0.0.1
[28.06.25 01:28:27:989]  INFO gameserver.GameServer: IP INTERNO =============================127.0.0.1
[28.06.25 01:28:27:989]  INFO gameserver.GameServer: Maximo Online IP ====================== 3000
[28.06.25 01:28:27:990]  INFO gameserver.GameServer: Hora do Restart  =======================45 5 * * *
[28.06.25 01:28:28:143]  INFO gameserver.GameServer: -----------------------------------------=[ Loading Protection Configuration ]
[28.06.25 01:28:28:147]  INFO idfactory.IdFactory: IdFactory: Clear characters online status.
[28.06.25 01:28:28:179]  INFO idfactory.IdFactory: IdFactory: Cleaned 0 elements from database in 0sec.
[28.06.25 01:28:28:188]  INFO idfactory.IdFactory: IdFactory: Extracted 2 used id's from characters
[28.06.25 01:28:28:188]  INFO idfactory.IdFactory: IdFactory: Extracted 46 used id's from items
[28.06.25 01:28:28:189]  INFO idfactory.IdFactory: IdFactory: Extracted total 48 used id's.
[28.06.25 01:28:28:189]  INFO idfactory.BitSetIDFactory: IdFactory: 102912 id's available.
[28.06.25 01:28:28:283]  INFO gameserver.GameServer: ===============[Loading Scripts]==================
[28.06.25 01:28:28:284]  INFO scripts.Scripts: Scripts: Loading...
[28.06.25 01:28:28:284]  INFO scripts.Scripts: Loading Server Scripts
[28.06.25 01:28:28:580]  INFO scripts.Scripts: Scripts: Loaded 1288 classes.
[28.06.25 01:28:28:587]  INFO vote.VoteMain: Loaded: Vote System - Individual Reward.
[28.06.25 01:28:28:588]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Twitch Manager ]
[28.06.25 01:28:28:612]  INFO gameserver.GameServer: ----------------------------------------------------------=[ AntiFeedManager ]
[28.06.25 01:28:28:612]  INFO gameserver.GameServer: -------------------------------------------------------------=[ QuestManager ]
[28.06.25 01:28:28:613]  INFO gameserver.GameServer: QuestManager : loaded 0 quest's
[28.06.25 01:28:28:623]  INFO tables.FakePlayersTable: FakePlayersTable: Loaded 32002 Fake Players.
[28.06.25 01:28:28:630]  INFO utils.Strings: Loaded 418 translit entries.
[28.06.25 01:28:28:632]  INFO gameserver.GameTimeController: GameTimeController: initialized. Current time is 14:50 in the day.
[28.06.25 01:28:28:633]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Lineage World ]
[28.06.25 01:28:28:637]  INFO model.World: L2World: Creating regions: [256][272][32].
[28.06.25 01:28:28:637]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:28:28:639]  INFO htm.HtmCache: HtmCache: disabled.
[28.06.25 01:28:28:645]  INFO data.StringHolder: StringHolder: load strings: 1066 for lang: ENGLISH
[28.06.25 01:28:28:645]  INFO data.StringHolder: StringHolder: load strings: 876 for lang: RUSSIAN
[28.06.25 01:28:29:941]  INFO skills.SkillsEngine: SkillsEngine: Loaded 66051 skill templates from XML files. Max id: 91000, max level: 259
[28.06.25 01:28:30:541]  INFO holder.OptionDataHolder: OptionDataHolder: loaded 24975 option data(s) count.
[28.06.25 01:28:31:184]  INFO holder.ItemHolder: ItemHolder: loaded 19240 item(s) count.
[28.06.25 01:28:31:190]  INFO holder.FakePlayerNpcsHolder: FakePlayerNpcsHolder: loaded 8 fake player npcs(s) count.
[28.06.25 01:28:32:198]  INFO holder.NpcHolder: NpcHolder: loaded 10519 npc(s) count.
[28.06.25 01:28:32:204]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[28.06.25 01:28:32:217]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[28.06.25 01:28:32:243]  INFO holder.ExchangeItemHolder: ExchangeItemHolder: loaded 602 exchange item(s) count.
[28.06.25 01:28:32:247]  INFO holder.StaticObjectHolder: StaticObjectHolder: loaded 50 static object(s) count.
[28.06.25 01:28:32:345]  INFO holder.DoorHolder: DoorHolder: loaded 1155 door(s) count.
[28.06.25 01:28:32:397]  INFO holder.ZoneHolder: ZoneHolder: loaded 1260 zone(s) count.
[28.06.25 01:28:32:715]  INFO holder.SpawnHolder: SpawnHolder: loaded 28949 spawn(s) count.
[28.06.25 01:28:32:802]  INFO holder.InstantZoneHolder: InstantZoneHolder: loaded 94 instant zone(s) count.
[28.06.25 01:28:33:097]  INFO holder.AirshipDockHolder: AirshipDockHolder: loaded 4 airship dock(s) count.
[28.06.25 01:28:33:144]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 57215 normal learns for 103 classes.
[28.06.25 01:28:33:145]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 79 transfer learns for 3 classes.
[28.06.25 01:28:33:145]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 120 transformation learns for 6 races.
[28.06.25 01:28:33:145]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 662 fishing learns for 6 races.
[28.06.25 01:28:33:145]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 72 certification learns.
[28.06.25 01:28:33:146]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 3 collection learns.
[28.06.25 01:28:33:146]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 44 pledge learns.
[28.06.25 01:28:33:146]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 18 sub unit learns.
[28.06.25 01:28:33:153]  INFO holder.CharTemplateHolder: CharTemplateHolder: loaded 206 char template(s) count.
[28.06.25 01:28:33:245]  INFO holder.ResidenceHolder: ResidenceHolder: total size: 83
[28.06.25 01:28:33:246]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 44 clanhall(s).
[28.06.25 01:28:33:246]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 castle(s).
[28.06.25 01:28:33:246]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 21 fortress(s).
[28.06.25 01:28:33:247]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 dominion(s).
[28.06.25 01:28:33:302]  INFO instancemanager.RaidBossSpawnManager: RaidBossSpawnManager: Loaded 194 Statuses
[28.06.25 01:28:33:317]  INFO entity.SevenSigns: SevenSigns: Currently in the Competition (Quest Event) period!
[28.06.25 01:28:33:317]  INFO entity.SevenSigns: SevenSigns: The Seal of Avarice remains unclaimed.
[28.06.25 01:28:33:318]  INFO entity.SevenSigns: SevenSigns: The Seal of Gnosis remains unclaimed.
[28.06.25 01:28:33:318]  INFO entity.SevenSigns: SevenSigns: The Seal of Strife remains unclaimed.
[28.06.25 01:28:33:318]  INFO entity.SevenSigns: SevenSigns: The Competition this week, if the trend continue, will end with a tie.
[28.06.25 01:28:33:319]  INFO entity.SevenSigns: SevenSigns: Next period begins in 2 days, 16 hours and 31 mins.
[28.06.25 01:28:33:430]  INFO holder.EventHolder: EventHolder: loaded 105 event(s) count.
[28.06.25 01:28:33:443]  INFO holder.FightClubMapHolder: FightClubMapHolder: loaded 42 fight club map(s) count.
[28.06.25 01:28:33:447]  INFO holder.CubicHolder: CubicHolder: loaded 145 cubic(s) count.
[28.06.25 01:28:33:485]  INFO holder.BuyListHolder: TradeController: Loaded 4 file(s).
[28.06.25 01:28:33:485]  INFO holder.BuyListHolder: TradeController: Loaded 25014 Items.
[28.06.25 01:28:33:486]  INFO holder.BuyListHolder: TradeController: Loaded 726 Buylists.
[28.06.25 01:28:33:500]  INFO holder.RecipeHolder: RecipeController: Loaded 998 Recipes.
[28.06.25 01:28:33:848]  INFO holder.ProductHolder: ProductItemTable: Loaded 0 product item on sale.
[28.06.25 01:28:33:858]  INFO holder.HennaHolder: HennaHolder: loaded 180 henna(s) count.
[28.06.25 01:28:33:862]  INFO holder.EnchantItemHolder: EnchantItemHolder: load 4 enchant scroll(s).
[28.06.25 01:28:33:865]  INFO holder.SoulCrystalHolder: SoulCrystalHolder: loaded 54 soul crystal(s) count.
[28.06.25 01:28:33:869]  INFO holder.ArmorSetsHolder: ArmorSetsHolder: loaded 199 armor sets(s) count.
[28.06.25 01:28:33:875]  INFO holder.PetitionGroupHolder: PetitionGroupHolder: loaded 3 petition group(s) count.
[28.06.25 01:28:33:878]  INFO holder.DressArmorHolder: DressArmorHolder: loaded 57 dress armor(s) count.
[28.06.25 01:28:33:882]  INFO holder.DressCloakHolder: DressCloakHolder: loaded 7 dress cloak(s) count.
[28.06.25 01:28:33:885]  INFO holder.DressShieldHolder: DressShieldHolder: loaded 10 dress shield(s) count.
[28.06.25 01:28:33:889]  INFO holder.DressWeaponHolder: DressWeaponHolder: loaded 401 dress weapon(s) count.
[28.06.25 01:28:33:931]  INFO holder.AugmentationDataHolder: AugmentationDataHolder: loaded 1022 augmentation data(s) count.
[28.06.25 01:28:33:934]  INFO holder.PremiumHolder: PremiumHolder: loaded 5 premium(s) count.
[28.06.25 01:28:33:937]  INFO holder.FoundationHolder: FoundationHolder: loaded 268 foundation(s) count.
[28.06.25 01:28:33:941]  INFO holder.DonationHolder: DonationHolder: loaded 192 donation(s) count.
[28.06.25 01:28:33:944]  INFO holder.TournamentMapHolder: TournamentMapHolder: loaded 3 tournament map(s) count.
[28.06.25 01:28:33:947]  INFO holder.FacebookCommentsHolder: FacebookCommentsHolder: loaded 2 facebook comments(s) count.
[28.06.25 01:28:33:947]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Banned HWIDS ]
[28.06.25 01:28:33:949]  INFO security.HWIDBan: black list (Hwid) loaded size: 0
[28.06.25 01:28:33:949]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Clan Crests ]
[28.06.25 01:28:33:950]  INFO cache.CrestCache: CrestCache: Loaded 0 crests
[28.06.25 01:28:33:951]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Loading Images ]
[28.06.25 01:28:33:952]  INFO cache.ImagesCache: ImagesCache: Loading images...
[28.06.25 01:28:44:939]  INFO cache.ImagesCache: ImagesCache: Loaded 8734 images
[28.06.25 01:28:44:939]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:28:44:944]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Fish Table ]
[28.06.25 01:28:44:947]  INFO tables.FishTable: FishTable: Loaded 270 fishes.
[28.06.25 01:28:44:948]  INFO tables.FishTable: FishTable: Loaded 919 fish rewards.
[28.06.25 01:28:44:949]  INFO gameserver.GameServer: -------------------------------------------------------------------=[ Skills ]
[28.06.25 01:28:44:949]  INFO tables.SkillTreeTable: SkillTreeTable: Loaded 526 enchanted skills.
[28.06.25 01:28:44:979]  INFO datatables.EnchantNamesTable: EnchantNamesTable: Loaded: 1319 skill enchant names
[28.06.25 01:28:44:979]  INFO gameserver.GameServer: --------------------------------------------------------=[ Augmentation Data ]
[28.06.25 01:28:44:982]  INFO tables.AugmentationData: Initializing AugmentationData.
[28.06.25 01:28:44:996]  INFO tables.AugmentationData: AugmentationData: 10 bad skill(s) were skipped.
[28.06.25 01:28:45:010]  INFO tables.AugmentationData: AugmentationData: Loaded: 52 augmentation stats.
[28.06.25 01:28:45:010]  INFO tables.AugmentationData: AugmentationData: Loaded: 24 accessory augmentation stats.
[28.06.25 01:28:45:014]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 0
[28.06.25 01:28:45:014]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 1
[28.06.25 01:28:45:015]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 2
[28.06.25 01:28:45:017]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 3
[28.06.25 01:28:45:017]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 4
[28.06.25 01:28:45:018]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 5
[28.06.25 01:28:45:018]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 6
[28.06.25 01:28:45:018]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 7
[28.06.25 01:28:45:019]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 8
[28.06.25 01:28:45:019]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 9
[28.06.25 01:28:45:022]  INFO tables.EnchantHPBonusTable: EnchantHPBonusTable: Loaded bonuses for 6 grades.
[28.06.25 01:28:45:022]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Level Up Table ]
[28.06.25 01:28:45:024]  INFO tables.LevelUpTable: LevelUpData: Loaded 103 Character Level Up Templates.
[28.06.25 01:28:45:027]  INFO tables.PetSkillsTable: PetSkillsTable: Loaded 2558 skills.
[28.06.25 01:28:45:027]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Item Logs ]
[28.06.25 01:28:45:028]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Auctioneer ]
[28.06.25 01:28:45:029]  INFO itemauction.ItemAuctionManager: Initializing ItemAuctionManager
[28.06.25 01:28:45:039]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 1 auction(s) for instance 32320.
[28.06.25 01:28:45:043]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 1 on 2025.07.01 17:00:00 for instance 32320
[28.06.25 01:28:45:045]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 2 auction(s) for instance 32321.
[28.06.25 01:28:45:046]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 4 on 2025.07.03 17:00:00 for instance 32321
[28.06.25 01:28:45:048]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 1 auction(s) for instance 32322.
[28.06.25 01:28:45:048]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 3 on 2025.06.28 17:00:00 for instance 32322
[28.06.25 01:28:45:048]  INFO itemauction.ItemAuctionManager: ItemAuctionManager: Loaded 3 instance(s).
[28.06.25 01:28:45:049]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Masterio Pack ]
[28.06.25 01:28:45:083]  INFO gameserver.GameServer: ------------------------------------------------------=[ Merge System Loaded ]
[28.06.25 01:28:45:104]  INFO bosses.AntharasManager: AntharasManager: State of Antharas is NOTSPAWN.
[28.06.25 01:28:45:105]  INFO bosses.AntharasManager: AntharasManager: Next spawn date of Antharas is 21:00 31.12.1969.
[28.06.25 01:28:45:106]  INFO bosses.BaiumManager: BaiumManager: State of Baium is NOTSPAWN.
[28.06.25 01:28:45:110]  INFO bosses.BaiumManager: BaiumManager: Next spawn date: 21:00 31.12.1969
[28.06.25 01:28:45:121]  INFO bosses.BelethManager: Beleth Manager: Loaded successfully
[28.06.25 01:28:45:123]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Loaded 20 Mysterious-Box spawns.
[28.06.25 01:28:45:128]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 physical monsters spawns.
[28.06.25 01:28:45:132]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 magical monsters spawns.
[28.06.25 01:28:45:133]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 92 Church of duke monsters spawns.
[28.06.25 01:28:45:135]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 68 Emperor's grave NPC spawns.
[28.06.25 01:28:45:136]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Conquerors' Sepulcher Manager
[28.06.25 01:28:45:136]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Emperors' Sepulcher Manager
[28.06.25 01:28:45:137]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Great Sages' Sepulcher Manager
[28.06.25 01:28:45:137]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Judges' Sepulcher Manager
[28.06.25 01:28:45:138]  INFO bosses.FourSepulchersManager: FourSepulchersManager: Beginning in Attack time
[28.06.25 01:28:45:139]  INFO bosses.SailrenManager: SailrenManager: State of Sailren is NOTSPAWN.
[28.06.25 01:28:45:140]  INFO bosses.SailrenManager: SailrenManager: Next spawn date of Sailren is 21:00 31.12.1969.
[28.06.25 01:28:45:141]  INFO bosses.ValakasManager: ValakasManager: State of Valakas is NOTSPAWN.
[28.06.25 01:28:45:141]  INFO bosses.ValakasManager: ValakasManager: Next spawn date of Valakas is 21:00 31.12.1969.
[28.06.25 01:28:45:141]  INFO threading.RunnableImpl: Sat Jun 28 01:28:45 BRT 2025 Atk announce scheduled to 30.0 minute of this hour.
[28.06.25 01:28:45:233]  INFO tables.PetDataTable: PetDataTable: Loaded 3870 pets.
[28.06.25 01:28:45:236]  INFO model.Manor: ManorManager: Loaded 270 seeds
[28.06.25 01:28:45:369]  INFO scriptconfig.ScriptConfig: Loaded Service: ScripsConfig
[28.06.25 01:28:45:370]  INFO community.CharacterServicesCommunity: === CharacterServicesCommunity onLoad called ===
[28.06.25 01:28:45:371]  INFO community.CharacterServicesCommunity: COMMUNITYBOARD_ENABLED: true
[28.06.25 01:28:45:371]  INFO community.CharacterServicesCommunity: EnableMergeCommunity: false
[28.06.25 01:28:45:371]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Character Services Community loaded.
[28.06.25 01:28:45:446]  INFO dailyquests.AbstractDPScript: DailyQuestHandler: Loaded DailyQuests data
[28.06.25 01:28:45:446]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Handler registered successfully.
[28.06.25 01:28:45:456]  INFO model.Player: Loaded Service: SellPcService
[28.06.25 01:28:45:459]  INFO community.CareerManager: CommunityBoard: Manage Career service loaded.
[28.06.25 01:28:45:462]  INFO community.CommunityAuctionHouse: CommunityBoard: Auction System Service loaded.
[28.06.25 01:28:45:468]  INFO community.CommunityAugment: CommunityBoard: Augmentation Manager loaded with 188 lifestone skills.
[28.06.25 01:28:45:469]  INFO community.CommunityBoard: CommunityBoard: service loaded.
[28.06.25 01:28:45:470]  INFO community.CommunityBoardAcademy: CommunityBoard: Academy service loaded.
[28.06.25 01:28:45:470]  INFO community.CommunityBosses: CommunityBoard: Bosses loaded.
[28.06.25 01:28:45:473]  INFO community.CommunityClan: CommunityBoard: Clan service loaded.
[28.06.25 01:28:45:473]  INFO community.CommunityDropCalculator: CommunityBoard: Drop Calculator service loaded.
[28.06.25 01:28:45:473]  INFO community.CommunityEvents: CommunityBoard: Community Events service loaded.
[28.06.25 01:28:45:474]  INFO community.CommunityFacebook: CommunityBoard: Facebook Rewards page loaded.
[28.06.25 01:28:45:475]  INFO community.CommunityNpcs: CommunityBoard: Npcs loaded.
[28.06.25 01:28:45:476]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[28.06.25 01:28:45:482]  INFO community.CommunityTournament: CommunityBoard: Tournament loaded.
[28.06.25 01:28:45:482]  INFO community.CommunityWarehouse: CommunityBoard: Warehouse loaded.
[28.06.25 01:28:45:483]  INFO community.Forge: CommunityBoard: Forge loaded.
[28.06.25 01:28:45:485]  INFO community.ServicesCommunity: ServicesCommunity: Services Community service loaded.
[28.06.25 01:28:45:491]  INFO gameserver.GameServer: ===============[Spawn Manager]==================
[28.06.25 01:28:45:590]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1000 npc for group: NONE
[28.06.25 01:28:45:780]  INFO instancemanager.SpawnManager: SpawnManager: spawned 5000 npc for group: NONE
[28.06.25 01:28:45:815]  INFO naia.NaiaTowerManager: Naia Tower Manager: Loaded 12 rooms
[28.06.25 01:28:45:912]  INFO instancemanager.SpawnManager: SpawnManager: spawned 7000 npc for group: NONE
[28.06.25 01:28:46:313]  INFO instancemanager.SpawnManager: SpawnManager: spawned 12000 npc for group: NONE
[28.06.25 01:28:46:414]  INFO instancemanager.SpawnManager: SpawnManager: spawned 13000 npc for group: NONE
[28.06.25 01:28:47:520]  INFO instancemanager.SpawnManager: SpawnManager: spawned 21000 npc for group: NONE
[28.06.25 01:28:47:662]  INFO instancemanager.SpawnManager: SpawnManager: spawned 22000 npc for group: NONE
[28.06.25 01:28:48:196]  INFO instancemanager.SpawnManager: SpawnManager: spawned 25000 npc for group: NONE
[28.06.25 01:28:48:398]  INFO instancemanager.SpawnManager: SpawnManager: spawned 26000 npc for group: NONE
[28.06.25 01:28:48:815]  INFO instancemanager.SpawnManager: SpawnManager: spawned 28000 npc for group: NONE
[28.06.25 01:28:49:885]  INFO instancemanager.SpawnManager: SpawnManager: spawned 32000 npc for group: NONE
[28.06.25 01:28:50:791]  INFO instancemanager.SpawnManager: SpawnManager: spawned 35000 npc for group: NONE
[28.06.25 01:28:51:097]  INFO instancemanager.SpawnManager: SpawnManager: spawned 36000 npc for group: NONE
[28.06.25 01:28:51:495]  INFO instancemanager.SpawnManager: SpawnManager: spawned 37000 npc for group: NONE
[28.06.25 01:28:51:995]  INFO instancemanager.SpawnManager: SpawnManager: spawned 38373 npc; spawns: 17087; group: NONE
[28.06.25 01:28:51:995]  INFO gameserver.GameServer: --------------------------------------------------------------------=[ Boats ]
[28.06.25 01:28:51:996]  INFO data.BoatHolder: BoatHolder: loaded 2 boat(s) count.
[28.06.25 01:28:51:999]  INFO data.BoatHolder: BoatHolder: Spawning: RunePrimeval_Vehicle
[28.06.25 01:28:51:999]  INFO data.BoatHolder: BoatHolder: Spawning: AdenGracia_Airship
[28.06.25 01:28:52:000]  INFO holder.StaticObjectHolder: StaticObjectHolder: spawned: 50 static object(s).
[28.06.25 01:28:52:000]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Dimensional Rift ]
[28.06.25 01:28:52:006]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 13 room types with 112 rooms.
[28.06.25 01:28:52:006]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 300 DimensionalRift spawns, 0 errors.
[28.06.25 01:28:52:011]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Seven Signs ]
[28.06.25 01:28:52:023]  INFO instancemanager.AutoSpawnManager: AutoSpawnHandler: Loaded 50 handlers in total.
[28.06.25 01:28:52:023]  INFO gameserver.GameServer: ===================================================================
[28.06.25 01:28:52:024]  INFO gameserver.GameServer: ===================[Loading Olympiad System]=======================
[28.06.25 01:28:52:029]  INFO olympiad.Olympiad: Olympiad System: Loading Olympiad System....
[28.06.25 01:28:52:030]  INFO olympiad.Olympiad: Olympiad System: Currently in Validation Period
[28.06.25 01:28:52:030]  INFO olympiad.Olympiad: Olympiad System: Period Ends....
[28.06.25 01:28:52:031]  INFO olympiad.Olympiad: Olympiad System: In 0 days, 10 hours and 33 mins.
[28.06.25 01:28:52:031]  INFO olympiad.Olympiad: Olympiad System: Loaded 0 Noblesses
[28.06.25 01:28:52:033]  INFO entity.Hero: Hero System: Loaded 0 Heroes.
[28.06.25 01:28:52:034]  INFO entity.Hero: Hero System: Loaded 0 all time Heroes.
[28.06.25 01:28:52:034]  INFO gameserver.GameServer: ===================[Olympiad System Loaded]=======================
[28.06.25 01:28:52:034]  INFO gameserver.GameServer: ===================================================================
[28.06.25 01:28:52:036]  INFO instancemanager.PetitionManager: Initializing PetitionManager
[28.06.25 01:28:52:041]  INFO instancemanager.CursedWeaponsManager: CursedWeaponsManager: Loaded 2 cursed weapon(s).
[28.06.25 01:28:52:042]  INFO gameserver.GameServer: ------------------------------------------=[ Loaded Small Achievement System ]
[28.06.25 01:28:52:055]  INFO gameserver.GameServer: --------------------------------------------------------------=[ ItemHandler ]
[28.06.25 01:28:52:056]  INFO items.ItemHandler: ItemHandler: loaded 0 item handler(s) count.
[28.06.25 01:28:52:056]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Admin Commands ]
[28.06.25 01:28:52:056]  INFO admincommands.AdminCommandHandler: AdminCommandHandler: loaded 475 admin command handler(s) count.
[28.06.25 01:28:52:057]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Players Commands ]
[28.06.25 01:28:52:060]  INFO usercommands.UserCommandHandler: UserCommandHandler: loaded 17 user command handler(s) count.
[28.06.25 01:28:52:061]  INFO voicecommands.VoicedCommandHandler: VoicedCommandHandler: loaded 102 voiced command handler(s) count.
[28.06.25 01:28:52:064]  INFO gameserver.GameServer: ======================[Tournament By Kara`]==========================
[28.06.25 01:28:52:067]  INFO tournament.TournamentHolder: [TournamentHolder] Initialized 5 tournament holders:
[28.06.25 01:28:52:067]  INFO tournament.TournamentHolder: 1 vs 1
[28.06.25 01:28:52:067]  INFO tournament.TournamentHolder: 2 vs 2
[28.06.25 01:28:52:068]  INFO tournament.TournamentHolder: 4 vs 4
[28.06.25 01:28:52:068]  INFO tournament.TournamentHolder: 5 vs 5
[28.06.25 01:28:52:068]  INFO tournament.TournamentHolder: 6 vs 6
[28.06.25 01:28:52:070]  INFO gameserver.GameServer: ======================[Loading Castels & Clan Halls]==========================
[28.06.25 01:28:52:116]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Gludio Castle time - 16:00 29.06.2025
[28.06.25 01:28:52:121]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Dion Castle time - 16:00 29.06.2025
[28.06.25 01:28:52:129]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Giran Castle time - 20:00 29.06.2025
[28.06.25 01:28:52:135]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Oren Castle time - 20:00 29.06.2025
[28.06.25 01:28:52:140]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Aden Castle time - 16:00 29.06.2025
[28.06.25 01:28:52:146]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Innadril Castle time - 20:00 29.06.2025
[28.06.25 01:28:52:153]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Goddard Castle time - 20:00 29.06.2025
[28.06.25 01:28:52:159]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Rune Castle time - 16:00 29.06.2025
[28.06.25 01:28:52:163]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Schuttgart Castle time - 16:00 29.06.2025
[28.06.25 01:28:52:166]  INFO impl.ClanHallNpcSiegeEvent: ClanHallNpcSiegeEvent: Fortress Of Resistance time - 18:00 30.06.2025
[28.06.25 01:28:52:168]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:169]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:171]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Topaz Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:172]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Ruby Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:174]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Crystal Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:176]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:177]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:178]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:179]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:181]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Atramental Barracks time - 15:00 02.07.2025
[28.06.25 01:28:52:182]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Scarlet Barracks time - 15:00 02.07.2025
[28.06.25 01:28:52:184]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Viridian Barracks time - 15:00 02.07.2025
[28.06.25 01:28:52:184]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Devastated Castle time - 18:00 28.06.2025
[28.06.25 01:28:52:185]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Bandit Stronghold time - 22:00 28.06.2025
[28.06.25 01:28:52:186]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:188]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:189]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:190]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[28.06.25 01:28:52:192]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Gold Manor time - 15:00 02.07.2025
[28.06.25 01:28:52:193]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:195]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:196]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:197]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:199]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:201]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[28.06.25 01:28:52:202]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:204]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:205]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:207]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:209]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Mont Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:210]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Astaire Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:211]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Aria Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:212]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Yiana Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:214]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Roien Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:216]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Luna Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:217]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Traban Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:219]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Eisen Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:220]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Heavy Metal Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:221]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Molten Ore Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:223]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Titan Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:224]  INFO impl.ClanHallMiniGameEvent: ClanHallMiniGameEvent: Rainbow Springs time - 22:00 28.06.2025
[28.06.25 01:28:52:225]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Wild Beast Reserve time - 21:00 28.06.2025
[28.06.25 01:28:52:225]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Fortress of Dead time - 18:00 28.06.2025
[28.06.25 01:28:52:227]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Gludio Dominion time - undefined
[28.06.25 01:28:52:227]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Dion Dominion time - undefined
[28.06.25 01:28:52:227]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Giran Dominion time - undefined
[28.06.25 01:28:52:228]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Oren Dominion time - undefined
[28.06.25 01:28:52:230]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Aden Dominion time - undefined
[28.06.25 01:28:52:230]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Innadril Dominion time - undefined
[28.06.25 01:28:52:231]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Goddard Dominion time - undefined
[28.06.25 01:28:52:232]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Rune Dominion time - undefined
[28.06.25 01:28:52:232]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Schuttgart Dominion time - undefined
[28.06.25 01:28:52:236]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Shanty Fortress time - undefined
[28.06.25 01:28:52:240]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Southern Fortress time - undefined
[28.06.25 01:28:52:245]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hive Fortress time - undefined
[28.06.25 01:28:52:249]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Valley Fortress time - undefined
[28.06.25 01:28:52:253]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Ivory Fortress time - undefined
[28.06.25 01:28:52:257]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Narsell Fortress time - undefined
[28.06.25 01:28:52:261]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Bayou Fortress time - undefined
[28.06.25 01:28:52:265]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: White Sands Fortress time - undefined
[28.06.25 01:28:52:270]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Borderland Fortress time - undefined
[28.06.25 01:28:52:273]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Swamp Fortress time - undefined
[28.06.25 01:28:52:278]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Archaic Fortress time - undefined
[28.06.25 01:28:52:283]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Floran Fortress time - undefined
[28.06.25 01:28:52:286]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Cloud Mountain Fortress time - undefined
[28.06.25 01:28:52:289]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Tanor Fortress time - undefined
[28.06.25 01:28:52:293]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Dragonspine Fortress time - undefined
[28.06.25 01:28:52:296]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Antharas Fortress time - undefined
[28.06.25 01:28:52:300]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Western Fortress time - undefined
[28.06.25 01:28:52:304]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hunter's Fortress time - undefined
[28.06.25 01:28:52:307]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Aaru Fortress time - undefined
[28.06.25 01:28:52:311]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Demon Fortress time - undefined
[28.06.25 01:28:52:314]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Monastic Fortress time - undefined
[28.06.25 01:28:52:316]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 70-75 Level time - 01:30 28.06.2025
[28.06.25 01:28:52:316]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 76-79 Level time - 01:30 28.06.2025
[28.06.25 01:28:52:316]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 80-85 Level time - 01:30 28.06.2025
[28.06.25 01:28:52:317]  INFO fightclub.LastManStandingEvent: LastManStandingEvent: Last Hero time - undefined
[28.06.25 01:28:52:317]  INFO fightclub.TeamVSTeamEvent: TeamVSTeamEvent: Team VS Team time - undefined
[28.06.25 01:28:52:317]  INFO fightclub.LuckyCreaturesEvent: LuckyCreaturesEvent: Lucky Creatures time - undefined
[28.06.25 01:28:52:317]  INFO fightclub.CaptureTheFlagEvent: CaptureTheFlagEvent: Capture The Flag time - undefined
[28.06.25 01:28:52:318]  INFO fightclub.FFATreasureHuntEvent: FFATreasureHuntEvent: Treasure Hunt time - undefined
[28.06.25 01:28:52:318]  INFO fightclub.KoreanStyleEvent: KoreanStyleEvent: Korean Style time - undefined
[28.06.25 01:28:52:318]  INFO fightclub.ProtectTheKingEvent: ProtectTheKingEvent: Protect The King time - undefined
[28.06.25 01:28:52:318]  INFO fightclub.FightForThroneEvent: FightForThroneEvent: Fight For Throne time - undefined
[28.06.25 01:28:52:319]  INFO fightclub.DeathMatchEvent: DeathMatchEvent: Death Match time - undefined
[28.06.25 01:28:52:319]  INFO fightclub.RussianRouletteEvent: RussianRouletteEvent: Russian Roulette time - undefined
[28.06.25 01:28:52:320]  INFO instancemanager.CastleManorManager: Manor System: Initializing...
[28.06.25 01:28:52:327]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:28:52:328]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Auto Cleaner ]
[28.06.25 01:28:52:328]  INFO gameserver.GameServer: IdFactory: Free ObjectID's remaining: 1879004760
[28.06.25 01:28:52:328]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:28:52:329]  INFO instancemanager.CoupleManager: Initializing CoupleManager
[28.06.25 01:28:52:329]  INFO instancemanager.CoupleManager: Loaded: 0 couples(s)
[28.06.25 01:28:52:331]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Hellbound ]
[28.06.25 01:28:52:339]  INFO instancemanager.HellboundManager: HellboundManager: Loaded 335 spawn entries.
[28.06.25 01:28:52:511]  INFO instancemanager.HellboundManager: HellboundManager: Spawned 623 mobs and NPCs according to the current Hellbound stage
[28.06.25 01:28:52:512]  INFO instancemanager.HellboundManager: Hellbound Manager: Loaded
[28.06.25 01:28:52:514]  INFO naia.NaiaTowerManager: Naia Core Manager: Loaded
[28.06.25 01:28:52:515]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:28:52:515]  INFO instancemanager.SoDManager: Seed of Destruction Manager: Loaded
[28.06.25 01:28:52:516]  INFO instancemanager.SoIManager: Seed of Infinity Manager: Loaded. Current stage is: 1
[28.06.25 01:28:52:518]  INFO instancemanager.SpawnManager: SpawnManager: spawned 4 npc; spawns: 4; group: soi_world_mouths
[28.06.25 01:28:52:519]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: soi_world_abyssgaze2
[28.06.25 01:28:52:519]  INFO instancemanager.BloodAltarManager: Blood Altar Manager: Initializing...
[28.06.25 01:28:52:538]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_alive_npc
[28.06.25 01:28:52:541]  INFO gameserver.GameServer: Preparing Drop Calculator
[28.06.25 01:28:52:606]  INFO gameserver.GameServer: ----------------------------------------------------------=[ Offline Buffers ]
[28.06.25 01:28:52:607]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loading offline buffers...
[28.06.25 01:28:52:608]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loaded: 0 offline buffer(s)
[28.06.25 01:28:52:619]  INFO achievements.Achievements: Achievement System: Loaded 4 achievement categories and 39 achievements.
[28.06.25 01:28:52:621]  INFO gameserver.GameServer: Emotions Loaded....
[28.06.25 01:28:52:621]  INFO gameserver.GameServer: --------------------------------------------------=[ DataBase Cleaner Loaded ]
[28.06.25 01:28:52:622]  INFO dao.CharacterDAO: Found 0 characters to delete!
[28.06.25 01:28:52:703]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Initializing
[28.06.25 01:28:52:704]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Load OK
[28.06.25 01:28:52:704]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Loaded 0 announce.
[28.06.25 01:28:52:706]  INFO gameserver.GameServer: ---------------------------------------------------=[ Loaded Facebook System ]
[28.06.25 01:28:52:709]  INFO gameserver.GameServer: -----------------------------------------------------=[ Loaded Stream System ]
[28.06.25 01:28:52:713]  INFO holder.ProxiesHolder: ProxiesHolder: loaded 20 proxies(s) count.
[28.06.25 01:28:52:714]  INFO gameserver.GameServer: ------------------------------------------------------=[ Loaded Proxy System ]
[28.06.25 01:28:55:249]  INFO donation.DonationReader: Donation Reader service started
[28.06.25 01:28:55:249]  INFO gameserver.GameServer: --------------------------------------------=[ Loaded AUTO - Donation System ]
[28.06.25 01:28:55:252]  INFO taskmanager.BackupTaskManager: Next Auto Backup in 5 H., 11 Min.
[28.06.25 01:28:55:260]  INFO votingengine.VotingSettings: VotingSettings: Loaded 3 messages, 4 drops!
[28.06.25 01:28:55:262]  INFO votingengine.VotingRewardAPI: VoteReward: Has been successfully loaded!
[28.06.25 01:28:55:264]  INFO model.PhantomPlayers: Loading phantom players...
[28.06.25 01:28:55:272]  INFO model.PhantomPlayers: Loaded 32002 possible phantom names.
[28.06.25 01:28:55:272]  INFO model.PhantomPlayers: Loaded 7172 possible phantom titles.
[28.06.25 01:28:55:273]  INFO model.PhantomPlayers: PhantomPlayers: Cached 0 players.
[28.06.25 01:28:55:273]  INFO model.PhantomPlayers: PhantomPlayers: Free names 32002.
[28.06.25 01:28:55:274]  INFO model.PhantomPlayers: Loaded 0 phantom players from database with a maximum of 3000 phantoms.
[28.06.25 01:28:55:274]  INFO model.PhantomPlayers: Scheduled spawner with 1 seconds delay.
[28.06.25 01:28:55:474]  INFO instancemanager.SpawnManager: SpawnManager: spawned 443 npc; spawns: 121; group: DAY
[28.06.25 01:28:55:581]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dawn_spawn
[28.06.25 01:28:55:855]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dusk_spawn
[28.06.25 01:28:55:855]  INFO gameserver.GameServer: Telnet server is currently disabled.
[28.06.25 01:28:55:857]  INFO gameserver.Shutdown: Scheduled server restart in 4h 16m 5s.
[28.06.25 01:28:55:858]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:28:55:858]  INFO gameserver.GameServer: >>>>>>>>>>>>>>> GameServer Started <<<<<<<<<<<<<<
[28.06.25 01:28:55:858]  INFO gameserver.GameServer: Maximum Numbers of Connected Players: 3000
[28.06.25 01:28:55:858]  INFO gameserver.GameServer: ====================================================================
[28.06.25 01:28:55:873]  INFO gameserver.GameServer: AllowedMemory: ........... 6291456 KB
[28.06.25 01:28:55:873]  INFO gameserver.GameServer: 
Allocated: ............... 4194304 KB (66.6667%)
[28.06.25 01:28:55:873]  INFO gameserver.GameServer: 
Non-Allocated: ........... 2097152 KB (33.3333%)
[28.06.25 01:28:55:873]  INFO gameserver.GameServer: 
AllocatedMemory: ......... 4194304 KB
[28.06.25 01:28:55:874]  INFO gameserver.GameServer: Used: .................... 1145449 KB (18.2064%)
[28.06.25 01:28:55:874]  INFO gameserver.GameServer: 
Unused (cached): ......... 3048854 KB (48.4602%)
[28.06.25 01:28:55:874]  INFO gameserver.GameServer: 
UseableMemory: ........... 5146006 KB (81.7936%)
[28.06.25 01:28:55:874]  INFO gameserver.GameServer: 

[28.06.25 01:28:55:874]  INFO gameserver.GameServer: ====================================================================
[28.06.25 01:28:55:877]  INFO loginservercon.AuthServerCommunication: Connecting to authserver on 127.0.0.1:9014
[28.06.25 01:28:55:889]  INFO lspackets.AuthResponse: Registered on authserver as 1 [Bartz]
[28.06.25 01:29:32:765]  INFO community.CharacterServicesCommunity: onBypassCommand called - bypass: _bbscharservices;changesex
[28.06.25 01:29:32:765]  INFO community.CharacterServicesCommunity: cmd: _bbscharservices, action: changesex
[28.06.25 01:29:42:831]  INFO community.CharacterServicesCommunity: onBypassCommand called - bypass: _bbscharservices;registeremail
[28.06.25 01:29:42:831]  INFO community.CharacterServicesCommunity: cmd: _bbscharservices, action: registeremail
[28.06.25 01:29:57:272]  INFO community.CharacterServicesCommunity: === onWriteCommand called ===
[28.06.25 01:29:57:272]  INFO community.CharacterServicesCommunity: Player: Dzu
[28.06.25 01:29:57:272]  INFO community.CharacterServicesCommunity: Bypass: '_bbscharservices;registeremail;submit'
[28.06.25 01:29:57:273]  INFO community.CharacterServicesCommunity: arg1: '0'
[28.06.25 01:29:57:273]  INFO community.CharacterServicesCommunity: arg2: '0'
[28.06.25 01:29:57:273]  INFO community.CharacterServicesCommunity: arg3: '<EMAIL>'
[28.06.25 01:29:57:273]  INFO community.CharacterServicesCommunity: arg4: '<EMAIL>'
[28.06.25 01:29:57:273]  INFO community.CharacterServicesCommunity: arg5: ''
[28.06.25 01:29:57:274]  INFO community.CharacterServicesCommunity: Processing email registration - email: <EMAIL>, confirmEmail: <EMAIL>
[28.06.25 01:29:57:276]  INFO utils.EmailVerification: Verification code stored for account: siniez, email: <EMAIL>
[28.06.25 01:29:57:277]  INFO community.CharacterServicesCommunity: About to send email - Config.MAIL_USER: '<EMAIL>', Config.MAIL_PASS: 'SET'
[28.06.25 01:29:57:277]  INFO utils.EmailVerification: Checking email configuration - MAIL_USER: '<EMAIL>', MAIL_PASS: 'SET'
[28.06.25 01:30:32:706]  INFO tasks.RestoreOfflineTraders: Restored 0 offline traders
[28.06.25 01:33:20:173]  INFO gameserver.GameServer: ============================================================================
[28.06.25 01:33:20:174]  INFO gameserver.GameServer: Nome: .................................................................... GAME SERVER
[28.06.25 01:33:20:175]  INFO gameserver.GameServer: Project Revision: ........................................................ LII [MultVerso]
[28.06.25 01:33:20:175]  INFO gameserver.GameServer: Update: .................................................................. High Five: Part 5
[28.06.25 01:33:20:175]  INFO gameserver.GameServer: Numero da Revision:....................................................... 2.0
[28.06.25 01:33:20:175]  INFO gameserver.GameServer: Build Revision: .......................................................... MultVerso
[28.06.25 01:33:20:175]  INFO gameserver.GameServer: Build date: .............................................................. 2025.06.28 01:32
[28.06.25 01:33:20:175]  INFO gameserver.GameServer: Compiler version: ........................................................ 17.0.15+9-LTS-241 (Oracle Corporation)
[28.06.25 01:33:20:175]  INFO gameserver.GameServer: ============================================================================
[28.06.25 01:33:20:215]  INFO gameserver.Config: Loaded 6 trade words.
[28.06.25 01:33:20:224]  INFO gameserver.Config: Abuse: Loaded 15 abuse words.
[28.06.25 01:33:20:282]  INFO gameserver.Config: Loaded 313 Configs!
[28.06.25 01:33:20:288]  INFO gameserver.GameServer: IP EXTERNO =============================127.0.0.1
[28.06.25 01:33:20:288]  INFO gameserver.GameServer: IP INTERNO =============================127.0.0.1
[28.06.25 01:33:20:289]  INFO gameserver.GameServer: Maximo Online IP ====================== 3000
[28.06.25 01:33:20:290]  INFO gameserver.GameServer: Hora do Restart  =======================45 5 * * *
[28.06.25 01:33:20:457]  INFO gameserver.GameServer: -----------------------------------------=[ Loading Protection Configuration ]
[28.06.25 01:33:20:461]  INFO idfactory.IdFactory: IdFactory: Clear characters online status.
[28.06.25 01:33:20:473]  INFO idfactory.IdFactory: IdFactory: Cleaned 0 elements from database in 0sec.
[28.06.25 01:33:20:482]  INFO idfactory.IdFactory: IdFactory: Extracted 2 used id's from characters
[28.06.25 01:33:20:483]  INFO idfactory.IdFactory: IdFactory: Extracted 46 used id's from items
[28.06.25 01:33:20:483]  INFO idfactory.IdFactory: IdFactory: Extracted total 48 used id's.
[28.06.25 01:33:20:484]  INFO idfactory.BitSetIDFactory: IdFactory: 102912 id's available.
[28.06.25 01:33:20:609]  INFO gameserver.GameServer: ===============[Loading Scripts]==================
[28.06.25 01:33:20:610]  INFO scripts.Scripts: Scripts: Loading...
[28.06.25 01:33:20:610]  INFO scripts.Scripts: Loading Server Scripts
[28.06.25 01:33:20:921]  INFO scripts.Scripts: Scripts: Loaded 1288 classes.
[28.06.25 01:33:20:929]  INFO vote.VoteMain: Loaded: Vote System - Individual Reward.
[28.06.25 01:33:20:930]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Twitch Manager ]
[28.06.25 01:33:20:955]  INFO gameserver.GameServer: ----------------------------------------------------------=[ AntiFeedManager ]
[28.06.25 01:33:20:956]  INFO gameserver.GameServer: -------------------------------------------------------------=[ QuestManager ]
[28.06.25 01:33:20:956]  INFO gameserver.GameServer: QuestManager : loaded 0 quest's
[28.06.25 01:33:20:968]  INFO tables.FakePlayersTable: FakePlayersTable: Loaded 32002 Fake Players.
[28.06.25 01:33:20:976]  INFO utils.Strings: Loaded 418 translit entries.
[28.06.25 01:33:20:978]  INFO gameserver.GameTimeController: GameTimeController: initialized. Current time is 15:20 in the day.
[28.06.25 01:33:20:979]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Lineage World ]
[28.06.25 01:33:20:982]  INFO model.World: L2World: Creating regions: [256][272][32].
[28.06.25 01:33:20:982]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:33:20:983]  INFO htm.HtmCache: HtmCache: disabled.
[28.06.25 01:33:20:991]  INFO data.StringHolder: StringHolder: load strings: 1066 for lang: ENGLISH
[28.06.25 01:33:20:991]  INFO data.StringHolder: StringHolder: load strings: 876 for lang: RUSSIAN
[28.06.25 01:33:22:535]  INFO skills.SkillsEngine: SkillsEngine: Loaded 66051 skill templates from XML files. Max id: 91000, max level: 259
[28.06.25 01:33:23:361]  INFO holder.OptionDataHolder: OptionDataHolder: loaded 24975 option data(s) count.
[28.06.25 01:33:24:161]  INFO holder.ItemHolder: ItemHolder: loaded 19240 item(s) count.
[28.06.25 01:33:24:167]  INFO holder.FakePlayerNpcsHolder: FakePlayerNpcsHolder: loaded 8 fake player npcs(s) count.
[28.06.25 01:33:25:207]  INFO holder.NpcHolder: NpcHolder: loaded 10519 npc(s) count.
[28.06.25 01:33:25:215]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[28.06.25 01:33:25:232]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[28.06.25 01:33:25:259]  INFO holder.ExchangeItemHolder: ExchangeItemHolder: loaded 602 exchange item(s) count.
[28.06.25 01:33:25:264]  INFO holder.StaticObjectHolder: StaticObjectHolder: loaded 50 static object(s) count.
[28.06.25 01:33:25:360]  INFO holder.DoorHolder: DoorHolder: loaded 1155 door(s) count.
[28.06.25 01:33:25:407]  INFO holder.ZoneHolder: ZoneHolder: loaded 1260 zone(s) count.
[28.06.25 01:33:25:714]  INFO holder.SpawnHolder: SpawnHolder: loaded 28949 spawn(s) count.
[28.06.25 01:33:25:813]  INFO holder.InstantZoneHolder: InstantZoneHolder: loaded 94 instant zone(s) count.
[28.06.25 01:33:26:123]  INFO holder.AirshipDockHolder: AirshipDockHolder: loaded 4 airship dock(s) count.
[28.06.25 01:33:26:168]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 57215 normal learns for 103 classes.
[28.06.25 01:33:26:168]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 79 transfer learns for 3 classes.
[28.06.25 01:33:26:169]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 120 transformation learns for 6 races.
[28.06.25 01:33:26:169]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 662 fishing learns for 6 races.
[28.06.25 01:33:26:170]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 72 certification learns.
[28.06.25 01:33:26:170]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 3 collection learns.
[28.06.25 01:33:26:170]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 44 pledge learns.
[28.06.25 01:33:26:170]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 18 sub unit learns.
[28.06.25 01:33:26:178]  INFO holder.CharTemplateHolder: CharTemplateHolder: loaded 206 char template(s) count.
[28.06.25 01:33:26:266]  INFO holder.ResidenceHolder: ResidenceHolder: total size: 83
[28.06.25 01:33:26:267]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 44 clanhall(s).
[28.06.25 01:33:26:268]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 castle(s).
[28.06.25 01:33:26:268]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 21 fortress(s).
[28.06.25 01:33:26:268]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 dominion(s).
[28.06.25 01:33:26:321]  INFO instancemanager.RaidBossSpawnManager: RaidBossSpawnManager: Loaded 194 Statuses
[28.06.25 01:33:26:335]  INFO entity.SevenSigns: SevenSigns: Currently in the Competition (Quest Event) period!
[28.06.25 01:33:26:335]  INFO entity.SevenSigns: SevenSigns: The Seal of Avarice remains unclaimed.
[28.06.25 01:33:26:336]  INFO entity.SevenSigns: SevenSigns: The Seal of Gnosis remains unclaimed.
[28.06.25 01:33:26:336]  INFO entity.SevenSigns: SevenSigns: The Seal of Strife remains unclaimed.
[28.06.25 01:33:26:337]  INFO entity.SevenSigns: SevenSigns: The Competition this week, if the trend continue, will end with a tie.
[28.06.25 01:33:26:337]  INFO entity.SevenSigns: SevenSigns: Next period begins in 2 days, 16 hours and 26 mins.
[28.06.25 01:33:26:451]  INFO holder.EventHolder: EventHolder: loaded 105 event(s) count.
[28.06.25 01:33:26:465]  INFO holder.FightClubMapHolder: FightClubMapHolder: loaded 42 fight club map(s) count.
[28.06.25 01:33:26:469]  INFO holder.CubicHolder: CubicHolder: loaded 145 cubic(s) count.
[28.06.25 01:33:26:506]  INFO holder.BuyListHolder: TradeController: Loaded 4 file(s).
[28.06.25 01:33:26:506]  INFO holder.BuyListHolder: TradeController: Loaded 25014 Items.
[28.06.25 01:33:26:507]  INFO holder.BuyListHolder: TradeController: Loaded 726 Buylists.
[28.06.25 01:33:26:520]  INFO holder.RecipeHolder: RecipeController: Loaded 998 Recipes.
[28.06.25 01:33:26:844]  INFO holder.ProductHolder: ProductItemTable: Loaded 0 product item on sale.
[28.06.25 01:33:26:853]  INFO holder.HennaHolder: HennaHolder: loaded 180 henna(s) count.
[28.06.25 01:33:26:856]  INFO holder.EnchantItemHolder: EnchantItemHolder: load 4 enchant scroll(s).
[28.06.25 01:33:26:859]  INFO holder.SoulCrystalHolder: SoulCrystalHolder: loaded 54 soul crystal(s) count.
[28.06.25 01:33:26:862]  INFO holder.ArmorSetsHolder: ArmorSetsHolder: loaded 199 armor sets(s) count.
[28.06.25 01:33:26:869]  INFO holder.PetitionGroupHolder: PetitionGroupHolder: loaded 3 petition group(s) count.
[28.06.25 01:33:26:872]  INFO holder.DressArmorHolder: DressArmorHolder: loaded 57 dress armor(s) count.
[28.06.25 01:33:26:874]  INFO holder.DressCloakHolder: DressCloakHolder: loaded 7 dress cloak(s) count.
[28.06.25 01:33:26:876]  INFO holder.DressShieldHolder: DressShieldHolder: loaded 10 dress shield(s) count.
[28.06.25 01:33:26:879]  INFO holder.DressWeaponHolder: DressWeaponHolder: loaded 401 dress weapon(s) count.
[28.06.25 01:33:26:920]  INFO holder.AugmentationDataHolder: AugmentationDataHolder: loaded 1022 augmentation data(s) count.
[28.06.25 01:33:26:924]  INFO holder.PremiumHolder: PremiumHolder: loaded 5 premium(s) count.
[28.06.25 01:33:26:926]  INFO holder.FoundationHolder: FoundationHolder: loaded 268 foundation(s) count.
[28.06.25 01:33:26:931]  INFO holder.DonationHolder: DonationHolder: loaded 192 donation(s) count.
[28.06.25 01:33:26:933]  INFO holder.TournamentMapHolder: TournamentMapHolder: loaded 3 tournament map(s) count.
[28.06.25 01:33:26:941]  INFO holder.FacebookCommentsHolder: FacebookCommentsHolder: loaded 2 facebook comments(s) count.
[28.06.25 01:33:26:941]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Banned HWIDS ]
[28.06.25 01:33:26:942]  INFO security.HWIDBan: black list (Hwid) loaded size: 0
[28.06.25 01:33:26:942]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Clan Crests ]
[28.06.25 01:33:26:943]  INFO cache.CrestCache: CrestCache: Loaded 0 crests
[28.06.25 01:33:26:944]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Loading Images ]
[28.06.25 01:33:26:947]  INFO cache.ImagesCache: ImagesCache: Loading images...
