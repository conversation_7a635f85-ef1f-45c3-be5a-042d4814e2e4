[28.06.25 00:00:01:165]  INFO instancemanager.SpawnManager: SpawnManager: spawned 443 npc; spawns: 121; group: DAY
[28.06.25 00:00:11:904]  INFO olympiad.CompEndTask: Olympiad System: Olympiad Game Ended
[28.06.25 00:00:11:905]  INFO olympiad.Olympiad: Olympiad System: Competition Period Starts in 0 days, 18 hours and 0 mins.
[28.06.25 00:00:11:905]  INFO olympiad.Olympiad: Olympiad System: Event starts/started: Sat Jun 28 18:00:11 BRT 2025
[28.06.25 00:01:59:107]  INFO olympiad.OlympiadEndTask: Olympiad System: Starting Validation period. Time to end validation:719
[28.06.25 00:06:00:812]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, before:0 toRemove:0 after:0 to Change:0
[28.06.25 00:06:00:813]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, Before: 0 Final:0
[28.06.25 00:06:00:814]  INFO fightclubmanager.FightClubEventManager: Last Hero: Removing room because it doesnt have enough players
[28.06.25 00:18:12:774]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_dead_npc
[28.06.25 00:18:12:777]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_aden
[28.06.25 00:18:12:778]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: bloodaltar_boss_darkelf
[28.06.25 00:18:12:781]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_dion
[28.06.25 00:18:12:783]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_dwarw
[28.06.25 00:18:12:785]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_giran
[28.06.25 00:18:12:787]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_gludin
[28.06.25 00:18:12:789]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_gludio
[28.06.25 00:18:12:790]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_goddart
[28.06.25 00:18:12:792]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_heine
[28.06.25 00:18:12:793]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: bloodaltar_boss_orc
[28.06.25 00:18:12:794]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_oren
[28.06.25 00:18:12:795]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: bloodaltar_boss_schutgart
[28.06.25 00:48:05:354]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[28.06.25 00:48:12:794]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_alive_npc
[28.06.25 00:50:05:032]  INFO threading.RunnableImpl: FourSepulchersManager: Entry time: Sat Jun 28 00:55:05 BRT 2025
[28.06.25 01:06:00:810]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, before:0 toRemove:0 after:0 to Change:0
[28.06.25 01:06:00:810]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, Before: 0 Final:0
[28.06.25 01:06:00:811]  INFO fightclubmanager.FightClubEventManager: Team VS Team: Removing room because it doesnt have enough players
[28.06.25 01:28:27:873]  INFO gameserver.GameServer: ============================================================================
[28.06.25 01:28:27:874]  INFO gameserver.GameServer: Nome: .................................................................... GAME SERVER
[28.06.25 01:28:27:874]  INFO gameserver.GameServer: Project Revision: ........................................................ LII [MultVerso]
[28.06.25 01:28:27:874]  INFO gameserver.GameServer: Update: .................................................................. High Five: Part 5
[28.06.25 01:28:27:874]  INFO gameserver.GameServer: Numero da Revision:....................................................... 2.0
[28.06.25 01:28:27:874]  INFO gameserver.GameServer: Build Revision: .......................................................... MultVerso
[28.06.25 01:28:27:875]  INFO gameserver.GameServer: Build date: .............................................................. 2025.06.28 01:25
[28.06.25 01:28:27:875]  INFO gameserver.GameServer: Compiler version: ........................................................ 17.0.15+9-LTS-241 (Oracle Corporation)
[28.06.25 01:28:27:875]  INFO gameserver.GameServer: ============================================================================
[28.06.25 01:28:27:917]  INFO gameserver.Config: Loaded 6 trade words.
[28.06.25 01:28:27:927]  INFO gameserver.Config: Abuse: Loaded 15 abuse words.
[28.06.25 01:28:27:981]  INFO gameserver.Config: Loaded 313 Configs!
[28.06.25 01:28:27:988]  INFO gameserver.GameServer: IP EXTERNO =============================127.0.0.1
[28.06.25 01:28:27:989]  INFO gameserver.GameServer: IP INTERNO =============================127.0.0.1
[28.06.25 01:28:27:989]  INFO gameserver.GameServer: Maximo Online IP ====================== 3000
[28.06.25 01:28:27:990]  INFO gameserver.GameServer: Hora do Restart  =======================45 5 * * *
[28.06.25 01:28:28:143]  INFO gameserver.GameServer: -----------------------------------------=[ Loading Protection Configuration ]
[28.06.25 01:28:28:147]  INFO idfactory.IdFactory: IdFactory: Clear characters online status.
[28.06.25 01:28:28:179]  INFO idfactory.IdFactory: IdFactory: Cleaned 0 elements from database in 0sec.
[28.06.25 01:28:28:188]  INFO idfactory.IdFactory: IdFactory: Extracted 2 used id's from characters
[28.06.25 01:28:28:188]  INFO idfactory.IdFactory: IdFactory: Extracted 46 used id's from items
[28.06.25 01:28:28:189]  INFO idfactory.IdFactory: IdFactory: Extracted total 48 used id's.
[28.06.25 01:28:28:189]  INFO idfactory.BitSetIDFactory: IdFactory: 102912 id's available.
[28.06.25 01:28:28:283]  INFO gameserver.GameServer: ===============[Loading Scripts]==================
[28.06.25 01:28:28:284]  INFO scripts.Scripts: Scripts: Loading...
[28.06.25 01:28:28:284]  INFO scripts.Scripts: Loading Server Scripts
[28.06.25 01:28:28:580]  INFO scripts.Scripts: Scripts: Loaded 1288 classes.
[28.06.25 01:28:28:587]  INFO vote.VoteMain: Loaded: Vote System - Individual Reward.
[28.06.25 01:28:28:588]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Twitch Manager ]
[28.06.25 01:28:28:612]  INFO gameserver.GameServer: ----------------------------------------------------------=[ AntiFeedManager ]
[28.06.25 01:28:28:612]  INFO gameserver.GameServer: -------------------------------------------------------------=[ QuestManager ]
[28.06.25 01:28:28:613]  INFO gameserver.GameServer: QuestManager : loaded 0 quest's
[28.06.25 01:28:28:623]  INFO tables.FakePlayersTable: FakePlayersTable: Loaded 32002 Fake Players.
[28.06.25 01:28:28:630]  INFO utils.Strings: Loaded 418 translit entries.
[28.06.25 01:28:28:632]  INFO gameserver.GameTimeController: GameTimeController: initialized. Current time is 14:50 in the day.
[28.06.25 01:28:28:633]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Lineage World ]
[28.06.25 01:28:28:637]  INFO model.World: L2World: Creating regions: [256][272][32].
[28.06.25 01:28:28:637]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:28:28:639]  INFO htm.HtmCache: HtmCache: disabled.
[28.06.25 01:28:28:645]  INFO data.StringHolder: StringHolder: load strings: 1066 for lang: ENGLISH
[28.06.25 01:28:28:645]  INFO data.StringHolder: StringHolder: load strings: 876 for lang: RUSSIAN
[28.06.25 01:28:29:941]  INFO skills.SkillsEngine: SkillsEngine: Loaded 66051 skill templates from XML files. Max id: 91000, max level: 259
[28.06.25 01:28:30:541]  INFO holder.OptionDataHolder: OptionDataHolder: loaded 24975 option data(s) count.
[28.06.25 01:28:31:184]  INFO holder.ItemHolder: ItemHolder: loaded 19240 item(s) count.
[28.06.25 01:28:31:190]  INFO holder.FakePlayerNpcsHolder: FakePlayerNpcsHolder: loaded 8 fake player npcs(s) count.
[28.06.25 01:28:32:198]  INFO holder.NpcHolder: NpcHolder: loaded 10519 npc(s) count.
[28.06.25 01:28:32:204]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[28.06.25 01:28:32:217]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[28.06.25 01:28:32:243]  INFO holder.ExchangeItemHolder: ExchangeItemHolder: loaded 602 exchange item(s) count.
[28.06.25 01:28:32:247]  INFO holder.StaticObjectHolder: StaticObjectHolder: loaded 50 static object(s) count.
[28.06.25 01:28:32:345]  INFO holder.DoorHolder: DoorHolder: loaded 1155 door(s) count.
[28.06.25 01:28:32:397]  INFO holder.ZoneHolder: ZoneHolder: loaded 1260 zone(s) count.
[28.06.25 01:28:32:715]  INFO holder.SpawnHolder: SpawnHolder: loaded 28949 spawn(s) count.
[28.06.25 01:28:32:802]  INFO holder.InstantZoneHolder: InstantZoneHolder: loaded 94 instant zone(s) count.
[28.06.25 01:28:33:097]  INFO holder.AirshipDockHolder: AirshipDockHolder: loaded 4 airship dock(s) count.
[28.06.25 01:28:33:144]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 57215 normal learns for 103 classes.
[28.06.25 01:28:33:145]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 79 transfer learns for 3 classes.
[28.06.25 01:28:33:145]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 120 transformation learns for 6 races.
[28.06.25 01:28:33:145]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 662 fishing learns for 6 races.
[28.06.25 01:28:33:145]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 72 certification learns.
[28.06.25 01:28:33:146]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 3 collection learns.
[28.06.25 01:28:33:146]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 44 pledge learns.
[28.06.25 01:28:33:146]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 18 sub unit learns.
[28.06.25 01:28:33:153]  INFO holder.CharTemplateHolder: CharTemplateHolder: loaded 206 char template(s) count.
[28.06.25 01:28:33:245]  INFO holder.ResidenceHolder: ResidenceHolder: total size: 83
[28.06.25 01:28:33:246]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 44 clanhall(s).
[28.06.25 01:28:33:246]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 castle(s).
[28.06.25 01:28:33:246]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 21 fortress(s).
[28.06.25 01:28:33:247]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 dominion(s).
[28.06.25 01:28:33:302]  INFO instancemanager.RaidBossSpawnManager: RaidBossSpawnManager: Loaded 194 Statuses
[28.06.25 01:28:33:317]  INFO entity.SevenSigns: SevenSigns: Currently in the Competition (Quest Event) period!
[28.06.25 01:28:33:317]  INFO entity.SevenSigns: SevenSigns: The Seal of Avarice remains unclaimed.
[28.06.25 01:28:33:318]  INFO entity.SevenSigns: SevenSigns: The Seal of Gnosis remains unclaimed.
[28.06.25 01:28:33:318]  INFO entity.SevenSigns: SevenSigns: The Seal of Strife remains unclaimed.
[28.06.25 01:28:33:318]  INFO entity.SevenSigns: SevenSigns: The Competition this week, if the trend continue, will end with a tie.
[28.06.25 01:28:33:319]  INFO entity.SevenSigns: SevenSigns: Next period begins in 2 days, 16 hours and 31 mins.
[28.06.25 01:28:33:430]  INFO holder.EventHolder: EventHolder: loaded 105 event(s) count.
[28.06.25 01:28:33:443]  INFO holder.FightClubMapHolder: FightClubMapHolder: loaded 42 fight club map(s) count.
[28.06.25 01:28:33:447]  INFO holder.CubicHolder: CubicHolder: loaded 145 cubic(s) count.
[28.06.25 01:28:33:485]  INFO holder.BuyListHolder: TradeController: Loaded 4 file(s).
[28.06.25 01:28:33:485]  INFO holder.BuyListHolder: TradeController: Loaded 25014 Items.
[28.06.25 01:28:33:486]  INFO holder.BuyListHolder: TradeController: Loaded 726 Buylists.
[28.06.25 01:28:33:500]  INFO holder.RecipeHolder: RecipeController: Loaded 998 Recipes.
[28.06.25 01:28:33:848]  INFO holder.ProductHolder: ProductItemTable: Loaded 0 product item on sale.
[28.06.25 01:28:33:858]  INFO holder.HennaHolder: HennaHolder: loaded 180 henna(s) count.
[28.06.25 01:28:33:862]  INFO holder.EnchantItemHolder: EnchantItemHolder: load 4 enchant scroll(s).
[28.06.25 01:28:33:865]  INFO holder.SoulCrystalHolder: SoulCrystalHolder: loaded 54 soul crystal(s) count.
[28.06.25 01:28:33:869]  INFO holder.ArmorSetsHolder: ArmorSetsHolder: loaded 199 armor sets(s) count.
[28.06.25 01:28:33:875]  INFO holder.PetitionGroupHolder: PetitionGroupHolder: loaded 3 petition group(s) count.
[28.06.25 01:28:33:878]  INFO holder.DressArmorHolder: DressArmorHolder: loaded 57 dress armor(s) count.
[28.06.25 01:28:33:882]  INFO holder.DressCloakHolder: DressCloakHolder: loaded 7 dress cloak(s) count.
[28.06.25 01:28:33:885]  INFO holder.DressShieldHolder: DressShieldHolder: loaded 10 dress shield(s) count.
[28.06.25 01:28:33:889]  INFO holder.DressWeaponHolder: DressWeaponHolder: loaded 401 dress weapon(s) count.
[28.06.25 01:28:33:931]  INFO holder.AugmentationDataHolder: AugmentationDataHolder: loaded 1022 augmentation data(s) count.
[28.06.25 01:28:33:934]  INFO holder.PremiumHolder: PremiumHolder: loaded 5 premium(s) count.
[28.06.25 01:28:33:937]  INFO holder.FoundationHolder: FoundationHolder: loaded 268 foundation(s) count.
[28.06.25 01:28:33:941]  INFO holder.DonationHolder: DonationHolder: loaded 192 donation(s) count.
[28.06.25 01:28:33:944]  INFO holder.TournamentMapHolder: TournamentMapHolder: loaded 3 tournament map(s) count.
[28.06.25 01:28:33:947]  INFO holder.FacebookCommentsHolder: FacebookCommentsHolder: loaded 2 facebook comments(s) count.
[28.06.25 01:28:33:947]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Banned HWIDS ]
[28.06.25 01:28:33:949]  INFO security.HWIDBan: black list (Hwid) loaded size: 0
[28.06.25 01:28:33:949]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Clan Crests ]
[28.06.25 01:28:33:950]  INFO cache.CrestCache: CrestCache: Loaded 0 crests
[28.06.25 01:28:33:951]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Loading Images ]
[28.06.25 01:28:33:952]  INFO cache.ImagesCache: ImagesCache: Loading images...
[28.06.25 01:28:44:939]  INFO cache.ImagesCache: ImagesCache: Loaded 8734 images
[28.06.25 01:28:44:939]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:28:44:944]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Fish Table ]
[28.06.25 01:28:44:947]  INFO tables.FishTable: FishTable: Loaded 270 fishes.
[28.06.25 01:28:44:948]  INFO tables.FishTable: FishTable: Loaded 919 fish rewards.
[28.06.25 01:28:44:949]  INFO gameserver.GameServer: -------------------------------------------------------------------=[ Skills ]
[28.06.25 01:28:44:949]  INFO tables.SkillTreeTable: SkillTreeTable: Loaded 526 enchanted skills.
[28.06.25 01:28:44:979]  INFO datatables.EnchantNamesTable: EnchantNamesTable: Loaded: 1319 skill enchant names
[28.06.25 01:28:44:979]  INFO gameserver.GameServer: --------------------------------------------------------=[ Augmentation Data ]
[28.06.25 01:28:44:982]  INFO tables.AugmentationData: Initializing AugmentationData.
[28.06.25 01:28:44:996]  INFO tables.AugmentationData: AugmentationData: 10 bad skill(s) were skipped.
[28.06.25 01:28:45:010]  INFO tables.AugmentationData: AugmentationData: Loaded: 52 augmentation stats.
[28.06.25 01:28:45:010]  INFO tables.AugmentationData: AugmentationData: Loaded: 24 accessory augmentation stats.
[28.06.25 01:28:45:014]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 0
[28.06.25 01:28:45:014]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 1
[28.06.25 01:28:45:015]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 2
[28.06.25 01:28:45:017]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 3
[28.06.25 01:28:45:017]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 4
[28.06.25 01:28:45:018]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 5
[28.06.25 01:28:45:018]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 6
[28.06.25 01:28:45:018]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 7
[28.06.25 01:28:45:019]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 8
[28.06.25 01:28:45:019]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 9
[28.06.25 01:28:45:022]  INFO tables.EnchantHPBonusTable: EnchantHPBonusTable: Loaded bonuses for 6 grades.
[28.06.25 01:28:45:022]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Level Up Table ]
[28.06.25 01:28:45:024]  INFO tables.LevelUpTable: LevelUpData: Loaded 103 Character Level Up Templates.
[28.06.25 01:28:45:027]  INFO tables.PetSkillsTable: PetSkillsTable: Loaded 2558 skills.
[28.06.25 01:28:45:027]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Item Logs ]
[28.06.25 01:28:45:028]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Auctioneer ]
[28.06.25 01:28:45:029]  INFO itemauction.ItemAuctionManager: Initializing ItemAuctionManager
[28.06.25 01:28:45:039]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 1 auction(s) for instance 32320.
[28.06.25 01:28:45:043]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 1 on 2025.07.01 17:00:00 for instance 32320
[28.06.25 01:28:45:045]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 2 auction(s) for instance 32321.
[28.06.25 01:28:45:046]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 4 on 2025.07.03 17:00:00 for instance 32321
[28.06.25 01:28:45:048]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 1 auction(s) for instance 32322.
[28.06.25 01:28:45:048]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 3 on 2025.06.28 17:00:00 for instance 32322
[28.06.25 01:28:45:048]  INFO itemauction.ItemAuctionManager: ItemAuctionManager: Loaded 3 instance(s).
[28.06.25 01:28:45:049]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Masterio Pack ]
[28.06.25 01:28:45:083]  INFO gameserver.GameServer: ------------------------------------------------------=[ Merge System Loaded ]
[28.06.25 01:28:45:104]  INFO bosses.AntharasManager: AntharasManager: State of Antharas is NOTSPAWN.
[28.06.25 01:28:45:105]  INFO bosses.AntharasManager: AntharasManager: Next spawn date of Antharas is 21:00 31.12.1969.
[28.06.25 01:28:45:106]  INFO bosses.BaiumManager: BaiumManager: State of Baium is NOTSPAWN.
[28.06.25 01:28:45:110]  INFO bosses.BaiumManager: BaiumManager: Next spawn date: 21:00 31.12.1969
[28.06.25 01:28:45:121]  INFO bosses.BelethManager: Beleth Manager: Loaded successfully
[28.06.25 01:28:45:123]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Loaded 20 Mysterious-Box spawns.
[28.06.25 01:28:45:128]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 physical monsters spawns.
[28.06.25 01:28:45:132]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 magical monsters spawns.
[28.06.25 01:28:45:133]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 92 Church of duke monsters spawns.
[28.06.25 01:28:45:135]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 68 Emperor's grave NPC spawns.
[28.06.25 01:28:45:136]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Conquerors' Sepulcher Manager
[28.06.25 01:28:45:136]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Emperors' Sepulcher Manager
[28.06.25 01:28:45:137]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Great Sages' Sepulcher Manager
[28.06.25 01:28:45:137]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Judges' Sepulcher Manager
[28.06.25 01:28:45:138]  INFO bosses.FourSepulchersManager: FourSepulchersManager: Beginning in Attack time
[28.06.25 01:28:45:139]  INFO bosses.SailrenManager: SailrenManager: State of Sailren is NOTSPAWN.
[28.06.25 01:28:45:140]  INFO bosses.SailrenManager: SailrenManager: Next spawn date of Sailren is 21:00 31.12.1969.
[28.06.25 01:28:45:141]  INFO bosses.ValakasManager: ValakasManager: State of Valakas is NOTSPAWN.
[28.06.25 01:28:45:141]  INFO bosses.ValakasManager: ValakasManager: Next spawn date of Valakas is 21:00 31.12.1969.
[28.06.25 01:28:45:141]  INFO threading.RunnableImpl: Sat Jun 28 01:28:45 BRT 2025 Atk announce scheduled to 30.0 minute of this hour.
[28.06.25 01:28:45:233]  INFO tables.PetDataTable: PetDataTable: Loaded 3870 pets.
[28.06.25 01:28:45:236]  INFO model.Manor: ManorManager: Loaded 270 seeds
[28.06.25 01:28:45:369]  INFO scriptconfig.ScriptConfig: Loaded Service: ScripsConfig
[28.06.25 01:28:45:370]  INFO community.CharacterServicesCommunity: === CharacterServicesCommunity onLoad called ===
[28.06.25 01:28:45:371]  INFO community.CharacterServicesCommunity: COMMUNITYBOARD_ENABLED: true
[28.06.25 01:28:45:371]  INFO community.CharacterServicesCommunity: EnableMergeCommunity: false
[28.06.25 01:28:45:371]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Character Services Community loaded.
[28.06.25 01:28:45:446]  INFO dailyquests.AbstractDPScript: DailyQuestHandler: Loaded DailyQuests data
[28.06.25 01:28:45:446]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Handler registered successfully.
[28.06.25 01:28:45:456]  INFO model.Player: Loaded Service: SellPcService
[28.06.25 01:28:45:459]  INFO community.CareerManager: CommunityBoard: Manage Career service loaded.
[28.06.25 01:28:45:462]  INFO community.CommunityAuctionHouse: CommunityBoard: Auction System Service loaded.
[28.06.25 01:28:45:468]  INFO community.CommunityAugment: CommunityBoard: Augmentation Manager loaded with 188 lifestone skills.
[28.06.25 01:28:45:469]  INFO community.CommunityBoard: CommunityBoard: service loaded.
[28.06.25 01:28:45:470]  INFO community.CommunityBoardAcademy: CommunityBoard: Academy service loaded.
[28.06.25 01:28:45:470]  INFO community.CommunityBosses: CommunityBoard: Bosses loaded.
[28.06.25 01:28:45:473]  INFO community.CommunityClan: CommunityBoard: Clan service loaded.
[28.06.25 01:28:45:473]  INFO community.CommunityDropCalculator: CommunityBoard: Drop Calculator service loaded.
[28.06.25 01:28:45:473]  INFO community.CommunityEvents: CommunityBoard: Community Events service loaded.
[28.06.25 01:28:45:474]  INFO community.CommunityFacebook: CommunityBoard: Facebook Rewards page loaded.
[28.06.25 01:28:45:475]  INFO community.CommunityNpcs: CommunityBoard: Npcs loaded.
[28.06.25 01:28:45:476]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[28.06.25 01:28:45:482]  INFO community.CommunityTournament: CommunityBoard: Tournament loaded.
[28.06.25 01:28:45:482]  INFO community.CommunityWarehouse: CommunityBoard: Warehouse loaded.
[28.06.25 01:28:45:483]  INFO community.Forge: CommunityBoard: Forge loaded.
[28.06.25 01:28:45:485]  INFO community.ServicesCommunity: ServicesCommunity: Services Community service loaded.
[28.06.25 01:28:45:491]  INFO gameserver.GameServer: ===============[Spawn Manager]==================
[28.06.25 01:28:45:590]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1000 npc for group: NONE
[28.06.25 01:28:45:780]  INFO instancemanager.SpawnManager: SpawnManager: spawned 5000 npc for group: NONE
[28.06.25 01:28:45:815]  INFO naia.NaiaTowerManager: Naia Tower Manager: Loaded 12 rooms
[28.06.25 01:28:45:912]  INFO instancemanager.SpawnManager: SpawnManager: spawned 7000 npc for group: NONE
[28.06.25 01:28:46:313]  INFO instancemanager.SpawnManager: SpawnManager: spawned 12000 npc for group: NONE
[28.06.25 01:28:46:414]  INFO instancemanager.SpawnManager: SpawnManager: spawned 13000 npc for group: NONE
[28.06.25 01:28:47:520]  INFO instancemanager.SpawnManager: SpawnManager: spawned 21000 npc for group: NONE
[28.06.25 01:28:47:662]  INFO instancemanager.SpawnManager: SpawnManager: spawned 22000 npc for group: NONE
[28.06.25 01:28:48:196]  INFO instancemanager.SpawnManager: SpawnManager: spawned 25000 npc for group: NONE
[28.06.25 01:28:48:398]  INFO instancemanager.SpawnManager: SpawnManager: spawned 26000 npc for group: NONE
[28.06.25 01:28:48:815]  INFO instancemanager.SpawnManager: SpawnManager: spawned 28000 npc for group: NONE
[28.06.25 01:28:49:885]  INFO instancemanager.SpawnManager: SpawnManager: spawned 32000 npc for group: NONE
[28.06.25 01:28:50:791]  INFO instancemanager.SpawnManager: SpawnManager: spawned 35000 npc for group: NONE
[28.06.25 01:28:51:097]  INFO instancemanager.SpawnManager: SpawnManager: spawned 36000 npc for group: NONE
[28.06.25 01:28:51:495]  INFO instancemanager.SpawnManager: SpawnManager: spawned 37000 npc for group: NONE
[28.06.25 01:28:51:995]  INFO instancemanager.SpawnManager: SpawnManager: spawned 38373 npc; spawns: 17087; group: NONE
[28.06.25 01:28:51:995]  INFO gameserver.GameServer: --------------------------------------------------------------------=[ Boats ]
[28.06.25 01:28:51:996]  INFO data.BoatHolder: BoatHolder: loaded 2 boat(s) count.
[28.06.25 01:28:51:999]  INFO data.BoatHolder: BoatHolder: Spawning: RunePrimeval_Vehicle
[28.06.25 01:28:51:999]  INFO data.BoatHolder: BoatHolder: Spawning: AdenGracia_Airship
[28.06.25 01:28:52:000]  INFO holder.StaticObjectHolder: StaticObjectHolder: spawned: 50 static object(s).
[28.06.25 01:28:52:000]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Dimensional Rift ]
[28.06.25 01:28:52:006]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 13 room types with 112 rooms.
[28.06.25 01:28:52:006]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 300 DimensionalRift spawns, 0 errors.
[28.06.25 01:28:52:011]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Seven Signs ]
[28.06.25 01:28:52:023]  INFO instancemanager.AutoSpawnManager: AutoSpawnHandler: Loaded 50 handlers in total.
[28.06.25 01:28:52:023]  INFO gameserver.GameServer: ===================================================================
[28.06.25 01:28:52:024]  INFO gameserver.GameServer: ===================[Loading Olympiad System]=======================
[28.06.25 01:28:52:029]  INFO olympiad.Olympiad: Olympiad System: Loading Olympiad System....
[28.06.25 01:28:52:030]  INFO olympiad.Olympiad: Olympiad System: Currently in Validation Period
[28.06.25 01:28:52:030]  INFO olympiad.Olympiad: Olympiad System: Period Ends....
[28.06.25 01:28:52:031]  INFO olympiad.Olympiad: Olympiad System: In 0 days, 10 hours and 33 mins.
[28.06.25 01:28:52:031]  INFO olympiad.Olympiad: Olympiad System: Loaded 0 Noblesses
[28.06.25 01:28:52:033]  INFO entity.Hero: Hero System: Loaded 0 Heroes.
[28.06.25 01:28:52:034]  INFO entity.Hero: Hero System: Loaded 0 all time Heroes.
[28.06.25 01:28:52:034]  INFO gameserver.GameServer: ===================[Olympiad System Loaded]=======================
[28.06.25 01:28:52:034]  INFO gameserver.GameServer: ===================================================================
[28.06.25 01:28:52:036]  INFO instancemanager.PetitionManager: Initializing PetitionManager
[28.06.25 01:28:52:041]  INFO instancemanager.CursedWeaponsManager: CursedWeaponsManager: Loaded 2 cursed weapon(s).
[28.06.25 01:28:52:042]  INFO gameserver.GameServer: ------------------------------------------=[ Loaded Small Achievement System ]
[28.06.25 01:28:52:055]  INFO gameserver.GameServer: --------------------------------------------------------------=[ ItemHandler ]
[28.06.25 01:28:52:056]  INFO items.ItemHandler: ItemHandler: loaded 0 item handler(s) count.
[28.06.25 01:28:52:056]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Admin Commands ]
[28.06.25 01:28:52:056]  INFO admincommands.AdminCommandHandler: AdminCommandHandler: loaded 475 admin command handler(s) count.
[28.06.25 01:28:52:057]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Players Commands ]
[28.06.25 01:28:52:060]  INFO usercommands.UserCommandHandler: UserCommandHandler: loaded 17 user command handler(s) count.
[28.06.25 01:28:52:061]  INFO voicecommands.VoicedCommandHandler: VoicedCommandHandler: loaded 102 voiced command handler(s) count.
[28.06.25 01:28:52:064]  INFO gameserver.GameServer: ======================[Tournament By Kara`]==========================
[28.06.25 01:28:52:067]  INFO tournament.TournamentHolder: [TournamentHolder] Initialized 5 tournament holders:
[28.06.25 01:28:52:067]  INFO tournament.TournamentHolder: 1 vs 1
[28.06.25 01:28:52:067]  INFO tournament.TournamentHolder: 2 vs 2
[28.06.25 01:28:52:068]  INFO tournament.TournamentHolder: 4 vs 4
[28.06.25 01:28:52:068]  INFO tournament.TournamentHolder: 5 vs 5
[28.06.25 01:28:52:068]  INFO tournament.TournamentHolder: 6 vs 6
[28.06.25 01:28:52:070]  INFO gameserver.GameServer: ======================[Loading Castels & Clan Halls]==========================
[28.06.25 01:28:52:116]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Gludio Castle time - 16:00 29.06.2025
[28.06.25 01:28:52:121]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Dion Castle time - 16:00 29.06.2025
[28.06.25 01:28:52:129]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Giran Castle time - 20:00 29.06.2025
[28.06.25 01:28:52:135]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Oren Castle time - 20:00 29.06.2025
[28.06.25 01:28:52:140]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Aden Castle time - 16:00 29.06.2025
[28.06.25 01:28:52:146]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Innadril Castle time - 20:00 29.06.2025
[28.06.25 01:28:52:153]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Goddard Castle time - 20:00 29.06.2025
[28.06.25 01:28:52:159]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Rune Castle time - 16:00 29.06.2025
[28.06.25 01:28:52:163]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Schuttgart Castle time - 16:00 29.06.2025
[28.06.25 01:28:52:166]  INFO impl.ClanHallNpcSiegeEvent: ClanHallNpcSiegeEvent: Fortress Of Resistance time - 18:00 30.06.2025
[28.06.25 01:28:52:168]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:169]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:171]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Topaz Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:172]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Ruby Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:174]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Crystal Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:176]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:177]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:178]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:179]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:181]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Atramental Barracks time - 15:00 02.07.2025
[28.06.25 01:28:52:182]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Scarlet Barracks time - 15:00 02.07.2025
[28.06.25 01:28:52:184]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Viridian Barracks time - 15:00 02.07.2025
[28.06.25 01:28:52:184]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Devastated Castle time - 18:00 28.06.2025
[28.06.25 01:28:52:185]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Bandit Stronghold time - 22:00 28.06.2025
[28.06.25 01:28:52:186]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:188]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:189]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:190]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[28.06.25 01:28:52:192]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Gold Manor time - 15:00 02.07.2025
[28.06.25 01:28:52:193]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:195]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:196]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:197]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:199]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:201]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[28.06.25 01:28:52:202]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:204]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:205]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:207]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:209]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Mont Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:210]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Astaire Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:211]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Aria Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:212]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Yiana Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:214]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Roien Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:216]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Luna Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:217]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Traban Chamber time - 15:00 02.07.2025
[28.06.25 01:28:52:219]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Eisen Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:220]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Heavy Metal Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:221]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Molten Ore Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:223]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Titan Hall time - 15:00 02.07.2025
[28.06.25 01:28:52:224]  INFO impl.ClanHallMiniGameEvent: ClanHallMiniGameEvent: Rainbow Springs time - 22:00 28.06.2025
[28.06.25 01:28:52:225]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Wild Beast Reserve time - 21:00 28.06.2025
[28.06.25 01:28:52:225]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Fortress of Dead time - 18:00 28.06.2025
[28.06.25 01:28:52:227]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Gludio Dominion time - undefined
[28.06.25 01:28:52:227]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Dion Dominion time - undefined
[28.06.25 01:28:52:227]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Giran Dominion time - undefined
[28.06.25 01:28:52:228]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Oren Dominion time - undefined
[28.06.25 01:28:52:230]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Aden Dominion time - undefined
[28.06.25 01:28:52:230]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Innadril Dominion time - undefined
[28.06.25 01:28:52:231]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Goddard Dominion time - undefined
[28.06.25 01:28:52:232]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Rune Dominion time - undefined
[28.06.25 01:28:52:232]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Schuttgart Dominion time - undefined
[28.06.25 01:28:52:236]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Shanty Fortress time - undefined
[28.06.25 01:28:52:240]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Southern Fortress time - undefined
[28.06.25 01:28:52:245]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hive Fortress time - undefined
[28.06.25 01:28:52:249]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Valley Fortress time - undefined
[28.06.25 01:28:52:253]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Ivory Fortress time - undefined
[28.06.25 01:28:52:257]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Narsell Fortress time - undefined
[28.06.25 01:28:52:261]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Bayou Fortress time - undefined
[28.06.25 01:28:52:265]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: White Sands Fortress time - undefined
[28.06.25 01:28:52:270]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Borderland Fortress time - undefined
[28.06.25 01:28:52:273]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Swamp Fortress time - undefined
[28.06.25 01:28:52:278]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Archaic Fortress time - undefined
[28.06.25 01:28:52:283]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Floran Fortress time - undefined
[28.06.25 01:28:52:286]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Cloud Mountain Fortress time - undefined
[28.06.25 01:28:52:289]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Tanor Fortress time - undefined
[28.06.25 01:28:52:293]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Dragonspine Fortress time - undefined
[28.06.25 01:28:52:296]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Antharas Fortress time - undefined
[28.06.25 01:28:52:300]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Western Fortress time - undefined
[28.06.25 01:28:52:304]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hunter's Fortress time - undefined
[28.06.25 01:28:52:307]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Aaru Fortress time - undefined
[28.06.25 01:28:52:311]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Demon Fortress time - undefined
[28.06.25 01:28:52:314]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Monastic Fortress time - undefined
[28.06.25 01:28:52:316]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 70-75 Level time - 01:30 28.06.2025
[28.06.25 01:28:52:316]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 76-79 Level time - 01:30 28.06.2025
[28.06.25 01:28:52:316]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 80-85 Level time - 01:30 28.06.2025
[28.06.25 01:28:52:317]  INFO fightclub.LastManStandingEvent: LastManStandingEvent: Last Hero time - undefined
[28.06.25 01:28:52:317]  INFO fightclub.TeamVSTeamEvent: TeamVSTeamEvent: Team VS Team time - undefined
[28.06.25 01:28:52:317]  INFO fightclub.LuckyCreaturesEvent: LuckyCreaturesEvent: Lucky Creatures time - undefined
[28.06.25 01:28:52:317]  INFO fightclub.CaptureTheFlagEvent: CaptureTheFlagEvent: Capture The Flag time - undefined
[28.06.25 01:28:52:318]  INFO fightclub.FFATreasureHuntEvent: FFATreasureHuntEvent: Treasure Hunt time - undefined
[28.06.25 01:28:52:318]  INFO fightclub.KoreanStyleEvent: KoreanStyleEvent: Korean Style time - undefined
[28.06.25 01:28:52:318]  INFO fightclub.ProtectTheKingEvent: ProtectTheKingEvent: Protect The King time - undefined
[28.06.25 01:28:52:318]  INFO fightclub.FightForThroneEvent: FightForThroneEvent: Fight For Throne time - undefined
[28.06.25 01:28:52:319]  INFO fightclub.DeathMatchEvent: DeathMatchEvent: Death Match time - undefined
[28.06.25 01:28:52:319]  INFO fightclub.RussianRouletteEvent: RussianRouletteEvent: Russian Roulette time - undefined
[28.06.25 01:28:52:320]  INFO instancemanager.CastleManorManager: Manor System: Initializing...
[28.06.25 01:28:52:327]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:28:52:328]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Auto Cleaner ]
[28.06.25 01:28:52:328]  INFO gameserver.GameServer: IdFactory: Free ObjectID's remaining: 1879004760
[28.06.25 01:28:52:328]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:28:52:329]  INFO instancemanager.CoupleManager: Initializing CoupleManager
[28.06.25 01:28:52:329]  INFO instancemanager.CoupleManager: Loaded: 0 couples(s)
[28.06.25 01:28:52:331]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Hellbound ]
[28.06.25 01:28:52:339]  INFO instancemanager.HellboundManager: HellboundManager: Loaded 335 spawn entries.
[28.06.25 01:28:52:511]  INFO instancemanager.HellboundManager: HellboundManager: Spawned 623 mobs and NPCs according to the current Hellbound stage
[28.06.25 01:28:52:512]  INFO instancemanager.HellboundManager: Hellbound Manager: Loaded
[28.06.25 01:28:52:514]  INFO naia.NaiaTowerManager: Naia Core Manager: Loaded
[28.06.25 01:28:52:515]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:28:52:515]  INFO instancemanager.SoDManager: Seed of Destruction Manager: Loaded
[28.06.25 01:28:52:516]  INFO instancemanager.SoIManager: Seed of Infinity Manager: Loaded. Current stage is: 1
[28.06.25 01:28:52:518]  INFO instancemanager.SpawnManager: SpawnManager: spawned 4 npc; spawns: 4; group: soi_world_mouths
[28.06.25 01:28:52:519]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: soi_world_abyssgaze2
[28.06.25 01:28:52:519]  INFO instancemanager.BloodAltarManager: Blood Altar Manager: Initializing...
[28.06.25 01:28:52:538]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_alive_npc
[28.06.25 01:28:52:541]  INFO gameserver.GameServer: Preparing Drop Calculator
[28.06.25 01:28:52:606]  INFO gameserver.GameServer: ----------------------------------------------------------=[ Offline Buffers ]
[28.06.25 01:28:52:607]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loading offline buffers...
[28.06.25 01:28:52:608]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loaded: 0 offline buffer(s)
[28.06.25 01:28:52:619]  INFO achievements.Achievements: Achievement System: Loaded 4 achievement categories and 39 achievements.
[28.06.25 01:28:52:621]  INFO gameserver.GameServer: Emotions Loaded....
[28.06.25 01:28:52:621]  INFO gameserver.GameServer: --------------------------------------------------=[ DataBase Cleaner Loaded ]
[28.06.25 01:28:52:622]  INFO dao.CharacterDAO: Found 0 characters to delete!
[28.06.25 01:28:52:703]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Initializing
[28.06.25 01:28:52:704]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Load OK
[28.06.25 01:28:52:704]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Loaded 0 announce.
[28.06.25 01:28:52:706]  INFO gameserver.GameServer: ---------------------------------------------------=[ Loaded Facebook System ]
[28.06.25 01:28:52:709]  INFO gameserver.GameServer: -----------------------------------------------------=[ Loaded Stream System ]
[28.06.25 01:28:52:713]  INFO holder.ProxiesHolder: ProxiesHolder: loaded 20 proxies(s) count.
[28.06.25 01:28:52:714]  INFO gameserver.GameServer: ------------------------------------------------------=[ Loaded Proxy System ]
[28.06.25 01:28:55:249]  INFO donation.DonationReader: Donation Reader service started
[28.06.25 01:28:55:249]  INFO gameserver.GameServer: --------------------------------------------=[ Loaded AUTO - Donation System ]
[28.06.25 01:28:55:252]  INFO taskmanager.BackupTaskManager: Next Auto Backup in 5 H., 11 Min.
[28.06.25 01:28:55:260]  INFO votingengine.VotingSettings: VotingSettings: Loaded 3 messages, 4 drops!
[28.06.25 01:28:55:262]  INFO votingengine.VotingRewardAPI: VoteReward: Has been successfully loaded!
[28.06.25 01:28:55:264]  INFO model.PhantomPlayers: Loading phantom players...
[28.06.25 01:28:55:272]  INFO model.PhantomPlayers: Loaded 32002 possible phantom names.
[28.06.25 01:28:55:272]  INFO model.PhantomPlayers: Loaded 7172 possible phantom titles.
[28.06.25 01:28:55:273]  INFO model.PhantomPlayers: PhantomPlayers: Cached 0 players.
[28.06.25 01:28:55:273]  INFO model.PhantomPlayers: PhantomPlayers: Free names 32002.
[28.06.25 01:28:55:274]  INFO model.PhantomPlayers: Loaded 0 phantom players from database with a maximum of 3000 phantoms.
[28.06.25 01:28:55:274]  INFO model.PhantomPlayers: Scheduled spawner with 1 seconds delay.
[28.06.25 01:28:55:474]  INFO instancemanager.SpawnManager: SpawnManager: spawned 443 npc; spawns: 121; group: DAY
[28.06.25 01:28:55:581]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dawn_spawn
[28.06.25 01:28:55:855]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dusk_spawn
[28.06.25 01:28:55:855]  INFO gameserver.GameServer: Telnet server is currently disabled.
[28.06.25 01:28:55:857]  INFO gameserver.Shutdown: Scheduled server restart in 4h 16m 5s.
[28.06.25 01:28:55:858]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:28:55:858]  INFO gameserver.GameServer: >>>>>>>>>>>>>>> GameServer Started <<<<<<<<<<<<<<
[28.06.25 01:28:55:858]  INFO gameserver.GameServer: Maximum Numbers of Connected Players: 3000
[28.06.25 01:28:55:858]  INFO gameserver.GameServer: ====================================================================
[28.06.25 01:28:55:873]  INFO gameserver.GameServer: AllowedMemory: ........... 6291456 KB
[28.06.25 01:28:55:873]  INFO gameserver.GameServer: 
Allocated: ............... 4194304 KB (66.6667%)
[28.06.25 01:28:55:873]  INFO gameserver.GameServer: 
Non-Allocated: ........... 2097152 KB (33.3333%)
[28.06.25 01:28:55:873]  INFO gameserver.GameServer: 
AllocatedMemory: ......... 4194304 KB
[28.06.25 01:28:55:874]  INFO gameserver.GameServer: Used: .................... 1145449 KB (18.2064%)
[28.06.25 01:28:55:874]  INFO gameserver.GameServer: 
Unused (cached): ......... 3048854 KB (48.4602%)
[28.06.25 01:28:55:874]  INFO gameserver.GameServer: 
UseableMemory: ........... 5146006 KB (81.7936%)
[28.06.25 01:28:55:874]  INFO gameserver.GameServer: 

[28.06.25 01:28:55:874]  INFO gameserver.GameServer: ====================================================================
[28.06.25 01:28:55:877]  INFO loginservercon.AuthServerCommunication: Connecting to authserver on 127.0.0.1:9014
[28.06.25 01:28:55:889]  INFO lspackets.AuthResponse: Registered on authserver as 1 [Bartz]
[28.06.25 01:29:32:765]  INFO community.CharacterServicesCommunity: onBypassCommand called - bypass: _bbscharservices;changesex
[28.06.25 01:29:32:765]  INFO community.CharacterServicesCommunity: cmd: _bbscharservices, action: changesex
[28.06.25 01:29:42:831]  INFO community.CharacterServicesCommunity: onBypassCommand called - bypass: _bbscharservices;registeremail
[28.06.25 01:29:42:831]  INFO community.CharacterServicesCommunity: cmd: _bbscharservices, action: registeremail
[28.06.25 01:29:57:272]  INFO community.CharacterServicesCommunity: === onWriteCommand called ===
[28.06.25 01:29:57:272]  INFO community.CharacterServicesCommunity: Player: Dzu
[28.06.25 01:29:57:272]  INFO community.CharacterServicesCommunity: Bypass: '_bbscharservices;registeremail;submit'
[28.06.25 01:29:57:273]  INFO community.CharacterServicesCommunity: arg1: '0'
[28.06.25 01:29:57:273]  INFO community.CharacterServicesCommunity: arg2: '0'
[28.06.25 01:29:57:273]  INFO community.CharacterServicesCommunity: arg3: '<EMAIL>'
[28.06.25 01:29:57:273]  INFO community.CharacterServicesCommunity: arg4: '<EMAIL>'
[28.06.25 01:29:57:273]  INFO community.CharacterServicesCommunity: arg5: ''
[28.06.25 01:29:57:274]  INFO community.CharacterServicesCommunity: Processing email registration - email: <EMAIL>, confirmEmail: <EMAIL>
[28.06.25 01:29:57:276]  INFO utils.EmailVerification: Verification code stored for account: siniez, email: <EMAIL>
[28.06.25 01:29:57:277]  INFO community.CharacterServicesCommunity: About to send email - Config.MAIL_USER: '<EMAIL>', Config.MAIL_PASS: 'SET'
[28.06.25 01:29:57:277]  INFO utils.EmailVerification: Checking email configuration - MAIL_USER: '<EMAIL>', MAIL_PASS: 'SET'
[28.06.25 01:30:32:706]  INFO tasks.RestoreOfflineTraders: Restored 0 offline traders
[28.06.25 01:33:20:173]  INFO gameserver.GameServer: ============================================================================
[28.06.25 01:33:20:174]  INFO gameserver.GameServer: Nome: .................................................................... GAME SERVER
[28.06.25 01:33:20:175]  INFO gameserver.GameServer: Project Revision: ........................................................ LII [MultVerso]
[28.06.25 01:33:20:175]  INFO gameserver.GameServer: Update: .................................................................. High Five: Part 5
[28.06.25 01:33:20:175]  INFO gameserver.GameServer: Numero da Revision:....................................................... 2.0
[28.06.25 01:33:20:175]  INFO gameserver.GameServer: Build Revision: .......................................................... MultVerso
[28.06.25 01:33:20:175]  INFO gameserver.GameServer: Build date: .............................................................. 2025.06.28 01:32
[28.06.25 01:33:20:175]  INFO gameserver.GameServer: Compiler version: ........................................................ 17.0.15+9-LTS-241 (Oracle Corporation)
[28.06.25 01:33:20:175]  INFO gameserver.GameServer: ============================================================================
[28.06.25 01:33:20:215]  INFO gameserver.Config: Loaded 6 trade words.
[28.06.25 01:33:20:224]  INFO gameserver.Config: Abuse: Loaded 15 abuse words.
[28.06.25 01:33:20:282]  INFO gameserver.Config: Loaded 313 Configs!
[28.06.25 01:33:20:288]  INFO gameserver.GameServer: IP EXTERNO =============================127.0.0.1
[28.06.25 01:33:20:288]  INFO gameserver.GameServer: IP INTERNO =============================127.0.0.1
[28.06.25 01:33:20:289]  INFO gameserver.GameServer: Maximo Online IP ====================== 3000
[28.06.25 01:33:20:290]  INFO gameserver.GameServer: Hora do Restart  =======================45 5 * * *
[28.06.25 01:33:20:457]  INFO gameserver.GameServer: -----------------------------------------=[ Loading Protection Configuration ]
[28.06.25 01:33:20:461]  INFO idfactory.IdFactory: IdFactory: Clear characters online status.
[28.06.25 01:33:20:473]  INFO idfactory.IdFactory: IdFactory: Cleaned 0 elements from database in 0sec.
[28.06.25 01:33:20:482]  INFO idfactory.IdFactory: IdFactory: Extracted 2 used id's from characters
[28.06.25 01:33:20:483]  INFO idfactory.IdFactory: IdFactory: Extracted 46 used id's from items
[28.06.25 01:33:20:483]  INFO idfactory.IdFactory: IdFactory: Extracted total 48 used id's.
[28.06.25 01:33:20:484]  INFO idfactory.BitSetIDFactory: IdFactory: 102912 id's available.
[28.06.25 01:33:20:609]  INFO gameserver.GameServer: ===============[Loading Scripts]==================
[28.06.25 01:33:20:610]  INFO scripts.Scripts: Scripts: Loading...
[28.06.25 01:33:20:610]  INFO scripts.Scripts: Loading Server Scripts
[28.06.25 01:33:20:921]  INFO scripts.Scripts: Scripts: Loaded 1288 classes.
[28.06.25 01:33:20:929]  INFO vote.VoteMain: Loaded: Vote System - Individual Reward.
[28.06.25 01:33:20:930]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Twitch Manager ]
[28.06.25 01:33:20:955]  INFO gameserver.GameServer: ----------------------------------------------------------=[ AntiFeedManager ]
[28.06.25 01:33:20:956]  INFO gameserver.GameServer: -------------------------------------------------------------=[ QuestManager ]
[28.06.25 01:33:20:956]  INFO gameserver.GameServer: QuestManager : loaded 0 quest's
[28.06.25 01:33:20:968]  INFO tables.FakePlayersTable: FakePlayersTable: Loaded 32002 Fake Players.
[28.06.25 01:33:20:976]  INFO utils.Strings: Loaded 418 translit entries.
[28.06.25 01:33:20:978]  INFO gameserver.GameTimeController: GameTimeController: initialized. Current time is 15:20 in the day.
[28.06.25 01:33:20:979]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Lineage World ]
[28.06.25 01:33:20:982]  INFO model.World: L2World: Creating regions: [256][272][32].
[28.06.25 01:33:20:982]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:33:20:983]  INFO htm.HtmCache: HtmCache: disabled.
[28.06.25 01:33:20:991]  INFO data.StringHolder: StringHolder: load strings: 1066 for lang: ENGLISH
[28.06.25 01:33:20:991]  INFO data.StringHolder: StringHolder: load strings: 876 for lang: RUSSIAN
[28.06.25 01:33:22:535]  INFO skills.SkillsEngine: SkillsEngine: Loaded 66051 skill templates from XML files. Max id: 91000, max level: 259
[28.06.25 01:33:23:361]  INFO holder.OptionDataHolder: OptionDataHolder: loaded 24975 option data(s) count.
[28.06.25 01:33:24:161]  INFO holder.ItemHolder: ItemHolder: loaded 19240 item(s) count.
[28.06.25 01:33:24:167]  INFO holder.FakePlayerNpcsHolder: FakePlayerNpcsHolder: loaded 8 fake player npcs(s) count.
[28.06.25 01:33:25:207]  INFO holder.NpcHolder: NpcHolder: loaded 10519 npc(s) count.
[28.06.25 01:33:25:215]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[28.06.25 01:33:25:232]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[28.06.25 01:33:25:259]  INFO holder.ExchangeItemHolder: ExchangeItemHolder: loaded 602 exchange item(s) count.
[28.06.25 01:33:25:264]  INFO holder.StaticObjectHolder: StaticObjectHolder: loaded 50 static object(s) count.
[28.06.25 01:33:25:360]  INFO holder.DoorHolder: DoorHolder: loaded 1155 door(s) count.
[28.06.25 01:33:25:407]  INFO holder.ZoneHolder: ZoneHolder: loaded 1260 zone(s) count.
[28.06.25 01:33:25:714]  INFO holder.SpawnHolder: SpawnHolder: loaded 28949 spawn(s) count.
[28.06.25 01:33:25:813]  INFO holder.InstantZoneHolder: InstantZoneHolder: loaded 94 instant zone(s) count.
[28.06.25 01:33:26:123]  INFO holder.AirshipDockHolder: AirshipDockHolder: loaded 4 airship dock(s) count.
[28.06.25 01:33:26:168]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 57215 normal learns for 103 classes.
[28.06.25 01:33:26:168]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 79 transfer learns for 3 classes.
[28.06.25 01:33:26:169]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 120 transformation learns for 6 races.
[28.06.25 01:33:26:169]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 662 fishing learns for 6 races.
[28.06.25 01:33:26:170]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 72 certification learns.
[28.06.25 01:33:26:170]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 3 collection learns.
[28.06.25 01:33:26:170]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 44 pledge learns.
[28.06.25 01:33:26:170]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 18 sub unit learns.
[28.06.25 01:33:26:178]  INFO holder.CharTemplateHolder: CharTemplateHolder: loaded 206 char template(s) count.
[28.06.25 01:33:26:266]  INFO holder.ResidenceHolder: ResidenceHolder: total size: 83
[28.06.25 01:33:26:267]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 44 clanhall(s).
[28.06.25 01:33:26:268]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 castle(s).
[28.06.25 01:33:26:268]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 21 fortress(s).
[28.06.25 01:33:26:268]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 dominion(s).
[28.06.25 01:33:26:321]  INFO instancemanager.RaidBossSpawnManager: RaidBossSpawnManager: Loaded 194 Statuses
[28.06.25 01:33:26:335]  INFO entity.SevenSigns: SevenSigns: Currently in the Competition (Quest Event) period!
[28.06.25 01:33:26:335]  INFO entity.SevenSigns: SevenSigns: The Seal of Avarice remains unclaimed.
[28.06.25 01:33:26:336]  INFO entity.SevenSigns: SevenSigns: The Seal of Gnosis remains unclaimed.
[28.06.25 01:33:26:336]  INFO entity.SevenSigns: SevenSigns: The Seal of Strife remains unclaimed.
[28.06.25 01:33:26:337]  INFO entity.SevenSigns: SevenSigns: The Competition this week, if the trend continue, will end with a tie.
[28.06.25 01:33:26:337]  INFO entity.SevenSigns: SevenSigns: Next period begins in 2 days, 16 hours and 26 mins.
[28.06.25 01:33:26:451]  INFO holder.EventHolder: EventHolder: loaded 105 event(s) count.
[28.06.25 01:33:26:465]  INFO holder.FightClubMapHolder: FightClubMapHolder: loaded 42 fight club map(s) count.
[28.06.25 01:33:26:469]  INFO holder.CubicHolder: CubicHolder: loaded 145 cubic(s) count.
[28.06.25 01:33:26:506]  INFO holder.BuyListHolder: TradeController: Loaded 4 file(s).
[28.06.25 01:33:26:506]  INFO holder.BuyListHolder: TradeController: Loaded 25014 Items.
[28.06.25 01:33:26:507]  INFO holder.BuyListHolder: TradeController: Loaded 726 Buylists.
[28.06.25 01:33:26:520]  INFO holder.RecipeHolder: RecipeController: Loaded 998 Recipes.
[28.06.25 01:33:26:844]  INFO holder.ProductHolder: ProductItemTable: Loaded 0 product item on sale.
[28.06.25 01:33:26:853]  INFO holder.HennaHolder: HennaHolder: loaded 180 henna(s) count.
[28.06.25 01:33:26:856]  INFO holder.EnchantItemHolder: EnchantItemHolder: load 4 enchant scroll(s).
[28.06.25 01:33:26:859]  INFO holder.SoulCrystalHolder: SoulCrystalHolder: loaded 54 soul crystal(s) count.
[28.06.25 01:33:26:862]  INFO holder.ArmorSetsHolder: ArmorSetsHolder: loaded 199 armor sets(s) count.
[28.06.25 01:33:26:869]  INFO holder.PetitionGroupHolder: PetitionGroupHolder: loaded 3 petition group(s) count.
[28.06.25 01:33:26:872]  INFO holder.DressArmorHolder: DressArmorHolder: loaded 57 dress armor(s) count.
[28.06.25 01:33:26:874]  INFO holder.DressCloakHolder: DressCloakHolder: loaded 7 dress cloak(s) count.
[28.06.25 01:33:26:876]  INFO holder.DressShieldHolder: DressShieldHolder: loaded 10 dress shield(s) count.
[28.06.25 01:33:26:879]  INFO holder.DressWeaponHolder: DressWeaponHolder: loaded 401 dress weapon(s) count.
[28.06.25 01:33:26:920]  INFO holder.AugmentationDataHolder: AugmentationDataHolder: loaded 1022 augmentation data(s) count.
[28.06.25 01:33:26:924]  INFO holder.PremiumHolder: PremiumHolder: loaded 5 premium(s) count.
[28.06.25 01:33:26:926]  INFO holder.FoundationHolder: FoundationHolder: loaded 268 foundation(s) count.
[28.06.25 01:33:26:931]  INFO holder.DonationHolder: DonationHolder: loaded 192 donation(s) count.
[28.06.25 01:33:26:933]  INFO holder.TournamentMapHolder: TournamentMapHolder: loaded 3 tournament map(s) count.
[28.06.25 01:33:26:941]  INFO holder.FacebookCommentsHolder: FacebookCommentsHolder: loaded 2 facebook comments(s) count.
[28.06.25 01:33:26:941]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Banned HWIDS ]
[28.06.25 01:33:26:942]  INFO security.HWIDBan: black list (Hwid) loaded size: 0
[28.06.25 01:33:26:942]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Clan Crests ]
[28.06.25 01:33:26:943]  INFO cache.CrestCache: CrestCache: Loaded 0 crests
[28.06.25 01:33:26:944]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Loading Images ]
[28.06.25 01:33:26:947]  INFO cache.ImagesCache: ImagesCache: Loading images...
[28.06.25 01:33:45:085]  INFO gameserver.GameServer: ============================================================================
[28.06.25 01:33:45:091]  INFO gameserver.GameServer: Nome: .................................................................... GAME SERVER
[28.06.25 01:33:45:091]  INFO gameserver.GameServer: Project Revision: ........................................................ LII [MultVerso]
[28.06.25 01:33:45:092]  INFO gameserver.GameServer: Update: .................................................................. High Five: Part 5
[28.06.25 01:33:45:092]  INFO gameserver.GameServer: Numero da Revision:....................................................... 2.0
[28.06.25 01:33:45:092]  INFO gameserver.GameServer: Build Revision: .......................................................... MultVerso
[28.06.25 01:33:45:092]  INFO gameserver.GameServer: Build date: .............................................................. 2025.06.28 01:32
[28.06.25 01:33:45:092]  INFO gameserver.GameServer: Compiler version: ........................................................ 17.0.15+9-LTS-241 (Oracle Corporation)
[28.06.25 01:33:45:092]  INFO gameserver.GameServer: ============================================================================
[28.06.25 01:33:45:362]  INFO gameserver.Config: Loaded 6 trade words.
[28.06.25 01:33:45:402]  INFO gameserver.Config: Abuse: Loaded 15 abuse words.
[28.06.25 01:33:45:607]  INFO gameserver.Config: Loaded 313 Configs!
[28.06.25 01:33:45:633]  INFO gameserver.GameServer: IP EXTERNO =============================127.0.0.1
[28.06.25 01:33:45:633]  INFO gameserver.GameServer: IP INTERNO =============================127.0.0.1
[28.06.25 01:33:45:633]  INFO gameserver.GameServer: Maximo Online IP ====================== 3000
[28.06.25 01:33:45:634]  INFO gameserver.GameServer: Hora do Restart  =======================45 5 * * *
[28.06.25 01:33:45:790]  INFO gameserver.GameServer: -----------------------------------------=[ Loading Protection Configuration ]
[28.06.25 01:33:45:844]  INFO idfactory.IdFactory: IdFactory: Clear characters online status.
[28.06.25 01:33:45:898]  INFO idfactory.IdFactory: IdFactory: Cleaned 0 elements from database in 0sec.
[28.06.25 01:33:45:982]  INFO idfactory.IdFactory: IdFactory: Extracted 2 used id's from characters
[28.06.25 01:33:45:982]  INFO idfactory.IdFactory: IdFactory: Extracted 46 used id's from items
[28.06.25 01:33:45:983]  INFO idfactory.IdFactory: IdFactory: Extracted total 48 used id's.
[28.06.25 01:33:45:984]  INFO idfactory.BitSetIDFactory: IdFactory: 102912 id's available.
[28.06.25 01:33:46:685]  INFO gameserver.GameServer: ===============[Loading Scripts]==================
[28.06.25 01:33:46:693]  INFO scripts.Scripts: Scripts: Loading...
[28.06.25 01:33:46:693]  INFO scripts.Scripts: Loading Server Scripts
[28.06.25 01:33:47:133]  INFO scripts.Scripts: Scripts: Loaded 1288 classes.
[28.06.25 01:33:47:167]  INFO vote.VoteMain: Loaded: Vote System - Individual Reward.
[28.06.25 01:33:47:173]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Twitch Manager ]
[28.06.25 01:33:47:323]  INFO gameserver.GameServer: ----------------------------------------------------------=[ AntiFeedManager ]
[28.06.25 01:33:47:330]  INFO gameserver.GameServer: -------------------------------------------------------------=[ QuestManager ]
[28.06.25 01:33:47:331]  INFO gameserver.GameServer: QuestManager : loaded 0 quest's
[28.06.25 01:33:47:364]  INFO tables.FakePlayersTable: FakePlayersTable: Loaded 32002 Fake Players.
[28.06.25 01:33:47:407]  INFO utils.Strings: Loaded 418 translit entries.
[28.06.25 01:33:47:410]  INFO gameserver.GameTimeController: GameTimeController: initialized. Current time is 15:22 in the day.
[28.06.25 01:33:47:410]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Lineage World ]
[28.06.25 01:33:47:413]  INFO model.World: L2World: Creating regions: [256][272][32].
[28.06.25 01:33:47:413]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:33:47:416]  INFO htm.HtmCache: HtmCache: disabled.
[28.06.25 01:33:47:423]  INFO data.StringHolder: StringHolder: load strings: 1066 for lang: ENGLISH
[28.06.25 01:33:47:423]  INFO data.StringHolder: StringHolder: load strings: 876 for lang: RUSSIAN
[28.06.25 01:33:48:928]  INFO skills.SkillsEngine: SkillsEngine: Loaded 66051 skill templates from XML files. Max id: 91000, max level: 259
[28.06.25 01:33:49:573]  INFO holder.OptionDataHolder: OptionDataHolder: loaded 24975 option data(s) count.
[28.06.25 01:33:50:230]  INFO holder.ItemHolder: ItemHolder: loaded 19240 item(s) count.
[28.06.25 01:33:50:237]  INFO holder.FakePlayerNpcsHolder: FakePlayerNpcsHolder: loaded 8 fake player npcs(s) count.
[28.06.25 01:33:51:747]  INFO holder.NpcHolder: NpcHolder: loaded 10519 npc(s) count.
[28.06.25 01:33:51:759]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[28.06.25 01:33:51:775]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[28.06.25 01:33:51:812]  INFO holder.ExchangeItemHolder: ExchangeItemHolder: loaded 602 exchange item(s) count.
[28.06.25 01:33:51:817]  INFO holder.StaticObjectHolder: StaticObjectHolder: loaded 50 static object(s) count.
[28.06.25 01:33:51:937]  INFO holder.DoorHolder: DoorHolder: loaded 1155 door(s) count.
[28.06.25 01:33:52:002]  INFO holder.ZoneHolder: ZoneHolder: loaded 1260 zone(s) count.
[28.06.25 01:33:52:462]  INFO holder.SpawnHolder: SpawnHolder: loaded 28949 spawn(s) count.
[28.06.25 01:33:52:719]  INFO holder.InstantZoneHolder: InstantZoneHolder: loaded 94 instant zone(s) count.
[28.06.25 01:33:53:234]  INFO holder.AirshipDockHolder: AirshipDockHolder: loaded 4 airship dock(s) count.
[28.06.25 01:33:53:300]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 57215 normal learns for 103 classes.
[28.06.25 01:33:53:302]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 79 transfer learns for 3 classes.
[28.06.25 01:33:53:302]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 120 transformation learns for 6 races.
[28.06.25 01:33:53:305]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 662 fishing learns for 6 races.
[28.06.25 01:33:53:306]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 72 certification learns.
[28.06.25 01:33:53:306]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 3 collection learns.
[28.06.25 01:33:53:308]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 44 pledge learns.
[28.06.25 01:33:53:310]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 18 sub unit learns.
[28.06.25 01:33:53:328]  INFO holder.CharTemplateHolder: CharTemplateHolder: loaded 206 char template(s) count.
[28.06.25 01:33:53:518]  INFO holder.ResidenceHolder: ResidenceHolder: total size: 83
[28.06.25 01:33:53:519]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 44 clanhall(s).
[28.06.25 01:33:53:520]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 castle(s).
[28.06.25 01:33:53:521]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 21 fortress(s).
[28.06.25 01:33:53:522]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 dominion(s).
[28.06.25 01:33:53:661]  INFO instancemanager.RaidBossSpawnManager: RaidBossSpawnManager: Loaded 194 Statuses
[28.06.25 01:33:53:701]  INFO entity.SevenSigns: SevenSigns: Currently in the Competition (Quest Event) period!
[28.06.25 01:33:53:701]  INFO entity.SevenSigns: SevenSigns: The Seal of Avarice remains unclaimed.
[28.06.25 01:33:53:703]  INFO entity.SevenSigns: SevenSigns: The Seal of Gnosis remains unclaimed.
[28.06.25 01:33:53:704]  INFO entity.SevenSigns: SevenSigns: The Seal of Strife remains unclaimed.
[28.06.25 01:33:53:704]  INFO entity.SevenSigns: SevenSigns: The Competition this week, if the trend continue, will end with a tie.
[28.06.25 01:33:53:708]  INFO entity.SevenSigns: SevenSigns: Next period begins in 2 days, 16 hours and 26 mins.
[28.06.25 01:33:53:982]  INFO holder.EventHolder: EventHolder: loaded 105 event(s) count.
[28.06.25 01:33:54:017]  INFO holder.FightClubMapHolder: FightClubMapHolder: loaded 42 fight club map(s) count.
[28.06.25 01:33:54:023]  INFO holder.CubicHolder: CubicHolder: loaded 145 cubic(s) count.
[28.06.25 01:33:54:087]  INFO holder.BuyListHolder: TradeController: Loaded 4 file(s).
[28.06.25 01:33:54:087]  INFO holder.BuyListHolder: TradeController: Loaded 25014 Items.
[28.06.25 01:33:54:088]  INFO holder.BuyListHolder: TradeController: Loaded 726 Buylists.
[28.06.25 01:33:54:104]  INFO holder.RecipeHolder: RecipeController: Loaded 998 Recipes.
[28.06.25 01:33:54:726]  INFO holder.ProductHolder: ProductItemTable: Loaded 0 product item on sale.
[28.06.25 01:33:54:736]  INFO holder.HennaHolder: HennaHolder: loaded 180 henna(s) count.
[28.06.25 01:33:54:739]  INFO holder.EnchantItemHolder: EnchantItemHolder: load 4 enchant scroll(s).
[28.06.25 01:33:54:742]  INFO holder.SoulCrystalHolder: SoulCrystalHolder: loaded 54 soul crystal(s) count.
[28.06.25 01:33:54:746]  INFO holder.ArmorSetsHolder: ArmorSetsHolder: loaded 199 armor sets(s) count.
[28.06.25 01:33:54:751]  INFO holder.PetitionGroupHolder: PetitionGroupHolder: loaded 3 petition group(s) count.
[28.06.25 01:33:54:755]  INFO holder.DressArmorHolder: DressArmorHolder: loaded 57 dress armor(s) count.
[28.06.25 01:33:54:758]  INFO holder.DressCloakHolder: DressCloakHolder: loaded 7 dress cloak(s) count.
[28.06.25 01:33:54:760]  INFO holder.DressShieldHolder: DressShieldHolder: loaded 10 dress shield(s) count.
[28.06.25 01:33:54:765]  INFO holder.DressWeaponHolder: DressWeaponHolder: loaded 401 dress weapon(s) count.
[28.06.25 01:33:54:811]  INFO holder.AugmentationDataHolder: AugmentationDataHolder: loaded 1022 augmentation data(s) count.
[28.06.25 01:33:54:816]  INFO holder.PremiumHolder: PremiumHolder: loaded 5 premium(s) count.
[28.06.25 01:33:54:820]  INFO holder.FoundationHolder: FoundationHolder: loaded 268 foundation(s) count.
[28.06.25 01:33:54:826]  INFO holder.DonationHolder: DonationHolder: loaded 192 donation(s) count.
[28.06.25 01:33:54:829]  INFO holder.TournamentMapHolder: TournamentMapHolder: loaded 3 tournament map(s) count.
[28.06.25 01:33:54:835]  INFO holder.FacebookCommentsHolder: FacebookCommentsHolder: loaded 2 facebook comments(s) count.
[28.06.25 01:33:54:836]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Banned HWIDS ]
[28.06.25 01:33:54:838]  INFO security.HWIDBan: black list (Hwid) loaded size: 0
[28.06.25 01:33:54:839]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Clan Crests ]
[28.06.25 01:33:54:841]  INFO cache.CrestCache: CrestCache: Loaded 0 crests
[28.06.25 01:33:54:841]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Loading Images ]
[28.06.25 01:33:54:844]  INFO cache.ImagesCache: ImagesCache: Loading images...
[28.06.25 01:34:25:841]  INFO cache.ImagesCache: ImagesCache: Loaded 8734 images
[28.06.25 01:34:25:841]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:34:25:887]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Fish Table ]
[28.06.25 01:34:25:890]  INFO tables.FishTable: FishTable: Loaded 270 fishes.
[28.06.25 01:34:25:892]  INFO tables.FishTable: FishTable: Loaded 919 fish rewards.
[28.06.25 01:34:25:893]  INFO gameserver.GameServer: -------------------------------------------------------------------=[ Skills ]
[28.06.25 01:34:25:893]  INFO tables.SkillTreeTable: SkillTreeTable: Loaded 526 enchanted skills.
[28.06.25 01:34:26:013]  INFO datatables.EnchantNamesTable: EnchantNamesTable: Loaded: 1319 skill enchant names
[28.06.25 01:34:26:065]  INFO gameserver.GameServer: --------------------------------------------------------=[ Augmentation Data ]
[28.06.25 01:34:26:076]  INFO tables.AugmentationData: Initializing AugmentationData.
[28.06.25 01:34:26:119]  INFO tables.AugmentationData: AugmentationData: 10 bad skill(s) were skipped.
[28.06.25 01:34:26:161]  INFO tables.AugmentationData: AugmentationData: Loaded: 52 augmentation stats.
[28.06.25 01:34:26:164]  INFO tables.AugmentationData: AugmentationData: Loaded: 24 accessory augmentation stats.
[28.06.25 01:34:26:210]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 0
[28.06.25 01:34:26:224]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 1
[28.06.25 01:34:26:224]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 2
[28.06.25 01:34:26:224]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 3
[28.06.25 01:34:26:225]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 4
[28.06.25 01:34:26:225]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 5
[28.06.25 01:34:26:225]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 6
[28.06.25 01:34:26:225]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 7
[28.06.25 01:34:26:225]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 8
[28.06.25 01:34:26:226]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 9
[28.06.25 01:34:26:228]  INFO tables.EnchantHPBonusTable: EnchantHPBonusTable: Loaded bonuses for 6 grades.
[28.06.25 01:34:26:228]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Level Up Table ]
[28.06.25 01:34:26:231]  INFO tables.LevelUpTable: LevelUpData: Loaded 103 Character Level Up Templates.
[28.06.25 01:34:26:234]  INFO tables.PetSkillsTable: PetSkillsTable: Loaded 2558 skills.
[28.06.25 01:34:26:235]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Item Logs ]
[28.06.25 01:34:26:236]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Auctioneer ]
[28.06.25 01:34:26:237]  INFO itemauction.ItemAuctionManager: Initializing ItemAuctionManager
[28.06.25 01:34:26:283]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 1 auction(s) for instance 32320.
[28.06.25 01:34:26:287]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 1 on 2025.07.01 17:00:00 for instance 32320
[28.06.25 01:34:26:288]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 2 auction(s) for instance 32321.
[28.06.25 01:34:26:289]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 4 on 2025.07.03 17:00:00 for instance 32321
[28.06.25 01:34:26:290]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 1 auction(s) for instance 32322.
[28.06.25 01:34:26:290]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 3 on 2025.06.28 17:00:00 for instance 32322
[28.06.25 01:34:26:290]  INFO itemauction.ItemAuctionManager: ItemAuctionManager: Loaded 3 instance(s).
[28.06.25 01:34:26:290]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Masterio Pack ]
[28.06.25 01:34:26:400]  INFO gameserver.GameServer: ------------------------------------------------------=[ Merge System Loaded ]
[28.06.25 01:34:26:424]  INFO bosses.AntharasManager: AntharasManager: State of Antharas is NOTSPAWN.
[28.06.25 01:34:26:425]  INFO bosses.AntharasManager: AntharasManager: Next spawn date of Antharas is 21:00 31.12.1969.
[28.06.25 01:34:26:427]  INFO bosses.BaiumManager: BaiumManager: State of Baium is NOTSPAWN.
[28.06.25 01:34:26:437]  INFO bosses.BaiumManager: BaiumManager: Next spawn date: 21:00 31.12.1969
[28.06.25 01:34:26:492]  INFO bosses.BelethManager: Beleth Manager: Loaded successfully
[28.06.25 01:34:26:497]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Loaded 20 Mysterious-Box spawns.
[28.06.25 01:34:26:505]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 physical monsters spawns.
[28.06.25 01:34:26:510]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 magical monsters spawns.
[28.06.25 01:34:26:513]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 92 Church of duke monsters spawns.
[28.06.25 01:34:26:559]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 68 Emperor's grave NPC spawns.
[28.06.25 01:34:26:561]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Conquerors' Sepulcher Manager
[28.06.25 01:34:26:562]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Emperors' Sepulcher Manager
[28.06.25 01:34:26:563]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Great Sages' Sepulcher Manager
[28.06.25 01:34:26:564]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Judges' Sepulcher Manager
[28.06.25 01:34:26:565]  INFO bosses.FourSepulchersManager: FourSepulchersManager: Beginning in Attack time
[28.06.25 01:34:26:566]  INFO bosses.SailrenManager: SailrenManager: State of Sailren is NOTSPAWN.
[28.06.25 01:34:26:566]  INFO bosses.SailrenManager: SailrenManager: Next spawn date of Sailren is 21:00 31.12.1969.
[28.06.25 01:34:26:568]  INFO bosses.ValakasManager: ValakasManager: State of Valakas is NOTSPAWN.
[28.06.25 01:34:26:569]  INFO bosses.ValakasManager: ValakasManager: Next spawn date of Valakas is 21:00 31.12.1969.
[28.06.25 01:34:26:570]  INFO threading.RunnableImpl: Sat Jun 28 01:34:26 BRT 2025 Atk announce scheduled to 35.0 minute of this hour.
[28.06.25 01:34:27:039]  INFO tables.PetDataTable: PetDataTable: Loaded 3870 pets.
[28.06.25 01:34:27:044]  INFO model.Manor: ManorManager: Loaded 270 seeds
[28.06.25 01:34:27:462]  INFO scriptconfig.ScriptConfig: Loaded Service: ScripsConfig
[28.06.25 01:34:27:463]  INFO community.CharacterServicesCommunity: === CharacterServicesCommunity onLoad called ===
[28.06.25 01:34:27:464]  INFO community.CharacterServicesCommunity: COMMUNITYBOARD_ENABLED: true
[28.06.25 01:34:27:464]  INFO community.CharacterServicesCommunity: EnableMergeCommunity: false
[28.06.25 01:34:27:464]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Character Services Community loaded.
[28.06.25 01:34:27:687]  INFO dailyquests.AbstractDPScript: DailyQuestHandler: Loaded DailyQuests data
[28.06.25 01:34:27:687]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Handler registered successfully.
[28.06.25 01:34:27:697]  INFO model.Player: Loaded Service: SellPcService
[28.06.25 01:34:27:701]  INFO community.CareerManager: CommunityBoard: Manage Career service loaded.
[28.06.25 01:34:27:710]  INFO community.CommunityAuctionHouse: CommunityBoard: Auction System Service loaded.
[28.06.25 01:34:27:749]  INFO community.CommunityAugment: CommunityBoard: Augmentation Manager loaded with 188 lifestone skills.
[28.06.25 01:34:27:763]  INFO community.CommunityBoard: CommunityBoard: service loaded.
[28.06.25 01:34:27:764]  INFO community.CommunityBoardAcademy: CommunityBoard: Academy service loaded.
[28.06.25 01:34:27:765]  INFO community.CommunityBosses: CommunityBoard: Bosses loaded.
[28.06.25 01:34:27:768]  INFO community.CommunityClan: CommunityBoard: Clan service loaded.
[28.06.25 01:34:27:769]  INFO community.CommunityDropCalculator: CommunityBoard: Drop Calculator service loaded.
[28.06.25 01:34:27:770]  INFO community.CommunityEvents: CommunityBoard: Community Events service loaded.
[28.06.25 01:34:27:770]  INFO community.CommunityFacebook: CommunityBoard: Facebook Rewards page loaded.
[28.06.25 01:34:27:772]  INFO community.CommunityNpcs: CommunityBoard: Npcs loaded.
[28.06.25 01:34:27:773]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[28.06.25 01:34:27:779]  INFO community.CommunityTournament: CommunityBoard: Tournament loaded.
[28.06.25 01:34:27:779]  INFO community.CommunityWarehouse: CommunityBoard: Warehouse loaded.
[28.06.25 01:34:27:780]  INFO community.Forge: CommunityBoard: Forge loaded.
[28.06.25 01:34:27:781]  INFO community.ServicesCommunity: ServicesCommunity: Services Community service loaded.
[28.06.25 01:34:27:826]  INFO gameserver.GameServer: ===============[Spawn Manager]==================
[28.06.25 01:34:28:089]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1000 npc for group: NONE
[28.06.25 01:34:28:749]  INFO instancemanager.SpawnManager: SpawnManager: spawned 5000 npc for group: NONE
[28.06.25 01:34:28:870]  INFO naia.NaiaTowerManager: Naia Tower Manager: Loaded 12 rooms
[28.06.25 01:34:29:125]  INFO instancemanager.SpawnManager: SpawnManager: spawned 7000 npc for group: NONE
[28.06.25 01:34:29:910]  INFO instancemanager.SpawnManager: SpawnManager: spawned 12000 npc for group: NONE
[28.06.25 01:34:30:128]  INFO instancemanager.SpawnManager: SpawnManager: spawned 13000 npc for group: NONE
[28.06.25 01:34:32:769]  INFO instancemanager.SpawnManager: SpawnManager: spawned 21000 npc for group: NONE
[28.06.25 01:34:33:212]  INFO instancemanager.SpawnManager: SpawnManager: spawned 22000 npc for group: NONE
[28.06.25 01:34:35:066]  INFO instancemanager.SpawnManager: SpawnManager: spawned 25000 npc for group: NONE
[28.06.25 01:34:35:751]  INFO instancemanager.SpawnManager: SpawnManager: spawned 26000 npc for group: NONE
[28.06.25 01:34:37:212]  INFO instancemanager.SpawnManager: SpawnManager: spawned 28000 npc for group: NONE
[28.06.25 01:34:39:982]  INFO instancemanager.SpawnManager: SpawnManager: spawned 32000 npc for group: NONE
[28.06.25 01:34:40:782]  INFO instancemanager.SpawnManager: SpawnManager: spawned 35000 npc for group: NONE
[28.06.25 01:34:41:072]  INFO instancemanager.SpawnManager: SpawnManager: spawned 36000 npc for group: NONE
[28.06.25 01:34:41:370]  INFO instancemanager.SpawnManager: SpawnManager: spawned 37000 npc for group: NONE
[28.06.25 01:34:41:784]  INFO instancemanager.SpawnManager: SpawnManager: spawned 38373 npc; spawns: 17087; group: NONE
[28.06.25 01:34:41:784]  INFO gameserver.GameServer: --------------------------------------------------------------------=[ Boats ]
[28.06.25 01:34:41:785]  INFO data.BoatHolder: BoatHolder: loaded 2 boat(s) count.
[28.06.25 01:34:41:793]  INFO data.BoatHolder: BoatHolder: Spawning: RunePrimeval_Vehicle
[28.06.25 01:34:41:793]  INFO data.BoatHolder: BoatHolder: Spawning: AdenGracia_Airship
[28.06.25 01:34:41:794]  INFO holder.StaticObjectHolder: StaticObjectHolder: spawned: 50 static object(s).
[28.06.25 01:34:41:794]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Dimensional Rift ]
[28.06.25 01:34:41:848]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 13 room types with 112 rooms.
[28.06.25 01:34:41:848]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 300 DimensionalRift spawns, 0 errors.
[28.06.25 01:34:41:858]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Seven Signs ]
[28.06.25 01:34:41:930]  INFO instancemanager.AutoSpawnManager: AutoSpawnHandler: Loaded 50 handlers in total.
[28.06.25 01:34:41:931]  INFO gameserver.GameServer: ===================================================================
[28.06.25 01:34:41:932]  INFO gameserver.GameServer: ===================[Loading Olympiad System]=======================
[28.06.25 01:34:41:971]  INFO olympiad.Olympiad: Olympiad System: Loading Olympiad System....
[28.06.25 01:34:41:971]  INFO olympiad.Olympiad: Olympiad System: Currently in Validation Period
[28.06.25 01:34:41:972]  INFO olympiad.Olympiad: Olympiad System: Period Ends....
[28.06.25 01:34:41:973]  INFO olympiad.Olympiad: Olympiad System: In 0 days, 10 hours and 27 mins.
[28.06.25 01:34:41:973]  INFO olympiad.Olympiad: Olympiad System: Loaded 0 Noblesses
[28.06.25 01:34:41:976]  INFO entity.Hero: Hero System: Loaded 0 Heroes.
[28.06.25 01:34:41:976]  INFO entity.Hero: Hero System: Loaded 0 all time Heroes.
[28.06.25 01:34:41:976]  INFO gameserver.GameServer: ===================[Olympiad System Loaded]=======================
[28.06.25 01:34:41:976]  INFO gameserver.GameServer: ===================================================================
[28.06.25 01:34:41:977]  INFO instancemanager.PetitionManager: Initializing PetitionManager
[28.06.25 01:34:41:982]  INFO instancemanager.CursedWeaponsManager: CursedWeaponsManager: Loaded 2 cursed weapon(s).
[28.06.25 01:34:41:982]  INFO gameserver.GameServer: ------------------------------------------=[ Loaded Small Achievement System ]
[28.06.25 01:34:41:993]  INFO gameserver.GameServer: --------------------------------------------------------------=[ ItemHandler ]
[28.06.25 01:34:41:993]  INFO items.ItemHandler: ItemHandler: loaded 0 item handler(s) count.
[28.06.25 01:34:41:993]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Admin Commands ]
[28.06.25 01:34:41:993]  INFO admincommands.AdminCommandHandler: AdminCommandHandler: loaded 475 admin command handler(s) count.
[28.06.25 01:34:41:994]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Players Commands ]
[28.06.25 01:34:41:998]  INFO usercommands.UserCommandHandler: UserCommandHandler: loaded 17 user command handler(s) count.
[28.06.25 01:34:41:998]  INFO voicecommands.VoicedCommandHandler: VoicedCommandHandler: loaded 102 voiced command handler(s) count.
[28.06.25 01:34:42:001]  INFO gameserver.GameServer: ======================[Tournament By Kara`]==========================
[28.06.25 01:34:42:004]  INFO tournament.TournamentHolder: [TournamentHolder] Initialized 5 tournament holders:
[28.06.25 01:34:42:004]  INFO tournament.TournamentHolder: 1 vs 1
[28.06.25 01:34:42:004]  INFO tournament.TournamentHolder: 2 vs 2
[28.06.25 01:34:42:005]  INFO tournament.TournamentHolder: 4 vs 4
[28.06.25 01:34:42:005]  INFO tournament.TournamentHolder: 5 vs 5
[28.06.25 01:34:42:005]  INFO tournament.TournamentHolder: 6 vs 6
[28.06.25 01:34:42:006]  INFO gameserver.GameServer: ======================[Loading Castels & Clan Halls]==========================
[28.06.25 01:34:42:040]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Gludio Castle time - 16:00 29.06.2025
[28.06.25 01:34:42:044]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Dion Castle time - 16:00 29.06.2025
[28.06.25 01:34:42:048]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Giran Castle time - 20:00 29.06.2025
[28.06.25 01:34:42:052]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Oren Castle time - 20:00 29.06.2025
[28.06.25 01:34:42:056]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Aden Castle time - 16:00 29.06.2025
[28.06.25 01:34:42:062]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Innadril Castle time - 20:00 29.06.2025
[28.06.25 01:34:42:067]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Goddard Castle time - 20:00 29.06.2025
[28.06.25 01:34:42:072]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Rune Castle time - 16:00 29.06.2025
[28.06.25 01:34:42:076]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Schuttgart Castle time - 16:00 29.06.2025
[28.06.25 01:34:42:078]  INFO impl.ClanHallNpcSiegeEvent: ClanHallNpcSiegeEvent: Fortress Of Resistance time - 18:00 30.06.2025
[28.06.25 01:34:42:093]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:34:42:096]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:34:42:100]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Topaz Hall time - 15:00 02.07.2025
[28.06.25 01:34:42:104]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Ruby Hall time - 15:00 02.07.2025
[28.06.25 01:34:42:107]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Crystal Hall time - 15:00 02.07.2025
[28.06.25 01:34:42:111]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:34:42:116]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[28.06.25 01:34:42:120]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:34:42:125]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[28.06.25 01:34:42:130]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Atramental Barracks time - 15:00 02.07.2025
[28.06.25 01:34:42:137]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Scarlet Barracks time - 15:00 02.07.2025
[28.06.25 01:34:42:139]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Viridian Barracks time - 15:00 02.07.2025
[28.06.25 01:34:42:140]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Devastated Castle time - 18:00 28.06.2025
[28.06.25 01:34:42:140]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Bandit Stronghold time - 22:00 28.06.2025
[28.06.25 01:34:42:142]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[28.06.25 01:34:42:145]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[28.06.25 01:34:42:147]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[28.06.25 01:34:42:149]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[28.06.25 01:34:42:152]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Gold Manor time - 15:00 02.07.2025
[28.06.25 01:34:42:175]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[28.06.25 01:34:42:177]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[28.06.25 01:34:42:178]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[28.06.25 01:34:42:180]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[28.06.25 01:34:42:182]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[28.06.25 01:34:42:184]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[28.06.25 01:34:42:187]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:34:42:191]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:34:42:194]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[28.06.25 01:34:42:198]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[28.06.25 01:34:42:202]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Mont Chamber time - 15:00 02.07.2025
[28.06.25 01:34:42:206]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Astaire Chamber time - 15:00 02.07.2025
[28.06.25 01:34:42:209]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Aria Chamber time - 15:00 02.07.2025
[28.06.25 01:34:42:213]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Yiana Chamber time - 15:00 02.07.2025
[28.06.25 01:34:42:217]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Roien Chamber time - 15:00 02.07.2025
[28.06.25 01:34:42:221]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Luna Chamber time - 15:00 02.07.2025
[28.06.25 01:34:42:226]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Traban Chamber time - 15:00 02.07.2025
[28.06.25 01:34:42:231]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Eisen Hall time - 15:00 02.07.2025
[28.06.25 01:34:42:234]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Heavy Metal Hall time - 15:00 02.07.2025
[28.06.25 01:34:42:237]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Molten Ore Hall time - 15:00 02.07.2025
[28.06.25 01:34:42:239]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Titan Hall time - 15:00 02.07.2025
[28.06.25 01:34:42:240]  INFO impl.ClanHallMiniGameEvent: ClanHallMiniGameEvent: Rainbow Springs time - 22:00 28.06.2025
[28.06.25 01:34:42:241]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Wild Beast Reserve time - 21:00 28.06.2025
[28.06.25 01:34:42:241]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Fortress of Dead time - 18:00 28.06.2025
[28.06.25 01:34:42:242]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Gludio Dominion time - undefined
[28.06.25 01:34:42:243]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Dion Dominion time - undefined
[28.06.25 01:34:42:243]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Giran Dominion time - undefined
[28.06.25 01:34:42:244]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Oren Dominion time - undefined
[28.06.25 01:34:42:244]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Aden Dominion time - undefined
[28.06.25 01:34:42:244]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Innadril Dominion time - undefined
[28.06.25 01:34:42:245]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Goddard Dominion time - undefined
[28.06.25 01:34:42:245]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Rune Dominion time - undefined
[28.06.25 01:34:42:246]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Schuttgart Dominion time - undefined
[28.06.25 01:34:42:250]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Shanty Fortress time - undefined
[28.06.25 01:34:42:254]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Southern Fortress time - undefined
[28.06.25 01:34:42:258]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hive Fortress time - undefined
[28.06.25 01:34:42:262]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Valley Fortress time - undefined
[28.06.25 01:34:42:265]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Ivory Fortress time - undefined
[28.06.25 01:34:42:268]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Narsell Fortress time - undefined
[28.06.25 01:34:42:272]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Bayou Fortress time - undefined
[28.06.25 01:34:42:276]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: White Sands Fortress time - undefined
[28.06.25 01:34:42:280]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Borderland Fortress time - undefined
[28.06.25 01:34:42:284]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Swamp Fortress time - undefined
[28.06.25 01:34:42:288]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Archaic Fortress time - undefined
[28.06.25 01:34:42:293]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Floran Fortress time - undefined
[28.06.25 01:34:42:298]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Cloud Mountain Fortress time - undefined
[28.06.25 01:34:42:303]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Tanor Fortress time - undefined
[28.06.25 01:34:42:308]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Dragonspine Fortress time - undefined
[28.06.25 01:34:42:318]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Antharas Fortress time - undefined
[28.06.25 01:34:42:324]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Western Fortress time - undefined
[28.06.25 01:34:42:331]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hunter's Fortress time - undefined
[28.06.25 01:34:42:337]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Aaru Fortress time - undefined
[28.06.25 01:34:42:344]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Demon Fortress time - undefined
[28.06.25 01:34:42:352]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Monastic Fortress time - undefined
[28.06.25 01:34:42:353]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 70-75 Level time - 02:00 28.06.2025
[28.06.25 01:34:42:353]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 76-79 Level time - 02:00 28.06.2025
[28.06.25 01:34:42:354]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 80-85 Level time - 02:00 28.06.2025
[28.06.25 01:34:42:354]  INFO fightclub.LastManStandingEvent: LastManStandingEvent: Last Hero time - undefined
[28.06.25 01:34:42:354]  INFO fightclub.TeamVSTeamEvent: TeamVSTeamEvent: Team VS Team time - undefined
[28.06.25 01:34:42:354]  INFO fightclub.LuckyCreaturesEvent: LuckyCreaturesEvent: Lucky Creatures time - undefined
[28.06.25 01:34:42:354]  INFO fightclub.CaptureTheFlagEvent: CaptureTheFlagEvent: Capture The Flag time - undefined
[28.06.25 01:34:42:354]  INFO fightclub.FFATreasureHuntEvent: FFATreasureHuntEvent: Treasure Hunt time - undefined
[28.06.25 01:34:42:355]  INFO fightclub.KoreanStyleEvent: KoreanStyleEvent: Korean Style time - undefined
[28.06.25 01:34:42:356]  INFO fightclub.ProtectTheKingEvent: ProtectTheKingEvent: Protect The King time - undefined
[28.06.25 01:34:42:356]  INFO fightclub.FightForThroneEvent: FightForThroneEvent: Fight For Throne time - undefined
[28.06.25 01:34:42:356]  INFO fightclub.DeathMatchEvent: DeathMatchEvent: Death Match time - undefined
[28.06.25 01:34:42:356]  INFO fightclub.RussianRouletteEvent: RussianRouletteEvent: Russian Roulette time - undefined
[28.06.25 01:34:42:357]  INFO instancemanager.CastleManorManager: Manor System: Initializing...
[28.06.25 01:34:42:359]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:34:42:359]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Auto Cleaner ]
[28.06.25 01:34:42:359]  INFO gameserver.GameServer: IdFactory: Free ObjectID's remaining: 1879004651
[28.06.25 01:34:42:359]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:34:42:360]  INFO instancemanager.CoupleManager: Initializing CoupleManager
[28.06.25 01:34:42:360]  INFO instancemanager.CoupleManager: Loaded: 0 couples(s)
[28.06.25 01:34:42:362]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Hellbound ]
[28.06.25 01:34:42:369]  INFO instancemanager.HellboundManager: HellboundManager: Loaded 335 spawn entries.
[28.06.25 01:34:42:499]  INFO instancemanager.HellboundManager: HellboundManager: Spawned 623 mobs and NPCs according to the current Hellbound stage
[28.06.25 01:34:42:499]  INFO instancemanager.HellboundManager: Hellbound Manager: Loaded
[28.06.25 01:34:42:500]  INFO naia.NaiaTowerManager: Naia Core Manager: Loaded
[28.06.25 01:34:42:501]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:34:42:501]  INFO instancemanager.SoDManager: Seed of Destruction Manager: Loaded
[28.06.25 01:34:42:502]  INFO instancemanager.SoIManager: Seed of Infinity Manager: Loaded. Current stage is: 1
[28.06.25 01:34:42:503]  INFO instancemanager.SpawnManager: SpawnManager: spawned 4 npc; spawns: 4; group: soi_world_mouths
[28.06.25 01:34:42:503]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: soi_world_abyssgaze2
[28.06.25 01:34:42:504]  INFO instancemanager.BloodAltarManager: Blood Altar Manager: Initializing...
[28.06.25 01:34:42:512]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_alive_npc
[28.06.25 01:34:42:515]  INFO gameserver.GameServer: Preparing Drop Calculator
[28.06.25 01:34:42:577]  INFO gameserver.GameServer: ----------------------------------------------------------=[ Offline Buffers ]
[28.06.25 01:34:42:577]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loading offline buffers...
[28.06.25 01:34:42:578]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loaded: 0 offline buffer(s)
[28.06.25 01:34:42:604]  INFO achievements.Achievements: Achievement System: Loaded 4 achievement categories and 39 achievements.
[28.06.25 01:34:42:605]  INFO gameserver.GameServer: Emotions Loaded....
[28.06.25 01:34:42:606]  INFO gameserver.GameServer: --------------------------------------------------=[ DataBase Cleaner Loaded ]
[28.06.25 01:34:42:606]  INFO dao.CharacterDAO: Found 0 characters to delete!
[28.06.25 01:34:42:689]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Initializing
[28.06.25 01:34:42:690]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Load OK
[28.06.25 01:34:42:691]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Loaded 0 announce.
[28.06.25 01:34:42:692]  INFO gameserver.GameServer: ---------------------------------------------------=[ Loaded Facebook System ]
[28.06.25 01:34:42:694]  INFO gameserver.GameServer: -----------------------------------------------------=[ Loaded Stream System ]
[28.06.25 01:34:42:698]  INFO holder.ProxiesHolder: ProxiesHolder: loaded 20 proxies(s) count.
[28.06.25 01:34:42:699]  INFO gameserver.GameServer: ------------------------------------------------------=[ Loaded Proxy System ]
[28.06.25 01:34:45:151]  INFO donation.DonationReader: Donation Reader service started
[28.06.25 01:34:45:151]  INFO gameserver.GameServer: --------------------------------------------=[ Loaded AUTO - Donation System ]
[28.06.25 01:34:45:154]  INFO taskmanager.BackupTaskManager: Next Auto Backup in 5 H., 5 Min.
[28.06.25 01:34:45:163]  INFO votingengine.VotingSettings: VotingSettings: Loaded 3 messages, 4 drops!
[28.06.25 01:34:45:165]  INFO votingengine.VotingRewardAPI: VoteReward: Has been successfully loaded!
[28.06.25 01:34:45:166]  INFO model.PhantomPlayers: Loading phantom players...
[28.06.25 01:34:45:172]  INFO model.PhantomPlayers: Loaded 32002 possible phantom names.
[28.06.25 01:34:45:172]  INFO model.PhantomPlayers: Loaded 7172 possible phantom titles.
[28.06.25 01:34:45:173]  INFO model.PhantomPlayers: PhantomPlayers: Cached 0 players.
[28.06.25 01:34:45:173]  INFO model.PhantomPlayers: PhantomPlayers: Free names 32002.
[28.06.25 01:34:45:174]  INFO model.PhantomPlayers: Loaded 0 phantom players from database with a maximum of 3000 phantoms.
[28.06.25 01:34:45:174]  INFO model.PhantomPlayers: Scheduled spawner with 1 seconds delay.
[28.06.25 01:34:45:338]  INFO instancemanager.SpawnManager: SpawnManager: spawned 443 npc; spawns: 121; group: DAY
[28.06.25 01:34:45:414]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dawn_spawn
[28.06.25 01:34:45:648]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dusk_spawn
[28.06.25 01:34:45:648]  INFO gameserver.GameServer: Telnet server is currently disabled.
[28.06.25 01:34:45:650]  INFO gameserver.Shutdown: Scheduled server restart in 4h 10m 15s.
[28.06.25 01:34:45:651]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:34:45:651]  INFO gameserver.GameServer: >>>>>>>>>>>>>>> GameServer Started <<<<<<<<<<<<<<
[28.06.25 01:34:45:651]  INFO gameserver.GameServer: Maximum Numbers of Connected Players: 3000
[28.06.25 01:34:45:651]  INFO gameserver.GameServer: ====================================================================
[28.06.25 01:34:45:663]  INFO gameserver.GameServer: AllowedMemory: ........... 6291456 KB
[28.06.25 01:34:45:663]  INFO gameserver.GameServer: 
Allocated: ............... 4194304 KB (66.6667%)
[28.06.25 01:34:45:663]  INFO gameserver.GameServer: 
Non-Allocated: ........... 2097152 KB (33.3333%)
[28.06.25 01:34:45:664]  INFO gameserver.GameServer: 
AllocatedMemory: ......... 4194304 KB
[28.06.25 01:34:45:665]  INFO gameserver.GameServer: Used: .................... 1156908 KB (18.3886%)
[28.06.25 01:34:45:665]  INFO gameserver.GameServer: 
Unused (cached): ......... 3037395 KB (48.2781%)
[28.06.25 01:34:45:665]  INFO gameserver.GameServer: 
UseableMemory: ........... 5134547 KB (81.6114%)
[28.06.25 01:34:45:666]  INFO gameserver.GameServer: 

[28.06.25 01:34:45:666]  INFO gameserver.GameServer: ====================================================================
[28.06.25 01:34:45:668]  INFO loginservercon.AuthServerCommunication: Connecting to authserver on 127.0.0.1:9014
[28.06.25 01:34:45:677]  INFO lspackets.AuthResponse: Registered on authserver as 1 [Bartz]
[28.06.25 01:35:22:614]  INFO community.CharacterServicesCommunity: onBypassCommand called - bypass: _bbscharservices;registeremail
[28.06.25 01:35:22:614]  INFO community.CharacterServicesCommunity: cmd: _bbscharservices, action: registeremail
[28.06.25 01:35:33:182]  INFO community.CharacterServicesCommunity: === onWriteCommand called ===
[28.06.25 01:35:33:182]  INFO community.CharacterServicesCommunity: Player: Dzu
[28.06.25 01:35:33:182]  INFO community.CharacterServicesCommunity: Bypass: '_bbscharservices;registeremail;submit'
[28.06.25 01:35:33:182]  INFO community.CharacterServicesCommunity: arg1: '0'
[28.06.25 01:35:33:182]  INFO community.CharacterServicesCommunity: arg2: '0'
[28.06.25 01:35:33:183]  INFO community.CharacterServicesCommunity: arg3: '<EMAIL>'
[28.06.25 01:35:33:183]  INFO community.CharacterServicesCommunity: arg4: '<EMAIL>'
[28.06.25 01:35:33:183]  INFO community.CharacterServicesCommunity: arg5: ''
[28.06.25 01:35:33:183]  INFO community.CharacterServicesCommunity: Processing email registration - email: <EMAIL>, confirmEmail: <EMAIL>
[28.06.25 01:35:33:196]  INFO utils.EmailVerification: Verification code stored for account: siniez, email: <EMAIL>
[28.06.25 01:35:33:196]  INFO community.CharacterServicesCommunity: About to send email - Config.MAIL_USER: '<EMAIL>', Config.MAIL_PASS: 'SET'
[28.06.25 01:35:33:196]  INFO utils.EmailVerification: Checking email configuration - MAIL_USER: '<EMAIL>', MAIL_PASS: 'SET'
[28.06.25 01:35:34:160]  INFO utils.EmailVerification: SMTP Initial: 220 smtp.gmail.com ESMTP af79cd13be357-7d44317e671sm241084985a.41 - gsmtp
[28.06.25 01:35:34:364]  INFO utils.EmailVerification: SMTP EHLO: 250-smtp.gmail.com at your service, [***************]
250-SIZE ********
250-8BITMIME
250-STARTTLS
250-ENHANCEDSTATUSCODES
250-PIPELINING
250-CHUNKING
250 SMTPUTF8

[28.06.25 01:35:34:563]  INFO utils.EmailVerification: SMTP STARTTLS: 220 2.0.0 Ready to start TLS
[28.06.25 01:35:34:900]  INFO utils.EmailVerification: SMTP EHLO after TLS: 250-smtp.gmail.com at your service, [***************]
250-SIZE ********
250-8BITMIME
250-AUTH LOGIN PLAIN XOAUTH2 PLAIN-CLIENTTOKEN OAUTHBEARER XOAUTH
250-ENHANCEDSTATUSCODES
250-PIPELINING
250-CHUNKING
250 SMTPUTF8

[28.06.25 01:35:35:099]  INFO utils.EmailVerification: SMTP AUTH LOGIN: 334 VXNlcm5hbWU6
[28.06.25 01:35:35:299]  INFO utils.EmailVerification: SMTP Username: 334 UGFzc3dvcmQ6
[28.06.25 01:35:35:648]  INFO utils.EmailVerification: SMTP Password: 235 2.7.0 Accepted
[28.06.25 01:35:35:848]  INFO utils.EmailVerification: SMTP MAIL FROM: 250 2.1.0 OK af79cd13be357-7d44317e671sm241084985a.41 - gsmtp
[28.06.25 01:35:36:052]  INFO utils.EmailVerification: SMTP RCPT TO: 250 2.1.5 OK af79cd13be357-7d44317e671sm241084985a.41 - gsmtp
[28.06.25 01:35:36:418]  INFO utils.EmailVerification: SMTP DATA: 354 Go ahead af79cd13be357-7d44317e671sm241084985a.41 - gsmtp
[28.06.25 01:35:37:233]  INFO utils.EmailVerification: SMTP Email sent: 250 2.0.0 OK  1751085338 af79cd13be357-7d44317e671sm241084985a.41 - gsmtp
[28.06.25 01:35:37:432]  INFO utils.EmailVerification: SMTP QUIT: 221 2.0.0 closing connection af79cd13be357-7d44317e671sm241084985a.41 - gsmtp
[28.06.25 01:35:37:432]  INFO utils.EmailVerification: Email sent successfully to: <EMAIL> for player: Dzu
[28.06.25 01:36:22:692]  INFO tasks.RestoreOfflineTraders: Restored 0 offline traders
[28.06.25 01:36:30:827]  INFO community.CharacterServicesCommunity: onBypassCommand called - bypass: _bbscharservices;verifyemail;submit;$verificationcode
[28.06.25 01:36:30:827]  INFO community.CharacterServicesCommunity: cmd: _bbscharservices, action: verifyemail
[28.06.25 01:36:30:828]  INFO community.CharacterServicesCommunity: Processing email verification via bypass - code: '$verificationcode'
[28.06.25 01:36:30:828]  INFO community.CharacterServicesCommunity: Processing email verification - code: '$verificationcode', length: 17
[28.06.25 01:36:45:862]  INFO community.CharacterServicesCommunity: onBypassCommand called - bypass: _bbscharservices;registeremail
[28.06.25 01:36:45:862]  INFO community.CharacterServicesCommunity: cmd: _bbscharservices, action: registeremail
[28.06.25 01:36:59:739]  INFO community.CharacterServicesCommunity: === onWriteCommand called ===
[28.06.25 01:36:59:740]  INFO community.CharacterServicesCommunity: Player: Dzu
[28.06.25 01:36:59:740]  INFO community.CharacterServicesCommunity: Bypass: '_bbscharservices;registeremail;submit'
[28.06.25 01:36:59:740]  INFO community.CharacterServicesCommunity: arg1: '0'
[28.06.25 01:36:59:740]  INFO community.CharacterServicesCommunity: arg2: '0'
[28.06.25 01:36:59:740]  INFO community.CharacterServicesCommunity: arg3: '<EMAIL>'
[28.06.25 01:36:59:740]  INFO community.CharacterServicesCommunity: arg4: '<EMAIL>'
[28.06.25 01:36:59:740]  INFO community.CharacterServicesCommunity: arg5: ''
[28.06.25 01:36:59:741]  INFO community.CharacterServicesCommunity: Processing email registration - email: <EMAIL>, confirmEmail: <EMAIL>
[28.06.25 01:36:59:763]  INFO utils.EmailVerification: Verification code stored for account: siniez, email: <EMAIL>
[28.06.25 01:36:59:763]  INFO community.CharacterServicesCommunity: About to send email - Config.MAIL_USER: '<EMAIL>', Config.MAIL_PASS: 'SET'
[28.06.25 01:36:59:763]  INFO utils.EmailVerification: Checking email configuration - MAIL_USER: '<EMAIL>', MAIL_PASS: 'SET'
[28.06.25 01:37:00:050]  INFO utils.EmailVerification: SMTP Initial: 220 smtp.gmail.com ESMTP 6a1803df08f44-6fd771bf9a1sm31558266d6.41 - gsmtp
[28.06.25 01:37:00:255]  INFO utils.EmailVerification: SMTP EHLO: 250-smtp.gmail.com at your service, [***************]
250-SIZE ********
250-8BITMIME
250-STARTTLS
250-ENHANCEDSTATUSCODES
250-PIPELINING
250-CHUNKING
250 SMTPUTF8

[28.06.25 01:37:00:457]  INFO utils.EmailVerification: SMTP STARTTLS: 220 2.0.0 Ready to start TLS
[28.06.25 01:37:00:803]  INFO utils.EmailVerification: SMTP EHLO after TLS: 250-smtp.gmail.com at your service, [***************]
250-SIZE ********
250-8BITMIME
250-AUTH LOGIN PLAIN XOAUTH2 PLAIN-CLIENTTOKEN OAUTHBEARER XOAUTH
250-ENHANCEDSTATUSCODES
250-PIPELINING
250-CHUNKING
250 SMTPUTF8

[28.06.25 01:37:01:013]  INFO utils.EmailVerification: SMTP AUTH LOGIN: 334 VXNlcm5hbWU6
[28.06.25 01:37:01:215]  INFO utils.EmailVerification: SMTP Username: 334 UGFzc3dvcmQ6
[28.06.25 01:37:01:562]  INFO utils.EmailVerification: SMTP Password: 235 2.7.0 Accepted
[28.06.25 01:37:01:767]  INFO utils.EmailVerification: SMTP MAIL FROM: 250 2.1.0 OK 6a1803df08f44-6fd771bf9a1sm31558266d6.41 - gsmtp
[28.06.25 01:37:01:970]  INFO utils.EmailVerification: SMTP RCPT TO: 250 2.1.5 OK 6a1803df08f44-6fd771bf9a1sm31558266d6.41 - gsmtp
[28.06.25 01:37:02:294]  INFO utils.EmailVerification: SMTP DATA: 354 Go ahead 6a1803df08f44-6fd771bf9a1sm31558266d6.41 - gsmtp
[28.06.25 01:37:02:954]  INFO utils.EmailVerification: SMTP Email sent: 250 2.0.0 OK  1751085424 6a1803df08f44-6fd771bf9a1sm31558266d6.41 - gsmtp
[28.06.25 01:37:03:156]  INFO utils.EmailVerification: SMTP QUIT: 221 2.0.0 closing connection 6a1803df08f44-6fd771bf9a1sm31558266d6.41 - gsmtp
[28.06.25 01:37:03:156]  INFO utils.EmailVerification: Email sent successfully to: <EMAIL> for player: Dzu
[28.06.25 01:37:26:137]  INFO community.CharacterServicesCommunity: onBypassCommand called - bypass: _bbscharservices;verifyemail;submit;$verificationcode
[28.06.25 01:37:26:137]  INFO community.CharacterServicesCommunity: cmd: _bbscharservices, action: verifyemail
[28.06.25 01:37:26:138]  INFO community.CharacterServicesCommunity: Processing email verification via bypass - code: '$verificationcode'
[28.06.25 01:37:26:138]  INFO community.CharacterServicesCommunity: Processing email verification - code: '$verificationcode', length: 17
[28.06.25 01:37:46:852]  INFO community.CharacterServicesCommunity: onBypassCommand called - bypass: _bbscharservices;verifyemail;submit;$verificationcode
[28.06.25 01:37:46:852]  INFO community.CharacterServicesCommunity: cmd: _bbscharservices, action: verifyemail
[28.06.25 01:37:46:853]  INFO community.CharacterServicesCommunity: Processing email verification via bypass - code: '$verificationcode'
[28.06.25 01:37:46:853]  INFO community.CharacterServicesCommunity: Processing email verification - code: '$verificationcode', length: 17
[28.06.25 01:38:17:463]  INFO community.CharacterServicesCommunity: onBypassCommand called - bypass: _bbscharservices;verifyemail;submit;$verificationcode
[28.06.25 01:38:17:463]  INFO community.CharacterServicesCommunity: cmd: _bbscharservices, action: verifyemail
[28.06.25 01:38:17:464]  INFO community.CharacterServicesCommunity: Processing email verification via bypass - code: '$verificationcode'
[28.06.25 01:38:17:464]  INFO community.CharacterServicesCommunity: Processing email verification - code: '$verificationcode', length: 17
[28.06.25 01:38:20:056]  INFO community.CharacterServicesCommunity: onBypassCommand called - bypass: _bbscharservices;verifyemail;resend
[28.06.25 01:38:20:056]  INFO community.CharacterServicesCommunity: cmd: _bbscharservices, action: verifyemail
[28.06.25 01:38:20:076]  INFO utils.EmailVerification: Verification code stored for account: siniez, email: <EMAIL>
[28.06.25 01:38:20:076]  INFO utils.EmailVerification: Checking email configuration - MAIL_USER: '<EMAIL>', MAIL_PASS: 'SET'
[28.06.25 01:38:21:037]  INFO utils.EmailVerification: SMTP Initial: 220 smtp.gmail.com ESMTP 6a1803df08f44-6fd7730aea1sm31649846d6.112 - gsmtp
[28.06.25 01:38:21:248]  INFO utils.EmailVerification: SMTP EHLO: 250-smtp.gmail.com at your service, [***************]
250-SIZE ********
250-8BITMIME
250-STARTTLS
250-ENHANCEDSTATUSCODES
250-PIPELINING
250-CHUNKING
250 SMTPUTF8

[28.06.25 01:38:21:455]  INFO utils.EmailVerification: SMTP STARTTLS: 220 2.0.0 Ready to start TLS
[28.06.25 01:38:21:798]  INFO utils.EmailVerification: SMTP EHLO after TLS: 250-smtp.gmail.com at your service, [***************]
250-SIZE ********
250-8BITMIME
250-AUTH LOGIN PLAIN XOAUTH2 PLAIN-CLIENTTOKEN OAUTHBEARER XOAUTH
250-ENHANCEDSTATUSCODES
250-PIPELINING
250-CHUNKING
250 SMTPUTF8

[28.06.25 01:38:22:005]  INFO utils.EmailVerification: SMTP AUTH LOGIN: 334 VXNlcm5hbWU6
[28.06.25 01:38:22:212]  INFO utils.EmailVerification: SMTP Username: 334 UGFzc3dvcmQ6
[28.06.25 01:38:22:560]  INFO utils.EmailVerification: SMTP Password: 235 2.7.0 Accepted
[28.06.25 01:38:22:773]  INFO utils.EmailVerification: SMTP MAIL FROM: 250 2.1.0 OK 6a1803df08f44-6fd7730aea1sm31649846d6.112 - gsmtp
[28.06.25 01:38:22:981]  INFO utils.EmailVerification: SMTP RCPT TO: 250 2.1.5 OK 6a1803df08f44-6fd7730aea1sm31649846d6.112 - gsmtp
[28.06.25 01:38:23:299]  INFO utils.EmailVerification: SMTP DATA: 354 Go ahead 6a1803df08f44-6fd7730aea1sm31649846d6.112 - gsmtp
[28.06.25 01:38:23:894]  INFO utils.EmailVerification: SMTP Email sent: 250 2.0.0 OK  1751085505 6a1803df08f44-6fd7730aea1sm31649846d6.112 - gsmtp
[28.06.25 01:38:24:101]  INFO utils.EmailVerification: SMTP QUIT: 221 2.0.0 closing connection 6a1803df08f44-6fd7730aea1sm31649846d6.112 - gsmtp
[28.06.25 01:38:24:101]  INFO utils.EmailVerification: Email sent successfully to: <EMAIL> for player: Dzu
[28.06.25 01:38:52:510]  INFO community.CharacterServicesCommunity: onBypassCommand called - bypass: _bbscharservices;verifyemail;submit;$verificationcode
[28.06.25 01:38:52:510]  INFO community.CharacterServicesCommunity: cmd: _bbscharservices, action: verifyemail
[28.06.25 01:38:52:511]  INFO community.CharacterServicesCommunity: Processing email verification via bypass - code: '$verificationcode'
[28.06.25 01:38:52:511]  INFO community.CharacterServicesCommunity: Processing email verification - code: '$verificationcode', length: 17
[28.06.25 01:43:58:960]  INFO gameserver.GameServer: ============================================================================
[28.06.25 01:43:58:962]  INFO gameserver.GameServer: Nome: .................................................................... GAME SERVER
[28.06.25 01:43:58:962]  INFO gameserver.GameServer: Project Revision: ........................................................ LII [MultVerso]
[28.06.25 01:43:58:962]  INFO gameserver.GameServer: Update: .................................................................. High Five: Part 5
[28.06.25 01:43:58:962]  INFO gameserver.GameServer: Numero da Revision:....................................................... 2.0
[28.06.25 01:43:58:963]  INFO gameserver.GameServer: Build Revision: .......................................................... MultVerso
[28.06.25 01:43:58:963]  INFO gameserver.GameServer: Build date: .............................................................. 2025.06.28 01:42
[28.06.25 01:43:58:963]  INFO gameserver.GameServer: Compiler version: ........................................................ 17.0.15+9-LTS-241 (Oracle Corporation)
[28.06.25 01:43:58:963]  INFO gameserver.GameServer: ============================================================================
[28.06.25 01:43:59:014]  INFO gameserver.Config: Loaded 6 trade words.
[28.06.25 01:43:59:027]  INFO gameserver.Config: Abuse: Loaded 15 abuse words.
[28.06.25 01:43:59:099]  INFO gameserver.Config: Loaded 313 Configs!
[28.06.25 01:43:59:107]  INFO gameserver.GameServer: IP EXTERNO =============================127.0.0.1
[28.06.25 01:43:59:107]  INFO gameserver.GameServer: IP INTERNO =============================127.0.0.1
[28.06.25 01:43:59:107]  INFO gameserver.GameServer: Maximo Online IP ====================== 3000
[28.06.25 01:43:59:109]  INFO gameserver.GameServer: Hora do Restart  =======================45 5 * * *
[28.06.25 01:43:59:285]  INFO gameserver.GameServer: -----------------------------------------=[ Loading Protection Configuration ]
[28.06.25 01:43:59:290]  INFO idfactory.IdFactory: IdFactory: Clear characters online status.
[28.06.25 01:43:59:308]  INFO idfactory.IdFactory: IdFactory: Cleaned 0 elements from database in 0sec.
[28.06.25 01:43:59:320]  INFO idfactory.IdFactory: IdFactory: Extracted 2 used id's from characters
[28.06.25 01:43:59:321]  INFO idfactory.IdFactory: IdFactory: Extracted 46 used id's from items
[28.06.25 01:43:59:322]  INFO idfactory.IdFactory: IdFactory: Extracted total 48 used id's.
[28.06.25 01:43:59:323]  INFO idfactory.BitSetIDFactory: IdFactory: 102912 id's available.
[28.06.25 01:43:59:483]  INFO gameserver.GameServer: ===============[Loading Scripts]==================
[28.06.25 01:43:59:484]  INFO scripts.Scripts: Scripts: Loading...
[28.06.25 01:43:59:484]  INFO scripts.Scripts: Loading Server Scripts
[28.06.25 01:43:59:833]  INFO scripts.Scripts: Scripts: Loaded 1288 classes.
[28.06.25 01:43:59:843]  INFO vote.VoteMain: Loaded: Vote System - Individual Reward.
[28.06.25 01:43:59:844]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Twitch Manager ]
[28.06.25 01:43:59:877]  INFO gameserver.GameServer: ----------------------------------------------------------=[ AntiFeedManager ]
[28.06.25 01:43:59:878]  INFO gameserver.GameServer: -------------------------------------------------------------=[ QuestManager ]
[28.06.25 01:43:59:879]  INFO gameserver.GameServer: QuestManager : loaded 0 quest's
[28.06.25 01:43:59:891]  INFO tables.FakePlayersTable: FakePlayersTable: Loaded 32002 Fake Players.
[28.06.25 01:43:59:900]  INFO utils.Strings: Loaded 418 translit entries.
[28.06.25 01:43:59:903]  INFO gameserver.GameTimeController: GameTimeController: initialized. Current time is 16:23 in the day.
[28.06.25 01:43:59:903]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Lineage World ]
[28.06.25 01:43:59:907]  INFO model.World: L2World: Creating regions: [256][272][32].
[28.06.25 01:43:59:907]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:43:59:909]  INFO htm.HtmCache: HtmCache: disabled.
[28.06.25 01:43:59:918]  INFO data.StringHolder: StringHolder: load strings: 1066 for lang: ENGLISH
[28.06.25 01:43:59:918]  INFO data.StringHolder: StringHolder: load strings: 876 for lang: RUSSIAN
[28.06.25 01:44:01:499]  INFO skills.SkillsEngine: SkillsEngine: Loaded 66051 skill templates from XML files. Max id: 91000, max level: 259
[28.06.25 01:44:02:285]  INFO holder.OptionDataHolder: OptionDataHolder: loaded 24975 option data(s) count.
[28.06.25 01:44:02:950]  INFO holder.ItemHolder: ItemHolder: loaded 19240 item(s) count.
[28.06.25 01:44:02:956]  INFO holder.FakePlayerNpcsHolder: FakePlayerNpcsHolder: loaded 8 fake player npcs(s) count.
[28.06.25 01:44:04:010]  INFO holder.NpcHolder: NpcHolder: loaded 10519 npc(s) count.
[28.06.25 01:44:04:017]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[28.06.25 01:44:04:033]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[28.06.25 01:44:04:059]  INFO holder.ExchangeItemHolder: ExchangeItemHolder: loaded 602 exchange item(s) count.
[28.06.25 01:44:04:063]  INFO holder.StaticObjectHolder: StaticObjectHolder: loaded 50 static object(s) count.
[28.06.25 01:44:04:156]  INFO holder.DoorHolder: DoorHolder: loaded 1155 door(s) count.
[28.06.25 01:44:04:209]  INFO holder.ZoneHolder: ZoneHolder: loaded 1260 zone(s) count.
[28.06.25 01:44:04:533]  INFO holder.SpawnHolder: SpawnHolder: loaded 28949 spawn(s) count.
[28.06.25 01:44:04:623]  INFO holder.InstantZoneHolder: InstantZoneHolder: loaded 94 instant zone(s) count.
[28.06.25 01:44:04:920]  INFO holder.AirshipDockHolder: AirshipDockHolder: loaded 4 airship dock(s) count.
[28.06.25 01:44:04:962]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 57215 normal learns for 103 classes.
[28.06.25 01:44:04:962]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 79 transfer learns for 3 classes.
[28.06.25 01:44:04:963]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 120 transformation learns for 6 races.
[28.06.25 01:44:04:963]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 662 fishing learns for 6 races.
[28.06.25 01:44:04:963]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 72 certification learns.
[28.06.25 01:44:04:963]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 3 collection learns.
[28.06.25 01:44:04:963]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 44 pledge learns.
[28.06.25 01:44:04:964]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 18 sub unit learns.
[28.06.25 01:44:04:971]  INFO holder.CharTemplateHolder: CharTemplateHolder: loaded 206 char template(s) count.
[28.06.25 01:44:05:057]  INFO holder.ResidenceHolder: ResidenceHolder: total size: 83
[28.06.25 01:44:05:057]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 44 clanhall(s).
[28.06.25 01:44:05:058]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 castle(s).
[28.06.25 01:44:05:058]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 21 fortress(s).
[28.06.25 01:44:05:058]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 dominion(s).
[28.06.25 01:44:05:120]  INFO instancemanager.RaidBossSpawnManager: RaidBossSpawnManager: Loaded 194 Statuses
[28.06.25 01:44:05:134]  INFO entity.SevenSigns: SevenSigns: Currently in the Competition (Quest Event) period!
[28.06.25 01:44:05:134]  INFO entity.SevenSigns: SevenSigns: The Seal of Avarice remains unclaimed.
[28.06.25 01:44:05:135]  INFO entity.SevenSigns: SevenSigns: The Seal of Gnosis remains unclaimed.
[28.06.25 01:44:05:135]  INFO entity.SevenSigns: SevenSigns: The Seal of Strife remains unclaimed.
[28.06.25 01:44:05:135]  INFO entity.SevenSigns: SevenSigns: The Competition this week, if the trend continue, will end with a tie.
[28.06.25 01:44:05:136]  INFO entity.SevenSigns: SevenSigns: Next period begins in 2 days, 16 hours and 15 mins.
[28.06.25 01:44:05:248]  INFO holder.EventHolder: EventHolder: loaded 105 event(s) count.
[28.06.25 01:44:05:261]  INFO holder.FightClubMapHolder: FightClubMapHolder: loaded 42 fight club map(s) count.
[28.06.25 01:44:05:264]  INFO holder.CubicHolder: CubicHolder: loaded 145 cubic(s) count.
[28.06.25 01:44:05:301]  INFO holder.BuyListHolder: TradeController: Loaded 4 file(s).
[28.06.25 01:44:05:301]  INFO holder.BuyListHolder: TradeController: Loaded 25014 Items.
[28.06.25 01:44:05:302]  INFO holder.BuyListHolder: TradeController: Loaded 726 Buylists.
[28.06.25 01:44:05:316]  INFO holder.RecipeHolder: RecipeController: Loaded 998 Recipes.
[28.06.25 01:44:05:684]  INFO holder.ProductHolder: ProductItemTable: Loaded 0 product item on sale.
[28.06.25 01:44:05:693]  INFO holder.HennaHolder: HennaHolder: loaded 180 henna(s) count.
[28.06.25 01:44:05:697]  INFO holder.EnchantItemHolder: EnchantItemHolder: load 4 enchant scroll(s).
[28.06.25 01:44:05:699]  INFO holder.SoulCrystalHolder: SoulCrystalHolder: loaded 54 soul crystal(s) count.
[28.06.25 01:44:05:703]  INFO holder.ArmorSetsHolder: ArmorSetsHolder: loaded 199 armor sets(s) count.
[28.06.25 01:44:05:710]  INFO holder.PetitionGroupHolder: PetitionGroupHolder: loaded 3 petition group(s) count.
[28.06.25 01:44:05:713]  INFO holder.DressArmorHolder: DressArmorHolder: loaded 57 dress armor(s) count.
[28.06.25 01:44:05:714]  INFO holder.DressCloakHolder: DressCloakHolder: loaded 7 dress cloak(s) count.
[28.06.25 01:44:05:716]  INFO holder.DressShieldHolder: DressShieldHolder: loaded 10 dress shield(s) count.
[28.06.25 01:44:05:719]  INFO holder.DressWeaponHolder: DressWeaponHolder: loaded 401 dress weapon(s) count.
[28.06.25 01:44:05:758]  INFO holder.AugmentationDataHolder: AugmentationDataHolder: loaded 1022 augmentation data(s) count.
[28.06.25 01:44:05:762]  INFO holder.PremiumHolder: PremiumHolder: loaded 5 premium(s) count.
[28.06.25 01:44:05:764]  INFO holder.FoundationHolder: FoundationHolder: loaded 268 foundation(s) count.
[28.06.25 01:44:05:771]  INFO holder.DonationHolder: DonationHolder: loaded 192 donation(s) count.
[28.06.25 01:44:05:774]  INFO holder.TournamentMapHolder: TournamentMapHolder: loaded 3 tournament map(s) count.
[28.06.25 01:44:05:789]  INFO holder.FacebookCommentsHolder: FacebookCommentsHolder: loaded 2 facebook comments(s) count.
[28.06.25 01:44:05:789]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Banned HWIDS ]
[28.06.25 01:44:05:791]  INFO security.HWIDBan: black list (Hwid) loaded size: 0
[28.06.25 01:44:05:792]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Clan Crests ]
[28.06.25 01:44:05:793]  INFO cache.CrestCache: CrestCache: Loaded 0 crests
[28.06.25 01:44:05:793]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Loading Images ]
[28.06.25 01:44:05:795]  INFO cache.ImagesCache: ImagesCache: Loading images...
[28.06.25 01:44:16:487]  INFO cache.ImagesCache: ImagesCache: Loaded 8734 images
[28.06.25 01:44:16:487]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:44:16:492]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Fish Table ]
[28.06.25 01:44:16:495]  INFO tables.FishTable: FishTable: Loaded 270 fishes.
[28.06.25 01:44:16:497]  INFO tables.FishTable: FishTable: Loaded 919 fish rewards.
[28.06.25 01:44:16:498]  INFO gameserver.GameServer: -------------------------------------------------------------------=[ Skills ]
[28.06.25 01:44:16:498]  INFO tables.SkillTreeTable: SkillTreeTable: Loaded 526 enchanted skills.
[28.06.25 01:44:16:524]  INFO datatables.EnchantNamesTable: EnchantNamesTable: Loaded: 1319 skill enchant names
[28.06.25 01:44:16:524]  INFO gameserver.GameServer: --------------------------------------------------------=[ Augmentation Data ]
[28.06.25 01:44:16:526]  INFO tables.AugmentationData: Initializing AugmentationData.
[28.06.25 01:44:16:537]  INFO tables.AugmentationData: AugmentationData: 10 bad skill(s) were skipped.
[28.06.25 01:44:16:550]  INFO tables.AugmentationData: AugmentationData: Loaded: 52 augmentation stats.
[28.06.25 01:44:16:551]  INFO tables.AugmentationData: AugmentationData: Loaded: 24 accessory augmentation stats.
[28.06.25 01:44:16:553]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 0
[28.06.25 01:44:16:554]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 1
[28.06.25 01:44:16:554]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 2
[28.06.25 01:44:16:554]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 3
[28.06.25 01:44:16:554]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 4
[28.06.25 01:44:16:555]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 5
[28.06.25 01:44:16:555]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 6
[28.06.25 01:44:16:555]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 7
[28.06.25 01:44:16:555]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 8
[28.06.25 01:44:16:556]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 9
[28.06.25 01:44:16:558]  INFO tables.EnchantHPBonusTable: EnchantHPBonusTable: Loaded bonuses for 6 grades.
[28.06.25 01:44:16:558]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Level Up Table ]
[28.06.25 01:44:16:561]  INFO tables.LevelUpTable: LevelUpData: Loaded 103 Character Level Up Templates.
[28.06.25 01:44:16:563]  INFO tables.PetSkillsTable: PetSkillsTable: Loaded 2558 skills.
[28.06.25 01:44:16:564]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Item Logs ]
[28.06.25 01:44:16:565]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Auctioneer ]
[28.06.25 01:44:16:566]  INFO itemauction.ItemAuctionManager: Initializing ItemAuctionManager
[28.06.25 01:44:16:576]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 1 auction(s) for instance 32320.
[28.06.25 01:44:16:579]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 1 on 2025.07.01 17:00:00 for instance 32320
[28.06.25 01:44:16:581]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 2 auction(s) for instance 32321.
[28.06.25 01:44:16:581]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 4 on 2025.07.03 17:00:00 for instance 32321
[28.06.25 01:44:16:582]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 1 auction(s) for instance 32322.
[28.06.25 01:44:16:582]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 3 on 2025.06.28 17:00:00 for instance 32322
[28.06.25 01:44:16:583]  INFO itemauction.ItemAuctionManager: ItemAuctionManager: Loaded 3 instance(s).
[28.06.25 01:44:16:583]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Masterio Pack ]
[28.06.25 01:44:16:619]  INFO gameserver.GameServer: ------------------------------------------------------=[ Merge System Loaded ]
[28.06.25 01:44:16:640]  INFO bosses.AntharasManager: AntharasManager: State of Antharas is NOTSPAWN.
[28.06.25 01:44:16:641]  INFO bosses.AntharasManager: AntharasManager: Next spawn date of Antharas is 21:00 31.12.1969.
[28.06.25 01:44:16:642]  INFO bosses.BaiumManager: BaiumManager: State of Baium is NOTSPAWN.
[28.06.25 01:44:16:646]  INFO bosses.BaiumManager: BaiumManager: Next spawn date: 21:00 31.12.1969
[28.06.25 01:44:16:659]  INFO bosses.BelethManager: Beleth Manager: Loaded successfully
[28.06.25 01:44:16:660]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Loaded 20 Mysterious-Box spawns.
[28.06.25 01:44:16:664]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 physical monsters spawns.
[28.06.25 01:44:16:667]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 magical monsters spawns.
[28.06.25 01:44:16:668]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 92 Church of duke monsters spawns.
[28.06.25 01:44:16:670]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 68 Emperor's grave NPC spawns.
[28.06.25 01:44:16:671]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Conquerors' Sepulcher Manager
[28.06.25 01:44:16:671]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Emperors' Sepulcher Manager
[28.06.25 01:44:16:672]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Great Sages' Sepulcher Manager
[28.06.25 01:44:16:672]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Judges' Sepulcher Manager
[28.06.25 01:44:16:673]  INFO bosses.FourSepulchersManager: FourSepulchersManager: Beginning in Attack time
[28.06.25 01:44:16:674]  INFO bosses.SailrenManager: SailrenManager: State of Sailren is NOTSPAWN.
[28.06.25 01:44:16:674]  INFO bosses.SailrenManager: SailrenManager: Next spawn date of Sailren is 21:00 31.12.1969.
[28.06.25 01:44:16:675]  INFO bosses.ValakasManager: ValakasManager: State of Valakas is NOTSPAWN.
[28.06.25 01:44:16:675]  INFO bosses.ValakasManager: ValakasManager: Next spawn date of Valakas is 21:00 31.12.1969.
[28.06.25 01:44:16:682]  INFO threading.RunnableImpl: Sat Jun 28 01:44:16 BRT 2025 Atk announce scheduled to 45.0 minute of this hour.
[28.06.25 01:44:16:783]  INFO tables.PetDataTable: PetDataTable: Loaded 3870 pets.
[28.06.25 01:44:16:786]  INFO model.Manor: ManorManager: Loaded 270 seeds
[28.06.25 01:44:16:880]  INFO scriptconfig.ScriptConfig: Loaded Service: ScripsConfig
[28.06.25 01:44:16:880]  INFO community.CharacterServicesCommunity: === CharacterServicesCommunity onLoad called ===
[28.06.25 01:44:16:881]  INFO community.CharacterServicesCommunity: COMMUNITYBOARD_ENABLED: true
[28.06.25 01:44:16:881]  INFO community.CharacterServicesCommunity: EnableMergeCommunity: false
[28.06.25 01:44:16:881]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Character Services Community loaded.
[28.06.25 01:44:16:941]  INFO dailyquests.AbstractDPScript: DailyQuestHandler: Loaded DailyQuests data
[28.06.25 01:44:16:941]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Handler registered successfully.
[28.06.25 01:44:16:947]  INFO model.Player: Loaded Service: SellPcService
[28.06.25 01:44:16:949]  INFO community.CareerManager: CommunityBoard: Manage Career service loaded.
[28.06.25 01:44:16:951]  INFO community.CommunityAuctionHouse: CommunityBoard: Auction System Service loaded.
[28.06.25 01:44:16:955]  INFO community.CommunityAugment: CommunityBoard: Augmentation Manager loaded with 188 lifestone skills.
[28.06.25 01:44:16:955]  INFO community.CommunityBoard: CommunityBoard: service loaded.
[28.06.25 01:44:16:956]  INFO community.CommunityBoardAcademy: CommunityBoard: Academy service loaded.
[28.06.25 01:44:16:957]  INFO community.CommunityBosses: CommunityBoard: Bosses loaded.
[28.06.25 01:44:16:959]  INFO community.CommunityClan: CommunityBoard: Clan service loaded.
[28.06.25 01:44:16:959]  INFO community.CommunityDropCalculator: CommunityBoard: Drop Calculator service loaded.
[28.06.25 01:44:16:959]  INFO community.CommunityEvents: CommunityBoard: Community Events service loaded.
[28.06.25 01:44:16:960]  INFO community.CommunityFacebook: CommunityBoard: Facebook Rewards page loaded.
[28.06.25 01:44:16:961]  INFO community.CommunityNpcs: CommunityBoard: Npcs loaded.
[28.06.25 01:44:16:961]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[28.06.25 01:44:16:966]  INFO community.CommunityTournament: CommunityBoard: Tournament loaded.
[28.06.25 01:44:16:966]  INFO community.CommunityWarehouse: CommunityBoard: Warehouse loaded.
[28.06.25 01:44:16:967]  INFO community.Forge: CommunityBoard: Forge loaded.
[28.06.25 01:44:16:968]  INFO community.ServicesCommunity: ServicesCommunity: Services Community service loaded.
[28.06.25 01:44:16:973]  INFO gameserver.GameServer: ===============[Spawn Manager]==================
[28.06.25 01:44:17:047]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1000 npc for group: NONE
[28.06.25 01:44:17:188]  INFO instancemanager.SpawnManager: SpawnManager: spawned 5000 npc for group: NONE
[28.06.25 01:44:17:212]  INFO naia.NaiaTowerManager: Naia Tower Manager: Loaded 12 rooms
[28.06.25 01:44:17:287]  INFO instancemanager.SpawnManager: SpawnManager: spawned 7000 npc for group: NONE
[28.06.25 01:44:17:622]  INFO instancemanager.SpawnManager: SpawnManager: spawned 12000 npc for group: NONE
[28.06.25 01:44:17:704]  INFO instancemanager.SpawnManager: SpawnManager: spawned 13000 npc for group: NONE
[28.06.25 01:44:18:562]  INFO instancemanager.SpawnManager: SpawnManager: spawned 21000 npc for group: NONE
[28.06.25 01:44:18:689]  INFO instancemanager.SpawnManager: SpawnManager: spawned 22000 npc for group: NONE
[28.06.25 01:44:19:145]  INFO instancemanager.SpawnManager: SpawnManager: spawned 25000 npc for group: NONE
[28.06.25 01:44:19:318]  INFO instancemanager.SpawnManager: SpawnManager: spawned 26000 npc for group: NONE
[28.06.25 01:44:19:688]  INFO instancemanager.SpawnManager: SpawnManager: spawned 28000 npc for group: NONE
[28.06.25 01:44:20:587]  INFO instancemanager.SpawnManager: SpawnManager: spawned 32000 npc for group: NONE
[28.06.25 01:44:21:541]  INFO instancemanager.SpawnManager: SpawnManager: spawned 35000 npc for group: NONE
[28.06.25 01:44:21:867]  INFO instancemanager.SpawnManager: SpawnManager: spawned 36000 npc for group: NONE
[28.06.25 01:44:22:165]  INFO instancemanager.SpawnManager: SpawnManager: spawned 37000 npc for group: NONE
[28.06.25 01:44:22:607]  INFO instancemanager.SpawnManager: SpawnManager: spawned 38373 npc; spawns: 17087; group: NONE
[28.06.25 01:44:22:607]  INFO gameserver.GameServer: --------------------------------------------------------------------=[ Boats ]
[28.06.25 01:44:22:608]  INFO data.BoatHolder: BoatHolder: loaded 2 boat(s) count.
[28.06.25 01:44:22:609]  INFO data.BoatHolder: BoatHolder: Spawning: RunePrimeval_Vehicle
[28.06.25 01:44:22:609]  INFO data.BoatHolder: BoatHolder: Spawning: AdenGracia_Airship
[28.06.25 01:44:22:611]  INFO holder.StaticObjectHolder: StaticObjectHolder: spawned: 50 static object(s).
[28.06.25 01:44:22:611]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Dimensional Rift ]
[28.06.25 01:44:22:615]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 13 room types with 112 rooms.
[28.06.25 01:44:22:615]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 300 DimensionalRift spawns, 0 errors.
[28.06.25 01:44:22:619]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Seven Signs ]
[28.06.25 01:44:22:627]  INFO instancemanager.AutoSpawnManager: AutoSpawnHandler: Loaded 50 handlers in total.
[28.06.25 01:44:22:627]  INFO gameserver.GameServer: ===================================================================
[28.06.25 01:44:22:628]  INFO gameserver.GameServer: ===================[Loading Olympiad System]=======================
[28.06.25 01:44:22:632]  INFO olympiad.Olympiad: Olympiad System: Loading Olympiad System....
[28.06.25 01:44:22:632]  INFO olympiad.Olympiad: Olympiad System: Currently in Validation Period
[28.06.25 01:44:22:632]  INFO olympiad.Olympiad: Olympiad System: Period Ends....
[28.06.25 01:44:22:632]  INFO olympiad.Olympiad: Olympiad System: In 0 days, 10 hours and 17 mins.
[28.06.25 01:44:22:633]  INFO olympiad.Olympiad: Olympiad System: Loaded 0 Noblesses
[28.06.25 01:44:22:635]  INFO entity.Hero: Hero System: Loaded 0 Heroes.
[28.06.25 01:44:22:635]  INFO entity.Hero: Hero System: Loaded 0 all time Heroes.
[28.06.25 01:44:22:635]  INFO gameserver.GameServer: ===================[Olympiad System Loaded]=======================
[28.06.25 01:44:22:635]  INFO gameserver.GameServer: ===================================================================
[28.06.25 01:44:22:636]  INFO instancemanager.PetitionManager: Initializing PetitionManager
[28.06.25 01:44:22:641]  INFO instancemanager.CursedWeaponsManager: CursedWeaponsManager: Loaded 2 cursed weapon(s).
[28.06.25 01:44:22:641]  INFO gameserver.GameServer: ------------------------------------------=[ Loaded Small Achievement System ]
[28.06.25 01:44:22:649]  INFO gameserver.GameServer: --------------------------------------------------------------=[ ItemHandler ]
[28.06.25 01:44:22:649]  INFO items.ItemHandler: ItemHandler: loaded 0 item handler(s) count.
[28.06.25 01:44:22:650]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Admin Commands ]
[28.06.25 01:44:22:650]  INFO admincommands.AdminCommandHandler: AdminCommandHandler: loaded 475 admin command handler(s) count.
[28.06.25 01:44:22:650]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Players Commands ]
[28.06.25 01:44:22:654]  INFO usercommands.UserCommandHandler: UserCommandHandler: loaded 17 user command handler(s) count.
[28.06.25 01:44:22:654]  INFO voicecommands.VoicedCommandHandler: VoicedCommandHandler: loaded 102 voiced command handler(s) count.
[28.06.25 01:44:22:657]  INFO gameserver.GameServer: ======================[Tournament By Kara`]==========================
[28.06.25 01:44:22:659]  INFO tournament.TournamentHolder: [TournamentHolder] Initialized 5 tournament holders:
[28.06.25 01:44:22:659]  INFO tournament.TournamentHolder: 1 vs 1
[28.06.25 01:44:22:659]  INFO tournament.TournamentHolder: 2 vs 2
[28.06.25 01:44:22:659]  INFO tournament.TournamentHolder: 4 vs 4
[28.06.25 01:44:22:660]  INFO tournament.TournamentHolder: 5 vs 5
[28.06.25 01:44:22:660]  INFO tournament.TournamentHolder: 6 vs 6
[28.06.25 01:44:22:661]  INFO gameserver.GameServer: ======================[Loading Castels & Clan Halls]==========================
[28.06.25 01:44:22:691]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Gludio Castle time - 16:00 29.06.2025
[28.06.25 01:44:22:695]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Dion Castle time - 16:00 29.06.2025
[28.06.25 01:44:22:698]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Giran Castle time - 20:00 29.06.2025
[28.06.25 01:44:22:701]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Oren Castle time - 20:00 29.06.2025
[28.06.25 01:44:22:705]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Aden Castle time - 16:00 29.06.2025
[28.06.25 01:44:22:711]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Innadril Castle time - 20:00 29.06.2025
[28.06.25 01:44:22:716]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Goddard Castle time - 20:00 29.06.2025
[28.06.25 01:44:22:720]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Rune Castle time - 16:00 29.06.2025
[28.06.25 01:44:22:724]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Schuttgart Castle time - 16:00 29.06.2025
[28.06.25 01:44:22:726]  INFO impl.ClanHallNpcSiegeEvent: ClanHallNpcSiegeEvent: Fortress Of Resistance time - 18:00 30.06.2025
[28.06.25 01:44:22:727]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:44:22:729]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:44:22:730]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Topaz Hall time - 15:00 02.07.2025
[28.06.25 01:44:22:732]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Ruby Hall time - 15:00 02.07.2025
[28.06.25 01:44:22:733]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Crystal Hall time - 15:00 02.07.2025
[28.06.25 01:44:22:735]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:44:22:736]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[28.06.25 01:44:22:737]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:44:22:738]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[28.06.25 01:44:22:740]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Atramental Barracks time - 15:00 02.07.2025
[28.06.25 01:44:22:742]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Scarlet Barracks time - 15:00 02.07.2025
[28.06.25 01:44:22:743]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Viridian Barracks time - 15:00 02.07.2025
[28.06.25 01:44:22:744]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Devastated Castle time - 18:00 28.06.2025
[28.06.25 01:44:22:744]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Bandit Stronghold time - 22:00 28.06.2025
[28.06.25 01:44:22:745]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[28.06.25 01:44:22:746]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[28.06.25 01:44:22:748]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[28.06.25 01:44:22:749]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[28.06.25 01:44:22:750]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Gold Manor time - 15:00 02.07.2025
[28.06.25 01:44:22:752]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[28.06.25 01:44:22:753]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[28.06.25 01:44:22:754]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[28.06.25 01:44:22:755]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[28.06.25 01:44:22:756]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[28.06.25 01:44:22:758]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[28.06.25 01:44:22:759]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:44:22:760]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:44:22:762]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[28.06.25 01:44:22:763]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[28.06.25 01:44:22:764]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Mont Chamber time - 15:00 02.07.2025
[28.06.25 01:44:22:765]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Astaire Chamber time - 15:00 02.07.2025
[28.06.25 01:44:22:767]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Aria Chamber time - 15:00 02.07.2025
[28.06.25 01:44:22:768]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Yiana Chamber time - 15:00 02.07.2025
[28.06.25 01:44:22:769]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Roien Chamber time - 15:00 02.07.2025
[28.06.25 01:44:22:770]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Luna Chamber time - 15:00 02.07.2025
[28.06.25 01:44:22:771]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Traban Chamber time - 15:00 02.07.2025
[28.06.25 01:44:22:772]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Eisen Hall time - 15:00 02.07.2025
[28.06.25 01:44:22:773]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Heavy Metal Hall time - 15:00 02.07.2025
[28.06.25 01:44:22:775]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Molten Ore Hall time - 15:00 02.07.2025
[28.06.25 01:44:22:776]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Titan Hall time - 15:00 02.07.2025
[28.06.25 01:44:22:777]  INFO impl.ClanHallMiniGameEvent: ClanHallMiniGameEvent: Rainbow Springs time - 22:00 28.06.2025
[28.06.25 01:44:22:777]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Wild Beast Reserve time - 21:00 28.06.2025
[28.06.25 01:44:22:777]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Fortress of Dead time - 18:00 28.06.2025
[28.06.25 01:44:22:779]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Gludio Dominion time - undefined
[28.06.25 01:44:22:779]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Dion Dominion time - undefined
[28.06.25 01:44:22:779]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Giran Dominion time - undefined
[28.06.25 01:44:22:780]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Oren Dominion time - undefined
[28.06.25 01:44:22:780]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Aden Dominion time - undefined
[28.06.25 01:44:22:780]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Innadril Dominion time - undefined
[28.06.25 01:44:22:781]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Goddard Dominion time - undefined
[28.06.25 01:44:22:781]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Rune Dominion time - undefined
[28.06.25 01:44:22:781]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Schuttgart Dominion time - undefined
[28.06.25 01:44:22:784]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Shanty Fortress time - undefined
[28.06.25 01:44:22:786]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Southern Fortress time - undefined
[28.06.25 01:44:22:788]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hive Fortress time - undefined
[28.06.25 01:44:22:790]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Valley Fortress time - undefined
[28.06.25 01:44:22:793]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Ivory Fortress time - undefined
[28.06.25 01:44:22:795]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Narsell Fortress time - undefined
[28.06.25 01:44:22:798]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Bayou Fortress time - undefined
[28.06.25 01:44:22:800]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: White Sands Fortress time - undefined
[28.06.25 01:44:22:804]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Borderland Fortress time - undefined
[28.06.25 01:44:22:807]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Swamp Fortress time - undefined
[28.06.25 01:44:22:809]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Archaic Fortress time - undefined
[28.06.25 01:44:22:812]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Floran Fortress time - undefined
[28.06.25 01:44:22:814]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Cloud Mountain Fortress time - undefined
[28.06.25 01:44:22:816]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Tanor Fortress time - undefined
[28.06.25 01:44:22:819]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Dragonspine Fortress time - undefined
[28.06.25 01:44:22:822]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Antharas Fortress time - undefined
[28.06.25 01:44:22:824]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Western Fortress time - undefined
[28.06.25 01:44:22:827]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hunter's Fortress time - undefined
[28.06.25 01:44:22:829]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Aaru Fortress time - undefined
[28.06.25 01:44:22:832]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Demon Fortress time - undefined
[28.06.25 01:44:22:835]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Monastic Fortress time - undefined
[28.06.25 01:44:22:836]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 70-75 Level time - 02:00 28.06.2025
[28.06.25 01:44:22:836]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 76-79 Level time - 02:00 28.06.2025
[28.06.25 01:44:22:837]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 80-85 Level time - 02:00 28.06.2025
[28.06.25 01:44:22:837]  INFO fightclub.LastManStandingEvent: LastManStandingEvent: Last Hero time - undefined
[28.06.25 01:44:22:837]  INFO fightclub.TeamVSTeamEvent: TeamVSTeamEvent: Team VS Team time - undefined
[28.06.25 01:44:22:837]  INFO fightclub.LuckyCreaturesEvent: LuckyCreaturesEvent: Lucky Creatures time - undefined
[28.06.25 01:44:22:837]  INFO fightclub.CaptureTheFlagEvent: CaptureTheFlagEvent: Capture The Flag time - undefined
[28.06.25 01:44:22:837]  INFO fightclub.FFATreasureHuntEvent: FFATreasureHuntEvent: Treasure Hunt time - undefined
[28.06.25 01:44:22:838]  INFO fightclub.KoreanStyleEvent: KoreanStyleEvent: Korean Style time - undefined
[28.06.25 01:44:22:838]  INFO fightclub.ProtectTheKingEvent: ProtectTheKingEvent: Protect The King time - undefined
[28.06.25 01:44:22:838]  INFO fightclub.FightForThroneEvent: FightForThroneEvent: Fight For Throne time - undefined
[28.06.25 01:44:22:838]  INFO fightclub.DeathMatchEvent: DeathMatchEvent: Death Match time - undefined
[28.06.25 01:44:22:838]  INFO fightclub.RussianRouletteEvent: RussianRouletteEvent: Russian Roulette time - undefined
[28.06.25 01:44:22:839]  INFO instancemanager.CastleManorManager: Manor System: Initializing...
[28.06.25 01:44:22:841]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:44:22:841]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Auto Cleaner ]
[28.06.25 01:44:22:841]  INFO gameserver.GameServer: IdFactory: Free ObjectID's remaining: 1879004837
[28.06.25 01:44:22:842]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:44:22:842]  INFO instancemanager.CoupleManager: Initializing CoupleManager
[28.06.25 01:44:22:842]  INFO instancemanager.CoupleManager: Loaded: 0 couples(s)
[28.06.25 01:44:22:844]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Hellbound ]
[28.06.25 01:44:22:851]  INFO instancemanager.HellboundManager: HellboundManager: Loaded 335 spawn entries.
[28.06.25 01:44:22:979]  INFO instancemanager.HellboundManager: HellboundManager: Spawned 623 mobs and NPCs according to the current Hellbound stage
[28.06.25 01:44:22:979]  INFO instancemanager.HellboundManager: Hellbound Manager: Loaded
[28.06.25 01:44:22:980]  INFO naia.NaiaTowerManager: Naia Core Manager: Loaded
[28.06.25 01:44:22:981]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:44:22:981]  INFO instancemanager.SoDManager: Seed of Destruction Manager: Loaded
[28.06.25 01:44:22:981]  INFO instancemanager.SoIManager: Seed of Infinity Manager: Loaded. Current stage is: 1
[28.06.25 01:44:22:983]  INFO instancemanager.SpawnManager: SpawnManager: spawned 4 npc; spawns: 4; group: soi_world_mouths
[28.06.25 01:44:22:983]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: soi_world_abyssgaze2
[28.06.25 01:44:22:983]  INFO instancemanager.BloodAltarManager: Blood Altar Manager: Initializing...
[28.06.25 01:44:23:000]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_alive_npc
[28.06.25 01:44:23:003]  INFO gameserver.GameServer: Preparing Drop Calculator
[28.06.25 01:44:23:062]  INFO gameserver.GameServer: ----------------------------------------------------------=[ Offline Buffers ]
[28.06.25 01:44:23:062]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loading offline buffers...
[28.06.25 01:44:23:063]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loaded: 0 offline buffer(s)
[28.06.25 01:44:23:072]  INFO achievements.Achievements: Achievement System: Loaded 4 achievement categories and 39 achievements.
[28.06.25 01:44:23:074]  INFO gameserver.GameServer: Emotions Loaded....
[28.06.25 01:44:23:074]  INFO gameserver.GameServer: --------------------------------------------------=[ DataBase Cleaner Loaded ]
[28.06.25 01:44:23:075]  INFO dao.CharacterDAO: Found 0 characters to delete!
[28.06.25 01:44:23:149]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Initializing
[28.06.25 01:44:23:150]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Load OK
[28.06.25 01:44:23:150]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Loaded 0 announce.
[28.06.25 01:44:23:152]  INFO gameserver.GameServer: ---------------------------------------------------=[ Loaded Facebook System ]
[28.06.25 01:44:23:154]  INFO gameserver.GameServer: -----------------------------------------------------=[ Loaded Stream System ]
[28.06.25 01:44:23:157]  INFO holder.ProxiesHolder: ProxiesHolder: loaded 20 proxies(s) count.
[28.06.25 01:44:23:157]  INFO gameserver.GameServer: ------------------------------------------------------=[ Loaded Proxy System ]
[28.06.25 01:44:24:921]  INFO donation.DonationReader: Donation Reader service started
[28.06.25 01:44:24:921]  INFO gameserver.GameServer: --------------------------------------------=[ Loaded AUTO - Donation System ]
[28.06.25 01:44:24:924]  INFO taskmanager.BackupTaskManager: Next Auto Backup in 4 H., 55 Min.
[28.06.25 01:44:24:951]  INFO votingengine.VotingSettings: VotingSettings: Loaded 3 messages, 4 drops!
[28.06.25 01:44:24:954]  INFO votingengine.VotingRewardAPI: VoteReward: Has been successfully loaded!
[28.06.25 01:44:24:957]  INFO model.PhantomPlayers: Loading phantom players...
[28.06.25 01:44:24:971]  INFO model.PhantomPlayers: Loaded 32002 possible phantom names.
[28.06.25 01:44:24:971]  INFO model.PhantomPlayers: Loaded 7172 possible phantom titles.
[28.06.25 01:44:24:972]  INFO model.PhantomPlayers: PhantomPlayers: Cached 0 players.
[28.06.25 01:44:24:972]  INFO model.PhantomPlayers: PhantomPlayers: Free names 32002.
[28.06.25 01:44:24:973]  INFO model.PhantomPlayers: Loaded 0 phantom players from database with a maximum of 3000 phantoms.
[28.06.25 01:44:24:974]  INFO model.PhantomPlayers: Scheduled spawner with 1 seconds delay.
[28.06.25 01:44:25:226]  INFO instancemanager.SpawnManager: SpawnManager: spawned 443 npc; spawns: 121; group: DAY
[28.06.25 01:44:25:326]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dawn_spawn
[28.06.25 01:44:25:571]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dusk_spawn
[28.06.25 01:44:25:571]  INFO gameserver.GameServer: Telnet server is currently disabled.
[28.06.25 01:44:25:573]  INFO gameserver.Shutdown: Scheduled server restart in 4h 35s.
[28.06.25 01:44:25:574]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:44:25:574]  INFO gameserver.GameServer: >>>>>>>>>>>>>>> GameServer Started <<<<<<<<<<<<<<
[28.06.25 01:44:25:574]  INFO gameserver.GameServer: Maximum Numbers of Connected Players: 3000
[28.06.25 01:44:25:574]  INFO gameserver.GameServer: ====================================================================
[28.06.25 01:44:25:590]  INFO gameserver.GameServer: AllowedMemory: ........... 6291456 KB
[28.06.25 01:44:25:590]  INFO gameserver.GameServer: 
Allocated: ............... 4194304 KB (66.6667%)
[28.06.25 01:44:25:591]  INFO gameserver.GameServer: 
Non-Allocated: ........... 2097152 KB (33.3333%)
[28.06.25 01:44:25:592]  INFO gameserver.GameServer: 
AllocatedMemory: ......... 4194304 KB
[28.06.25 01:44:25:592]  INFO gameserver.GameServer: Used: .................... 1145538 KB (18.2078%)
[28.06.25 01:44:25:592]  INFO gameserver.GameServer: 
Unused (cached): ......... 3048765 KB (48.4588%)
[28.06.25 01:44:25:593]  INFO gameserver.GameServer: 
UseableMemory: ........... 5145917 KB (81.7922%)
[28.06.25 01:44:25:593]  INFO gameserver.GameServer: 

[28.06.25 01:44:25:593]  INFO gameserver.GameServer: ====================================================================
[28.06.25 01:44:25:596]  INFO loginservercon.AuthServerCommunication: Connecting to authserver on 127.0.0.1:9014
[28.06.25 01:44:25:610]  INFO lspackets.AuthResponse: Registered on authserver as 1 [Bartz]
[28.06.25 01:45:17:207] ERROR loginservercon.AuthServerCommunication: AuthServer I/O error
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:401)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:434)
	at l2mv.gameserver.network.loginservercon.AuthServerCommunication.read(AuthServerCommunication.java:258)
	at l2mv.gameserver.network.loginservercon.AuthServerCommunication.run(AuthServerCommunication.java:220)
[28.06.25 01:46:47:467]  INFO gameserver.GameServer: ============================================================================
[28.06.25 01:46:47:468]  INFO gameserver.GameServer: Nome: .................................................................... GAME SERVER
[28.06.25 01:46:47:468]  INFO gameserver.GameServer: Project Revision: ........................................................ LII [MultVerso]
[28.06.25 01:46:47:468]  INFO gameserver.GameServer: Update: .................................................................. High Five: Part 5
[28.06.25 01:46:47:469]  INFO gameserver.GameServer: Numero da Revision:....................................................... 2.0
[28.06.25 01:46:47:469]  INFO gameserver.GameServer: Build Revision: .......................................................... MultVerso
[28.06.25 01:46:47:469]  INFO gameserver.GameServer: Build date: .............................................................. 2025.06.28 01:45
[28.06.25 01:46:47:469]  INFO gameserver.GameServer: Compiler version: ........................................................ 17.0.15+9-LTS-241 (Oracle Corporation)
[28.06.25 01:46:47:469]  INFO gameserver.GameServer: ============================================================================
[28.06.25 01:46:47:505]  INFO gameserver.Config: Loaded 6 trade words.
[28.06.25 01:46:47:512]  INFO gameserver.Config: Abuse: Loaded 15 abuse words.
[28.06.25 01:46:47:553]  INFO gameserver.Config: Loaded 313 Configs!
[28.06.25 01:46:47:560]  INFO gameserver.GameServer: IP EXTERNO =============================127.0.0.1
[28.06.25 01:46:47:560]  INFO gameserver.GameServer: IP INTERNO =============================127.0.0.1
[28.06.25 01:46:47:560]  INFO gameserver.GameServer: Maximo Online IP ====================== 3000
[28.06.25 01:46:47:561]  INFO gameserver.GameServer: Hora do Restart  =======================45 5 * * *
[28.06.25 01:46:47:707]  INFO gameserver.GameServer: -----------------------------------------=[ Loading Protection Configuration ]
[28.06.25 01:46:47:710]  INFO idfactory.IdFactory: IdFactory: Clear characters online status.
[28.06.25 01:46:47:722]  INFO idfactory.IdFactory: IdFactory: Cleaned 0 elements from database in 0sec.
[28.06.25 01:46:47:728]  INFO idfactory.IdFactory: IdFactory: Extracted 2 used id's from characters
[28.06.25 01:46:47:728]  INFO idfactory.IdFactory: IdFactory: Extracted 46 used id's from items
[28.06.25 01:46:47:729]  INFO idfactory.IdFactory: IdFactory: Extracted total 48 used id's.
[28.06.25 01:46:47:729]  INFO idfactory.BitSetIDFactory: IdFactory: 102912 id's available.
[28.06.25 01:46:47:824]  INFO gameserver.GameServer: ===============[Loading Scripts]==================
[28.06.25 01:46:47:825]  INFO scripts.Scripts: Scripts: Loading...
[28.06.25 01:46:47:825]  INFO scripts.Scripts: Loading Server Scripts
[28.06.25 01:46:48:106]  INFO scripts.Scripts: Scripts: Loaded 1288 classes.
[28.06.25 01:46:48:113]  INFO vote.VoteMain: Loaded: Vote System - Individual Reward.
[28.06.25 01:46:48:113]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Twitch Manager ]
[28.06.25 01:46:48:137]  INFO gameserver.GameServer: ----------------------------------------------------------=[ AntiFeedManager ]
[28.06.25 01:46:48:138]  INFO gameserver.GameServer: -------------------------------------------------------------=[ QuestManager ]
[28.06.25 01:46:48:138]  INFO gameserver.GameServer: QuestManager : loaded 0 quest's
[28.06.25 01:46:48:148]  INFO tables.FakePlayersTable: FakePlayersTable: Loaded 32002 Fake Players.
[28.06.25 01:46:48:154]  INFO utils.Strings: Loaded 418 translit entries.
[28.06.25 01:46:48:156]  INFO gameserver.GameTimeController: GameTimeController: initialized. Current time is 16:40 in the day.
[28.06.25 01:46:48:156]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Lineage World ]
[28.06.25 01:46:48:159]  INFO model.World: L2World: Creating regions: [256][272][32].
[28.06.25 01:46:48:159]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:46:48:161]  INFO htm.HtmCache: HtmCache: disabled.
[28.06.25 01:46:48:167]  INFO data.StringHolder: StringHolder: load strings: 1066 for lang: ENGLISH
[28.06.25 01:46:48:167]  INFO data.StringHolder: StringHolder: load strings: 876 for lang: RUSSIAN
[28.06.25 01:46:49:544]  INFO skills.SkillsEngine: SkillsEngine: Loaded 66051 skill templates from XML files. Max id: 91000, max level: 259
[28.06.25 01:46:50:210]  INFO holder.OptionDataHolder: OptionDataHolder: loaded 24975 option data(s) count.
[28.06.25 01:46:50:768]  INFO holder.ItemHolder: ItemHolder: loaded 19240 item(s) count.
[28.06.25 01:46:50:773]  INFO holder.FakePlayerNpcsHolder: FakePlayerNpcsHolder: loaded 8 fake player npcs(s) count.
[28.06.25 01:46:51:823]  INFO holder.NpcHolder: NpcHolder: loaded 10519 npc(s) count.
[28.06.25 01:46:51:829]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[28.06.25 01:46:51:841]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[28.06.25 01:46:51:865]  INFO holder.ExchangeItemHolder: ExchangeItemHolder: loaded 602 exchange item(s) count.
[28.06.25 01:46:51:868]  INFO holder.StaticObjectHolder: StaticObjectHolder: loaded 50 static object(s) count.
[28.06.25 01:46:51:942]  INFO holder.DoorHolder: DoorHolder: loaded 1155 door(s) count.
[28.06.25 01:46:51:986]  INFO holder.ZoneHolder: ZoneHolder: loaded 1260 zone(s) count.
[28.06.25 01:46:52:308]  INFO holder.SpawnHolder: SpawnHolder: loaded 28949 spawn(s) count.
[28.06.25 01:46:52:409]  INFO holder.InstantZoneHolder: InstantZoneHolder: loaded 94 instant zone(s) count.
[28.06.25 01:46:52:716]  INFO holder.AirshipDockHolder: AirshipDockHolder: loaded 4 airship dock(s) count.
[28.06.25 01:46:52:784]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 57215 normal learns for 103 classes.
[28.06.25 01:46:52:784]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 79 transfer learns for 3 classes.
[28.06.25 01:46:52:789]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 120 transformation learns for 6 races.
[28.06.25 01:46:52:791]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 662 fishing learns for 6 races.
[28.06.25 01:46:52:792]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 72 certification learns.
[28.06.25 01:46:52:794]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 3 collection learns.
[28.06.25 01:46:52:796]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 44 pledge learns.
[28.06.25 01:46:52:797]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 18 sub unit learns.
[28.06.25 01:46:52:806]  INFO holder.CharTemplateHolder: CharTemplateHolder: loaded 206 char template(s) count.
[28.06.25 01:46:52:904]  INFO holder.ResidenceHolder: ResidenceHolder: total size: 83
[28.06.25 01:46:52:904]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 44 clanhall(s).
[28.06.25 01:46:52:905]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 castle(s).
[28.06.25 01:46:52:905]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 21 fortress(s).
[28.06.25 01:46:52:905]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 dominion(s).
[28.06.25 01:46:52:960]  INFO instancemanager.RaidBossSpawnManager: RaidBossSpawnManager: Loaded 194 Statuses
[28.06.25 01:46:52:973]  INFO entity.SevenSigns: SevenSigns: Currently in the Competition (Quest Event) period!
[28.06.25 01:46:52:973]  INFO entity.SevenSigns: SevenSigns: The Seal of Avarice remains unclaimed.
[28.06.25 01:46:52:973]  INFO entity.SevenSigns: SevenSigns: The Seal of Gnosis remains unclaimed.
[28.06.25 01:46:52:974]  INFO entity.SevenSigns: SevenSigns: The Seal of Strife remains unclaimed.
[28.06.25 01:46:52:974]  INFO entity.SevenSigns: SevenSigns: The Competition this week, if the trend continue, will end with a tie.
[28.06.25 01:46:52:975]  INFO entity.SevenSigns: SevenSigns: Next period begins in 2 days, 16 hours and 13 mins.
[28.06.25 01:46:53:068]  INFO holder.EventHolder: EventHolder: loaded 105 event(s) count.
[28.06.25 01:46:53:081]  INFO holder.FightClubMapHolder: FightClubMapHolder: loaded 42 fight club map(s) count.
[28.06.25 01:46:53:085]  INFO holder.CubicHolder: CubicHolder: loaded 145 cubic(s) count.
[28.06.25 01:46:53:119]  INFO holder.BuyListHolder: TradeController: Loaded 4 file(s).
[28.06.25 01:46:53:120]  INFO holder.BuyListHolder: TradeController: Loaded 25014 Items.
[28.06.25 01:46:53:121]  INFO holder.BuyListHolder: TradeController: Loaded 726 Buylists.
[28.06.25 01:46:53:134]  INFO holder.RecipeHolder: RecipeController: Loaded 998 Recipes.
[28.06.25 01:46:53:421]  INFO holder.ProductHolder: ProductItemTable: Loaded 0 product item on sale.
[28.06.25 01:46:53:430]  INFO holder.HennaHolder: HennaHolder: loaded 180 henna(s) count.
[28.06.25 01:46:53:435]  INFO holder.EnchantItemHolder: EnchantItemHolder: load 4 enchant scroll(s).
[28.06.25 01:46:53:437]  INFO holder.SoulCrystalHolder: SoulCrystalHolder: loaded 54 soul crystal(s) count.
[28.06.25 01:46:53:441]  INFO holder.ArmorSetsHolder: ArmorSetsHolder: loaded 199 armor sets(s) count.
[28.06.25 01:46:53:447]  INFO holder.PetitionGroupHolder: PetitionGroupHolder: loaded 3 petition group(s) count.
[28.06.25 01:46:53:451]  INFO holder.DressArmorHolder: DressArmorHolder: loaded 57 dress armor(s) count.
[28.06.25 01:46:53:453]  INFO holder.DressCloakHolder: DressCloakHolder: loaded 7 dress cloak(s) count.
[28.06.25 01:46:53:456]  INFO holder.DressShieldHolder: DressShieldHolder: loaded 10 dress shield(s) count.
[28.06.25 01:46:53:459]  INFO holder.DressWeaponHolder: DressWeaponHolder: loaded 401 dress weapon(s) count.
[28.06.25 01:46:53:507]  INFO holder.AugmentationDataHolder: AugmentationDataHolder: loaded 1022 augmentation data(s) count.
[28.06.25 01:46:53:511]  INFO holder.PremiumHolder: PremiumHolder: loaded 5 premium(s) count.
[28.06.25 01:46:53:513]  INFO holder.FoundationHolder: FoundationHolder: loaded 268 foundation(s) count.
[28.06.25 01:46:53:517]  INFO holder.DonationHolder: DonationHolder: loaded 192 donation(s) count.
[28.06.25 01:46:53:520]  INFO holder.TournamentMapHolder: TournamentMapHolder: loaded 3 tournament map(s) count.
[28.06.25 01:46:53:523]  INFO holder.FacebookCommentsHolder: FacebookCommentsHolder: loaded 2 facebook comments(s) count.
[28.06.25 01:46:53:523]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Banned HWIDS ]
[28.06.25 01:46:53:524]  INFO security.HWIDBan: black list (Hwid) loaded size: 0
[28.06.25 01:46:53:524]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Clan Crests ]
[28.06.25 01:46:53:525]  INFO cache.CrestCache: CrestCache: Loaded 0 crests
[28.06.25 01:46:53:526]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Loading Images ]
[28.06.25 01:46:53:527]  INFO cache.ImagesCache: ImagesCache: Loading images...
[28.06.25 01:47:04:737]  INFO cache.ImagesCache: ImagesCache: Loaded 8734 images
[28.06.25 01:47:04:737]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:47:04:741]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Fish Table ]
[28.06.25 01:47:04:744]  INFO tables.FishTable: FishTable: Loaded 270 fishes.
[28.06.25 01:47:04:746]  INFO tables.FishTable: FishTable: Loaded 919 fish rewards.
[28.06.25 01:47:04:746]  INFO gameserver.GameServer: -------------------------------------------------------------------=[ Skills ]
[28.06.25 01:47:04:746]  INFO tables.SkillTreeTable: SkillTreeTable: Loaded 526 enchanted skills.
[28.06.25 01:47:04:771]  INFO datatables.EnchantNamesTable: EnchantNamesTable: Loaded: 1319 skill enchant names
[28.06.25 01:47:04:771]  INFO gameserver.GameServer: --------------------------------------------------------=[ Augmentation Data ]
[28.06.25 01:47:04:773]  INFO tables.AugmentationData: Initializing AugmentationData.
[28.06.25 01:47:04:784]  INFO tables.AugmentationData: AugmentationData: 10 bad skill(s) were skipped.
[28.06.25 01:47:04:796]  INFO tables.AugmentationData: AugmentationData: Loaded: 52 augmentation stats.
[28.06.25 01:47:04:796]  INFO tables.AugmentationData: AugmentationData: Loaded: 24 accessory augmentation stats.
[28.06.25 01:47:04:798]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 0
[28.06.25 01:47:04:798]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 1
[28.06.25 01:47:04:798]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 2
[28.06.25 01:47:04:799]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 3
[28.06.25 01:47:04:799]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 4
[28.06.25 01:47:04:799]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 5
[28.06.25 01:47:04:799]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 6
[28.06.25 01:47:04:800]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 7
[28.06.25 01:47:04:800]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 8
[28.06.25 01:47:04:800]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 9
[28.06.25 01:47:04:802]  INFO tables.EnchantHPBonusTable: EnchantHPBonusTable: Loaded bonuses for 6 grades.
[28.06.25 01:47:04:802]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Level Up Table ]
[28.06.25 01:47:04:806]  INFO tables.LevelUpTable: LevelUpData: Loaded 103 Character Level Up Templates.
[28.06.25 01:47:04:808]  INFO tables.PetSkillsTable: PetSkillsTable: Loaded 2558 skills.
[28.06.25 01:47:04:808]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Item Logs ]
[28.06.25 01:47:04:809]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Auctioneer ]
[28.06.25 01:47:04:810]  INFO itemauction.ItemAuctionManager: Initializing ItemAuctionManager
[28.06.25 01:47:04:818]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 1 auction(s) for instance 32320.
[28.06.25 01:47:04:822]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 1 on 2025.07.01 17:00:00 for instance 32320
[28.06.25 01:47:04:824]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 2 auction(s) for instance 32321.
[28.06.25 01:47:04:824]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 4 on 2025.07.03 17:00:00 for instance 32321
[28.06.25 01:47:04:825]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 1 auction(s) for instance 32322.
[28.06.25 01:47:04:825]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 3 on 2025.06.28 17:00:00 for instance 32322
[28.06.25 01:47:04:826]  INFO itemauction.ItemAuctionManager: ItemAuctionManager: Loaded 3 instance(s).
[28.06.25 01:47:04:826]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Masterio Pack ]
[28.06.25 01:47:04:854]  INFO gameserver.GameServer: ------------------------------------------------------=[ Merge System Loaded ]
[28.06.25 01:47:04:871]  INFO bosses.AntharasManager: AntharasManager: State of Antharas is NOTSPAWN.
[28.06.25 01:47:04:872]  INFO bosses.AntharasManager: AntharasManager: Next spawn date of Antharas is 21:00 31.12.1969.
[28.06.25 01:47:04:873]  INFO bosses.BaiumManager: BaiumManager: State of Baium is NOTSPAWN.
[28.06.25 01:47:04:877]  INFO bosses.BaiumManager: BaiumManager: Next spawn date: 21:00 31.12.1969
[28.06.25 01:47:04:886]  INFO bosses.BelethManager: Beleth Manager: Loaded successfully
[28.06.25 01:47:04:888]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Loaded 20 Mysterious-Box spawns.
[28.06.25 01:47:04:891]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 physical monsters spawns.
[28.06.25 01:47:04:894]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 magical monsters spawns.
[28.06.25 01:47:04:895]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 92 Church of duke monsters spawns.
[28.06.25 01:47:04:896]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 68 Emperor's grave NPC spawns.
[28.06.25 01:47:04:897]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Conquerors' Sepulcher Manager
[28.06.25 01:47:04:898]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Emperors' Sepulcher Manager
[28.06.25 01:47:04:901]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Great Sages' Sepulcher Manager
[28.06.25 01:47:04:902]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Judges' Sepulcher Manager
[28.06.25 01:47:04:902]  INFO bosses.FourSepulchersManager: FourSepulchersManager: Beginning in Attack time
[28.06.25 01:47:04:904]  INFO bosses.SailrenManager: SailrenManager: State of Sailren is NOTSPAWN.
[28.06.25 01:47:04:904]  INFO bosses.SailrenManager: SailrenManager: Next spawn date of Sailren is 21:00 31.12.1969.
[28.06.25 01:47:04:905]  INFO bosses.ValakasManager: ValakasManager: State of Valakas is NOTSPAWN.
[28.06.25 01:47:04:906]  INFO bosses.ValakasManager: ValakasManager: Next spawn date of Valakas is 21:00 31.12.1969.
[28.06.25 01:47:04:906]  INFO threading.RunnableImpl: Sat Jun 28 01:47:04 BRT 2025 Atk announce scheduled to 50.0 minute of this hour.
[28.06.25 01:47:04:986]  INFO tables.PetDataTable: PetDataTable: Loaded 3870 pets.
[28.06.25 01:47:04:991]  INFO model.Manor: ManorManager: Loaded 270 seeds
[28.06.25 01:47:05:077]  INFO scriptconfig.ScriptConfig: Loaded Service: ScripsConfig
[28.06.25 01:47:05:077]  INFO community.CharacterServicesCommunity: === CharacterServicesCommunity onLoad called ===
[28.06.25 01:47:05:078]  INFO community.CharacterServicesCommunity: COMMUNITYBOARD_ENABLED: true
[28.06.25 01:47:05:078]  INFO community.CharacterServicesCommunity: EnableMergeCommunity: false
[28.06.25 01:47:05:078]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Character Services Community loaded.
[28.06.25 01:47:05:129]  INFO dailyquests.AbstractDPScript: DailyQuestHandler: Loaded DailyQuests data
[28.06.25 01:47:05:129]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Handler registered successfully.
[28.06.25 01:47:05:138]  INFO model.Player: Loaded Service: SellPcService
[28.06.25 01:47:05:140]  INFO community.CareerManager: CommunityBoard: Manage Career service loaded.
[28.06.25 01:47:05:142]  INFO community.CommunityAuctionHouse: CommunityBoard: Auction System Service loaded.
[28.06.25 01:47:05:148]  INFO community.CommunityAugment: CommunityBoard: Augmentation Manager loaded with 188 lifestone skills.
[28.06.25 01:47:05:149]  INFO community.CommunityBoard: CommunityBoard: service loaded.
[28.06.25 01:47:05:149]  INFO community.CommunityBoardAcademy: CommunityBoard: Academy service loaded.
[28.06.25 01:47:05:150]  INFO community.CommunityBosses: CommunityBoard: Bosses loaded.
[28.06.25 01:47:05:152]  INFO community.CommunityClan: CommunityBoard: Clan service loaded.
[28.06.25 01:47:05:152]  INFO community.CommunityDropCalculator: CommunityBoard: Drop Calculator service loaded.
[28.06.25 01:47:05:153]  INFO community.CommunityEvents: CommunityBoard: Community Events service loaded.
[28.06.25 01:47:05:153]  INFO community.CommunityFacebook: CommunityBoard: Facebook Rewards page loaded.
[28.06.25 01:47:05:154]  INFO community.CommunityNpcs: CommunityBoard: Npcs loaded.
[28.06.25 01:47:05:155]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[28.06.25 01:47:05:159]  INFO community.CommunityTournament: CommunityBoard: Tournament loaded.
[28.06.25 01:47:05:159]  INFO community.CommunityWarehouse: CommunityBoard: Warehouse loaded.
[28.06.25 01:47:05:160]  INFO community.Forge: CommunityBoard: Forge loaded.
[28.06.25 01:47:05:161]  INFO community.ServicesCommunity: ServicesCommunity: Services Community service loaded.
[28.06.25 01:47:05:166]  INFO gameserver.GameServer: ===============[Spawn Manager]==================
[28.06.25 01:47:05:235]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1000 npc for group: NONE
[28.06.25 01:47:05:375]  INFO instancemanager.SpawnManager: SpawnManager: spawned 5000 npc for group: NONE
[28.06.25 01:47:05:400]  INFO naia.NaiaTowerManager: Naia Tower Manager: Loaded 12 rooms
[28.06.25 01:47:05:483]  INFO instancemanager.SpawnManager: SpawnManager: spawned 7000 npc for group: NONE
[28.06.25 01:47:05:821]  INFO instancemanager.SpawnManager: SpawnManager: spawned 12000 npc for group: NONE
[28.06.25 01:47:05:903]  INFO instancemanager.SpawnManager: SpawnManager: spawned 13000 npc for group: NONE
[28.06.25 01:47:06:844]  INFO instancemanager.SpawnManager: SpawnManager: spawned 21000 npc for group: NONE
[28.06.25 01:47:06:979]  INFO instancemanager.SpawnManager: SpawnManager: spawned 22000 npc for group: NONE
[28.06.25 01:47:07:486]  INFO instancemanager.SpawnManager: SpawnManager: spawned 25000 npc for group: NONE
[28.06.25 01:47:07:663]  INFO instancemanager.SpawnManager: SpawnManager: spawned 26000 npc for group: NONE
[28.06.25 01:47:08:034]  INFO instancemanager.SpawnManager: SpawnManager: spawned 28000 npc for group: NONE
[28.06.25 01:47:08:898]  INFO instancemanager.SpawnManager: SpawnManager: spawned 32000 npc for group: NONE
[28.06.25 01:47:09:650]  INFO instancemanager.SpawnManager: SpawnManager: spawned 35000 npc for group: NONE
[28.06.25 01:47:09:926]  INFO instancemanager.SpawnManager: SpawnManager: spawned 36000 npc for group: NONE
[28.06.25 01:47:10:188]  INFO instancemanager.SpawnManager: SpawnManager: spawned 37000 npc for group: NONE
[28.06.25 01:47:10:559]  INFO instancemanager.SpawnManager: SpawnManager: spawned 38373 npc; spawns: 17087; group: NONE
[28.06.25 01:47:10:559]  INFO gameserver.GameServer: --------------------------------------------------------------------=[ Boats ]
[28.06.25 01:47:10:560]  INFO data.BoatHolder: BoatHolder: loaded 2 boat(s) count.
[28.06.25 01:47:10:561]  INFO data.BoatHolder: BoatHolder: Spawning: RunePrimeval_Vehicle
[28.06.25 01:47:10:561]  INFO data.BoatHolder: BoatHolder: Spawning: AdenGracia_Airship
[28.06.25 01:47:10:562]  INFO holder.StaticObjectHolder: StaticObjectHolder: spawned: 50 static object(s).
[28.06.25 01:47:10:562]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Dimensional Rift ]
[28.06.25 01:47:10:576]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 13 room types with 112 rooms.
[28.06.25 01:47:10:576]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 300 DimensionalRift spawns, 0 errors.
[28.06.25 01:47:10:580]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Seven Signs ]
[28.06.25 01:47:10:589]  INFO instancemanager.AutoSpawnManager: AutoSpawnHandler: Loaded 50 handlers in total.
[28.06.25 01:47:10:589]  INFO gameserver.GameServer: ===================================================================
[28.06.25 01:47:10:589]  INFO gameserver.GameServer: ===================[Loading Olympiad System]=======================
[28.06.25 01:47:10:593]  INFO olympiad.Olympiad: Olympiad System: Loading Olympiad System....
[28.06.25 01:47:10:593]  INFO olympiad.Olympiad: Olympiad System: Currently in Validation Period
[28.06.25 01:47:10:594]  INFO olympiad.Olympiad: Olympiad System: Period Ends....
[28.06.25 01:47:10:594]  INFO olympiad.Olympiad: Olympiad System: In 0 days, 10 hours and 14 mins.
[28.06.25 01:47:10:594]  INFO olympiad.Olympiad: Olympiad System: Loaded 0 Noblesses
[28.06.25 01:47:10:596]  INFO entity.Hero: Hero System: Loaded 0 Heroes.
[28.06.25 01:47:10:596]  INFO entity.Hero: Hero System: Loaded 0 all time Heroes.
[28.06.25 01:47:10:596]  INFO gameserver.GameServer: ===================[Olympiad System Loaded]=======================
[28.06.25 01:47:10:596]  INFO gameserver.GameServer: ===================================================================
[28.06.25 01:47:10:597]  INFO instancemanager.PetitionManager: Initializing PetitionManager
[28.06.25 01:47:10:602]  INFO instancemanager.CursedWeaponsManager: CursedWeaponsManager: Loaded 2 cursed weapon(s).
[28.06.25 01:47:10:602]  INFO gameserver.GameServer: ------------------------------------------=[ Loaded Small Achievement System ]
[28.06.25 01:47:10:610]  INFO gameserver.GameServer: --------------------------------------------------------------=[ ItemHandler ]
[28.06.25 01:47:10:610]  INFO items.ItemHandler: ItemHandler: loaded 0 item handler(s) count.
[28.06.25 01:47:10:611]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Admin Commands ]
[28.06.25 01:47:10:611]  INFO admincommands.AdminCommandHandler: AdminCommandHandler: loaded 475 admin command handler(s) count.
[28.06.25 01:47:10:611]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Players Commands ]
[28.06.25 01:47:10:615]  INFO usercommands.UserCommandHandler: UserCommandHandler: loaded 17 user command handler(s) count.
[28.06.25 01:47:10:615]  INFO voicecommands.VoicedCommandHandler: VoicedCommandHandler: loaded 102 voiced command handler(s) count.
[28.06.25 01:47:10:618]  INFO gameserver.GameServer: ======================[Tournament By Kara`]==========================
[28.06.25 01:47:10:620]  INFO tournament.TournamentHolder: [TournamentHolder] Initialized 5 tournament holders:
[28.06.25 01:47:10:620]  INFO tournament.TournamentHolder: 1 vs 1
[28.06.25 01:47:10:620]  INFO tournament.TournamentHolder: 2 vs 2
[28.06.25 01:47:10:620]  INFO tournament.TournamentHolder: 4 vs 4
[28.06.25 01:47:10:620]  INFO tournament.TournamentHolder: 5 vs 5
[28.06.25 01:47:10:621]  INFO tournament.TournamentHolder: 6 vs 6
[28.06.25 01:47:10:622]  INFO gameserver.GameServer: ======================[Loading Castels & Clan Halls]==========================
[28.06.25 01:47:10:656]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Gludio Castle time - 16:00 29.06.2025
[28.06.25 01:47:10:659]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Dion Castle time - 16:00 29.06.2025
[28.06.25 01:47:10:662]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Giran Castle time - 20:00 29.06.2025
[28.06.25 01:47:10:666]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Oren Castle time - 20:00 29.06.2025
[28.06.25 01:47:10:669]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Aden Castle time - 16:00 29.06.2025
[28.06.25 01:47:10:675]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Innadril Castle time - 20:00 29.06.2025
[28.06.25 01:47:10:679]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Goddard Castle time - 20:00 29.06.2025
[28.06.25 01:47:10:684]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Rune Castle time - 16:00 29.06.2025
[28.06.25 01:47:10:688]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Schuttgart Castle time - 16:00 29.06.2025
[28.06.25 01:47:10:691]  INFO impl.ClanHallNpcSiegeEvent: ClanHallNpcSiegeEvent: Fortress Of Resistance time - 18:00 30.06.2025
[28.06.25 01:47:10:693]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:47:10:695]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:47:10:696]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Topaz Hall time - 15:00 02.07.2025
[28.06.25 01:47:10:697]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Ruby Hall time - 15:00 02.07.2025
[28.06.25 01:47:10:699]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Crystal Hall time - 15:00 02.07.2025
[28.06.25 01:47:10:700]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:47:10:702]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[28.06.25 01:47:10:703]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:47:10:705]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[28.06.25 01:47:10:706]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Atramental Barracks time - 15:00 02.07.2025
[28.06.25 01:47:10:708]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Scarlet Barracks time - 15:00 02.07.2025
[28.06.25 01:47:10:709]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Viridian Barracks time - 15:00 02.07.2025
[28.06.25 01:47:10:709]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Devastated Castle time - 18:00 28.06.2025
[28.06.25 01:47:10:710]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Bandit Stronghold time - 22:00 28.06.2025
[28.06.25 01:47:10:711]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[28.06.25 01:47:10:712]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[28.06.25 01:47:10:714]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[28.06.25 01:47:10:715]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[28.06.25 01:47:10:717]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Gold Manor time - 15:00 02.07.2025
[28.06.25 01:47:10:718]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[28.06.25 01:47:10:719]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[28.06.25 01:47:10:721]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[28.06.25 01:47:10:722]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[28.06.25 01:47:10:723]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[28.06.25 01:47:10:724]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[28.06.25 01:47:10:726]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:47:10:727]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:47:10:728]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[28.06.25 01:47:10:730]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[28.06.25 01:47:10:731]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Mont Chamber time - 15:00 02.07.2025
[28.06.25 01:47:10:732]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Astaire Chamber time - 15:00 02.07.2025
[28.06.25 01:47:10:734]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Aria Chamber time - 15:00 02.07.2025
[28.06.25 01:47:10:736]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Yiana Chamber time - 15:00 02.07.2025
[28.06.25 01:47:10:737]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Roien Chamber time - 15:00 02.07.2025
[28.06.25 01:47:10:738]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Luna Chamber time - 15:00 02.07.2025
[28.06.25 01:47:10:739]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Traban Chamber time - 15:00 02.07.2025
[28.06.25 01:47:10:740]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Eisen Hall time - 15:00 02.07.2025
[28.06.25 01:47:10:741]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Heavy Metal Hall time - 15:00 02.07.2025
[28.06.25 01:47:10:742]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Molten Ore Hall time - 15:00 02.07.2025
[28.06.25 01:47:10:742]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Titan Hall time - 15:00 02.07.2025
[28.06.25 01:47:10:744]  INFO impl.ClanHallMiniGameEvent: ClanHallMiniGameEvent: Rainbow Springs time - 22:00 28.06.2025
[28.06.25 01:47:10:744]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Wild Beast Reserve time - 21:00 28.06.2025
[28.06.25 01:47:10:744]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Fortress of Dead time - 18:00 28.06.2025
[28.06.25 01:47:10:745]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Gludio Dominion time - undefined
[28.06.25 01:47:10:746]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Dion Dominion time - undefined
[28.06.25 01:47:10:746]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Giran Dominion time - undefined
[28.06.25 01:47:10:747]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Oren Dominion time - undefined
[28.06.25 01:47:10:747]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Aden Dominion time - undefined
[28.06.25 01:47:10:748]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Innadril Dominion time - undefined
[28.06.25 01:47:10:748]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Goddard Dominion time - undefined
[28.06.25 01:47:10:749]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Rune Dominion time - undefined
[28.06.25 01:47:10:749]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Schuttgart Dominion time - undefined
[28.06.25 01:47:10:751]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Shanty Fortress time - undefined
[28.06.25 01:47:10:753]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Southern Fortress time - undefined
[28.06.25 01:47:10:756]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hive Fortress time - undefined
[28.06.25 01:47:10:758]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Valley Fortress time - undefined
[28.06.25 01:47:10:760]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Ivory Fortress time - undefined
[28.06.25 01:47:10:763]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Narsell Fortress time - undefined
[28.06.25 01:47:10:765]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Bayou Fortress time - undefined
[28.06.25 01:47:10:768]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: White Sands Fortress time - undefined
[28.06.25 01:47:10:770]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Borderland Fortress time - undefined
[28.06.25 01:47:10:773]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Swamp Fortress time - undefined
[28.06.25 01:47:10:775]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Archaic Fortress time - undefined
[28.06.25 01:47:10:778]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Floran Fortress time - undefined
[28.06.25 01:47:10:780]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Cloud Mountain Fortress time - undefined
[28.06.25 01:47:10:783]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Tanor Fortress time - undefined
[28.06.25 01:47:10:785]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Dragonspine Fortress time - undefined
[28.06.25 01:47:10:788]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Antharas Fortress time - undefined
[28.06.25 01:47:10:790]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Western Fortress time - undefined
[28.06.25 01:47:10:793]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hunter's Fortress time - undefined
[28.06.25 01:47:10:795]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Aaru Fortress time - undefined
[28.06.25 01:47:10:798]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Demon Fortress time - undefined
[28.06.25 01:47:10:800]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Monastic Fortress time - undefined
[28.06.25 01:47:10:801]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 70-75 Level time - 02:00 28.06.2025
[28.06.25 01:47:10:801]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 76-79 Level time - 02:00 28.06.2025
[28.06.25 01:47:10:802]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 80-85 Level time - 02:00 28.06.2025
[28.06.25 01:47:10:802]  INFO fightclub.LastManStandingEvent: LastManStandingEvent: Last Hero time - undefined
[28.06.25 01:47:10:802]  INFO fightclub.TeamVSTeamEvent: TeamVSTeamEvent: Team VS Team time - undefined
[28.06.25 01:47:10:802]  INFO fightclub.LuckyCreaturesEvent: LuckyCreaturesEvent: Lucky Creatures time - undefined
[28.06.25 01:47:10:802]  INFO fightclub.CaptureTheFlagEvent: CaptureTheFlagEvent: Capture The Flag time - undefined
[28.06.25 01:47:10:802]  INFO fightclub.FFATreasureHuntEvent: FFATreasureHuntEvent: Treasure Hunt time - undefined
[28.06.25 01:47:10:803]  INFO fightclub.KoreanStyleEvent: KoreanStyleEvent: Korean Style time - undefined
[28.06.25 01:47:10:803]  INFO fightclub.ProtectTheKingEvent: ProtectTheKingEvent: Protect The King time - undefined
[28.06.25 01:47:10:803]  INFO fightclub.FightForThroneEvent: FightForThroneEvent: Fight For Throne time - undefined
[28.06.25 01:47:10:803]  INFO fightclub.DeathMatchEvent: DeathMatchEvent: Death Match time - undefined
[28.06.25 01:47:10:803]  INFO fightclub.RussianRouletteEvent: RussianRouletteEvent: Russian Roulette time - undefined
[28.06.25 01:47:10:804]  INFO instancemanager.CastleManorManager: Manor System: Initializing...
[28.06.25 01:47:10:805]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:47:10:806]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Auto Cleaner ]
[28.06.25 01:47:10:806]  INFO gameserver.GameServer: IdFactory: Free ObjectID's remaining: 1879004835
[28.06.25 01:47:10:806]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:47:10:807]  INFO instancemanager.CoupleManager: Initializing CoupleManager
[28.06.25 01:47:10:807]  INFO instancemanager.CoupleManager: Loaded: 0 couples(s)
[28.06.25 01:47:10:808]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Hellbound ]
[28.06.25 01:47:10:814]  INFO instancemanager.HellboundManager: HellboundManager: Loaded 335 spawn entries.
[28.06.25 01:47:10:937]  INFO instancemanager.HellboundManager: HellboundManager: Spawned 623 mobs and NPCs according to the current Hellbound stage
[28.06.25 01:47:10:937]  INFO instancemanager.HellboundManager: Hellbound Manager: Loaded
[28.06.25 01:47:10:938]  INFO naia.NaiaTowerManager: Naia Core Manager: Loaded
[28.06.25 01:47:10:938]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:47:10:939]  INFO instancemanager.SoDManager: Seed of Destruction Manager: Loaded
[28.06.25 01:47:10:939]  INFO instancemanager.SoIManager: Seed of Infinity Manager: Loaded. Current stage is: 1
[28.06.25 01:47:10:940]  INFO instancemanager.SpawnManager: SpawnManager: spawned 4 npc; spawns: 4; group: soi_world_mouths
[28.06.25 01:47:10:940]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: soi_world_abyssgaze2
[28.06.25 01:47:10:941]  INFO instancemanager.BloodAltarManager: Blood Altar Manager: Initializing...
[28.06.25 01:47:10:950]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_alive_npc
[28.06.25 01:47:10:952]  INFO gameserver.GameServer: Preparing Drop Calculator
[28.06.25 01:47:11:015]  INFO gameserver.GameServer: ----------------------------------------------------------=[ Offline Buffers ]
[28.06.25 01:47:11:016]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loading offline buffers...
[28.06.25 01:47:11:017]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loaded: 0 offline buffer(s)
[28.06.25 01:47:11:034]  INFO achievements.Achievements: Achievement System: Loaded 4 achievement categories and 39 achievements.
[28.06.25 01:47:11:036]  INFO gameserver.GameServer: Emotions Loaded....
[28.06.25 01:47:11:036]  INFO gameserver.GameServer: --------------------------------------------------=[ DataBase Cleaner Loaded ]
[28.06.25 01:47:11:036]  INFO dao.CharacterDAO: Found 0 characters to delete!
[28.06.25 01:47:11:103]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Initializing
[28.06.25 01:47:11:104]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Load OK
[28.06.25 01:47:11:105]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Loaded 0 announce.
[28.06.25 01:47:11:106]  INFO gameserver.GameServer: ---------------------------------------------------=[ Loaded Facebook System ]
[28.06.25 01:47:11:108]  INFO gameserver.GameServer: -----------------------------------------------------=[ Loaded Stream System ]
[28.06.25 01:47:11:112]  INFO holder.ProxiesHolder: ProxiesHolder: loaded 20 proxies(s) count.
[28.06.25 01:47:11:112]  INFO gameserver.GameServer: ------------------------------------------------------=[ Loaded Proxy System ]
[28.06.25 01:47:12:836]  INFO donation.DonationReader: Donation Reader service started
[28.06.25 01:47:12:836]  INFO gameserver.GameServer: --------------------------------------------=[ Loaded AUTO - Donation System ]
[28.06.25 01:47:12:838]  INFO taskmanager.BackupTaskManager: Next Auto Backup in 4 H., 52 Min.
[28.06.25 01:47:12:845]  INFO votingengine.VotingSettings: VotingSettings: Loaded 3 messages, 4 drops!
[28.06.25 01:47:12:847]  INFO votingengine.VotingRewardAPI: VoteReward: Has been successfully loaded!
[28.06.25 01:47:12:849]  INFO model.PhantomPlayers: Loading phantom players...
[28.06.25 01:47:12:855]  INFO model.PhantomPlayers: Loaded 32002 possible phantom names.
[28.06.25 01:47:12:855]  INFO model.PhantomPlayers: Loaded 7172 possible phantom titles.
[28.06.25 01:47:12:855]  INFO model.PhantomPlayers: PhantomPlayers: Cached 0 players.
[28.06.25 01:47:12:856]  INFO model.PhantomPlayers: PhantomPlayers: Free names 32002.
[28.06.25 01:47:12:856]  INFO model.PhantomPlayers: Loaded 0 phantom players from database with a maximum of 3000 phantoms.
[28.06.25 01:47:12:856]  INFO model.PhantomPlayers: Scheduled spawner with 1 seconds delay.
[28.06.25 01:47:12:992]  INFO instancemanager.SpawnManager: SpawnManager: spawned 443 npc; spawns: 121; group: DAY
[28.06.25 01:47:13:061]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dawn_spawn
[28.06.25 01:47:13:249]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dusk_spawn
[28.06.25 01:47:13:250]  INFO gameserver.GameServer: Telnet server is currently disabled.
[28.06.25 01:47:13:252]  INFO gameserver.Shutdown: Scheduled server restart in 3h 57m 47s.
[28.06.25 01:47:13:252]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:47:13:252]  INFO gameserver.GameServer: >>>>>>>>>>>>>>> GameServer Started <<<<<<<<<<<<<<
[28.06.25 01:47:13:253]  INFO gameserver.GameServer: Maximum Numbers of Connected Players: 3000
[28.06.25 01:47:13:253]  INFO gameserver.GameServer: ====================================================================
[28.06.25 01:47:13:270]  INFO gameserver.GameServer: AllowedMemory: ........... 6291456 KB
[28.06.25 01:47:13:270]  INFO gameserver.GameServer: 
Allocated: ............... 4194304 KB (66.6667%)
[28.06.25 01:47:13:271]  INFO gameserver.GameServer: 
Non-Allocated: ........... 2097152 KB (33.3333%)
[28.06.25 01:47:13:271]  INFO gameserver.GameServer: 
AllocatedMemory: ......... 4194304 KB
[28.06.25 01:47:13:271]  INFO gameserver.GameServer: Used: .................... 1142696 KB (18.1627%)
[28.06.25 01:47:13:271]  INFO gameserver.GameServer: 
Unused (cached): ......... 3051607 KB (48.504%)
[28.06.25 01:47:13:271]  INFO gameserver.GameServer: 
UseableMemory: ........... 5148759 KB (81.8373%)
[28.06.25 01:47:13:271]  INFO gameserver.GameServer: 

[28.06.25 01:47:13:271]  INFO gameserver.GameServer: ====================================================================
[28.06.25 01:47:13:273]  INFO loginservercon.AuthServerCommunication: Connecting to authserver on 127.0.0.1:9014
[28.06.25 01:47:13:283]  INFO lspackets.AuthResponse: Registered on authserver as 1 [Bartz]
[28.06.25 01:48:27:270]  INFO community.CharacterServicesCommunity: onBypassCommand called - bypass: _bbscharservices;registeremail
[28.06.25 01:48:27:270]  INFO community.CharacterServicesCommunity: cmd: _bbscharservices, action: registeremail
[28.06.25 01:48:38:659]  INFO community.CharacterServicesCommunity: === onWriteCommand called ===
[28.06.25 01:48:38:659]  INFO community.CharacterServicesCommunity: Player: Dzu
[28.06.25 01:48:38:659]  INFO community.CharacterServicesCommunity: Bypass: '_bbscharservices;registeremail;submit'
[28.06.25 01:48:38:659]  INFO community.CharacterServicesCommunity: arg1: '0'
[28.06.25 01:48:38:659]  INFO community.CharacterServicesCommunity: arg2: '0'
[28.06.25 01:48:38:660]  INFO community.CharacterServicesCommunity: arg3: '<EMAIL>'
[28.06.25 01:48:38:660]  INFO community.CharacterServicesCommunity: arg4: '<EMAIL>'
[28.06.25 01:48:38:660]  INFO community.CharacterServicesCommunity: arg5: ''
[28.06.25 01:48:38:660]  INFO community.CharacterServicesCommunity: Processing email registration - email: <EMAIL>, confirmEmail: <EMAIL>
[28.06.25 01:48:38:664]  INFO utils.EmailVerification: Verification code stored for account: siniez, email: <EMAIL>
[28.06.25 01:48:38:664]  INFO community.CharacterServicesCommunity: About to send email - Config.MAIL_USER: '<EMAIL>', Config.MAIL_PASS: 'SET'
[28.06.25 01:48:38:664]  INFO utils.EmailVerification: Checking email configuration - MAIL_USER: '<EMAIL>', MAIL_PASS: 'SET'
[28.06.25 01:48:38:948]  INFO utils.EmailVerification: SMTP Initial: 220 smtp.gmail.com ESMTP 6a1803df08f44-6fd7718d31asm31636136d6.18 - gsmtp
[28.06.25 01:48:39:160]  INFO utils.EmailVerification: SMTP EHLO: 250-smtp.gmail.com at your service, [***************]
250-SIZE ********
250-8BITMIME
250-STARTTLS
250-ENHANCEDSTATUSCODES
250-PIPELINING
250-CHUNKING
250 SMTPUTF8

[28.06.25 01:48:39:369]  INFO utils.EmailVerification: SMTP STARTTLS: 220 2.0.0 Ready to start TLS
[28.06.25 01:48:39:733]  INFO utils.EmailVerification: SMTP EHLO after TLS: 250-smtp.gmail.com at your service, [***************]
250-SIZE ********
250-8BITMIME
250-AUTH LOGIN PLAIN XOAUTH2 PLAIN-CLIENTTOKEN OAUTHBEARER XOAUTH
250-ENHANCEDSTATUSCODES
250-PIPELINING
250-CHUNKING
250 SMTPUTF8

[28.06.25 01:48:39:947]  INFO utils.EmailVerification: SMTP AUTH LOGIN: 334 VXNlcm5hbWU6
[28.06.25 01:48:40:156]  INFO utils.EmailVerification: SMTP Username: 334 UGFzc3dvcmQ6
[28.06.25 01:48:40:511]  INFO utils.EmailVerification: SMTP Password: 235 2.7.0 Accepted
[28.06.25 01:48:40:720]  INFO utils.EmailVerification: SMTP MAIL FROM: 250 2.1.0 OK 6a1803df08f44-6fd7718d31asm31636136d6.18 - gsmtp
[28.06.25 01:48:40:929]  INFO utils.EmailVerification: SMTP RCPT TO: 250 2.1.5 OK 6a1803df08f44-6fd7718d31asm31636136d6.18 - gsmtp
[28.06.25 01:48:41:243]  INFO utils.EmailVerification: SMTP DATA: 354 Go ahead 6a1803df08f44-6fd7718d31asm31636136d6.18 - gsmtp
[28.06.25 01:48:41:971]  INFO utils.EmailVerification: SMTP Email sent: 250 2.0.0 OK  1751086123 6a1803df08f44-6fd7718d31asm31636136d6.18 - gsmtp
[28.06.25 01:48:42:179]  INFO utils.EmailVerification: SMTP QUIT: 221 2.0.0 closing connection 6a1803df08f44-6fd7718d31asm31636136d6.18 - gsmtp
[28.06.25 01:48:42:179]  INFO utils.EmailVerification: Email sent successfully to: <EMAIL> for player: Dzu
[28.06.25 01:48:51:104]  INFO tasks.RestoreOfflineTraders: Restored 0 offline traders
[28.06.25 01:49:33:325]  INFO community.CharacterServicesCommunity: onBypassCommand called - bypass: _bbscharservices;verifyemail;submit;$verificationcode
[28.06.25 01:49:33:325]  INFO community.CharacterServicesCommunity: cmd: _bbscharservices, action: verifyemail
[28.06.25 01:49:33:325]  INFO community.CharacterServicesCommunity: Processing email verification via bypass - code: '$verificationcode'
[28.06.25 01:49:33:325]  INFO community.CharacterServicesCommunity: === EMAIL VERIFICATION DEBUG ===
[28.06.25 01:49:33:325]  INFO community.CharacterServicesCommunity: Player: Dzu
[28.06.25 01:49:33:325]  INFO community.CharacterServicesCommunity: Account: siniez
[28.06.25 01:49:33:326]  INFO community.CharacterServicesCommunity: Input code: '$verificationcode'
[28.06.25 01:49:33:326]  INFO community.CharacterServicesCommunity: Input code length: 17
[28.06.25 01:49:33:326]  INFO community.CharacterServicesCommunity: Stored code: '605926'
[28.06.25 01:49:33:326]  INFO community.CharacterServicesCommunity: Stored code length: 6
[28.06.25 01:49:33:326]  INFO community.CharacterServicesCommunity: Codes match: false
[28.06.25 01:49:33:327]  INFO community.CharacterServicesCommunity: ===============================
[28.06.25 01:50:04:903]  INFO threading.RunnableImpl: FourSepulchersManager: Entry time: Sat Jun 28 01:55:04 BRT 2025
[28.06.25 01:52:41:893]  INFO gameserver.GameServer: ============================================================================
[28.06.25 01:52:41:894]  INFO gameserver.GameServer: Nome: .................................................................... GAME SERVER
[28.06.25 01:52:41:894]  INFO gameserver.GameServer: Project Revision: ........................................................ LII [MultVerso]
[28.06.25 01:52:41:894]  INFO gameserver.GameServer: Update: .................................................................. High Five: Part 5
[28.06.25 01:52:41:895]  INFO gameserver.GameServer: Numero da Revision:....................................................... 2.0
[28.06.25 01:52:41:895]  INFO gameserver.GameServer: Build Revision: .......................................................... MultVerso
[28.06.25 01:52:41:895]  INFO gameserver.GameServer: Build date: .............................................................. 2025.06.28 01:51
[28.06.25 01:52:41:895]  INFO gameserver.GameServer: Compiler version: ........................................................ 17.0.15+9-LTS-241 (Oracle Corporation)
[28.06.25 01:52:41:895]  INFO gameserver.GameServer: ============================================================================
[28.06.25 01:52:41:939]  INFO gameserver.Config: Loaded 6 trade words.
[28.06.25 01:52:41:949]  INFO gameserver.Config: Abuse: Loaded 15 abuse words.
[28.06.25 01:52:42:002]  INFO gameserver.Config: Loaded 313 Configs!
[28.06.25 01:52:42:010]  INFO gameserver.GameServer: IP EXTERNO =============================127.0.0.1
[28.06.25 01:52:42:011]  INFO gameserver.GameServer: IP INTERNO =============================127.0.0.1
[28.06.25 01:52:42:011]  INFO gameserver.GameServer: Maximo Online IP ====================== 3000
[28.06.25 01:52:42:011]  INFO gameserver.GameServer: Hora do Restart  =======================45 5 * * *
[28.06.25 01:52:42:186]  INFO gameserver.GameServer: -----------------------------------------=[ Loading Protection Configuration ]
[28.06.25 01:52:42:190]  INFO idfactory.IdFactory: IdFactory: Clear characters online status.
[28.06.25 01:52:42:233]  INFO idfactory.IdFactory: IdFactory: Cleaned 0 elements from database in 0sec.
[28.06.25 01:52:42:240]  INFO idfactory.IdFactory: IdFactory: Extracted 2 used id's from characters
[28.06.25 01:52:42:241]  INFO idfactory.IdFactory: IdFactory: Extracted 46 used id's from items
[28.06.25 01:52:42:242]  INFO idfactory.IdFactory: IdFactory: Extracted total 48 used id's.
[28.06.25 01:52:42:242]  INFO idfactory.BitSetIDFactory: IdFactory: 102912 id's available.
[28.06.25 01:52:42:358]  INFO gameserver.GameServer: ===============[Loading Scripts]==================
[28.06.25 01:52:42:359]  INFO scripts.Scripts: Scripts: Loading...
[28.06.25 01:52:42:360]  INFO scripts.Scripts: Loading Server Scripts
[28.06.25 01:52:42:704]  INFO scripts.Scripts: Scripts: Loaded 1288 classes.
[28.06.25 01:52:42:712]  INFO vote.VoteMain: Loaded: Vote System - Individual Reward.
[28.06.25 01:52:42:713]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Twitch Manager ]
[28.06.25 01:52:42:749]  INFO gameserver.GameServer: ----------------------------------------------------------=[ AntiFeedManager ]
[28.06.25 01:52:42:749]  INFO gameserver.GameServer: -------------------------------------------------------------=[ QuestManager ]
[28.06.25 01:52:42:751]  INFO gameserver.GameServer: QuestManager : loaded 0 quest's
[28.06.25 01:52:42:768]  INFO tables.FakePlayersTable: FakePlayersTable: Loaded 32002 Fake Players.
[28.06.25 01:52:42:787]  INFO utils.Strings: Loaded 418 translit entries.
[28.06.25 01:52:42:789]  INFO gameserver.GameTimeController: GameTimeController: initialized. Current time is 17:16 in the day.
[28.06.25 01:52:42:790]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Lineage World ]
[28.06.25 01:52:42:793]  INFO model.World: L2World: Creating regions: [256][272][32].
[28.06.25 01:52:42:794]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:52:42:795]  INFO htm.HtmCache: HtmCache: disabled.
[28.06.25 01:52:42:805]  INFO data.StringHolder: StringHolder: load strings: 1066 for lang: ENGLISH
[28.06.25 01:52:42:807]  INFO data.StringHolder: StringHolder: load strings: 876 for lang: RUSSIAN
[28.06.25 01:52:44:902]  INFO skills.SkillsEngine: SkillsEngine: Loaded 66051 skill templates from XML files. Max id: 91000, max level: 259
[28.06.25 01:52:45:869]  INFO holder.OptionDataHolder: OptionDataHolder: loaded 24975 option data(s) count.
[28.06.25 01:52:46:874]  INFO holder.ItemHolder: ItemHolder: loaded 19240 item(s) count.
[28.06.25 01:52:46:881]  INFO holder.FakePlayerNpcsHolder: FakePlayerNpcsHolder: loaded 8 fake player npcs(s) count.
[28.06.25 01:52:48:302]  INFO holder.NpcHolder: NpcHolder: loaded 10519 npc(s) count.
[28.06.25 01:52:48:310]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[28.06.25 01:52:48:324]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[28.06.25 01:52:48:359]  INFO holder.ExchangeItemHolder: ExchangeItemHolder: loaded 602 exchange item(s) count.
[28.06.25 01:52:48:363]  INFO holder.StaticObjectHolder: StaticObjectHolder: loaded 50 static object(s) count.
[28.06.25 01:52:48:492]  INFO holder.DoorHolder: DoorHolder: loaded 1155 door(s) count.
[28.06.25 01:52:48:552]  INFO holder.ZoneHolder: ZoneHolder: loaded 1260 zone(s) count.
[28.06.25 01:52:48:914]  INFO holder.SpawnHolder: SpawnHolder: loaded 28949 spawn(s) count.
[28.06.25 01:52:49:024]  INFO holder.InstantZoneHolder: InstantZoneHolder: loaded 94 instant zone(s) count.
[28.06.25 01:52:49:355]  INFO holder.AirshipDockHolder: AirshipDockHolder: loaded 4 airship dock(s) count.
[28.06.25 01:52:49:402]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 57215 normal learns for 103 classes.
[28.06.25 01:52:49:402]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 79 transfer learns for 3 classes.
[28.06.25 01:52:49:404]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 120 transformation learns for 6 races.
[28.06.25 01:52:49:404]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 662 fishing learns for 6 races.
[28.06.25 01:52:49:404]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 72 certification learns.
[28.06.25 01:52:49:404]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 3 collection learns.
[28.06.25 01:52:49:404]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 44 pledge learns.
[28.06.25 01:52:49:404]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 18 sub unit learns.
[28.06.25 01:52:49:415]  INFO holder.CharTemplateHolder: CharTemplateHolder: loaded 206 char template(s) count.
[28.06.25 01:52:49:532]  INFO holder.ResidenceHolder: ResidenceHolder: total size: 83
[28.06.25 01:52:49:532]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 44 clanhall(s).
[28.06.25 01:52:49:533]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 castle(s).
[28.06.25 01:52:49:533]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 21 fortress(s).
[28.06.25 01:52:49:533]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 dominion(s).
[28.06.25 01:52:49:602]  INFO instancemanager.RaidBossSpawnManager: RaidBossSpawnManager: Loaded 194 Statuses
[28.06.25 01:52:49:619]  INFO entity.SevenSigns: SevenSigns: Currently in the Competition (Quest Event) period!
[28.06.25 01:52:49:619]  INFO entity.SevenSigns: SevenSigns: The Seal of Avarice remains unclaimed.
[28.06.25 01:52:49:619]  INFO entity.SevenSigns: SevenSigns: The Seal of Gnosis remains unclaimed.
[28.06.25 01:52:49:620]  INFO entity.SevenSigns: SevenSigns: The Seal of Strife remains unclaimed.
[28.06.25 01:52:49:620]  INFO entity.SevenSigns: SevenSigns: The Competition this week, if the trend continue, will end with a tie.
[28.06.25 01:52:49:621]  INFO entity.SevenSigns: SevenSigns: Next period begins in 2 days, 16 hours and 7 mins.
[28.06.25 01:52:49:748]  INFO holder.EventHolder: EventHolder: loaded 105 event(s) count.
[28.06.25 01:52:49:762]  INFO holder.FightClubMapHolder: FightClubMapHolder: loaded 42 fight club map(s) count.
[28.06.25 01:52:49:767]  INFO holder.CubicHolder: CubicHolder: loaded 145 cubic(s) count.
[28.06.25 01:52:49:804]  INFO holder.BuyListHolder: TradeController: Loaded 4 file(s).
[28.06.25 01:52:49:805]  INFO holder.BuyListHolder: TradeController: Loaded 25014 Items.
[28.06.25 01:52:49:806]  INFO holder.BuyListHolder: TradeController: Loaded 726 Buylists.
[28.06.25 01:52:49:820]  INFO holder.RecipeHolder: RecipeController: Loaded 998 Recipes.
[28.06.25 01:52:50:231]  INFO holder.ProductHolder: ProductItemTable: Loaded 0 product item on sale.
[28.06.25 01:52:50:242]  INFO holder.HennaHolder: HennaHolder: loaded 180 henna(s) count.
[28.06.25 01:52:50:245]  INFO holder.EnchantItemHolder: EnchantItemHolder: load 4 enchant scroll(s).
[28.06.25 01:52:50:249]  INFO holder.SoulCrystalHolder: SoulCrystalHolder: loaded 54 soul crystal(s) count.
[28.06.25 01:52:50:252]  INFO holder.ArmorSetsHolder: ArmorSetsHolder: loaded 199 armor sets(s) count.
[28.06.25 01:52:50:257]  INFO holder.PetitionGroupHolder: PetitionGroupHolder: loaded 3 petition group(s) count.
[28.06.25 01:52:50:260]  INFO holder.DressArmorHolder: DressArmorHolder: loaded 57 dress armor(s) count.
[28.06.25 01:52:50:262]  INFO holder.DressCloakHolder: DressCloakHolder: loaded 7 dress cloak(s) count.
[28.06.25 01:52:50:265]  INFO holder.DressShieldHolder: DressShieldHolder: loaded 10 dress shield(s) count.
[28.06.25 01:52:50:268]  INFO holder.DressWeaponHolder: DressWeaponHolder: loaded 401 dress weapon(s) count.
[28.06.25 01:52:50:318]  INFO holder.AugmentationDataHolder: AugmentationDataHolder: loaded 1022 augmentation data(s) count.
[28.06.25 01:52:50:324]  INFO holder.PremiumHolder: PremiumHolder: loaded 5 premium(s) count.
[28.06.25 01:52:50:327]  INFO holder.FoundationHolder: FoundationHolder: loaded 268 foundation(s) count.
[28.06.25 01:52:50:346]  INFO holder.DonationHolder: DonationHolder: loaded 192 donation(s) count.
[28.06.25 01:52:50:349]  INFO holder.TournamentMapHolder: TournamentMapHolder: loaded 3 tournament map(s) count.
[28.06.25 01:52:50:354]  INFO holder.FacebookCommentsHolder: FacebookCommentsHolder: loaded 2 facebook comments(s) count.
[28.06.25 01:52:50:354]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Banned HWIDS ]
[28.06.25 01:52:50:355]  INFO security.HWIDBan: black list (Hwid) loaded size: 0
[28.06.25 01:52:50:356]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Clan Crests ]
[28.06.25 01:52:50:357]  INFO cache.CrestCache: CrestCache: Loaded 0 crests
[28.06.25 01:52:50:358]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Loading Images ]
[28.06.25 01:52:50:360]  INFO cache.ImagesCache: ImagesCache: Loading images...
[28.06.25 01:53:02:319]  INFO cache.ImagesCache: ImagesCache: Loaded 8734 images
[28.06.25 01:53:02:319]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:53:02:323]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Fish Table ]
[28.06.25 01:53:02:326]  INFO tables.FishTable: FishTable: Loaded 270 fishes.
[28.06.25 01:53:02:328]  INFO tables.FishTable: FishTable: Loaded 919 fish rewards.
[28.06.25 01:53:02:329]  INFO gameserver.GameServer: -------------------------------------------------------------------=[ Skills ]
[28.06.25 01:53:02:329]  INFO tables.SkillTreeTable: SkillTreeTable: Loaded 526 enchanted skills.
[28.06.25 01:53:02:359]  INFO datatables.EnchantNamesTable: EnchantNamesTable: Loaded: 1319 skill enchant names
[28.06.25 01:53:02:359]  INFO gameserver.GameServer: --------------------------------------------------------=[ Augmentation Data ]
[28.06.25 01:53:02:360]  INFO tables.AugmentationData: Initializing AugmentationData.
[28.06.25 01:53:02:371]  INFO tables.AugmentationData: AugmentationData: 10 bad skill(s) were skipped.
[28.06.25 01:53:02:383]  INFO tables.AugmentationData: AugmentationData: Loaded: 52 augmentation stats.
[28.06.25 01:53:02:383]  INFO tables.AugmentationData: AugmentationData: Loaded: 24 accessory augmentation stats.
[28.06.25 01:53:02:386]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 0
[28.06.25 01:53:02:386]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 1
[28.06.25 01:53:02:387]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 2
[28.06.25 01:53:02:387]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 3
[28.06.25 01:53:02:387]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 4
[28.06.25 01:53:02:387]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 5
[28.06.25 01:53:02:387]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 6
[28.06.25 01:53:02:388]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 7
[28.06.25 01:53:02:388]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 8
[28.06.25 01:53:02:388]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 9
[28.06.25 01:53:02:390]  INFO tables.EnchantHPBonusTable: EnchantHPBonusTable: Loaded bonuses for 6 grades.
[28.06.25 01:53:02:390]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Level Up Table ]
[28.06.25 01:53:02:393]  INFO tables.LevelUpTable: LevelUpData: Loaded 103 Character Level Up Templates.
[28.06.25 01:53:02:396]  INFO tables.PetSkillsTable: PetSkillsTable: Loaded 2558 skills.
[28.06.25 01:53:02:396]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Item Logs ]
[28.06.25 01:53:02:398]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Auctioneer ]
[28.06.25 01:53:02:398]  INFO itemauction.ItemAuctionManager: Initializing ItemAuctionManager
[28.06.25 01:53:02:408]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 1 auction(s) for instance 32320.
[28.06.25 01:53:02:413]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 1 on 2025.07.01 17:00:00 for instance 32320
[28.06.25 01:53:02:415]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 2 auction(s) for instance 32321.
[28.06.25 01:53:02:415]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 4 on 2025.07.03 17:00:00 for instance 32321
[28.06.25 01:53:02:417]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 1 auction(s) for instance 32322.
[28.06.25 01:53:02:417]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 3 on 2025.06.28 17:00:00 for instance 32322
[28.06.25 01:53:02:417]  INFO itemauction.ItemAuctionManager: ItemAuctionManager: Loaded 3 instance(s).
[28.06.25 01:53:02:418]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Masterio Pack ]
[28.06.25 01:53:02:450]  INFO gameserver.GameServer: ------------------------------------------------------=[ Merge System Loaded ]
[28.06.25 01:53:02:469]  INFO bosses.AntharasManager: AntharasManager: State of Antharas is NOTSPAWN.
[28.06.25 01:53:02:470]  INFO bosses.AntharasManager: AntharasManager: Next spawn date of Antharas is 21:00 31.12.1969.
[28.06.25 01:53:02:472]  INFO bosses.BaiumManager: BaiumManager: State of Baium is NOTSPAWN.
[28.06.25 01:53:02:476]  INFO bosses.BaiumManager: BaiumManager: Next spawn date: 21:00 31.12.1969
[28.06.25 01:53:02:487]  INFO bosses.BelethManager: Beleth Manager: Loaded successfully
[28.06.25 01:53:02:489]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Loaded 20 Mysterious-Box spawns.
[28.06.25 01:53:02:494]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 physical monsters spawns.
[28.06.25 01:53:02:498]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 magical monsters spawns.
[28.06.25 01:53:02:499]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 92 Church of duke monsters spawns.
[28.06.25 01:53:02:500]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 68 Emperor's grave NPC spawns.
[28.06.25 01:53:02:502]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Conquerors' Sepulcher Manager
[28.06.25 01:53:02:503]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Emperors' Sepulcher Manager
[28.06.25 01:53:02:503]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Great Sages' Sepulcher Manager
[28.06.25 01:53:02:504]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Judges' Sepulcher Manager
[28.06.25 01:53:02:504]  INFO bosses.FourSepulchersManager: FourSepulchersManager: Beginning in Cooldown time
[28.06.25 01:53:02:504]  INFO threading.RunnableImpl: FourSepulchersManager: Entry time: Sat Jun 28 01:55:02 BRT 2025
[28.06.25 01:53:02:505]  INFO bosses.SailrenManager: SailrenManager: State of Sailren is NOTSPAWN.
[28.06.25 01:53:02:505]  INFO bosses.SailrenManager: SailrenManager: Next spawn date of Sailren is 21:00 31.12.1969.
[28.06.25 01:53:02:509]  INFO bosses.ValakasManager: ValakasManager: State of Valakas is NOTSPAWN.
[28.06.25 01:53:02:509]  INFO bosses.ValakasManager: ValakasManager: Next spawn date of Valakas is 21:00 31.12.1969.
[28.06.25 01:53:02:593]  INFO tables.PetDataTable: PetDataTable: Loaded 3870 pets.
[28.06.25 01:53:02:599]  INFO model.Manor: ManorManager: Loaded 270 seeds
[28.06.25 01:53:02:699]  INFO scriptconfig.ScriptConfig: Loaded Service: ScripsConfig
[28.06.25 01:53:02:699]  INFO community.CharacterServicesCommunity: === CharacterServicesCommunity onLoad called ===
[28.06.25 01:53:02:700]  INFO community.CharacterServicesCommunity: COMMUNITYBOARD_ENABLED: true
[28.06.25 01:53:02:700]  INFO community.CharacterServicesCommunity: EnableMergeCommunity: false
[28.06.25 01:53:02:701]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Character Services Community loaded.
[28.06.25 01:53:02:777]  INFO dailyquests.AbstractDPScript: DailyQuestHandler: Loaded DailyQuests data
[28.06.25 01:53:02:777]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Handler registered successfully.
[28.06.25 01:53:02:785]  INFO model.Player: Loaded Service: SellPcService
[28.06.25 01:53:02:788]  INFO community.CareerManager: CommunityBoard: Manage Career service loaded.
[28.06.25 01:53:02:790]  INFO community.CommunityAuctionHouse: CommunityBoard: Auction System Service loaded.
[28.06.25 01:53:02:794]  INFO community.CommunityAugment: CommunityBoard: Augmentation Manager loaded with 188 lifestone skills.
[28.06.25 01:53:02:795]  INFO community.CommunityBoard: CommunityBoard: service loaded.
[28.06.25 01:53:02:795]  INFO community.CommunityBoardAcademy: CommunityBoard: Academy service loaded.
[28.06.25 01:53:02:796]  INFO community.CommunityBosses: CommunityBoard: Bosses loaded.
[28.06.25 01:53:02:799]  INFO community.CommunityClan: CommunityBoard: Clan service loaded.
[28.06.25 01:53:02:799]  INFO community.CommunityDropCalculator: CommunityBoard: Drop Calculator service loaded.
[28.06.25 01:53:02:802]  INFO community.CommunityEvents: CommunityBoard: Community Events service loaded.
[28.06.25 01:53:02:803]  INFO community.CommunityFacebook: CommunityBoard: Facebook Rewards page loaded.
[28.06.25 01:53:02:804]  INFO community.CommunityNpcs: CommunityBoard: Npcs loaded.
[28.06.25 01:53:02:805]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[28.06.25 01:53:02:811]  INFO community.CommunityTournament: CommunityBoard: Tournament loaded.
[28.06.25 01:53:02:811]  INFO community.CommunityWarehouse: CommunityBoard: Warehouse loaded.
[28.06.25 01:53:02:812]  INFO community.Forge: CommunityBoard: Forge loaded.
[28.06.25 01:53:02:814]  INFO community.ServicesCommunity: ServicesCommunity: Services Community service loaded.
[28.06.25 01:53:02:819]  INFO gameserver.GameServer: ===============[Spawn Manager]==================
[28.06.25 01:53:02:902]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1000 npc for group: NONE
[28.06.25 01:53:03:070]  INFO instancemanager.SpawnManager: SpawnManager: spawned 5000 npc for group: NONE
[28.06.25 01:53:03:101]  INFO naia.NaiaTowerManager: Naia Tower Manager: Loaded 12 rooms
[28.06.25 01:53:03:197]  INFO instancemanager.SpawnManager: SpawnManager: spawned 7000 npc for group: NONE
[28.06.25 01:53:03:583]  INFO instancemanager.SpawnManager: SpawnManager: spawned 12000 npc for group: NONE
[28.06.25 01:53:03:678]  INFO instancemanager.SpawnManager: SpawnManager: spawned 13000 npc for group: NONE
[28.06.25 01:53:04:756]  INFO instancemanager.SpawnManager: SpawnManager: spawned 21000 npc for group: NONE
[28.06.25 01:53:04:898]  INFO instancemanager.SpawnManager: SpawnManager: spawned 22000 npc for group: NONE
[28.06.25 01:53:05:447]  INFO instancemanager.SpawnManager: SpawnManager: spawned 25000 npc for group: NONE
[28.06.25 01:53:05:644]  INFO instancemanager.SpawnManager: SpawnManager: spawned 26000 npc for group: NONE
[28.06.25 01:53:06:042]  INFO instancemanager.SpawnManager: SpawnManager: spawned 28000 npc for group: NONE
[28.06.25 01:53:07:015]  INFO instancemanager.SpawnManager: SpawnManager: spawned 32000 npc for group: NONE
[28.06.25 01:53:08:001]  INFO instancemanager.SpawnManager: SpawnManager: spawned 35000 npc for group: NONE
[28.06.25 01:53:08:332]  INFO instancemanager.SpawnManager: SpawnManager: spawned 36000 npc for group: NONE
[28.06.25 01:53:08:671]  INFO instancemanager.SpawnManager: SpawnManager: spawned 37000 npc for group: NONE
[28.06.25 01:53:09:138]  INFO instancemanager.SpawnManager: SpawnManager: spawned 38373 npc; spawns: 17087; group: NONE
[28.06.25 01:53:09:139]  INFO gameserver.GameServer: --------------------------------------------------------------------=[ Boats ]
[28.06.25 01:53:09:140]  INFO data.BoatHolder: BoatHolder: loaded 2 boat(s) count.
[28.06.25 01:53:09:144]  INFO data.BoatHolder: BoatHolder: Spawning: RunePrimeval_Vehicle
[28.06.25 01:53:09:144]  INFO data.BoatHolder: BoatHolder: Spawning: AdenGracia_Airship
[28.06.25 01:53:09:145]  INFO holder.StaticObjectHolder: StaticObjectHolder: spawned: 50 static object(s).
[28.06.25 01:53:09:145]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Dimensional Rift ]
[28.06.25 01:53:09:149]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 13 room types with 112 rooms.
[28.06.25 01:53:09:150]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 300 DimensionalRift spawns, 0 errors.
[28.06.25 01:53:09:155]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Seven Signs ]
[28.06.25 01:53:09:164]  INFO instancemanager.AutoSpawnManager: AutoSpawnHandler: Loaded 50 handlers in total.
[28.06.25 01:53:09:164]  INFO gameserver.GameServer: ===================================================================
[28.06.25 01:53:09:165]  INFO gameserver.GameServer: ===================[Loading Olympiad System]=======================
[28.06.25 01:53:09:169]  INFO olympiad.Olympiad: Olympiad System: Loading Olympiad System....
[28.06.25 01:53:09:170]  INFO olympiad.Olympiad: Olympiad System: Currently in Validation Period
[28.06.25 01:53:09:170]  INFO olympiad.Olympiad: Olympiad System: Period Ends....
[28.06.25 01:53:09:171]  INFO olympiad.Olympiad: Olympiad System: In 0 days, 10 hours and 8 mins.
[28.06.25 01:53:09:171]  INFO olympiad.Olympiad: Olympiad System: Loaded 0 Noblesses
[28.06.25 01:53:09:173]  INFO entity.Hero: Hero System: Loaded 0 Heroes.
[28.06.25 01:53:09:174]  INFO entity.Hero: Hero System: Loaded 0 all time Heroes.
[28.06.25 01:53:09:174]  INFO gameserver.GameServer: ===================[Olympiad System Loaded]=======================
[28.06.25 01:53:09:174]  INFO gameserver.GameServer: ===================================================================
[28.06.25 01:53:09:175]  INFO instancemanager.PetitionManager: Initializing PetitionManager
[28.06.25 01:53:09:180]  INFO instancemanager.CursedWeaponsManager: CursedWeaponsManager: Loaded 2 cursed weapon(s).
[28.06.25 01:53:09:180]  INFO gameserver.GameServer: ------------------------------------------=[ Loaded Small Achievement System ]
[28.06.25 01:53:09:192]  INFO gameserver.GameServer: --------------------------------------------------------------=[ ItemHandler ]
[28.06.25 01:53:09:192]  INFO items.ItemHandler: ItemHandler: loaded 0 item handler(s) count.
[28.06.25 01:53:09:193]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Admin Commands ]
[28.06.25 01:53:09:193]  INFO admincommands.AdminCommandHandler: AdminCommandHandler: loaded 475 admin command handler(s) count.
[28.06.25 01:53:09:193]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Players Commands ]
[28.06.25 01:53:09:198]  INFO usercommands.UserCommandHandler: UserCommandHandler: loaded 17 user command handler(s) count.
[28.06.25 01:53:09:198]  INFO voicecommands.VoicedCommandHandler: VoicedCommandHandler: loaded 102 voiced command handler(s) count.
[28.06.25 01:53:09:202]  INFO gameserver.GameServer: ======================[Tournament By Kara`]==========================
[28.06.25 01:53:09:206]  INFO tournament.TournamentHolder: [TournamentHolder] Initialized 5 tournament holders:
[28.06.25 01:53:09:206]  INFO tournament.TournamentHolder: 1 vs 1
[28.06.25 01:53:09:206]  INFO tournament.TournamentHolder: 2 vs 2
[28.06.25 01:53:09:206]  INFO tournament.TournamentHolder: 4 vs 4
[28.06.25 01:53:09:206]  INFO tournament.TournamentHolder: 5 vs 5
[28.06.25 01:53:09:206]  INFO tournament.TournamentHolder: 6 vs 6
[28.06.25 01:53:09:208]  INFO gameserver.GameServer: ======================[Loading Castels & Clan Halls]==========================
[28.06.25 01:53:09:243]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Gludio Castle time - 16:00 29.06.2025
[28.06.25 01:53:09:247]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Dion Castle time - 16:00 29.06.2025
[28.06.25 01:53:09:250]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Giran Castle time - 20:00 29.06.2025
[28.06.25 01:53:09:253]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Oren Castle time - 20:00 29.06.2025
[28.06.25 01:53:09:257]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Aden Castle time - 16:00 29.06.2025
[28.06.25 01:53:09:263]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Innadril Castle time - 20:00 29.06.2025
[28.06.25 01:53:09:268]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Goddard Castle time - 20:00 29.06.2025
[28.06.25 01:53:09:272]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Rune Castle time - 16:00 29.06.2025
[28.06.25 01:53:09:277]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Schuttgart Castle time - 16:00 29.06.2025
[28.06.25 01:53:09:279]  INFO impl.ClanHallNpcSiegeEvent: ClanHallNpcSiegeEvent: Fortress Of Resistance time - 18:00 30.06.2025
[28.06.25 01:53:09:281]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:53:09:283]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:53:09:285]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Topaz Hall time - 15:00 02.07.2025
[28.06.25 01:53:09:286]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Ruby Hall time - 15:00 02.07.2025
[28.06.25 01:53:09:287]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Crystal Hall time - 15:00 02.07.2025
[28.06.25 01:53:09:288]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:53:09:291]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[28.06.25 01:53:09:292]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:53:09:293]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[28.06.25 01:53:09:295]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Atramental Barracks time - 15:00 02.07.2025
[28.06.25 01:53:09:296]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Scarlet Barracks time - 15:00 02.07.2025
[28.06.25 01:53:09:297]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Viridian Barracks time - 15:00 02.07.2025
[28.06.25 01:53:09:297]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Devastated Castle time - 18:00 28.06.2025
[28.06.25 01:53:09:298]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Bandit Stronghold time - 22:00 28.06.2025
[28.06.25 01:53:09:298]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[28.06.25 01:53:09:299]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[28.06.25 01:53:09:300]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[28.06.25 01:53:09:304]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[28.06.25 01:53:09:305]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Gold Manor time - 15:00 02.07.2025
[28.06.25 01:53:09:306]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[28.06.25 01:53:09:307]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[28.06.25 01:53:09:309]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[28.06.25 01:53:09:311]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[28.06.25 01:53:09:312]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[28.06.25 01:53:09:313]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[28.06.25 01:53:09:315]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:53:09:316]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:53:09:318]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[28.06.25 01:53:09:319]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[28.06.25 01:53:09:320]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Mont Chamber time - 15:00 02.07.2025
[28.06.25 01:53:09:321]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Astaire Chamber time - 15:00 02.07.2025
[28.06.25 01:53:09:322]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Aria Chamber time - 15:00 02.07.2025
[28.06.25 01:53:09:324]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Yiana Chamber time - 15:00 02.07.2025
[28.06.25 01:53:09:325]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Roien Chamber time - 15:00 02.07.2025
[28.06.25 01:53:09:326]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Luna Chamber time - 15:00 02.07.2025
[28.06.25 01:53:09:327]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Traban Chamber time - 15:00 02.07.2025
[28.06.25 01:53:09:328]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Eisen Hall time - 15:00 02.07.2025
[28.06.25 01:53:09:329]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Heavy Metal Hall time - 15:00 02.07.2025
[28.06.25 01:53:09:330]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Molten Ore Hall time - 15:00 02.07.2025
[28.06.25 01:53:09:331]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Titan Hall time - 15:00 02.07.2025
[28.06.25 01:53:09:332]  INFO impl.ClanHallMiniGameEvent: ClanHallMiniGameEvent: Rainbow Springs time - 22:00 28.06.2025
[28.06.25 01:53:09:332]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Wild Beast Reserve time - 21:00 28.06.2025
[28.06.25 01:53:09:332]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Fortress of Dead time - 18:00 28.06.2025
[28.06.25 01:53:09:334]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Gludio Dominion time - undefined
[28.06.25 01:53:09:335]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Dion Dominion time - undefined
[28.06.25 01:53:09:335]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Giran Dominion time - undefined
[28.06.25 01:53:09:336]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Oren Dominion time - undefined
[28.06.25 01:53:09:338]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Aden Dominion time - undefined
[28.06.25 01:53:09:338]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Innadril Dominion time - undefined
[28.06.25 01:53:09:339]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Goddard Dominion time - undefined
[28.06.25 01:53:09:339]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Rune Dominion time - undefined
[28.06.25 01:53:09:339]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Schuttgart Dominion time - undefined
[28.06.25 01:53:09:344]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Shanty Fortress time - undefined
[28.06.25 01:53:09:346]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Southern Fortress time - undefined
[28.06.25 01:53:09:349]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hive Fortress time - undefined
[28.06.25 01:53:09:352]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Valley Fortress time - undefined
[28.06.25 01:53:09:355]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Ivory Fortress time - undefined
[28.06.25 01:53:09:357]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Narsell Fortress time - undefined
[28.06.25 01:53:09:360]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Bayou Fortress time - undefined
[28.06.25 01:53:09:362]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: White Sands Fortress time - undefined
[28.06.25 01:53:09:365]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Borderland Fortress time - undefined
[28.06.25 01:53:09:368]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Swamp Fortress time - undefined
[28.06.25 01:53:09:372]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Archaic Fortress time - undefined
[28.06.25 01:53:09:376]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Floran Fortress time - undefined
[28.06.25 01:53:09:380]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Cloud Mountain Fortress time - undefined
[28.06.25 01:53:09:384]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Tanor Fortress time - undefined
[28.06.25 01:53:09:388]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Dragonspine Fortress time - undefined
[28.06.25 01:53:09:391]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Antharas Fortress time - undefined
[28.06.25 01:53:09:394]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Western Fortress time - undefined
[28.06.25 01:53:09:397]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hunter's Fortress time - undefined
[28.06.25 01:53:09:400]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Aaru Fortress time - undefined
[28.06.25 01:53:09:403]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Demon Fortress time - undefined
[28.06.25 01:53:09:408]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Monastic Fortress time - undefined
[28.06.25 01:53:09:410]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 70-75 Level time - 02:00 28.06.2025
[28.06.25 01:53:09:410]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 76-79 Level time - 02:00 28.06.2025
[28.06.25 01:53:09:410]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 80-85 Level time - 02:00 28.06.2025
[28.06.25 01:53:09:411]  INFO fightclub.LastManStandingEvent: LastManStandingEvent: Last Hero time - undefined
[28.06.25 01:53:09:411]  INFO fightclub.TeamVSTeamEvent: TeamVSTeamEvent: Team VS Team time - undefined
[28.06.25 01:53:09:411]  INFO fightclub.LuckyCreaturesEvent: LuckyCreaturesEvent: Lucky Creatures time - undefined
[28.06.25 01:53:09:412]  INFO fightclub.CaptureTheFlagEvent: CaptureTheFlagEvent: Capture The Flag time - undefined
[28.06.25 01:53:09:412]  INFO fightclub.FFATreasureHuntEvent: FFATreasureHuntEvent: Treasure Hunt time - undefined
[28.06.25 01:53:09:412]  INFO fightclub.KoreanStyleEvent: KoreanStyleEvent: Korean Style time - undefined
[28.06.25 01:53:09:413]  INFO fightclub.ProtectTheKingEvent: ProtectTheKingEvent: Protect The King time - undefined
[28.06.25 01:53:09:414]  INFO fightclub.FightForThroneEvent: FightForThroneEvent: Fight For Throne time - undefined
[28.06.25 01:53:09:415]  INFO fightclub.DeathMatchEvent: DeathMatchEvent: Death Match time - undefined
[28.06.25 01:53:09:415]  INFO fightclub.RussianRouletteEvent: RussianRouletteEvent: Russian Roulette time - undefined
[28.06.25 01:53:09:416]  INFO instancemanager.CastleManorManager: Manor System: Initializing...
[28.06.25 01:53:09:419]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:53:09:420]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Auto Cleaner ]
[28.06.25 01:53:09:421]  INFO gameserver.GameServer: IdFactory: Free ObjectID's remaining: 1879004849
[28.06.25 01:53:09:421]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:53:09:422]  INFO instancemanager.CoupleManager: Initializing CoupleManager
[28.06.25 01:53:09:423]  INFO instancemanager.CoupleManager: Loaded: 0 couples(s)
[28.06.25 01:53:09:428]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Hellbound ]
[28.06.25 01:53:09:437]  INFO instancemanager.HellboundManager: HellboundManager: Loaded 335 spawn entries.
[28.06.25 01:53:09:589]  INFO instancemanager.HellboundManager: HellboundManager: Spawned 623 mobs and NPCs according to the current Hellbound stage
[28.06.25 01:53:09:590]  INFO instancemanager.HellboundManager: Hellbound Manager: Loaded
[28.06.25 01:53:09:592]  INFO naia.NaiaTowerManager: Naia Core Manager: Loaded
[28.06.25 01:53:09:593]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:53:09:593]  INFO instancemanager.SoDManager: Seed of Destruction Manager: Loaded
[28.06.25 01:53:09:594]  INFO instancemanager.SoIManager: Seed of Infinity Manager: Loaded. Current stage is: 1
[28.06.25 01:53:09:595]  INFO instancemanager.SpawnManager: SpawnManager: spawned 4 npc; spawns: 4; group: soi_world_mouths
[28.06.25 01:53:09:595]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: soi_world_abyssgaze2
[28.06.25 01:53:09:596]  INFO instancemanager.BloodAltarManager: Blood Altar Manager: Initializing...
[28.06.25 01:53:09:605]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_alive_npc
[28.06.25 01:53:09:608]  INFO gameserver.GameServer: Preparing Drop Calculator
[28.06.25 01:53:09:671]  INFO gameserver.GameServer: ----------------------------------------------------------=[ Offline Buffers ]
[28.06.25 01:53:09:673]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loading offline buffers...
[28.06.25 01:53:09:675]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loaded: 0 offline buffer(s)
[28.06.25 01:53:09:687]  INFO achievements.Achievements: Achievement System: Loaded 4 achievement categories and 39 achievements.
[28.06.25 01:53:09:689]  INFO gameserver.GameServer: Emotions Loaded....
[28.06.25 01:53:09:689]  INFO gameserver.GameServer: --------------------------------------------------=[ DataBase Cleaner Loaded ]
[28.06.25 01:53:09:690]  INFO dao.CharacterDAO: Found 0 characters to delete!
[28.06.25 01:53:09:767]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Initializing
[28.06.25 01:53:09:769]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Load OK
[28.06.25 01:53:09:769]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Loaded 0 announce.
[28.06.25 01:53:09:771]  INFO gameserver.GameServer: ---------------------------------------------------=[ Loaded Facebook System ]
[28.06.25 01:53:09:774]  INFO gameserver.GameServer: -----------------------------------------------------=[ Loaded Stream System ]
[28.06.25 01:53:09:778]  INFO holder.ProxiesHolder: ProxiesHolder: loaded 20 proxies(s) count.
[28.06.25 01:53:09:778]  INFO gameserver.GameServer: ------------------------------------------------------=[ Loaded Proxy System ]
[28.06.25 01:53:11:748]  INFO donation.DonationReader: Donation Reader service started
[28.06.25 01:53:11:748]  INFO gameserver.GameServer: --------------------------------------------=[ Loaded AUTO - Donation System ]
[28.06.25 01:53:11:750]  INFO taskmanager.BackupTaskManager: Next Auto Backup in 4 H., 46 Min.
[28.06.25 01:53:11:759]  INFO votingengine.VotingSettings: VotingSettings: Loaded 3 messages, 4 drops!
[28.06.25 01:53:11:761]  INFO votingengine.VotingRewardAPI: VoteReward: Has been successfully loaded!
[28.06.25 01:53:11:763]  INFO model.PhantomPlayers: Loading phantom players...
[28.06.25 01:53:11:769]  INFO model.PhantomPlayers: Loaded 32002 possible phantom names.
[28.06.25 01:53:11:770]  INFO model.PhantomPlayers: Loaded 7172 possible phantom titles.
[28.06.25 01:53:11:770]  INFO model.PhantomPlayers: PhantomPlayers: Cached 0 players.
[28.06.25 01:53:11:771]  INFO model.PhantomPlayers: PhantomPlayers: Free names 32002.
[28.06.25 01:53:11:771]  INFO model.PhantomPlayers: Loaded 0 phantom players from database with a maximum of 3000 phantoms.
[28.06.25 01:53:11:771]  INFO model.PhantomPlayers: Scheduled spawner with 1 seconds delay.
[28.06.25 01:53:11:944]  INFO instancemanager.SpawnManager: SpawnManager: spawned 443 npc; spawns: 121; group: DAY
[28.06.25 01:53:12:024]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dawn_spawn
[28.06.25 01:53:12:298]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dusk_spawn
[28.06.25 01:53:12:298]  INFO gameserver.GameServer: Telnet server is currently disabled.
[28.06.25 01:53:12:300]  INFO gameserver.Shutdown: Scheduled server restart in 3h 51m 48s.
[28.06.25 01:53:12:301]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:53:12:301]  INFO gameserver.GameServer: >>>>>>>>>>>>>>> GameServer Started <<<<<<<<<<<<<<
[28.06.25 01:53:12:301]  INFO gameserver.GameServer: Maximum Numbers of Connected Players: 3000
[28.06.25 01:53:12:301]  INFO gameserver.GameServer: ====================================================================
[28.06.25 01:53:12:321]  INFO gameserver.GameServer: AllowedMemory: ........... 6291456 KB
[28.06.25 01:53:12:321]  INFO gameserver.GameServer: 
Allocated: ............... 4194304 KB (66.6667%)
[28.06.25 01:53:12:321]  INFO gameserver.GameServer: 
Non-Allocated: ........... 2097152 KB (33.3333%)
[28.06.25 01:53:12:322]  INFO gameserver.GameServer: 
AllocatedMemory: ......... 4194304 KB
[28.06.25 01:53:12:322]  INFO gameserver.GameServer: Used: .................... 1152580 KB (18.3198%)
[28.06.25 01:53:12:322]  INFO gameserver.GameServer: 
Unused (cached): ......... 3041723 KB (48.3469%)
[28.06.25 01:53:12:322]  INFO gameserver.GameServer: 
UseableMemory: ........... 5138875 KB (81.6802%)
[28.06.25 01:53:12:322]  INFO gameserver.GameServer: 

[28.06.25 01:53:12:322]  INFO gameserver.GameServer: ====================================================================
[28.06.25 01:53:12:325]  INFO loginservercon.AuthServerCommunication: Connecting to authserver on 127.0.0.1:9014
[28.06.25 01:53:12:335]  INFO lspackets.AuthResponse: Registered on authserver as 1 [Bartz]
[28.06.25 01:53:55:713]  INFO community.CharacterServicesCommunity: onBypassCommand called - bypass: _bbscharservices;registeremail
[28.06.25 01:53:55:713]  INFO community.CharacterServicesCommunity: cmd: _bbscharservices, action: registeremail
[28.06.25 01:54:09:142]  INFO community.CharacterServicesCommunity: === onWriteCommand called ===
[28.06.25 01:54:09:142]  INFO community.CharacterServicesCommunity: Player: Dzu
[28.06.25 01:54:09:142]  INFO community.CharacterServicesCommunity: Bypass: '_bbscharservices;registeremail;submit'
[28.06.25 01:54:09:143]  INFO community.CharacterServicesCommunity: arg1: '0'
[28.06.25 01:54:09:143]  INFO community.CharacterServicesCommunity: arg2: '0'
[28.06.25 01:54:09:143]  INFO community.CharacterServicesCommunity: arg3: '<EMAIL>'
[28.06.25 01:54:09:143]  INFO community.CharacterServicesCommunity: arg4: '<EMAIL>'
[28.06.25 01:54:09:143]  INFO community.CharacterServicesCommunity: arg5: ''
[28.06.25 01:54:09:144]  INFO community.CharacterServicesCommunity: Processing email registration - email: <EMAIL>, confirmEmail: <EMAIL>
[28.06.25 01:54:09:156]  INFO utils.EmailVerification: Verification code stored for account: siniez, email: <EMAIL>
[28.06.25 01:54:09:157]  INFO community.CharacterServicesCommunity: About to send email - Config.MAIL_USER: '<EMAIL>', Config.MAIL_PASS: 'SET'
[28.06.25 01:54:09:157]  INFO utils.EmailVerification: Checking email configuration - MAIL_USER: '<EMAIL>', MAIL_PASS: 'SET'
[28.06.25 01:54:10:109]  INFO utils.EmailVerification: SMTP Initial: 220 smtp.gmail.com ESMTP d75a77b69052e-4a7fc55c383sm24174171cf.47 - gsmtp
[28.06.25 01:54:10:313]  INFO utils.EmailVerification: SMTP EHLO: 250-smtp.gmail.com at your service, [***************]
250-SIZE ********
250-8BITMIME
250-STARTTLS
250-ENHANCEDSTATUSCODES
250-PIPELINING
250-CHUNKING
250 SMTPUTF8

[28.06.25 01:54:10:515]  INFO utils.EmailVerification: SMTP STARTTLS: 220 2.0.0 Ready to start TLS
[28.06.25 01:54:10:854]  INFO utils.EmailVerification: SMTP EHLO after TLS: 250-smtp.gmail.com at your service, [***************]
250-SIZE ********
250-8BITMIME
250-AUTH LOGIN PLAIN XOAUTH2 PLAIN-CLIENTTOKEN OAUTHBEARER XOAUTH
250-ENHANCEDSTATUSCODES
250-PIPELINING
250-CHUNKING
250 SMTPUTF8

[28.06.25 01:54:11:055]  INFO utils.EmailVerification: SMTP AUTH LOGIN: 334 VXNlcm5hbWU6
[28.06.25 01:54:11:272]  INFO utils.EmailVerification: SMTP Username: 334 UGFzc3dvcmQ6
[28.06.25 01:54:11:610]  INFO utils.EmailVerification: SMTP Password: 235 2.7.0 Accepted
[28.06.25 01:54:11:811]  INFO utils.EmailVerification: SMTP MAIL FROM: 250 2.1.0 OK d75a77b69052e-4a7fc55c383sm24174171cf.47 - gsmtp
[28.06.25 01:54:12:013]  INFO utils.EmailVerification: SMTP RCPT TO: 250 2.1.5 OK d75a77b69052e-4a7fc55c383sm24174171cf.47 - gsmtp
[28.06.25 01:54:12:336]  INFO utils.EmailVerification: SMTP DATA: 354 Go ahead d75a77b69052e-4a7fc55c383sm24174171cf.47 - gsmtp
[28.06.25 01:54:12:976]  INFO utils.EmailVerification: SMTP Email sent: 250 2.0.0 OK  1751086454 d75a77b69052e-4a7fc55c383sm24174171cf.47 - gsmtp
[28.06.25 01:54:13:178]  INFO utils.EmailVerification: SMTP QUIT: 221 2.0.0 closing connection d75a77b69052e-4a7fc55c383sm24174171cf.47 - gsmtp
[28.06.25 01:54:13:178]  INFO utils.EmailVerification: Email sent successfully to: <EMAIL> for player: Dzu
[28.06.25 01:54:49:770]  INFO tasks.RestoreOfflineTraders: Restored 0 offline traders
[28.06.25 01:59:23:048]  INFO gameserver.GameServer: ============================================================================
[28.06.25 01:59:23:049]  INFO gameserver.GameServer: Nome: .................................................................... GAME SERVER
[28.06.25 01:59:23:049]  INFO gameserver.GameServer: Project Revision: ........................................................ LII [MultVerso]
[28.06.25 01:59:23:050]  INFO gameserver.GameServer: Update: .................................................................. High Five: Part 5
[28.06.25 01:59:23:050]  INFO gameserver.GameServer: Numero da Revision:....................................................... 2.0
[28.06.25 01:59:23:050]  INFO gameserver.GameServer: Build Revision: .......................................................... MultVerso
[28.06.25 01:59:23:050]  INFO gameserver.GameServer: Build date: .............................................................. 2025.06.28 01:57
[28.06.25 01:59:23:050]  INFO gameserver.GameServer: Compiler version: ........................................................ 17.0.15+9-LTS-241 (Oracle Corporation)
[28.06.25 01:59:23:050]  INFO gameserver.GameServer: ============================================================================
[28.06.25 01:59:23:083]  INFO gameserver.Config: Loaded 6 trade words.
[28.06.25 01:59:23:090]  INFO gameserver.Config: Abuse: Loaded 15 abuse words.
[28.06.25 01:59:23:128]  INFO gameserver.Config: Loaded 313 Configs!
[28.06.25 01:59:23:135]  INFO gameserver.GameServer: IP EXTERNO =============================127.0.0.1
[28.06.25 01:59:23:135]  INFO gameserver.GameServer: IP INTERNO =============================127.0.0.1
[28.06.25 01:59:23:135]  INFO gameserver.GameServer: Maximo Online IP ====================== 3000
[28.06.25 01:59:23:136]  INFO gameserver.GameServer: Hora do Restart  =======================45 5 * * *
[28.06.25 01:59:23:286]  INFO gameserver.GameServer: -----------------------------------------=[ Loading Protection Configuration ]
[28.06.25 01:59:23:288]  INFO idfactory.IdFactory: IdFactory: Clear characters online status.
[28.06.25 01:59:23:309]  INFO idfactory.IdFactory: IdFactory: Cleaned 0 elements from database in 0sec.
[28.06.25 01:59:23:316]  INFO idfactory.IdFactory: IdFactory: Extracted 2 used id's from characters
[28.06.25 01:59:23:316]  INFO idfactory.IdFactory: IdFactory: Extracted 46 used id's from items
[28.06.25 01:59:23:317]  INFO idfactory.IdFactory: IdFactory: Extracted total 48 used id's.
[28.06.25 01:59:23:317]  INFO idfactory.BitSetIDFactory: IdFactory: 102912 id's available.
[28.06.25 01:59:23:413]  INFO gameserver.GameServer: ===============[Loading Scripts]==================
[28.06.25 01:59:23:414]  INFO scripts.Scripts: Scripts: Loading...
[28.06.25 01:59:23:414]  INFO scripts.Scripts: Loading Server Scripts
[28.06.25 01:59:23:695]  INFO scripts.Scripts: Scripts: Loaded 1288 classes.
[28.06.25 01:59:23:702]  INFO vote.VoteMain: Loaded: Vote System - Individual Reward.
[28.06.25 01:59:23:703]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Twitch Manager ]
[28.06.25 01:59:23:728]  INFO gameserver.GameServer: ----------------------------------------------------------=[ AntiFeedManager ]
[28.06.25 01:59:23:728]  INFO gameserver.GameServer: -------------------------------------------------------------=[ QuestManager ]
[28.06.25 01:59:23:729]  INFO gameserver.GameServer: QuestManager : loaded 0 quest's
[28.06.25 01:59:23:739]  INFO tables.FakePlayersTable: FakePlayersTable: Loaded 32002 Fake Players.
[28.06.25 01:59:23:746]  INFO utils.Strings: Loaded 418 translit entries.
[28.06.25 01:59:23:748]  INFO gameserver.GameTimeController: GameTimeController: initialized. Current time is 17:56 in the day.
[28.06.25 01:59:23:749]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Lineage World ]
[28.06.25 01:59:23:751]  INFO model.World: L2World: Creating regions: [256][272][32].
[28.06.25 01:59:23:751]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:59:23:753]  INFO htm.HtmCache: HtmCache: disabled.
[28.06.25 01:59:23:759]  INFO data.StringHolder: StringHolder: load strings: 1066 for lang: ENGLISH
[28.06.25 01:59:23:760]  INFO data.StringHolder: StringHolder: load strings: 876 for lang: RUSSIAN
[28.06.25 01:59:25:082]  INFO skills.SkillsEngine: SkillsEngine: Loaded 66051 skill templates from XML files. Max id: 91000, max level: 259
[28.06.25 01:59:25:665]  INFO holder.OptionDataHolder: OptionDataHolder: loaded 24975 option data(s) count.
[28.06.25 01:59:26:259]  INFO holder.ItemHolder: ItemHolder: loaded 19240 item(s) count.
[28.06.25 01:59:26:263]  INFO holder.FakePlayerNpcsHolder: FakePlayerNpcsHolder: loaded 8 fake player npcs(s) count.
[28.06.25 01:59:27:238]  INFO holder.NpcHolder: NpcHolder: loaded 10519 npc(s) count.
[28.06.25 01:59:27:244]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[28.06.25 01:59:27:256]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[28.06.25 01:59:27:286]  INFO holder.ExchangeItemHolder: ExchangeItemHolder: loaded 602 exchange item(s) count.
[28.06.25 01:59:27:289]  INFO holder.StaticObjectHolder: StaticObjectHolder: loaded 50 static object(s) count.
[28.06.25 01:59:27:372]  INFO holder.DoorHolder: DoorHolder: loaded 1155 door(s) count.
[28.06.25 01:59:27:419]  INFO holder.ZoneHolder: ZoneHolder: loaded 1260 zone(s) count.
[28.06.25 01:59:27:715]  INFO holder.SpawnHolder: SpawnHolder: loaded 28949 spawn(s) count.
[28.06.25 01:59:27:785]  INFO holder.InstantZoneHolder: InstantZoneHolder: loaded 94 instant zone(s) count.
[28.06.25 01:59:28:073]  INFO holder.AirshipDockHolder: AirshipDockHolder: loaded 4 airship dock(s) count.
[28.06.25 01:59:28:115]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 57215 normal learns for 103 classes.
[28.06.25 01:59:28:115]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 79 transfer learns for 3 classes.
[28.06.25 01:59:28:115]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 120 transformation learns for 6 races.
[28.06.25 01:59:28:116]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 662 fishing learns for 6 races.
[28.06.25 01:59:28:116]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 72 certification learns.
[28.06.25 01:59:28:116]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 3 collection learns.
[28.06.25 01:59:28:116]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 44 pledge learns.
[28.06.25 01:59:28:116]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 18 sub unit learns.
[28.06.25 01:59:28:123]  INFO holder.CharTemplateHolder: CharTemplateHolder: loaded 206 char template(s) count.
[28.06.25 01:59:28:206]  INFO holder.ResidenceHolder: ResidenceHolder: total size: 83
[28.06.25 01:59:28:206]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 44 clanhall(s).
[28.06.25 01:59:28:207]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 castle(s).
[28.06.25 01:59:28:207]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 21 fortress(s).
[28.06.25 01:59:28:207]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 dominion(s).
[28.06.25 01:59:28:260]  INFO instancemanager.RaidBossSpawnManager: RaidBossSpawnManager: Loaded 194 Statuses
[28.06.25 01:59:28:272]  INFO entity.SevenSigns: SevenSigns: Currently in the Competition (Quest Event) period!
[28.06.25 01:59:28:273]  INFO entity.SevenSigns: SevenSigns: The Seal of Avarice remains unclaimed.
[28.06.25 01:59:28:273]  INFO entity.SevenSigns: SevenSigns: The Seal of Gnosis remains unclaimed.
[28.06.25 01:59:28:273]  INFO entity.SevenSigns: SevenSigns: The Seal of Strife remains unclaimed.
[28.06.25 01:59:28:273]  INFO entity.SevenSigns: SevenSigns: The Competition this week, if the trend continue, will end with a tie.
[28.06.25 01:59:28:274]  INFO entity.SevenSigns: SevenSigns: Next period begins in 2 days, 16 hours and 0 mins.
[28.06.25 01:59:28:375]  INFO holder.EventHolder: EventHolder: loaded 105 event(s) count.
[28.06.25 01:59:28:387]  INFO holder.FightClubMapHolder: FightClubMapHolder: loaded 42 fight club map(s) count.
[28.06.25 01:59:28:391]  INFO holder.CubicHolder: CubicHolder: loaded 145 cubic(s) count.
[28.06.25 01:59:28:430]  INFO holder.BuyListHolder: TradeController: Loaded 4 file(s).
[28.06.25 01:59:28:430]  INFO holder.BuyListHolder: TradeController: Loaded 25014 Items.
[28.06.25 01:59:28:431]  INFO holder.BuyListHolder: TradeController: Loaded 726 Buylists.
[28.06.25 01:59:28:444]  INFO holder.RecipeHolder: RecipeController: Loaded 998 Recipes.
[28.06.25 01:59:28:770]  INFO holder.ProductHolder: ProductItemTable: Loaded 0 product item on sale.
[28.06.25 01:59:28:779]  INFO holder.HennaHolder: HennaHolder: loaded 180 henna(s) count.
[28.06.25 01:59:28:783]  INFO holder.EnchantItemHolder: EnchantItemHolder: load 4 enchant scroll(s).
[28.06.25 01:59:28:786]  INFO holder.SoulCrystalHolder: SoulCrystalHolder: loaded 54 soul crystal(s) count.
[28.06.25 01:59:28:792]  INFO holder.ArmorSetsHolder: ArmorSetsHolder: loaded 199 armor sets(s) count.
[28.06.25 01:59:28:797]  INFO holder.PetitionGroupHolder: PetitionGroupHolder: loaded 3 petition group(s) count.
[28.06.25 01:59:28:801]  INFO holder.DressArmorHolder: DressArmorHolder: loaded 57 dress armor(s) count.
[28.06.25 01:59:28:803]  INFO holder.DressCloakHolder: DressCloakHolder: loaded 7 dress cloak(s) count.
[28.06.25 01:59:28:806]  INFO holder.DressShieldHolder: DressShieldHolder: loaded 10 dress shield(s) count.
[28.06.25 01:59:28:810]  INFO holder.DressWeaponHolder: DressWeaponHolder: loaded 401 dress weapon(s) count.
[28.06.25 01:59:28:860]  INFO holder.AugmentationDataHolder: AugmentationDataHolder: loaded 1022 augmentation data(s) count.
[28.06.25 01:59:28:864]  INFO holder.PremiumHolder: PremiumHolder: loaded 5 premium(s) count.
[28.06.25 01:59:28:866]  INFO holder.FoundationHolder: FoundationHolder: loaded 268 foundation(s) count.
[28.06.25 01:59:28:871]  INFO holder.DonationHolder: DonationHolder: loaded 192 donation(s) count.
[28.06.25 01:59:28:874]  INFO holder.TournamentMapHolder: TournamentMapHolder: loaded 3 tournament map(s) count.
[28.06.25 01:59:28:878]  INFO holder.FacebookCommentsHolder: FacebookCommentsHolder: loaded 2 facebook comments(s) count.
[28.06.25 01:59:28:878]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Banned HWIDS ]
[28.06.25 01:59:28:879]  INFO security.HWIDBan: black list (Hwid) loaded size: 0
[28.06.25 01:59:28:879]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Clan Crests ]
[28.06.25 01:59:28:881]  INFO cache.CrestCache: CrestCache: Loaded 0 crests
[28.06.25 01:59:28:881]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Loading Images ]
[28.06.25 01:59:28:883]  INFO cache.ImagesCache: ImagesCache: Loading images...
[28.06.25 01:59:39:550]  INFO cache.ImagesCache: ImagesCache: Loaded 8734 images
[28.06.25 01:59:39:551]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:59:39:555]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Fish Table ]
[28.06.25 01:59:39:557]  INFO tables.FishTable: FishTable: Loaded 270 fishes.
[28.06.25 01:59:39:559]  INFO tables.FishTable: FishTable: Loaded 919 fish rewards.
[28.06.25 01:59:39:559]  INFO gameserver.GameServer: -------------------------------------------------------------------=[ Skills ]
[28.06.25 01:59:39:560]  INFO tables.SkillTreeTable: SkillTreeTable: Loaded 526 enchanted skills.
[28.06.25 01:59:39:586]  INFO datatables.EnchantNamesTable: EnchantNamesTable: Loaded: 1319 skill enchant names
[28.06.25 01:59:39:586]  INFO gameserver.GameServer: --------------------------------------------------------=[ Augmentation Data ]
[28.06.25 01:59:39:587]  INFO tables.AugmentationData: Initializing AugmentationData.
[28.06.25 01:59:39:598]  INFO tables.AugmentationData: AugmentationData: 10 bad skill(s) were skipped.
[28.06.25 01:59:39:609]  INFO tables.AugmentationData: AugmentationData: Loaded: 52 augmentation stats.
[28.06.25 01:59:39:609]  INFO tables.AugmentationData: AugmentationData: Loaded: 24 accessory augmentation stats.
[28.06.25 01:59:39:612]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 0
[28.06.25 01:59:39:612]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 1
[28.06.25 01:59:39:612]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 2
[28.06.25 01:59:39:613]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 3
[28.06.25 01:59:39:613]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 4
[28.06.25 01:59:39:613]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 5
[28.06.25 01:59:39:613]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 6
[28.06.25 01:59:39:614]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 7
[28.06.25 01:59:39:614]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 8
[28.06.25 01:59:39:617]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 9
[28.06.25 01:59:39:619]  INFO tables.EnchantHPBonusTable: EnchantHPBonusTable: Loaded bonuses for 6 grades.
[28.06.25 01:59:39:619]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Level Up Table ]
[28.06.25 01:59:39:621]  INFO tables.LevelUpTable: LevelUpData: Loaded 103 Character Level Up Templates.
[28.06.25 01:59:39:624]  INFO tables.PetSkillsTable: PetSkillsTable: Loaded 2558 skills.
[28.06.25 01:59:39:624]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Item Logs ]
[28.06.25 01:59:39:625]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Auctioneer ]
[28.06.25 01:59:39:626]  INFO itemauction.ItemAuctionManager: Initializing ItemAuctionManager
[28.06.25 01:59:39:634]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 1 auction(s) for instance 32320.
[28.06.25 01:59:39:636]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 1 on 2025.07.01 17:00:00 for instance 32320
[28.06.25 01:59:39:638]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 2 auction(s) for instance 32321.
[28.06.25 01:59:39:638]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 4 on 2025.07.03 17:00:00 for instance 32321
[28.06.25 01:59:39:639]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 1 auction(s) for instance 32322.
[28.06.25 01:59:39:639]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 3 on 2025.06.28 17:00:00 for instance 32322
[28.06.25 01:59:39:640]  INFO itemauction.ItemAuctionManager: ItemAuctionManager: Loaded 3 instance(s).
[28.06.25 01:59:39:640]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Masterio Pack ]
[28.06.25 01:59:39:674]  INFO gameserver.GameServer: ------------------------------------------------------=[ Merge System Loaded ]
[28.06.25 01:59:39:696]  INFO bosses.AntharasManager: AntharasManager: State of Antharas is NOTSPAWN.
[28.06.25 01:59:39:698]  INFO bosses.AntharasManager: AntharasManager: Next spawn date of Antharas is 21:00 31.12.1969.
[28.06.25 01:59:39:701]  INFO bosses.BaiumManager: BaiumManager: State of Baium is NOTSPAWN.
[28.06.25 01:59:39:706]  INFO bosses.BaiumManager: BaiumManager: Next spawn date: 21:00 31.12.1969
[28.06.25 01:59:39:717]  INFO bosses.BelethManager: Beleth Manager: Loaded successfully
[28.06.25 01:59:39:720]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Loaded 20 Mysterious-Box spawns.
[28.06.25 01:59:39:723]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 physical monsters spawns.
[28.06.25 01:59:39:727]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 magical monsters spawns.
[28.06.25 01:59:39:729]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 92 Church of duke monsters spawns.
[28.06.25 01:59:39:730]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 68 Emperor's grave NPC spawns.
[28.06.25 01:59:39:732]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Conquerors' Sepulcher Manager
[28.06.25 01:59:39:732]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Emperors' Sepulcher Manager
[28.06.25 01:59:39:734]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Great Sages' Sepulcher Manager
[28.06.25 01:59:39:734]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Judges' Sepulcher Manager
[28.06.25 01:59:39:735]  INFO bosses.FourSepulchersManager: FourSepulchersManager: Beginning in WarmUp time
[28.06.25 01:59:39:736]  INFO bosses.SailrenManager: SailrenManager: State of Sailren is NOTSPAWN.
[28.06.25 01:59:39:736]  INFO bosses.SailrenManager: SailrenManager: Next spawn date of Sailren is 21:00 31.12.1969.
[28.06.25 01:59:39:736]  INFO bosses.ValakasManager: ValakasManager: State of Valakas is NOTSPAWN.
[28.06.25 01:59:39:737]  INFO bosses.ValakasManager: ValakasManager: Next spawn date of Valakas is 21:00 31.12.1969.
[28.06.25 01:59:39:823]  INFO tables.PetDataTable: PetDataTable: Loaded 3870 pets.
[28.06.25 01:59:39:829]  INFO model.Manor: ManorManager: Loaded 270 seeds
[28.06.25 01:59:39:945]  INFO scriptconfig.ScriptConfig: Loaded Service: ScripsConfig
[28.06.25 01:59:39:946]  INFO community.CharacterServicesCommunity: === CharacterServicesCommunity onLoad called ===
[28.06.25 01:59:39:946]  INFO community.CharacterServicesCommunity: COMMUNITYBOARD_ENABLED: true
[28.06.25 01:59:39:946]  INFO community.CharacterServicesCommunity: EnableMergeCommunity: false
[28.06.25 01:59:39:946]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Character Services Community loaded.
[28.06.25 01:59:40:000]  INFO dailyquests.AbstractDPScript: DailyQuestHandler: Loaded DailyQuests data
[28.06.25 01:59:40:000]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Handler registered successfully.
[28.06.25 01:59:40:008]  INFO model.Player: Loaded Service: SellPcService
[28.06.25 01:59:40:009]  INFO community.CareerManager: CommunityBoard: Manage Career service loaded.
[28.06.25 01:59:40:011]  INFO community.CommunityAuctionHouse: CommunityBoard: Auction System Service loaded.
[28.06.25 01:59:40:015]  INFO community.CommunityAugment: CommunityBoard: Augmentation Manager loaded with 188 lifestone skills.
[28.06.25 01:59:40:016]  INFO community.CommunityBoard: CommunityBoard: service loaded.
[28.06.25 01:59:40:017]  INFO community.CommunityBoardAcademy: CommunityBoard: Academy service loaded.
[28.06.25 01:59:40:017]  INFO community.CommunityBosses: CommunityBoard: Bosses loaded.
[28.06.25 01:59:40:020]  INFO community.CommunityClan: CommunityBoard: Clan service loaded.
[28.06.25 01:59:40:021]  INFO community.CommunityDropCalculator: CommunityBoard: Drop Calculator service loaded.
[28.06.25 01:59:40:021]  INFO community.CommunityEvents: CommunityBoard: Community Events service loaded.
[28.06.25 01:59:40:022]  INFO community.CommunityFacebook: CommunityBoard: Facebook Rewards page loaded.
[28.06.25 01:59:40:023]  INFO community.CommunityNpcs: CommunityBoard: Npcs loaded.
[28.06.25 01:59:40:024]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[28.06.25 01:59:40:029]  INFO community.CommunityTournament: CommunityBoard: Tournament loaded.
[28.06.25 01:59:40:029]  INFO community.CommunityWarehouse: CommunityBoard: Warehouse loaded.
[28.06.25 01:59:40:030]  INFO community.Forge: CommunityBoard: Forge loaded.
[28.06.25 01:59:40:031]  INFO community.ServicesCommunity: ServicesCommunity: Services Community service loaded.
[28.06.25 01:59:40:037]  INFO gameserver.GameServer: ===============[Spawn Manager]==================
[28.06.25 01:59:40:110]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1000 npc for group: NONE
[28.06.25 01:59:40:255]  INFO instancemanager.SpawnManager: SpawnManager: spawned 5000 npc for group: NONE
[28.06.25 01:59:40:281]  INFO naia.NaiaTowerManager: Naia Tower Manager: Loaded 12 rooms
[28.06.25 01:59:40:353]  INFO instancemanager.SpawnManager: SpawnManager: spawned 7000 npc for group: NONE
[28.06.25 01:59:40:704]  INFO instancemanager.SpawnManager: SpawnManager: spawned 12000 npc for group: NONE
[28.06.25 01:59:40:780]  INFO instancemanager.SpawnManager: SpawnManager: spawned 13000 npc for group: NONE
[28.06.25 01:59:41:740]  INFO instancemanager.SpawnManager: SpawnManager: spawned 21000 npc for group: NONE
[28.06.25 01:59:41:882]  INFO instancemanager.SpawnManager: SpawnManager: spawned 22000 npc for group: NONE
[28.06.25 01:59:42:382]  INFO instancemanager.SpawnManager: SpawnManager: spawned 25000 npc for group: NONE
[28.06.25 01:59:42:563]  INFO instancemanager.SpawnManager: SpawnManager: spawned 26000 npc for group: NONE
[28.06.25 01:59:42:960]  INFO instancemanager.SpawnManager: SpawnManager: spawned 28000 npc for group: NONE
[28.06.25 01:59:43:880]  INFO instancemanager.SpawnManager: SpawnManager: spawned 32000 npc for group: NONE
[28.06.25 01:59:44:700]  INFO instancemanager.SpawnManager: SpawnManager: spawned 35000 npc for group: NONE
[28.06.25 01:59:44:973]  INFO instancemanager.SpawnManager: SpawnManager: spawned 36000 npc for group: NONE
[28.06.25 01:59:45:245]  INFO instancemanager.SpawnManager: SpawnManager: spawned 37000 npc for group: NONE
[28.06.25 01:59:45:654]  INFO instancemanager.SpawnManager: SpawnManager: spawned 38373 npc; spawns: 17087; group: NONE
[28.06.25 01:59:45:655]  INFO gameserver.GameServer: --------------------------------------------------------------------=[ Boats ]
[28.06.25 01:59:45:655]  INFO data.BoatHolder: BoatHolder: loaded 2 boat(s) count.
[28.06.25 01:59:45:659]  INFO data.BoatHolder: BoatHolder: Spawning: RunePrimeval_Vehicle
[28.06.25 01:59:45:659]  INFO data.BoatHolder: BoatHolder: Spawning: AdenGracia_Airship
[28.06.25 01:59:45:660]  INFO holder.StaticObjectHolder: StaticObjectHolder: spawned: 50 static object(s).
[28.06.25 01:59:45:660]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Dimensional Rift ]
[28.06.25 01:59:45:666]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 13 room types with 112 rooms.
[28.06.25 01:59:45:666]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 300 DimensionalRift spawns, 0 errors.
[28.06.25 01:59:45:670]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Seven Signs ]
[28.06.25 01:59:45:681]  INFO instancemanager.AutoSpawnManager: AutoSpawnHandler: Loaded 50 handlers in total.
[28.06.25 01:59:45:682]  INFO gameserver.GameServer: ===================================================================
[28.06.25 01:59:45:682]  INFO gameserver.GameServer: ===================[Loading Olympiad System]=======================
[28.06.25 01:59:45:687]  INFO olympiad.Olympiad: Olympiad System: Loading Olympiad System....
[28.06.25 01:59:45:688]  INFO olympiad.Olympiad: Olympiad System: Currently in Validation Period
[28.06.25 01:59:45:689]  INFO olympiad.Olympiad: Olympiad System: Period Ends....
[28.06.25 01:59:45:690]  INFO olympiad.Olympiad: Olympiad System: In 0 days, 10 hours and 2 mins.
[28.06.25 01:59:45:690]  INFO olympiad.Olympiad: Olympiad System: Loaded 0 Noblesses
[28.06.25 01:59:45:692]  INFO entity.Hero: Hero System: Loaded 0 Heroes.
[28.06.25 01:59:45:692]  INFO entity.Hero: Hero System: Loaded 0 all time Heroes.
[28.06.25 01:59:45:692]  INFO gameserver.GameServer: ===================[Olympiad System Loaded]=======================
[28.06.25 01:59:45:692]  INFO gameserver.GameServer: ===================================================================
[28.06.25 01:59:45:693]  INFO instancemanager.PetitionManager: Initializing PetitionManager
[28.06.25 01:59:45:697]  INFO instancemanager.CursedWeaponsManager: CursedWeaponsManager: Loaded 2 cursed weapon(s).
[28.06.25 01:59:45:697]  INFO gameserver.GameServer: ------------------------------------------=[ Loaded Small Achievement System ]
[28.06.25 01:59:45:708]  INFO gameserver.GameServer: --------------------------------------------------------------=[ ItemHandler ]
[28.06.25 01:59:45:708]  INFO items.ItemHandler: ItemHandler: loaded 0 item handler(s) count.
[28.06.25 01:59:45:708]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Admin Commands ]
[28.06.25 01:59:45:709]  INFO admincommands.AdminCommandHandler: AdminCommandHandler: loaded 475 admin command handler(s) count.
[28.06.25 01:59:45:709]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Players Commands ]
[28.06.25 01:59:45:712]  INFO usercommands.UserCommandHandler: UserCommandHandler: loaded 17 user command handler(s) count.
[28.06.25 01:59:45:713]  INFO voicecommands.VoicedCommandHandler: VoicedCommandHandler: loaded 102 voiced command handler(s) count.
[28.06.25 01:59:45:716]  INFO gameserver.GameServer: ======================[Tournament By Kara`]==========================
[28.06.25 01:59:45:720]  INFO tournament.TournamentHolder: [TournamentHolder] Initialized 5 tournament holders:
[28.06.25 01:59:45:720]  INFO tournament.TournamentHolder: 1 vs 1
[28.06.25 01:59:45:720]  INFO tournament.TournamentHolder: 2 vs 2
[28.06.25 01:59:45:720]  INFO tournament.TournamentHolder: 4 vs 4
[28.06.25 01:59:45:720]  INFO tournament.TournamentHolder: 5 vs 5
[28.06.25 01:59:45:720]  INFO tournament.TournamentHolder: 6 vs 6
[28.06.25 01:59:45:721]  INFO gameserver.GameServer: ======================[Loading Castels & Clan Halls]==========================
[28.06.25 01:59:45:755]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Gludio Castle time - 16:00 29.06.2025
[28.06.25 01:59:45:761]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Dion Castle time - 16:00 29.06.2025
[28.06.25 01:59:45:766]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Giran Castle time - 20:00 29.06.2025
[28.06.25 01:59:45:772]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Oren Castle time - 20:00 29.06.2025
[28.06.25 01:59:45:797]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Aden Castle time - 16:00 29.06.2025
[28.06.25 01:59:45:803]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Innadril Castle time - 20:00 29.06.2025
[28.06.25 01:59:45:808]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Goddard Castle time - 20:00 29.06.2025
[28.06.25 01:59:45:813]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Rune Castle time - 16:00 29.06.2025
[28.06.25 01:59:45:816]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Schuttgart Castle time - 16:00 29.06.2025
[28.06.25 01:59:45:820]  INFO impl.ClanHallNpcSiegeEvent: ClanHallNpcSiegeEvent: Fortress Of Resistance time - 18:00 30.06.2025
[28.06.25 01:59:45:822]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:59:45:824]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:59:45:825]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Topaz Hall time - 15:00 02.07.2025
[28.06.25 01:59:45:827]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Ruby Hall time - 15:00 02.07.2025
[28.06.25 01:59:45:828]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Crystal Hall time - 15:00 02.07.2025
[28.06.25 01:59:45:830]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:59:45:832]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[28.06.25 01:59:45:834]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:59:45:835]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[28.06.25 01:59:45:836]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Atramental Barracks time - 15:00 02.07.2025
[28.06.25 01:59:45:837]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Scarlet Barracks time - 15:00 02.07.2025
[28.06.25 01:59:45:839]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Viridian Barracks time - 15:00 02.07.2025
[28.06.25 01:59:45:840]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Devastated Castle time - 18:00 28.06.2025
[28.06.25 01:59:45:840]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Bandit Stronghold time - 22:00 28.06.2025
[28.06.25 01:59:45:842]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[28.06.25 01:59:45:843]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[28.06.25 01:59:45:844]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[28.06.25 01:59:45:845]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[28.06.25 01:59:45:846]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Gold Manor time - 15:00 02.07.2025
[28.06.25 01:59:45:848]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[28.06.25 01:59:45:850]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[28.06.25 01:59:45:852]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[28.06.25 01:59:45:853]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[28.06.25 01:59:45:854]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[28.06.25 01:59:45:856]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[28.06.25 01:59:45:857]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[28.06.25 01:59:45:874]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[28.06.25 01:59:45:875]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[28.06.25 01:59:45:876]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[28.06.25 01:59:45:878]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Mont Chamber time - 15:00 02.07.2025
[28.06.25 01:59:45:879]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Astaire Chamber time - 15:00 02.07.2025
[28.06.25 01:59:45:880]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Aria Chamber time - 15:00 02.07.2025
[28.06.25 01:59:45:881]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Yiana Chamber time - 15:00 02.07.2025
[28.06.25 01:59:45:882]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Roien Chamber time - 15:00 02.07.2025
[28.06.25 01:59:45:883]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Luna Chamber time - 15:00 02.07.2025
[28.06.25 01:59:45:884]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Traban Chamber time - 15:00 02.07.2025
[28.06.25 01:59:45:885]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Eisen Hall time - 15:00 02.07.2025
[28.06.25 01:59:45:886]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Heavy Metal Hall time - 15:00 02.07.2025
[28.06.25 01:59:45:888]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Molten Ore Hall time - 15:00 02.07.2025
[28.06.25 01:59:45:889]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Titan Hall time - 15:00 02.07.2025
[28.06.25 01:59:45:890]  INFO impl.ClanHallMiniGameEvent: ClanHallMiniGameEvent: Rainbow Springs time - 22:00 28.06.2025
[28.06.25 01:59:45:891]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Wild Beast Reserve time - 21:00 28.06.2025
[28.06.25 01:59:45:891]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Fortress of Dead time - 18:00 28.06.2025
[28.06.25 01:59:45:892]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Gludio Dominion time - undefined
[28.06.25 01:59:45:892]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Dion Dominion time - undefined
[28.06.25 01:59:45:893]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Giran Dominion time - undefined
[28.06.25 01:59:45:893]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Oren Dominion time - undefined
[28.06.25 01:59:45:893]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Aden Dominion time - undefined
[28.06.25 01:59:45:893]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Innadril Dominion time - undefined
[28.06.25 01:59:45:894]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Goddard Dominion time - undefined
[28.06.25 01:59:45:894]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Rune Dominion time - undefined
[28.06.25 01:59:45:894]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Schuttgart Dominion time - undefined
[28.06.25 01:59:45:897]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Shanty Fortress time - undefined
[28.06.25 01:59:45:900]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Southern Fortress time - undefined
[28.06.25 01:59:45:903]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hive Fortress time - undefined
[28.06.25 01:59:45:905]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Valley Fortress time - undefined
[28.06.25 01:59:45:908]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Ivory Fortress time - undefined
[28.06.25 01:59:45:910]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Narsell Fortress time - undefined
[28.06.25 01:59:45:912]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Bayou Fortress time - undefined
[28.06.25 01:59:45:915]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: White Sands Fortress time - undefined
[28.06.25 01:59:45:918]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Borderland Fortress time - undefined
[28.06.25 01:59:45:921]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Swamp Fortress time - undefined
[28.06.25 01:59:45:924]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Archaic Fortress time - undefined
[28.06.25 01:59:45:927]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Floran Fortress time - undefined
[28.06.25 01:59:45:930]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Cloud Mountain Fortress time - undefined
[28.06.25 01:59:45:932]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Tanor Fortress time - undefined
[28.06.25 01:59:45:935]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Dragonspine Fortress time - undefined
[28.06.25 01:59:45:937]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Antharas Fortress time - undefined
[28.06.25 01:59:45:939]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Western Fortress time - undefined
[28.06.25 01:59:45:942]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hunter's Fortress time - undefined
[28.06.25 01:59:45:944]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Aaru Fortress time - undefined
[28.06.25 01:59:45:946]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Demon Fortress time - undefined
[28.06.25 01:59:45:949]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Monastic Fortress time - undefined
[28.06.25 01:59:45:950]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 70-75 Level time - 02:00 28.06.2025
[28.06.25 01:59:45:950]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 76-79 Level time - 02:00 28.06.2025
[28.06.25 01:59:45:950]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 80-85 Level time - 02:00 28.06.2025
[28.06.25 01:59:45:950]  INFO fightclub.LastManStandingEvent: LastManStandingEvent: Last Hero time - undefined
[28.06.25 01:59:45:951]  INFO fightclub.TeamVSTeamEvent: TeamVSTeamEvent: Team VS Team time - undefined
[28.06.25 01:59:45:951]  INFO fightclub.LuckyCreaturesEvent: LuckyCreaturesEvent: Lucky Creatures time - undefined
[28.06.25 01:59:45:951]  INFO fightclub.CaptureTheFlagEvent: CaptureTheFlagEvent: Capture The Flag time - undefined
[28.06.25 01:59:45:951]  INFO fightclub.FFATreasureHuntEvent: FFATreasureHuntEvent: Treasure Hunt time - undefined
[28.06.25 01:59:45:951]  INFO fightclub.KoreanStyleEvent: KoreanStyleEvent: Korean Style time - undefined
[28.06.25 01:59:45:951]  INFO fightclub.ProtectTheKingEvent: ProtectTheKingEvent: Protect The King time - undefined
[28.06.25 01:59:45:952]  INFO fightclub.FightForThroneEvent: FightForThroneEvent: Fight For Throne time - undefined
[28.06.25 01:59:45:952]  INFO fightclub.DeathMatchEvent: DeathMatchEvent: Death Match time - undefined
[28.06.25 01:59:45:952]  INFO fightclub.RussianRouletteEvent: RussianRouletteEvent: Russian Roulette time - undefined
[28.06.25 01:59:45:953]  INFO instancemanager.CastleManorManager: Manor System: Initializing...
[28.06.25 01:59:45:955]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:59:45:955]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Auto Cleaner ]
[28.06.25 01:59:45:955]  INFO gameserver.GameServer: IdFactory: Free ObjectID's remaining: 1879004873
[28.06.25 01:59:45:955]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:59:45:956]  INFO instancemanager.CoupleManager: Initializing CoupleManager
[28.06.25 01:59:45:956]  INFO instancemanager.CoupleManager: Loaded: 0 couples(s)
[28.06.25 01:59:45:958]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Hellbound ]
[28.06.25 01:59:45:965]  INFO instancemanager.HellboundManager: HellboundManager: Loaded 335 spawn entries.
[28.06.25 01:59:46:104]  INFO instancemanager.HellboundManager: HellboundManager: Spawned 623 mobs and NPCs according to the current Hellbound stage
[28.06.25 01:59:46:105]  INFO instancemanager.HellboundManager: Hellbound Manager: Loaded
[28.06.25 01:59:46:105]  INFO naia.NaiaTowerManager: Naia Core Manager: Loaded
[28.06.25 01:59:46:106]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:59:46:107]  INFO instancemanager.SoDManager: Seed of Destruction Manager: Loaded
[28.06.25 01:59:46:108]  INFO instancemanager.SoIManager: Seed of Infinity Manager: Loaded. Current stage is: 1
[28.06.25 01:59:46:109]  INFO instancemanager.SpawnManager: SpawnManager: spawned 4 npc; spawns: 4; group: soi_world_mouths
[28.06.25 01:59:46:110]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: soi_world_abyssgaze2
[28.06.25 01:59:46:111]  INFO instancemanager.BloodAltarManager: Blood Altar Manager: Initializing...
[28.06.25 01:59:46:121]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_alive_npc
[28.06.25 01:59:46:124]  INFO gameserver.GameServer: Preparing Drop Calculator
[28.06.25 01:59:46:182]  INFO gameserver.GameServer: ----------------------------------------------------------=[ Offline Buffers ]
[28.06.25 01:59:46:183]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loading offline buffers...
[28.06.25 01:59:46:184]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loaded: 0 offline buffer(s)
[28.06.25 01:59:46:194]  INFO achievements.Achievements: Achievement System: Loaded 4 achievement categories and 39 achievements.
[28.06.25 01:59:46:196]  INFO gameserver.GameServer: Emotions Loaded....
[28.06.25 01:59:46:196]  INFO gameserver.GameServer: --------------------------------------------------=[ DataBase Cleaner Loaded ]
[28.06.25 01:59:46:197]  INFO dao.CharacterDAO: Found 0 characters to delete!
[28.06.25 01:59:46:270]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Initializing
[28.06.25 01:59:46:271]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Load OK
[28.06.25 01:59:46:272]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Loaded 0 announce.
[28.06.25 01:59:46:273]  INFO gameserver.GameServer: ---------------------------------------------------=[ Loaded Facebook System ]
[28.06.25 01:59:46:274]  INFO gameserver.GameServer: -----------------------------------------------------=[ Loaded Stream System ]
[28.06.25 01:59:46:278]  INFO holder.ProxiesHolder: ProxiesHolder: loaded 20 proxies(s) count.
[28.06.25 01:59:46:278]  INFO gameserver.GameServer: ------------------------------------------------------=[ Loaded Proxy System ]
[28.06.25 01:59:48:938]  INFO donation.DonationReader: Donation Reader service started
[28.06.25 01:59:48:938]  INFO gameserver.GameServer: --------------------------------------------=[ Loaded AUTO - Donation System ]
[28.06.25 01:59:48:940]  INFO taskmanager.BackupTaskManager: Next Auto Backup in 4 H., 40 Min.
[28.06.25 01:59:48:955]  INFO votingengine.VotingSettings: VotingSettings: Loaded 3 messages, 4 drops!
[28.06.25 01:59:48:958]  INFO votingengine.VotingRewardAPI: VoteReward: Has been successfully loaded!
[28.06.25 01:59:48:964]  INFO model.PhantomPlayers: Loading phantom players...
[28.06.25 01:59:48:973]  INFO model.PhantomPlayers: Loaded 32002 possible phantom names.
[28.06.25 01:59:48:973]  INFO model.PhantomPlayers: Loaded 7172 possible phantom titles.
[28.06.25 01:59:48:974]  INFO model.PhantomPlayers: PhantomPlayers: Cached 0 players.
[28.06.25 01:59:48:975]  INFO model.PhantomPlayers: PhantomPlayers: Free names 32002.
[28.06.25 01:59:48:975]  INFO model.PhantomPlayers: Loaded 0 phantom players from database with a maximum of 3000 phantoms.
[28.06.25 01:59:48:975]  INFO model.PhantomPlayers: Scheduled spawner with 1 seconds delay.
[28.06.25 01:59:49:123]  INFO instancemanager.SpawnManager: SpawnManager: spawned 443 npc; spawns: 121; group: DAY
[28.06.25 01:59:49:215]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dawn_spawn
[28.06.25 01:59:49:404]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dusk_spawn
[28.06.25 01:59:49:404]  INFO gameserver.GameServer: Telnet server is currently disabled.
[28.06.25 01:59:49:407]  INFO gameserver.Shutdown: Scheduled server restart in 3h 45m 11s.
[28.06.25 01:59:49:408]  INFO gameserver.GameServer: ==============================================================================
[28.06.25 01:59:49:408]  INFO gameserver.GameServer: >>>>>>>>>>>>>>> GameServer Started <<<<<<<<<<<<<<
[28.06.25 01:59:49:408]  INFO gameserver.GameServer: Maximum Numbers of Connected Players: 3000
[28.06.25 01:59:49:408]  INFO gameserver.GameServer: ====================================================================
[28.06.25 01:59:49:429]  INFO gameserver.GameServer: AllowedMemory: ........... 6291456 KB
[28.06.25 01:59:49:429]  INFO gameserver.GameServer: 
Allocated: ............... 4194304 KB (66.6667%)
[28.06.25 01:59:49:429]  INFO gameserver.GameServer: 
Non-Allocated: ........... 2097152 KB (33.3333%)
[28.06.25 01:59:49:429]  INFO gameserver.GameServer: 
AllocatedMemory: ......... 4194304 KB
[28.06.25 01:59:49:429]  INFO gameserver.GameServer: Used: .................... 1120773 KB (17.8142%)
[28.06.25 01:59:49:429]  INFO gameserver.GameServer: 
Unused (cached): ......... 3073530 KB (48.8524%)
[28.06.25 01:59:49:429]  INFO gameserver.GameServer: 
UseableMemory: ........... 5170682 KB (82.1858%)
[28.06.25 01:59:49:430]  INFO gameserver.GameServer: 

[28.06.25 01:59:49:430]  INFO gameserver.GameServer: ====================================================================
[28.06.25 01:59:49:432]  INFO loginservercon.AuthServerCommunication: Connecting to authserver on 127.0.0.1:9014
[28.06.25 01:59:49:443]  INFO lspackets.AuthResponse: Registered on authserver as 1 [Bartz]
[28.06.25 02:00:39:746]  INFO threading.RunnableImpl: Sat Jun 28 02:00:39 BRT 2025 Atk announce scheduled to 0.0 minute of this hour.
[28.06.25 02:01:26:272]  INFO tasks.RestoreOfflineTraders: Restored 0 offline traders
[28.06.25 02:03:23:271]  INFO community.CharacterServicesCommunity: onBypassCommand called - bypass: _bbscharservices;registeremail
[28.06.25 02:03:23:271]  INFO community.CharacterServicesCommunity: cmd: _bbscharservices, action: registeremail
[28.06.25 02:03:34:385]  INFO community.CharacterServicesCommunity: === onWriteCommand called ===
[28.06.25 02:03:34:385]  INFO community.CharacterServicesCommunity: Player: Dzu
[28.06.25 02:03:34:385]  INFO community.CharacterServicesCommunity: Bypass: '_bbscharservices;registeremail;submit'
[28.06.25 02:03:34:386]  INFO community.CharacterServicesCommunity: arg1: '0'
[28.06.25 02:03:34:386]  INFO community.CharacterServicesCommunity: arg2: '0'
[28.06.25 02:03:34:387]  INFO community.CharacterServicesCommunity: arg3: '<EMAIL>'
[28.06.25 02:03:34:387]  INFO community.CharacterServicesCommunity: arg4: '<EMAIL>'
[28.06.25 02:03:34:390]  INFO community.CharacterServicesCommunity: arg5: ''
[28.06.25 02:03:34:392]  INFO community.CharacterServicesCommunity: Processing email registration - email: <EMAIL>, confirmEmail: <EMAIL>
[28.06.25 02:03:34:396]  INFO utils.EmailVerification: Verification code stored for account: siniez, email: <EMAIL>
[28.06.25 02:03:34:396]  INFO community.CharacterServicesCommunity: About to send email - Config.MAIL_USER: '<EMAIL>', Config.MAIL_PASS: 'SET'
[28.06.25 02:03:34:396]  INFO utils.EmailVerification: Checking email configuration - MAIL_USER: '<EMAIL>', MAIL_PASS: 'SET'
[28.06.25 02:03:34:697]  INFO utils.EmailVerification: SMTP Initial: 220 smtp.gmail.com ESMTP af79cd13be357-7d44316d8d9sm241597185a.37 - gsmtp
[28.06.25 02:03:34:928]  INFO utils.EmailVerification: SMTP EHLO: 250-smtp.gmail.com at your service, [***************]
250-SIZE ********
250-8BITMIME
250-STARTTLS
250-ENHANCEDSTATUSCODES
250-PIPELINING
250-CHUNKING
250 SMTPUTF8

[28.06.25 02:03:35:156]  INFO utils.EmailVerification: SMTP STARTTLS: 220 2.0.0 Ready to start TLS
[28.06.25 02:03:35:529]  INFO utils.EmailVerification: SMTP EHLO after TLS: 250-smtp.gmail.com at your service, [***************]
250-SIZE ********
250-8BITMIME
250-AUTH LOGIN PLAIN XOAUTH2 PLAIN-CLIENTTOKEN OAUTHBEARER XOAUTH
250-ENHANCEDSTATUSCODES
250-PIPELINING
250-CHUNKING
250 SMTPUTF8

[28.06.25 02:03:35:756]  INFO utils.EmailVerification: SMTP AUTH LOGIN: 334 VXNlcm5hbWU6
[28.06.25 02:03:35:993]  INFO utils.EmailVerification: SMTP Username: 334 UGFzc3dvcmQ6
[28.06.25 02:03:36:364]  INFO utils.EmailVerification: SMTP Password: 235 2.7.0 Accepted
[28.06.25 02:03:36:591]  INFO utils.EmailVerification: SMTP MAIL FROM: 250 2.1.0 OK af79cd13be357-7d44316d8d9sm241597185a.37 - gsmtp
[28.06.25 02:03:36:819]  INFO utils.EmailVerification: SMTP RCPT TO: 250 2.1.5 OK af79cd13be357-7d44316d8d9sm241597185a.37 - gsmtp
[28.06.25 02:03:37:159]  INFO utils.EmailVerification: SMTP DATA: 354 Go ahead af79cd13be357-7d44316d8d9sm241597185a.37 - gsmtp
[28.06.25 02:03:37:842]  INFO utils.EmailVerification: SMTP Email sent: 250 2.0.0 OK  1751087019 af79cd13be357-7d44316d8d9sm241597185a.37 - gsmtp
[28.06.25 02:03:38:069]  INFO utils.EmailVerification: SMTP QUIT: 221 2.0.0 closing connection af79cd13be357-7d44316d8d9sm241597185a.37 - gsmtp
[28.06.25 02:03:38:070]  INFO utils.EmailVerification: Email sent successfully to: <EMAIL> for player: Dzu
[28.06.25 02:06:00:205]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, before:0 toRemove:0 after:0 to Change:0
[28.06.25 02:06:00:205]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, Before: 0 Final:0
[28.06.25 02:06:00:206]  INFO fightclubmanager.FightClubEventManager: Capture The Flag: Removing room because it doesnt have enough players
