[01.07.25 00:00:00:336]  INFO instancemanager.SpawnManager: SpawnManager: spawned 443 npc; spawns: 121; group: DAY
[01.07.25 00:00:29:962]  INFO olympiad.CompEndTask: Olympiad System: Olympiad Game Ended
[01.07.25 00:00:29:964]  INFO olympiad.Olympiad: Olympiad System: Competition Period Starts in 0 days, 18 hours and 0 mins.
[01.07.25 00:00:29:964]  INFO olympiad.Olympiad: Olympiad System: Event starts/started: Tue Jul 01 18:00:29 BRT 2025
[01.07.25 00:06:00:552]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, before:0 toRemove:0 after:0 to Change:0
[01.07.25 00:06:00:552]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, Before: 0 Final:0
[01.07.25 00:06:00:553]  INFO fightclubmanager.FightClubEventManager: Last Hero: Removing room because it doesnt have enough players
[01.07.25 00:16:30:561]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_alive_npc
[01.07.25 00:46:05:843] ERROR loginservercon.AuthServerCommunication: AuthServer I/O error
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:401)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:434)
	at l2mv.gameserver.network.loginservercon.AuthServerCommunication.read(AuthServerCommunication.java:258)
	at l2mv.gameserver.network.loginservercon.AuthServerCommunication.run(AuthServerCommunication.java:220)
[01.07.25 00:46:10:845]  INFO loginservercon.AuthServerCommunication: Connecting to authserver on 127.0.0.1:9014
[01.07.25 00:46:10:857]  INFO lspackets.AuthResponse: Registered on authserver as 1 [Bartz]
[01.07.25 00:46:23:893]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[01.07.25 00:50:23:740]  INFO threading.RunnableImpl: FourSepulchersManager: Entry time: Tue Jul 01 00:55:23 BRT 2025
[01.07.25 01:06:00:552]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, before:0 toRemove:0 after:0 to Change:0
[01.07.25 01:06:00:554]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, Before: 0 Final:0
[01.07.25 01:06:00:556]  INFO fightclubmanager.FightClubEventManager: Team VS Team: Removing room because it doesnt have enough players
[01.07.25 01:46:23:907]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[01.07.25 01:50:23:771]  INFO threading.RunnableImpl: FourSepulchersManager: Entry time: Tue Jul 01 01:55:23 BRT 2025
[01.07.25 02:06:00:554]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, before:0 toRemove:0 after:0 to Change:0
[01.07.25 02:06:00:554]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, Before: 0 Final:0
[01.07.25 02:06:00:557]  INFO fightclubmanager.FightClubEventManager: Capture The Flag: Removing room because it doesnt have enough players
[01.07.25 02:46:07:133] ERROR loginservercon.AuthServerCommunication: AuthServer I/O error
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:401)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:434)
	at l2mv.gameserver.network.loginservercon.AuthServerCommunication.read(AuthServerCommunication.java:258)
	at l2mv.gameserver.network.loginservercon.AuthServerCommunication.run(AuthServerCommunication.java:220)
[01.07.25 02:46:12:136]  INFO loginservercon.AuthServerCommunication: Connecting to authserver on 127.0.0.1:9014
[01.07.25 02:46:12:151]  INFO lspackets.AuthResponse: Registered on authserver as 1 [Bartz]
[01.07.25 02:46:23:903]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[01.07.25 02:50:23:785]  INFO threading.RunnableImpl: FourSepulchersManager: Entry time: Tue Jul 01 02:55:23 BRT 2025
[01.07.25 03:00:00:320]  INFO instancemanager.SpawnManager: SpawnManager: spawned 444 npc; spawns: 122; group: NIGHT
[01.07.25 03:06:01:751]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, before:0 toRemove:0 after:0 to Change:0
[01.07.25 03:06:01:751]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, Before: 0 Final:0
[01.07.25 03:06:01:753]  INFO fightclubmanager.FightClubEventManager: Treasure Hunt: Removing room because it doesnt have enough players
[01.07.25 03:46:25:092]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[01.07.25 03:50:25:018]  INFO threading.RunnableImpl: FourSepulchersManager: Entry time: Tue Jul 01 03:55:25 BRT 2025
[01.07.25 04:00:01:466]  INFO instancemanager.SpawnManager: SpawnManager: spawned 443 npc; spawns: 121; group: DAY
[01.07.25 04:06:01:754]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, before:0 toRemove:0 after:0 to Change:0
[01.07.25 04:06:01:754]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, Before: 0 Final:0
[01.07.25 04:06:01:768]  INFO fightclubmanager.FightClubEventManager: Korean Style: Removing room because it doesnt have enough players
[01.07.25 04:16:31:691]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_dead_npc
[01.07.25 04:16:31:694]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_aden
[01.07.25 04:16:31:695]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: bloodaltar_boss_darkelf
[01.07.25 04:16:31:700]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_dion
[01.07.25 04:16:31:702]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_dwarw
[01.07.25 04:16:31:703]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_giran
[01.07.25 04:16:31:704]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_gludin
[01.07.25 04:16:31:705]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_gludio
[01.07.25 04:16:31:706]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_goddart
[01.07.25 04:16:31:707]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_heine
[01.07.25 04:16:31:708]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: bloodaltar_boss_orc
[01.07.25 04:16:31:709]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_oren
[01.07.25 04:16:31:709]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: bloodaltar_boss_schutgart
[01.07.25 04:46:09:540] ERROR loginservercon.AuthServerCommunication: AuthServer I/O error
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:401)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:434)
	at l2mv.gameserver.network.loginservercon.AuthServerCommunication.read(AuthServerCommunication.java:258)
	at l2mv.gameserver.network.loginservercon.AuthServerCommunication.run(AuthServerCommunication.java:220)
[01.07.25 04:46:14:541]  INFO loginservercon.AuthServerCommunication: Connecting to authserver on 127.0.0.1:9014
[01.07.25 04:46:14:554]  INFO lspackets.AuthResponse: Registered on authserver as 1 [Bartz]
[01.07.25 04:46:25:095]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[01.07.25 04:46:31:704]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_alive_npc
[01.07.25 04:50:25:063]  INFO threading.RunnableImpl: FourSepulchersManager: Entry time: Tue Jul 01 04:55:25 BRT 2025
[01.07.25 05:06:01:753]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, before:0 toRemove:0 after:0 to Change:0
[01.07.25 05:06:01:753]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, Before: 0 Final:0
[01.07.25 05:06:01:763]  INFO fightclubmanager.FightClubEventManager: Protect The King: Removing room because it doesnt have enough players
[01.07.25 05:45:00:831]  INFO gameserver.Shutdown: Shutting down LS/GS communication...
[01.07.25 05:45:00:831]  INFO gameserver.Shutdown: Shutting down scripts...
[01.07.25 05:45:00:952]  INFO gameserver.Shutdown: Disconnecting players...
[01.07.25 05:45:00:982]  INFO gameserver.Shutdown: Saving data...
[01.07.25 05:45:00:985]  INFO gameserver.Shutdown: SevenSignsFestival: Data saved.
[01.07.25 05:45:00:992]  WARN vote.VoteManager: VoteManager: Could not parse file voteReward.xml: C:\Users\<USER>\Documents\Projetos\Lineage II\Sources\Projeto_Forgotten_Lands\build\dist\gameserver\config\voteReward.xml (O sistema não pode encontrar o arquivo especificado)
[01.07.25 05:45:00:993]  INFO gameserver.Shutdown: PvpTable: Data saved [0 inserts and 0 updates].
[01.07.25 05:45:00:996]  INFO gameserver.Shutdown: SevenSigns: Data saved.
[01.07.25 05:45:00:997]  INFO gameserver.Shutdown: Olympiad: Data saved.
[01.07.25 05:45:00:997]  INFO gameserver.Shutdown: CoupleManager: Data saved.
[01.07.25 05:45:00:999]  INFO gameserver.Shutdown: FishingChampionShipManager: Data saved.
[01.07.25 05:45:00:999]  INFO gameserver.Shutdown: Hero: Data saved.
[01.07.25 05:45:01:006]  INFO gameserver.Shutdown: All HWID data saved.
[01.07.25 05:45:01:009]  INFO gameserver.Shutdown: All stream reward times saved
[01.07.25 05:45:01:011]  INFO gameserver.Shutdown: CursedWeaponsManager: Data saved,
[01.07.25 05:45:01:011]  INFO gameserver.Shutdown: Deleting Void Items...
[01.07.25 05:45:01:013]  INFO gameserver.Shutdown: Listeners...
[01.07.25 05:45:01:013]  INFO gameserver.Shutdown: Shutting down thread pool...
[01.07.25 05:45:11:031]  INFO gameserver.Shutdown: Shutting down selector...
[01.07.25 05:45:11:032]  INFO gameserver.Shutdown: Shutting down database communication...
[01.07.25 05:45:11:036]  INFO gameserver.Shutdown: Shutdown finished.
[01.07.25 05:45:11:347]  INFO gameserver.GameServer: ============================================================================
[01.07.25 05:45:11:348]  INFO gameserver.GameServer: Nome: .................................................................... GAME SERVER
[01.07.25 05:45:11:348]  INFO gameserver.GameServer: Project Revision: ........................................................ LII [MultVerso]
[01.07.25 05:45:11:349]  INFO gameserver.GameServer: Update: .................................................................. High Five: Part 5
[01.07.25 05:45:11:349]  INFO gameserver.GameServer: Numero da Revision:....................................................... 2.0
[01.07.25 05:45:11:349]  INFO gameserver.GameServer: Build Revision: .......................................................... MultVerso
[01.07.25 05:45:11:349]  INFO gameserver.GameServer: Build date: .............................................................. 2025.06.30 12:18
[01.07.25 05:45:11:349]  INFO gameserver.GameServer: Compiler version: ........................................................ 17.0.7+7 (Eclipse Adoptium)
[01.07.25 05:45:11:349]  INFO gameserver.GameServer: ============================================================================
[01.07.25 05:45:11:391]  INFO gameserver.Config: Loaded 6 trade words.
[01.07.25 05:45:11:399]  INFO gameserver.Config: Abuse: Loaded 15 abuse words.
[01.07.25 05:45:11:447]  INFO gameserver.Config: Loaded 313 Configs!
[01.07.25 05:45:11:453]  INFO gameserver.GameServer: IP EXTERNO =============================127.0.0.1
[01.07.25 05:45:11:453]  INFO gameserver.GameServer: IP INTERNO =============================127.0.0.1
[01.07.25 05:45:11:454]  INFO gameserver.GameServer: Maximo Online IP ====================== 3000
[01.07.25 05:45:11:455]  INFO gameserver.GameServer: Hora do Restart  =======================45 5 * * *
[01.07.25 05:45:11:636]  INFO gameserver.GameServer: -----------------------------------------=[ Loading Protection Configuration ]
[01.07.25 05:45:11:640]  INFO idfactory.IdFactory: IdFactory: Clear characters online status.
[01.07.25 05:45:11:676]  INFO idfactory.IdFactory: IdFactory: Cleaned 0 elements from database in 0sec.
[01.07.25 05:45:11:683]  INFO idfactory.IdFactory: IdFactory: Extracted 2 used id's from characters
[01.07.25 05:45:11:683]  INFO idfactory.IdFactory: IdFactory: Extracted 46 used id's from items
[01.07.25 05:45:11:684]  INFO idfactory.IdFactory: IdFactory: Extracted 1 used id's from clan_data
[01.07.25 05:45:11:684]  INFO idfactory.IdFactory: IdFactory: Extracted total 49 used id's.
[01.07.25 05:45:11:685]  INFO idfactory.BitSetIDFactory: IdFactory: 102912 id's available.
[01.07.25 05:45:11:775]  INFO gameserver.GameServer: ===============[Loading Scripts]==================
[01.07.25 05:45:11:776]  INFO scripts.Scripts: Scripts: Loading...
[01.07.25 05:45:11:776]  INFO scripts.Scripts: Loading Server Scripts
[01.07.25 05:45:12:045]  INFO scripts.Scripts: Scripts: Loaded 1288 classes.
[01.07.25 05:45:12:053]  INFO vote.VoteMain: Loaded: Vote System - Individual Reward.
[01.07.25 05:45:12:054]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Twitch Manager ]
[01.07.25 05:45:12:082]  INFO gameserver.GameServer: ----------------------------------------------------------=[ AntiFeedManager ]
[01.07.25 05:45:12:083]  INFO gameserver.GameServer: -------------------------------------------------------------=[ QuestManager ]
[01.07.25 05:45:12:083]  INFO gameserver.GameServer: QuestManager : loaded 0 quest's
[01.07.25 05:45:12:094]  INFO tables.FakePlayersTable: FakePlayersTable: Loaded 32002 Fake Players.
[01.07.25 05:45:12:100]  INFO utils.Strings: Loaded 418 translit entries.
[01.07.25 05:45:12:102]  INFO gameserver.GameTimeController: GameTimeController: initialized. Current time is 16:31 in the day.
[01.07.25 05:45:12:102]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Lineage World ]
[01.07.25 05:45:12:105]  INFO model.World: L2World: Creating regions: [256][272][32].
[01.07.25 05:45:12:106]  INFO gameserver.GameServer: ==============================================================================
[01.07.25 05:45:12:107]  INFO htm.HtmCache: HtmCache: disabled.
[01.07.25 05:45:12:114]  INFO data.StringHolder: StringHolder: load strings: 1066 for lang: ENGLISH
[01.07.25 05:45:12:114]  INFO data.StringHolder: StringHolder: load strings: 876 for lang: RUSSIAN
[01.07.25 05:45:13:383]  INFO skills.SkillsEngine: SkillsEngine: Loaded 66051 skill templates from XML files. Max id: 91000, max level: 259
[01.07.25 05:45:13:938]  INFO holder.OptionDataHolder: OptionDataHolder: loaded 24975 option data(s) count.
[01.07.25 05:45:14:480]  INFO holder.ItemHolder: ItemHolder: loaded 19240 item(s) count.
[01.07.25 05:45:14:485]  INFO holder.FakePlayerNpcsHolder: FakePlayerNpcsHolder: loaded 8 fake player npcs(s) count.
[01.07.25 05:45:15:437]  INFO holder.NpcHolder: NpcHolder: loaded 10519 npc(s) count.
[01.07.25 05:45:15:443]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[01.07.25 05:45:15:458]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[01.07.25 05:45:15:486]  INFO holder.ExchangeItemHolder: ExchangeItemHolder: loaded 602 exchange item(s) count.
[01.07.25 05:45:15:490]  INFO holder.StaticObjectHolder: StaticObjectHolder: loaded 50 static object(s) count.
[01.07.25 05:45:15:587]  INFO holder.DoorHolder: DoorHolder: loaded 1155 door(s) count.
[01.07.25 05:45:15:632]  INFO holder.ZoneHolder: ZoneHolder: loaded 1260 zone(s) count.
[01.07.25 05:45:15:924]  INFO holder.SpawnHolder: SpawnHolder: loaded 28949 spawn(s) count.
[01.07.25 05:45:15:996]  INFO holder.InstantZoneHolder: InstantZoneHolder: loaded 94 instant zone(s) count.
[01.07.25 05:45:16:299]  INFO holder.AirshipDockHolder: AirshipDockHolder: loaded 4 airship dock(s) count.
[01.07.25 05:45:16:341]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 57215 normal learns for 103 classes.
[01.07.25 05:45:16:341]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 79 transfer learns for 3 classes.
[01.07.25 05:45:16:342]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 120 transformation learns for 6 races.
[01.07.25 05:45:16:342]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 662 fishing learns for 6 races.
[01.07.25 05:45:16:342]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 72 certification learns.
[01.07.25 05:45:16:342]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 3 collection learns.
[01.07.25 05:45:16:343]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 44 pledge learns.
[01.07.25 05:45:16:343]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 18 sub unit learns.
[01.07.25 05:45:16:350]  INFO holder.CharTemplateHolder: CharTemplateHolder: loaded 206 char template(s) count.
[01.07.25 05:45:16:438]  INFO holder.ResidenceHolder: ResidenceHolder: total size: 83
[01.07.25 05:45:16:438]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 44 clanhall(s).
[01.07.25 05:45:16:439]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 castle(s).
[01.07.25 05:45:16:439]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 21 fortress(s).
[01.07.25 05:45:16:439]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 dominion(s).
[01.07.25 05:45:16:494]  INFO instancemanager.RaidBossSpawnManager: RaidBossSpawnManager: Loaded 194 Statuses
[01.07.25 05:45:16:513]  INFO entity.SevenSigns: SevenSigns: Currently in the Competition (Quest Event) period!
[01.07.25 05:45:16:513]  INFO entity.SevenSigns: SevenSigns: The Seal of Avarice remains unclaimed.
[01.07.25 05:45:16:513]  INFO entity.SevenSigns: SevenSigns: The Seal of Gnosis remains unclaimed.
[01.07.25 05:45:16:514]  INFO entity.SevenSigns: SevenSigns: The Seal of Strife remains unclaimed.
[01.07.25 05:45:16:514]  INFO entity.SevenSigns: SevenSigns: The Competition this week, if the trend continue, will end with a tie.
[01.07.25 05:45:16:515]  INFO entity.SevenSigns: SevenSigns: Next period begins in 6 days, 12 hours and 14 mins.
[01.07.25 05:45:16:605]  INFO holder.EventHolder: EventHolder: loaded 105 event(s) count.
[01.07.25 05:45:16:617]  INFO holder.FightClubMapHolder: FightClubMapHolder: loaded 42 fight club map(s) count.
[01.07.25 05:45:16:620]  INFO holder.CubicHolder: CubicHolder: loaded 145 cubic(s) count.
[01.07.25 05:45:16:656]  INFO holder.BuyListHolder: TradeController: Loaded 4 file(s).
[01.07.25 05:45:16:656]  INFO holder.BuyListHolder: TradeController: Loaded 25014 Items.
[01.07.25 05:45:16:657]  INFO holder.BuyListHolder: TradeController: Loaded 726 Buylists.
[01.07.25 05:45:16:670]  INFO holder.RecipeHolder: RecipeController: Loaded 998 Recipes.
[01.07.25 05:45:16:972]  INFO holder.ProductHolder: ProductItemTable: Loaded 0 product item on sale.
[01.07.25 05:45:16:981]  INFO holder.HennaHolder: HennaHolder: loaded 180 henna(s) count.
[01.07.25 05:45:16:984]  INFO holder.EnchantItemHolder: EnchantItemHolder: load 4 enchant scroll(s).
[01.07.25 05:45:16:987]  INFO holder.SoulCrystalHolder: SoulCrystalHolder: loaded 54 soul crystal(s) count.
[01.07.25 05:45:16:990]  INFO holder.ArmorSetsHolder: ArmorSetsHolder: loaded 199 armor sets(s) count.
[01.07.25 05:45:16:995]  INFO holder.PetitionGroupHolder: PetitionGroupHolder: loaded 3 petition group(s) count.
[01.07.25 05:45:16:997]  INFO holder.DressArmorHolder: DressArmorHolder: loaded 57 dress armor(s) count.
[01.07.25 05:45:16:999]  INFO holder.DressCloakHolder: DressCloakHolder: loaded 7 dress cloak(s) count.
[01.07.25 05:45:17:001]  INFO holder.DressShieldHolder: DressShieldHolder: loaded 10 dress shield(s) count.
[01.07.25 05:45:17:004]  INFO holder.DressWeaponHolder: DressWeaponHolder: loaded 401 dress weapon(s) count.
[01.07.25 05:45:17:045]  INFO holder.AugmentationDataHolder: AugmentationDataHolder: loaded 1022 augmentation data(s) count.
[01.07.25 05:45:17:049]  INFO holder.PremiumHolder: PremiumHolder: loaded 3 premium(s) count.
[01.07.25 05:45:17:052]  INFO holder.FoundationHolder: FoundationHolder: loaded 268 foundation(s) count.
[01.07.25 05:45:17:057]  INFO holder.DonationHolder: DonationHolder: loaded 192 donation(s) count.
[01.07.25 05:45:17:060]  INFO holder.TournamentMapHolder: TournamentMapHolder: loaded 3 tournament map(s) count.
[01.07.25 05:45:17:063]  INFO holder.FacebookCommentsHolder: FacebookCommentsHolder: loaded 2 facebook comments(s) count.
[01.07.25 05:45:17:064]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Banned HWIDS ]
[01.07.25 05:45:17:065]  INFO security.HWIDBan: black list (Hwid) loaded size: 0
[01.07.25 05:45:17:065]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Clan Crests ]
[01.07.25 05:45:17:078]  INFO cache.CrestCache: CrestCache: Loaded 0 crests
[01.07.25 05:45:17:078]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Loading Images ]
[01.07.25 05:45:17:080]  INFO cache.ImagesCache: ImagesCache: Loading images...
[01.07.25 05:45:26:649]  INFO cache.ImagesCache: ImagesCache: Loaded 8734 images
[01.07.25 05:45:26:649]  INFO gameserver.GameServer: ==============================================================================
[01.07.25 05:45:26:666]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Fish Table ]
[01.07.25 05:45:26:669]  INFO tables.FishTable: FishTable: Loaded 270 fishes.
[01.07.25 05:45:26:671]  INFO tables.FishTable: FishTable: Loaded 919 fish rewards.
[01.07.25 05:45:26:671]  INFO gameserver.GameServer: -------------------------------------------------------------------=[ Skills ]
[01.07.25 05:45:26:671]  INFO tables.SkillTreeTable: SkillTreeTable: Loaded 526 enchanted skills.
[01.07.25 05:45:26:699]  INFO datatables.EnchantNamesTable: EnchantNamesTable: Loaded: 1319 skill enchant names
[01.07.25 05:45:26:699]  INFO gameserver.GameServer: --------------------------------------------------------=[ Augmentation Data ]
[01.07.25 05:45:26:700]  INFO tables.AugmentationData: Initializing AugmentationData.
[01.07.25 05:45:26:711]  INFO tables.AugmentationData: AugmentationData: 10 bad skill(s) were skipped.
[01.07.25 05:45:26:722]  INFO tables.AugmentationData: AugmentationData: Loaded: 52 augmentation stats.
[01.07.25 05:45:26:722]  INFO tables.AugmentationData: AugmentationData: Loaded: 24 accessory augmentation stats.
[01.07.25 05:45:26:724]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 0
[01.07.25 05:45:26:724]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 1
[01.07.25 05:45:26:724]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 2
[01.07.25 05:45:26:725]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 3
[01.07.25 05:45:26:725]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 4
[01.07.25 05:45:26:725]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 5
[01.07.25 05:45:26:725]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 6
[01.07.25 05:45:26:725]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 7
[01.07.25 05:45:26:725]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 8
[01.07.25 05:45:26:726]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 9
[01.07.25 05:45:26:727]  INFO tables.EnchantHPBonusTable: EnchantHPBonusTable: Loaded bonuses for 6 grades.
[01.07.25 05:45:26:728]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Level Up Table ]
[01.07.25 05:45:26:730]  INFO tables.LevelUpTable: LevelUpData: Loaded 103 Character Level Up Templates.
[01.07.25 05:45:26:733]  INFO tables.PetSkillsTable: PetSkillsTable: Loaded 2558 skills.
[01.07.25 05:45:26:733]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Item Logs ]
[01.07.25 05:45:26:734]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Auctioneer ]
[01.07.25 05:45:26:735]  INFO itemauction.ItemAuctionManager: Initializing ItemAuctionManager
[01.07.25 05:45:26:740]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 1 auction(s) for instance 32320.
[01.07.25 05:45:26:742]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 1 on 2025.07.01 17:00:00 for instance 32320
[01.07.25 05:45:26:743]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 2 auction(s) for instance 32321.
[01.07.25 05:45:26:744]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 4 on 2025.07.03 17:00:00 for instance 32321
[01.07.25 05:45:26:746]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 2 auction(s) for instance 32322.
[01.07.25 05:45:26:746]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 5 on 2025.07.05 17:00:00 for instance 32322
[01.07.25 05:45:26:746]  INFO itemauction.ItemAuctionManager: ItemAuctionManager: Loaded 3 instance(s).
[01.07.25 05:45:26:746]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Masterio Pack ]
[01.07.25 05:45:26:777]  INFO gameserver.GameServer: ------------------------------------------------------=[ Merge System Loaded ]
[01.07.25 05:45:26:794]  INFO bosses.AntharasManager: AntharasManager: State of Antharas is NOTSPAWN.
[01.07.25 05:45:26:795]  INFO bosses.AntharasManager: AntharasManager: Next spawn date of Antharas is 21:00 31.12.1969.
[01.07.25 05:45:26:797]  INFO bosses.BaiumManager: BaiumManager: State of Baium is NOTSPAWN.
[01.07.25 05:45:26:800]  INFO bosses.BaiumManager: BaiumManager: Next spawn date: 21:00 31.12.1969
[01.07.25 05:45:26:811]  INFO bosses.BelethManager: Beleth Manager: Loaded successfully
[01.07.25 05:45:26:813]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Loaded 20 Mysterious-Box spawns.
[01.07.25 05:45:26:817]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 physical monsters spawns.
[01.07.25 05:45:26:819]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 magical monsters spawns.
[01.07.25 05:45:26:821]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 92 Church of duke monsters spawns.
[01.07.25 05:45:26:822]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 68 Emperor's grave NPC spawns.
[01.07.25 05:45:26:823]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Conquerors' Sepulcher Manager
[01.07.25 05:45:26:823]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Emperors' Sepulcher Manager
[01.07.25 05:45:26:824]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Great Sages' Sepulcher Manager
[01.07.25 05:45:26:824]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Judges' Sepulcher Manager
[01.07.25 05:45:26:825]  INFO bosses.FourSepulchersManager: FourSepulchersManager: Beginning in Attack time
[01.07.25 05:45:26:827]  INFO bosses.SailrenManager: SailrenManager: State of Sailren is NOTSPAWN.
[01.07.25 05:45:26:827]  INFO bosses.SailrenManager: SailrenManager: Next spawn date of Sailren is 21:00 31.12.1969.
[01.07.25 05:45:26:828]  INFO bosses.ValakasManager: ValakasManager: State of Valakas is NOTSPAWN.
[01.07.25 05:45:26:828]  INFO threading.RunnableImpl: Tue Jul 01 05:45:26 BRT 2025 Atk announce scheduled to 45.0 minute of this hour.
[01.07.25 05:45:26:828]  INFO bosses.ValakasManager: ValakasManager: Next spawn date of Valakas is 21:00 31.12.1969.
[01.07.25 05:45:26:923]  INFO tables.PetDataTable: PetDataTable: Loaded 3870 pets.
[01.07.25 05:45:26:927]  INFO model.Manor: ManorManager: Loaded 270 seeds
[01.07.25 05:45:27:027]  INFO scriptconfig.ScriptConfig: Loaded Service: ScripsConfig
[01.07.25 05:45:27:028]  INFO community.CharacterServicesCommunity: === CharacterServicesCommunity onLoad called ===
[01.07.25 05:45:27:028]  INFO community.CharacterServicesCommunity: COMMUNITYBOARD_ENABLED: true
[01.07.25 05:45:27:029]  INFO community.CharacterServicesCommunity: EnableMergeCommunity: false
[01.07.25 05:45:27:029]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Character Services Community loaded.
[01.07.25 05:45:27:095]  INFO dailyquests.AbstractDPScript: DailyQuestHandler: Loaded DailyQuests data
[01.07.25 05:45:27:095]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Handler registered successfully.
[01.07.25 05:45:27:102]  INFO model.Player: Loaded Service: SellPcService
[01.07.25 05:45:27:104]  INFO community.CareerManager: CommunityBoard: Manage Career service loaded.
[01.07.25 05:45:27:107]  INFO community.CommunityAuctionHouse: CommunityBoard: Auction System Service loaded.
[01.07.25 05:45:27:111]  INFO community.CommunityAugment: CommunityBoard: Augmentation Manager loaded with 188 lifestone skills.
[01.07.25 05:45:27:112]  INFO community.CommunityBoard: CommunityBoard: service loaded.
[01.07.25 05:45:27:113]  INFO community.CommunityBoardAcademy: CommunityBoard: Academy service loaded.
[01.07.25 05:45:27:113]  INFO community.CommunityBosses: CommunityBoard: Bosses loaded.
[01.07.25 05:45:27:115]  INFO community.CommunityClan: CommunityBoard: Clan service loaded.
[01.07.25 05:45:27:115]  INFO community.CommunityDropCalculator: CommunityBoard: Drop Calculator service loaded.
[01.07.25 05:45:27:116]  INFO community.CommunityEvents: CommunityBoard: Community Events service loaded.
[01.07.25 05:45:27:116]  INFO community.CommunityFacebook: CommunityBoard: Facebook Rewards page loaded.
[01.07.25 05:45:27:117]  INFO community.CommunityNpcs: CommunityBoard: Npcs loaded.
[01.07.25 05:45:27:118]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[01.07.25 05:45:27:123]  INFO community.CommunityTournament: CommunityBoard: Tournament loaded.
[01.07.25 05:45:27:124]  INFO community.CommunityWarehouse: CommunityBoard: Warehouse loaded.
[01.07.25 05:45:27:124]  INFO community.Forge: CommunityBoard: Forge loaded.
[01.07.25 05:45:27:125]  INFO community.ServicesCommunity: ServicesCommunity: Services Community service loaded.
[01.07.25 05:45:27:132]  INFO gameserver.GameServer: ===============[Spawn Manager]==================
[01.07.25 05:45:27:188]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1000 npc for group: NONE
[01.07.25 05:45:27:317]  INFO instancemanager.SpawnManager: SpawnManager: spawned 5000 npc for group: NONE
[01.07.25 05:45:27:341]  INFO naia.NaiaTowerManager: Naia Tower Manager: Loaded 12 rooms
[01.07.25 05:45:27:416]  INFO instancemanager.SpawnManager: SpawnManager: spawned 7000 npc for group: NONE
[01.07.25 05:45:27:766]  INFO instancemanager.SpawnManager: SpawnManager: spawned 12000 npc for group: NONE
[01.07.25 05:45:27:842]  INFO instancemanager.SpawnManager: SpawnManager: spawned 13000 npc for group: NONE
[01.07.25 05:45:28:741]  INFO instancemanager.SpawnManager: SpawnManager: spawned 21000 npc for group: NONE
[01.07.25 05:45:28:917]  INFO instancemanager.SpawnManager: SpawnManager: spawned 22000 npc for group: NONE
[01.07.25 05:45:29:381]  INFO instancemanager.SpawnManager: SpawnManager: spawned 25000 npc for group: NONE
[01.07.25 05:45:29:554]  INFO instancemanager.SpawnManager: SpawnManager: spawned 26000 npc for group: NONE
[01.07.25 05:45:29:911]  INFO instancemanager.SpawnManager: SpawnManager: spawned 28000 npc for group: NONE
[01.07.25 05:45:30:704]  INFO instancemanager.SpawnManager: SpawnManager: spawned 32000 npc for group: NONE
[01.07.25 05:45:31:448]  INFO instancemanager.SpawnManager: SpawnManager: spawned 35000 npc for group: NONE
[01.07.25 05:45:31:717]  INFO instancemanager.SpawnManager: SpawnManager: spawned 36000 npc for group: NONE
[01.07.25 05:45:31:970]  INFO instancemanager.SpawnManager: SpawnManager: spawned 37000 npc for group: NONE
[01.07.25 05:45:32:345]  INFO instancemanager.SpawnManager: SpawnManager: spawned 38373 npc; spawns: 17087; group: NONE
[01.07.25 05:45:32:345]  INFO gameserver.GameServer: --------------------------------------------------------------------=[ Boats ]
[01.07.25 05:45:32:346]  INFO data.BoatHolder: BoatHolder: loaded 2 boat(s) count.
[01.07.25 05:45:32:348]  INFO data.BoatHolder: BoatHolder: Spawning: RunePrimeval_Vehicle
[01.07.25 05:45:32:348]  INFO data.BoatHolder: BoatHolder: Spawning: AdenGracia_Airship
[01.07.25 05:45:32:349]  INFO holder.StaticObjectHolder: StaticObjectHolder: spawned: 50 static object(s).
[01.07.25 05:45:32:349]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Dimensional Rift ]
[01.07.25 05:45:32:363]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 13 room types with 112 rooms.
[01.07.25 05:45:32:364]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 300 DimensionalRift spawns, 0 errors.
[01.07.25 05:45:32:367]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Seven Signs ]
[01.07.25 05:45:32:377]  INFO instancemanager.AutoSpawnManager: AutoSpawnHandler: Loaded 50 handlers in total.
[01.07.25 05:45:32:377]  INFO gameserver.GameServer: ===================================================================
[01.07.25 05:45:32:378]  INFO gameserver.GameServer: ===================[Loading Olympiad System]=======================
[01.07.25 05:45:32:381]  INFO olympiad.Olympiad: Olympiad System: Loading Olympiad System....
[01.07.25 05:45:32:381]  INFO olympiad.Olympiad: Olympiad System: Currently in Olympiad Period
[01.07.25 05:45:32:381]  INFO olympiad.Olympiad: Olympiad System: Period Ends....
[01.07.25 05:45:32:382]  INFO olympiad.Olympiad: Olympiad System: In 3 days, 18 hours and 15 mins.
[01.07.25 05:45:32:382]  INFO olympiad.Olympiad: Olympiad System: Next Weekly Change is in....
[01.07.25 05:45:32:382]  INFO olympiad.Olympiad: Olympiad System: In 4 days, 6 hours and 50 mins.
[01.07.25 05:45:32:383]  INFO olympiad.Olympiad: Olympiad System: Loaded 0 Noblesses
[01.07.25 05:45:32:383]  INFO olympiad.Olympiad: Olympiad System: Competition Period Starts in 0 days, 12 hours and 15 mins.
[01.07.25 05:45:32:383]  INFO olympiad.Olympiad: Olympiad System: Event starts/started: Tue Jul 01 18:00:32 BRT 2025
[01.07.25 05:45:32:386]  INFO entity.Hero: Hero System: Loaded 0 Heroes.
[01.07.25 05:45:32:387]  INFO entity.Hero: Hero System: Loaded 0 all time Heroes.
[01.07.25 05:45:32:387]  INFO gameserver.GameServer: ===================[Olympiad System Loaded]=======================
[01.07.25 05:45:32:387]  INFO gameserver.GameServer: ===================================================================
[01.07.25 05:45:32:388]  INFO instancemanager.PetitionManager: Initializing PetitionManager
[01.07.25 05:45:32:392]  INFO instancemanager.CursedWeaponsManager: CursedWeaponsManager: Loaded 2 cursed weapon(s).
[01.07.25 05:45:32:392]  INFO gameserver.GameServer: ------------------------------------------=[ Loaded Small Achievement System ]
[01.07.25 05:45:32:402]  INFO gameserver.GameServer: --------------------------------------------------------------=[ ItemHandler ]
[01.07.25 05:45:32:402]  INFO items.ItemHandler: ItemHandler: loaded 0 item handler(s) count.
[01.07.25 05:45:32:402]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Admin Commands ]
[01.07.25 05:45:32:402]  INFO admincommands.AdminCommandHandler: AdminCommandHandler: loaded 475 admin command handler(s) count.
[01.07.25 05:45:32:402]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Players Commands ]
[01.07.25 05:45:32:406]  INFO usercommands.UserCommandHandler: UserCommandHandler: loaded 17 user command handler(s) count.
[01.07.25 05:45:32:406]  INFO voicecommands.VoicedCommandHandler: VoicedCommandHandler: loaded 102 voiced command handler(s) count.
[01.07.25 05:45:32:409]  INFO gameserver.GameServer: ======================[Tournament By Kara`]==========================
[01.07.25 05:45:32:411]  INFO tournament.TournamentHolder: [TournamentHolder] Initialized 5 tournament holders:
[01.07.25 05:45:32:411]  INFO tournament.TournamentHolder: 1 vs 1
[01.07.25 05:45:32:411]  INFO tournament.TournamentHolder: 2 vs 2
[01.07.25 05:45:32:411]  INFO tournament.TournamentHolder: 4 vs 4
[01.07.25 05:45:32:416]  INFO tournament.TournamentHolder: 5 vs 5
[01.07.25 05:45:32:417]  INFO tournament.TournamentHolder: 6 vs 6
[01.07.25 05:45:32:418]  INFO gameserver.GameServer: ======================[Loading Castels & Clan Halls]==========================
[01.07.25 05:45:32:451]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Gludio Castle time - 16:00 06.07.2025
[01.07.25 05:45:32:455]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Dion Castle time - 16:00 06.07.2025
[01.07.25 05:45:32:458]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Giran Castle time - 20:00 06.07.2025
[01.07.25 05:45:32:461]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Oren Castle time - 20:00 06.07.2025
[01.07.25 05:45:32:464]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Aden Castle time - 16:00 06.07.2025
[01.07.25 05:45:32:470]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Innadril Castle time - 20:00 06.07.2025
[01.07.25 05:45:32:474]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Goddard Castle time - 20:00 06.07.2025
[01.07.25 05:45:32:477]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Rune Castle time - 16:00 06.07.2025
[01.07.25 05:45:32:480]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Schuttgart Castle time - 16:00 06.07.2025
[01.07.25 05:45:32:482]  INFO impl.ClanHallNpcSiegeEvent: ClanHallNpcSiegeEvent: Fortress Of Resistance time - 18:00 07.07.2025
[01.07.25 05:45:32:484]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[01.07.25 05:45:32:485]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[01.07.25 05:45:32:486]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Topaz Hall time - 15:00 02.07.2025
[01.07.25 05:45:32:487]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Ruby Hall time - 15:00 02.07.2025
[01.07.25 05:45:32:489]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Crystal Hall time - 15:00 02.07.2025
[01.07.25 05:45:32:490]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[01.07.25 05:45:32:491]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[01.07.25 05:45:32:492]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[01.07.25 05:45:32:493]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[01.07.25 05:45:32:494]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Atramental Barracks time - 15:00 02.07.2025
[01.07.25 05:45:32:495]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Scarlet Barracks time - 15:00 02.07.2025
[01.07.25 05:45:32:496]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Viridian Barracks time - 15:00 02.07.2025
[01.07.25 05:45:32:497]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Devastated Castle time - 18:00 05.07.2025
[01.07.25 05:45:32:497]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Bandit Stronghold time - 22:00 05.07.2025
[01.07.25 05:45:32:498]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[01.07.25 05:45:32:499]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[01.07.25 05:45:32:500]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[01.07.25 05:45:32:501]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[01.07.25 05:45:32:502]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Gold Manor time - 15:00 02.07.2025
[01.07.25 05:45:32:503]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[01.07.25 05:45:32:504]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[01.07.25 05:45:32:505]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[01.07.25 05:45:32:506]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[01.07.25 05:45:32:507]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[01.07.25 05:45:32:508]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[01.07.25 05:45:32:509]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[01.07.25 05:45:32:510]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[01.07.25 05:45:32:512]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[01.07.25 05:45:32:513]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[01.07.25 05:45:32:514]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Mont Chamber time - 15:00 02.07.2025
[01.07.25 05:45:32:515]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Astaire Chamber time - 15:00 02.07.2025
[01.07.25 05:45:32:516]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Aria Chamber time - 15:00 02.07.2025
[01.07.25 05:45:32:517]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Yiana Chamber time - 15:00 02.07.2025
[01.07.25 05:45:32:518]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Roien Chamber time - 15:00 02.07.2025
[01.07.25 05:45:32:519]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Luna Chamber time - 15:00 02.07.2025
[01.07.25 05:45:32:519]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Traban Chamber time - 15:00 02.07.2025
[01.07.25 05:45:32:520]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Eisen Hall time - 15:00 02.07.2025
[01.07.25 05:45:32:521]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Heavy Metal Hall time - 15:00 02.07.2025
[01.07.25 05:45:32:522]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Molten Ore Hall time - 15:00 02.07.2025
[01.07.25 05:45:32:523]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Titan Hall time - 15:00 02.07.2025
[01.07.25 05:45:32:524]  INFO impl.ClanHallMiniGameEvent: ClanHallMiniGameEvent: Rainbow Springs time - 22:00 05.07.2025
[01.07.25 05:45:32:524]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Wild Beast Reserve time - 21:00 05.07.2025
[01.07.25 05:45:32:524]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Fortress of Dead time - 18:00 05.07.2025
[01.07.25 05:45:32:525]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Gludio Dominion time - undefined
[01.07.25 05:45:32:526]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Dion Dominion time - undefined
[01.07.25 05:45:32:526]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Giran Dominion time - undefined
[01.07.25 05:45:32:527]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Oren Dominion time - undefined
[01.07.25 05:45:32:528]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Aden Dominion time - undefined
[01.07.25 05:45:32:528]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Innadril Dominion time - undefined
[01.07.25 05:45:32:529]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Goddard Dominion time - undefined
[01.07.25 05:45:32:529]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Rune Dominion time - undefined
[01.07.25 05:45:32:529]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Schuttgart Dominion time - undefined
[01.07.25 05:45:32:532]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Shanty Fortress time - undefined
[01.07.25 05:45:32:536]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Southern Fortress time - undefined
[01.07.25 05:45:32:538]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hive Fortress time - undefined
[01.07.25 05:45:32:541]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Valley Fortress time - undefined
[01.07.25 05:45:32:543]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Ivory Fortress time - undefined
[01.07.25 05:45:32:546]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Narsell Fortress time - undefined
[01.07.25 05:45:32:549]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Bayou Fortress time - undefined
[01.07.25 05:45:32:551]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: White Sands Fortress time - undefined
[01.07.25 05:45:32:554]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Borderland Fortress time - undefined
[01.07.25 05:45:32:557]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Swamp Fortress time - undefined
[01.07.25 05:45:32:559]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Archaic Fortress time - undefined
[01.07.25 05:45:32:562]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Floran Fortress time - undefined
[01.07.25 05:45:32:569]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Cloud Mountain Fortress time - undefined
[01.07.25 05:45:32:571]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Tanor Fortress time - undefined
[01.07.25 05:45:32:574]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Dragonspine Fortress time - undefined
[01.07.25 05:45:32:578]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Antharas Fortress time - undefined
[01.07.25 05:45:32:580]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Western Fortress time - undefined
[01.07.25 05:45:32:583]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hunter's Fortress time - undefined
[01.07.25 05:45:32:585]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Aaru Fortress time - undefined
[01.07.25 05:45:32:588]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Demon Fortress time - undefined
[01.07.25 05:45:32:591]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Monastic Fortress time - undefined
[01.07.25 05:45:32:592]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 70-75 Level time - 06:00 01.07.2025
[01.07.25 05:45:32:592]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 76-79 Level time - 06:00 01.07.2025
[01.07.25 05:45:32:593]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 80-85 Level time - 06:00 01.07.2025
[01.07.25 05:45:32:593]  INFO fightclub.LastManStandingEvent: LastManStandingEvent: Last Hero time - undefined
[01.07.25 05:45:32:593]  INFO fightclub.TeamVSTeamEvent: TeamVSTeamEvent: Team VS Team time - undefined
[01.07.25 05:45:32:593]  INFO fightclub.LuckyCreaturesEvent: LuckyCreaturesEvent: Lucky Creatures time - undefined
[01.07.25 05:45:32:593]  INFO fightclub.CaptureTheFlagEvent: CaptureTheFlagEvent: Capture The Flag time - undefined
[01.07.25 05:45:32:593]  INFO fightclub.FFATreasureHuntEvent: FFATreasureHuntEvent: Treasure Hunt time - undefined
[01.07.25 05:45:32:594]  INFO fightclub.KoreanStyleEvent: KoreanStyleEvent: Korean Style time - undefined
[01.07.25 05:45:32:594]  INFO fightclub.ProtectTheKingEvent: ProtectTheKingEvent: Protect The King time - undefined
[01.07.25 05:45:32:594]  INFO fightclub.FightForThroneEvent: FightForThroneEvent: Fight For Throne time - undefined
[01.07.25 05:45:32:594]  INFO fightclub.DeathMatchEvent: DeathMatchEvent: Death Match time - undefined
[01.07.25 05:45:32:594]  INFO fightclub.RussianRouletteEvent: RussianRouletteEvent: Russian Roulette time - undefined
[01.07.25 05:45:32:595]  INFO instancemanager.CastleManorManager: Manor System: Initializing...
[01.07.25 05:45:32:596]  INFO gameserver.GameServer: ==============================================================================
[01.07.25 05:45:32:597]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Auto Cleaner ]
[01.07.25 05:45:32:597]  INFO gameserver.GameServer: IdFactory: Free ObjectID's remaining: 1879004840
[01.07.25 05:45:32:597]  INFO gameserver.GameServer: ==============================================================================
[01.07.25 05:45:32:598]  INFO instancemanager.CoupleManager: Initializing CoupleManager
[01.07.25 05:45:32:598]  INFO instancemanager.CoupleManager: Loaded: 0 couples(s)
[01.07.25 05:45:32:600]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Hellbound ]
[01.07.25 05:45:32:606]  INFO instancemanager.HellboundManager: HellboundManager: Loaded 335 spawn entries.
[01.07.25 05:45:32:730]  INFO instancemanager.HellboundManager: HellboundManager: Spawned 623 mobs and NPCs according to the current Hellbound stage
[01.07.25 05:45:32:730]  INFO instancemanager.HellboundManager: Hellbound Manager: Loaded
[01.07.25 05:45:32:731]  INFO naia.NaiaTowerManager: Naia Core Manager: Loaded
[01.07.25 05:45:32:732]  INFO gameserver.GameServer: ==============================================================================
[01.07.25 05:45:32:732]  INFO instancemanager.SoDManager: Seed of Destruction Manager: Loaded
[01.07.25 05:45:32:733]  INFO instancemanager.SoIManager: Seed of Infinity Manager: Loaded. Current stage is: 1
[01.07.25 05:45:32:734]  INFO instancemanager.SpawnManager: SpawnManager: spawned 4 npc; spawns: 4; group: soi_world_mouths
[01.07.25 05:45:32:734]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: soi_world_abyssgaze2
[01.07.25 05:45:32:735]  INFO instancemanager.BloodAltarManager: Blood Altar Manager: Initializing...
[01.07.25 05:45:32:744]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_alive_npc
[01.07.25 05:45:32:746]  INFO gameserver.GameServer: Preparing Drop Calculator
[01.07.25 05:45:32:805]  INFO gameserver.GameServer: ----------------------------------------------------------=[ Offline Buffers ]
[01.07.25 05:45:32:806]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loading offline buffers...
[01.07.25 05:45:32:807]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loaded: 0 offline buffer(s)
[01.07.25 05:45:32:816]  INFO achievements.Achievements: Achievement System: Loaded 4 achievement categories and 39 achievements.
[01.07.25 05:45:32:818]  INFO gameserver.GameServer: Emotions Loaded....
[01.07.25 05:45:32:818]  INFO gameserver.GameServer: --------------------------------------------------=[ DataBase Cleaner Loaded ]
[01.07.25 05:45:32:819]  INFO dao.CharacterDAO: Found 0 characters to delete!
[01.07.25 05:45:32:888]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Initializing
[01.07.25 05:45:32:889]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Load OK
[01.07.25 05:45:32:889]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Loaded 0 announce.
[01.07.25 05:45:32:890]  INFO gameserver.GameServer: ---------------------------------------------------=[ Loaded Facebook System ]
[01.07.25 05:45:32:893]  INFO gameserver.GameServer: -----------------------------------------------------=[ Loaded Stream System ]
[01.07.25 05:45:32:897]  INFO holder.ProxiesHolder: ProxiesHolder: loaded 20 proxies(s) count.
[01.07.25 05:45:32:898]  INFO gameserver.GameServer: ------------------------------------------------------=[ Loaded Proxy System ]
[01.07.25 05:45:35:349]  INFO donation.DonationReader: Donation Reader service started
[01.07.25 05:45:35:349]  INFO gameserver.GameServer: --------------------------------------------=[ Loaded AUTO - Donation System ]
[01.07.25 05:45:35:351]  INFO taskmanager.BackupTaskManager: Next Auto Backup in 54 Min.
[01.07.25 05:45:35:370]  INFO votingengine.VotingSettings: VotingSettings: Loaded 3 messages, 4 drops!
[01.07.25 05:45:35:373]  INFO votingengine.VotingRewardAPI: VoteReward: Has been successfully loaded!
[01.07.25 05:45:35:374]  INFO model.PhantomPlayers: Loading phantom players...
[01.07.25 05:45:35:382]  INFO model.PhantomPlayers: Loaded 32002 possible phantom names.
[01.07.25 05:45:35:382]  INFO model.PhantomPlayers: Loaded 7172 possible phantom titles.
[01.07.25 05:45:35:383]  INFO model.PhantomPlayers: PhantomPlayers: Cached 0 players.
[01.07.25 05:45:35:383]  INFO model.PhantomPlayers: PhantomPlayers: Free names 32002.
[01.07.25 05:45:35:383]  INFO model.PhantomPlayers: Loaded 0 phantom players from database with a maximum of 3000 phantoms.
[01.07.25 05:45:35:384]  INFO model.PhantomPlayers: Scheduled spawner with 1 seconds delay.
[01.07.25 05:45:35:526]  INFO instancemanager.SpawnManager: SpawnManager: spawned 443 npc; spawns: 121; group: DAY
[01.07.25 05:45:35:587]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dawn_spawn
[01.07.25 05:45:35:779]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dusk_spawn
[01.07.25 05:45:35:779]  INFO gameserver.GameServer: Telnet server is currently disabled.
[01.07.25 05:45:35:779]  INFO gameserver.GameServer: ==============================================================================
[01.07.25 05:45:35:779]  INFO gameserver.GameServer: >>>>>>>>>>>>>>> GameServer Started <<<<<<<<<<<<<<
[01.07.25 05:45:35:780]  INFO gameserver.GameServer: Maximum Numbers of Connected Players: 3000
[01.07.25 05:45:35:780]  INFO gameserver.GameServer: ====================================================================
[01.07.25 05:45:35:797]  INFO gameserver.GameServer: AllowedMemory: ........... 6291456 KB
[01.07.25 05:45:35:797]  INFO gameserver.GameServer: 
Allocated: ............... 4194304 KB (66.6667%)
[01.07.25 05:45:35:797]  INFO gameserver.GameServer: 
Non-Allocated: ........... 2097152 KB (33.3333%)
[01.07.25 05:45:35:797]  INFO gameserver.GameServer: 
AllocatedMemory: ......... 4194304 KB
[01.07.25 05:45:35:797]  INFO gameserver.GameServer: Used: .................... 1162975 KB (18.485%)
[01.07.25 05:45:35:798]  INFO gameserver.GameServer: 
Unused (cached): ......... 3031328 KB (48.1817%)
[01.07.25 05:45:35:799]  INFO gameserver.GameServer: 
UseableMemory: ........... 5128480 KB (81.515%)
[01.07.25 05:45:35:799]  INFO gameserver.GameServer: 

[01.07.25 05:45:35:799]  INFO gameserver.GameServer: ====================================================================
[01.07.25 05:45:35:801]  INFO loginservercon.AuthServerCommunication: Connecting to authserver on 127.0.0.1:9014
[01.07.25 05:45:35:805]  INFO lspackets.AuthResponse: Registered on authserver as 1 [Bartz]
[01.07.25 05:47:12:891]  INFO tasks.RestoreOfflineTraders: Restored 0 offline traders
[01.07.25 05:50:26:827]  INFO threading.RunnableImpl: FourSepulchersManager: Entry time: Tue Jul 01 05:55:26 BRT 2025
[01.07.25 06:00:00:003]  INFO threading.RunnableImpl: Manor System: Next period approved
[01.07.25 06:06:00:838]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, before:0 toRemove:0 after:0 to Change:0
[01.07.25 06:06:00:838]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, Before: 0 Final:0
[01.07.25 06:06:00:839]  INFO fightclubmanager.FightClubEventManager: Fight For Throne: Removing room because it doesnt have enough players
[01.07.25 06:15:32:799]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_dead_npc
[01.07.25 06:15:32:800]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_aden
[01.07.25 06:15:32:802]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: bloodaltar_boss_darkelf
[01.07.25 06:15:32:812]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_dion
[01.07.25 06:15:32:813]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_dwarw
[01.07.25 06:15:32:813]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_giran
[01.07.25 06:15:32:814]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_gludin
[01.07.25 06:15:32:814]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_gludio
[01.07.25 06:15:32:815]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_goddart
[01.07.25 06:15:32:816]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_heine
[01.07.25 06:15:32:816]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: bloodaltar_boss_orc
[01.07.25 06:15:32:817]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_oren
[01.07.25 06:15:32:817]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: bloodaltar_boss_schutgart
[01.07.25 06:30:00:409]  INFO tasks.TaskRecom: Recommendation Global Task: launched.
[01.07.25 06:30:00:409]  INFO tasks.TaskNevitSystem: Navit System Global Task: launched.
[01.07.25 06:30:00:409]  INFO tasks.TaskRecom: Recommendation Global Task: completed.
[01.07.25 06:30:00:409]  INFO tasks.TaskNevitSystem: Navit System Task: completed.
[01.07.25 06:40:00:010]  INFO dao.DatabaseBackupManager: Executing CMD: [mysqldump, p_h5, -r, C:\Users\<USER>\Documents\Projetos\Lineage II\Sources\Projeto_Forgotten_Lands\build\dist\gameserver\log\backups\p_h5 2025-07-01 06.40.sql]
[01.07.25 06:40:00:022] ERROR dao.DatabaseBackupManager: Error while making Backup!
java.io.IOException: Cannot run program "mysqldump": CreateProcess error=2, O sistema não pode encontrar o arquivo especificado
	at java.base/java.lang.ProcessBuilder.start(ProcessBuilder.java:1143)
	at java.base/java.lang.ProcessBuilder.start(ProcessBuilder.java:1073)
	at java.base/java.lang.Runtime.exec(Runtime.java:615)
	at java.base/java.lang.Runtime.exec(Runtime.java:474)
	at l2mv.gameserver.dao.DatabaseBackupManager.executeCMD(DatabaseBackupManager.java:77)
	at l2mv.gameserver.dao.DatabaseBackupManager.doBackup(DatabaseBackupManager.java:65)
	at l2mv.gameserver.taskmanager.BackupTaskManager$AutoBackup.runImpl(BackupTaskManager.java:80)
	at l2mv.commons.threading.RunnableImpl.run(RunnableImpl.java:21)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:577)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1589)
Caused by: java.io.IOException: CreateProcess error=2, O sistema não pode encontrar o arquivo especificado
	at java.base/java.lang.ProcessImpl.create(Native Method)
	at java.base/java.lang.ProcessImpl.<init>(ProcessImpl.java:500)
	at java.base/java.lang.ProcessImpl.start(ProcessImpl.java:159)
	at java.base/java.lang.ProcessBuilder.start(ProcessBuilder.java:1110)
	... 13 more
[01.07.25 06:40:00:025]  INFO taskmanager.BackupTaskManager: Backup finished. Server frozen for 0 seconds!
[01.07.25 06:40:00:025]  INFO taskmanager.BackupTaskManager: Next Auto Backup in 23 H., 59 Min.
[01.07.25 06:45:27:126]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[01.07.25 06:46:10:770] ERROR loginservercon.AuthServerCommunication: AuthServer I/O error
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:401)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:434)
	at l2mv.gameserver.network.loginservercon.AuthServerCommunication.read(AuthServerCommunication.java:258)
	at l2mv.gameserver.network.loginservercon.AuthServerCommunication.run(AuthServerCommunication.java:220)
[01.07.25 06:46:15:772]  INFO loginservercon.AuthServerCommunication: Connecting to authserver on 127.0.0.1:9014
[01.07.25 06:46:15:781]  INFO lspackets.AuthResponse: Registered on authserver as 1 [Bartz]
[01.07.25 06:50:26:871]  INFO threading.RunnableImpl: FourSepulchersManager: Entry time: Tue Jul 01 06:55:26 BRT 2025
[01.07.25 07:00:00:298]  INFO instancemanager.SpawnManager: SpawnManager: spawned 444 npc; spawns: 122; group: NIGHT
[01.07.25 07:06:00:823]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, before:0 toRemove:0 after:0 to Change:0
[01.07.25 07:06:00:823]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, Before: 0 Final:0
[01.07.25 07:06:00:825]  INFO fightclubmanager.FightClubEventManager: Death Match: Removing room because it doesnt have enough players
[01.07.25 07:15:32:781]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_alive_npc
[01.07.25 08:03:40:523]  INFO gameserver.GameServer: ============================================================================
[01.07.25 08:03:40:525]  INFO gameserver.GameServer: Nome: .................................................................... GAME SERVER
[01.07.25 08:03:40:525]  INFO gameserver.GameServer: Project Revision: ........................................................ LII [MultVerso]
[01.07.25 08:03:40:525]  INFO gameserver.GameServer: Update: .................................................................. High Five: Part 5
[01.07.25 08:03:40:525]  INFO gameserver.GameServer: Numero da Revision:....................................................... 2.0
[01.07.25 08:03:40:525]  INFO gameserver.GameServer: Build Revision: .......................................................... MultVerso
[01.07.25 08:03:40:526]  INFO gameserver.GameServer: Build date: .............................................................. 2025.06.30 12:18
[01.07.25 08:03:40:526]  INFO gameserver.GameServer: Compiler version: ........................................................ 17.0.7+7 (Eclipse Adoptium)
[01.07.25 08:03:40:526]  INFO gameserver.GameServer: ============================================================================
[01.07.25 08:03:40:564]  INFO gameserver.Config: Loaded 6 trade words.
[01.07.25 08:03:40:572]  INFO gameserver.Config: Abuse: Loaded 15 abuse words.
[01.07.25 08:03:40:617]  INFO gameserver.Config: Loaded 313 Configs!
[01.07.25 08:03:40:623]  INFO gameserver.GameServer: IP EXTERNO =============================127.0.0.1
[01.07.25 08:03:40:624]  INFO gameserver.GameServer: IP INTERNO =============================127.0.0.1
[01.07.25 08:03:40:624]  INFO gameserver.GameServer: Maximo Online IP ====================== 3000
[01.07.25 08:03:40:625]  INFO gameserver.GameServer: Hora do Restart  =======================45 5 * * *
[01.07.25 08:03:40:762]  INFO gameserver.GameServer: -----------------------------------------=[ Loading Protection Configuration ]
[01.07.25 08:03:40:768]  INFO idfactory.IdFactory: IdFactory: Clear characters online status.
[01.07.25 08:03:40:797]  INFO idfactory.IdFactory: IdFactory: Cleaned 0 elements from database in 0sec.
[01.07.25 08:03:40:804]  INFO idfactory.IdFactory: IdFactory: Extracted 2 used id's from characters
[01.07.25 08:03:40:804]  INFO idfactory.IdFactory: IdFactory: Extracted 46 used id's from items
[01.07.25 08:03:40:804]  INFO idfactory.IdFactory: IdFactory: Extracted 1 used id's from clan_data
[01.07.25 08:03:40:805]  INFO idfactory.IdFactory: IdFactory: Extracted total 49 used id's.
[01.07.25 08:03:40:805]  INFO idfactory.BitSetIDFactory: IdFactory: 102912 id's available.
[01.07.25 08:03:40:913]  INFO gameserver.GameServer: ===============[Loading Scripts]==================
[01.07.25 08:03:40:914]  INFO scripts.Scripts: Scripts: Loading...
[01.07.25 08:03:40:915]  INFO scripts.Scripts: Loading Server Scripts
[01.07.25 08:03:41:187]  INFO scripts.Scripts: Scripts: Loaded 1288 classes.
[01.07.25 08:03:41:195]  INFO vote.VoteMain: Loaded: Vote System - Individual Reward.
[01.07.25 08:03:41:196]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Twitch Manager ]
[01.07.25 08:03:41:224]  INFO gameserver.GameServer: ----------------------------------------------------------=[ AntiFeedManager ]
[01.07.25 08:03:41:224]  INFO gameserver.GameServer: -------------------------------------------------------------=[ QuestManager ]
[01.07.25 08:03:41:225]  INFO gameserver.GameServer: QuestManager : loaded 0 quest's
[01.07.25 08:03:41:236]  INFO tables.FakePlayersTable: FakePlayersTable: Loaded 32002 Fake Players.
[01.07.25 08:03:41:244]  INFO utils.Strings: Loaded 418 translit entries.
[01.07.25 08:03:41:246]  INFO gameserver.GameTimeController: GameTimeController: initialized. Current time is 6:22 in the day.
[01.07.25 08:03:41:246]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Lineage World ]
[01.07.25 08:03:41:249]  INFO model.World: L2World: Creating regions: [256][272][32].
[01.07.25 08:03:41:249]  INFO gameserver.GameServer: ==============================================================================
[01.07.25 08:03:41:251]  INFO htm.HtmCache: HtmCache: disabled.
[01.07.25 08:03:41:258]  INFO data.StringHolder: StringHolder: load strings: 1066 for lang: ENGLISH
[01.07.25 08:03:41:258]  INFO data.StringHolder: StringHolder: load strings: 876 for lang: RUSSIAN
[01.07.25 08:03:42:466]  INFO skills.SkillsEngine: SkillsEngine: Loaded 66051 skill templates from XML files. Max id: 91000, max level: 259
[01.07.25 08:03:43:002]  INFO holder.OptionDataHolder: OptionDataHolder: loaded 24975 option data(s) count.
[01.07.25 08:03:43:627]  INFO holder.ItemHolder: ItemHolder: loaded 19240 item(s) count.
[01.07.25 08:03:43:633]  INFO holder.FakePlayerNpcsHolder: FakePlayerNpcsHolder: loaded 8 fake player npcs(s) count.
[01.07.25 08:03:44:647]  INFO holder.NpcHolder: NpcHolder: loaded 10519 npc(s) count.
[01.07.25 08:03:44:655]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[01.07.25 08:03:44:672]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[01.07.25 08:03:44:706]  INFO holder.ExchangeItemHolder: ExchangeItemHolder: loaded 602 exchange item(s) count.
[01.07.25 08:03:44:711]  INFO holder.StaticObjectHolder: StaticObjectHolder: loaded 50 static object(s) count.
[01.07.25 08:03:44:816]  INFO holder.DoorHolder: DoorHolder: loaded 1155 door(s) count.
[01.07.25 08:03:44:871]  INFO holder.ZoneHolder: ZoneHolder: loaded 1260 zone(s) count.
[01.07.25 08:03:45:206]  INFO holder.SpawnHolder: SpawnHolder: loaded 28949 spawn(s) count.
[01.07.25 08:03:45:285]  INFO holder.InstantZoneHolder: InstantZoneHolder: loaded 94 instant zone(s) count.
[01.07.25 08:03:45:593]  INFO holder.AirshipDockHolder: AirshipDockHolder: loaded 4 airship dock(s) count.
[01.07.25 08:03:45:634]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 57215 normal learns for 103 classes.
[01.07.25 08:03:45:634]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 79 transfer learns for 3 classes.
[01.07.25 08:03:45:635]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 120 transformation learns for 6 races.
[01.07.25 08:03:45:635]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 662 fishing learns for 6 races.
[01.07.25 08:03:45:635]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 72 certification learns.
[01.07.25 08:03:45:635]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 3 collection learns.
[01.07.25 08:03:45:635]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 44 pledge learns.
[01.07.25 08:03:45:636]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 18 sub unit learns.
[01.07.25 08:03:45:643]  INFO holder.CharTemplateHolder: CharTemplateHolder: loaded 206 char template(s) count.
[01.07.25 08:03:45:739]  INFO holder.ResidenceHolder: ResidenceHolder: total size: 83
[01.07.25 08:03:45:739]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 44 clanhall(s).
[01.07.25 08:03:45:740]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 castle(s).
[01.07.25 08:03:45:740]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 21 fortress(s).
[01.07.25 08:03:45:740]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 dominion(s).
[01.07.25 08:03:45:803]  INFO instancemanager.RaidBossSpawnManager: RaidBossSpawnManager: Loaded 194 Statuses
[01.07.25 08:03:45:819]  INFO entity.SevenSigns: SevenSigns: Currently in the Competition (Quest Event) period!
[01.07.25 08:03:45:819]  INFO entity.SevenSigns: SevenSigns: The Seal of Avarice remains unclaimed.
[01.07.25 08:03:45:819]  INFO entity.SevenSigns: SevenSigns: The Seal of Gnosis remains unclaimed.
[01.07.25 08:03:45:820]  INFO entity.SevenSigns: SevenSigns: The Seal of Strife remains unclaimed.
[01.07.25 08:03:45:820]  INFO entity.SevenSigns: SevenSigns: The Competition this week, if the trend continue, will end with a tie.
[01.07.25 08:03:45:821]  INFO entity.SevenSigns: SevenSigns: Next period begins in 6 days, 9 hours and 56 mins.
[01.07.25 08:03:45:918]  INFO holder.EventHolder: EventHolder: loaded 105 event(s) count.
[01.07.25 08:03:45:929]  INFO holder.FightClubMapHolder: FightClubMapHolder: loaded 42 fight club map(s) count.
[01.07.25 08:03:45:933]  INFO holder.CubicHolder: CubicHolder: loaded 145 cubic(s) count.
[01.07.25 08:03:45:965]  INFO holder.BuyListHolder: TradeController: Loaded 4 file(s).
[01.07.25 08:03:45:965]  INFO holder.BuyListHolder: TradeController: Loaded 25014 Items.
[01.07.25 08:03:45:966]  INFO holder.BuyListHolder: TradeController: Loaded 726 Buylists.
[01.07.25 08:03:45:978]  INFO holder.RecipeHolder: RecipeController: Loaded 998 Recipes.
[01.07.25 08:03:46:300]  INFO holder.ProductHolder: ProductItemTable: Loaded 0 product item on sale.
[01.07.25 08:03:46:309]  INFO holder.HennaHolder: HennaHolder: loaded 180 henna(s) count.
[01.07.25 08:03:46:313]  INFO holder.EnchantItemHolder: EnchantItemHolder: load 4 enchant scroll(s).
[01.07.25 08:03:46:316]  INFO holder.SoulCrystalHolder: SoulCrystalHolder: loaded 54 soul crystal(s) count.
[01.07.25 08:03:46:320]  INFO holder.ArmorSetsHolder: ArmorSetsHolder: loaded 199 armor sets(s) count.
[01.07.25 08:03:46:324]  INFO holder.PetitionGroupHolder: PetitionGroupHolder: loaded 3 petition group(s) count.
[01.07.25 08:03:46:325]  INFO holder.DressArmorHolder: DressArmorHolder: loaded 57 dress armor(s) count.
[01.07.25 08:03:46:327]  INFO holder.DressCloakHolder: DressCloakHolder: loaded 7 dress cloak(s) count.
[01.07.25 08:03:46:329]  INFO holder.DressShieldHolder: DressShieldHolder: loaded 10 dress shield(s) count.
[01.07.25 08:03:46:331]  INFO holder.DressWeaponHolder: DressWeaponHolder: loaded 401 dress weapon(s) count.
[01.07.25 08:03:46:380]  INFO holder.AugmentationDataHolder: AugmentationDataHolder: loaded 1022 augmentation data(s) count.
[01.07.25 08:03:46:385]  INFO holder.PremiumHolder: PremiumHolder: loaded 3 premium(s) count.
[01.07.25 08:03:46:387]  INFO holder.FoundationHolder: FoundationHolder: loaded 268 foundation(s) count.
[01.07.25 08:03:46:392]  INFO holder.DonationHolder: DonationHolder: loaded 192 donation(s) count.
[01.07.25 08:03:46:396]  INFO holder.TournamentMapHolder: TournamentMapHolder: loaded 3 tournament map(s) count.
[01.07.25 08:03:46:400]  INFO holder.FacebookCommentsHolder: FacebookCommentsHolder: loaded 2 facebook comments(s) count.
[01.07.25 08:03:46:400]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Banned HWIDS ]
[01.07.25 08:03:46:401]  INFO security.HWIDBan: black list (Hwid) loaded size: 0
[01.07.25 08:03:46:401]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Clan Crests ]
[01.07.25 08:03:46:403]  INFO cache.CrestCache: CrestCache: Loaded 0 crests
[01.07.25 08:03:46:403]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Loading Images ]
[01.07.25 08:03:46:405]  INFO cache.ImagesCache: ImagesCache: Loading images...
[01.07.25 08:03:57:998]  INFO cache.ImagesCache: ImagesCache: Loaded 8734 images
[01.07.25 08:03:57:998]  INFO gameserver.GameServer: ==============================================================================
[01.07.25 08:03:58:015]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Fish Table ]
[01.07.25 08:03:58:017]  INFO tables.FishTable: FishTable: Loaded 270 fishes.
[01.07.25 08:03:58:019]  INFO tables.FishTable: FishTable: Loaded 919 fish rewards.
[01.07.25 08:03:58:020]  INFO gameserver.GameServer: -------------------------------------------------------------------=[ Skills ]
[01.07.25 08:03:58:020]  INFO tables.SkillTreeTable: SkillTreeTable: Loaded 526 enchanted skills.
[01.07.25 08:03:58:049]  INFO datatables.EnchantNamesTable: EnchantNamesTable: Loaded: 1319 skill enchant names
[01.07.25 08:03:58:050]  INFO gameserver.GameServer: --------------------------------------------------------=[ Augmentation Data ]
[01.07.25 08:03:58:052]  INFO tables.AugmentationData: Initializing AugmentationData.
[01.07.25 08:03:58:065]  INFO tables.AugmentationData: AugmentationData: 10 bad skill(s) were skipped.
[01.07.25 08:03:58:077]  INFO tables.AugmentationData: AugmentationData: Loaded: 52 augmentation stats.
[01.07.25 08:03:58:077]  INFO tables.AugmentationData: AugmentationData: Loaded: 24 accessory augmentation stats.
[01.07.25 08:03:58:080]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 0
[01.07.25 08:03:58:080]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 1
[01.07.25 08:03:58:081]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 2
[01.07.25 08:03:58:081]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 3
[01.07.25 08:03:58:081]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 4
[01.07.25 08:03:58:081]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 5
[01.07.25 08:03:58:081]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 6
[01.07.25 08:03:58:081]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 7
[01.07.25 08:03:58:081]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 8
[01.07.25 08:03:58:082]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 9
[01.07.25 08:03:58:084]  INFO tables.EnchantHPBonusTable: EnchantHPBonusTable: Loaded bonuses for 6 grades.
[01.07.25 08:03:58:084]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Level Up Table ]
[01.07.25 08:03:58:086]  INFO tables.LevelUpTable: LevelUpData: Loaded 103 Character Level Up Templates.
[01.07.25 08:03:58:088]  INFO tables.PetSkillsTable: PetSkillsTable: Loaded 2558 skills.
[01.07.25 08:03:58:088]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Item Logs ]
[01.07.25 08:03:58:089]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Auctioneer ]
[01.07.25 08:03:58:090]  INFO itemauction.ItemAuctionManager: Initializing ItemAuctionManager
[01.07.25 08:03:58:095]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 1 auction(s) for instance 32320.
[01.07.25 08:03:58:097]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 1 on 2025.07.01 17:00:00 for instance 32320
[01.07.25 08:03:58:099]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 2 auction(s) for instance 32321.
[01.07.25 08:03:58:099]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 4 on 2025.07.03 17:00:00 for instance 32321
[01.07.25 08:03:58:101]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 2 auction(s) for instance 32322.
[01.07.25 08:03:58:101]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 5 on 2025.07.05 17:00:00 for instance 32322
[01.07.25 08:03:58:102]  INFO itemauction.ItemAuctionManager: ItemAuctionManager: Loaded 3 instance(s).
[01.07.25 08:03:58:102]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Masterio Pack ]
[01.07.25 08:03:58:137]  INFO gameserver.GameServer: ------------------------------------------------------=[ Merge System Loaded ]
[01.07.25 08:03:58:155]  INFO bosses.AntharasManager: AntharasManager: State of Antharas is NOTSPAWN.
[01.07.25 08:03:58:156]  INFO bosses.AntharasManager: AntharasManager: Next spawn date of Antharas is 21:00 31.12.1969.
[01.07.25 08:03:58:157]  INFO bosses.BaiumManager: BaiumManager: State of Baium is NOTSPAWN.
[01.07.25 08:03:58:160]  INFO bosses.BaiumManager: BaiumManager: Next spawn date: 21:00 31.12.1969
[01.07.25 08:03:58:171]  INFO bosses.BelethManager: Beleth Manager: Loaded successfully
[01.07.25 08:03:58:172]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Loaded 20 Mysterious-Box spawns.
[01.07.25 08:03:58:176]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 physical monsters spawns.
[01.07.25 08:03:58:179]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 magical monsters spawns.
[01.07.25 08:03:58:180]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 92 Church of duke monsters spawns.
[01.07.25 08:03:58:181]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 68 Emperor's grave NPC spawns.
[01.07.25 08:03:58:182]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Conquerors' Sepulcher Manager
[01.07.25 08:03:58:183]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Emperors' Sepulcher Manager
[01.07.25 08:03:58:183]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Great Sages' Sepulcher Manager
[01.07.25 08:03:58:183]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Judges' Sepulcher Manager
[01.07.25 08:03:58:184]  INFO bosses.FourSepulchersManager: FourSepulchersManager: Beginning in Attack time
[01.07.25 08:03:58:186]  INFO bosses.SailrenManager: SailrenManager: State of Sailren is NOTSPAWN.
[01.07.25 08:03:58:186]  INFO bosses.SailrenManager: SailrenManager: Next spawn date of Sailren is 21:00 31.12.1969.
[01.07.25 08:03:58:187]  INFO threading.RunnableImpl: Tue Jul 01 08:03:58 BRT 2025 Atk announce scheduled to 5.0 minute of this hour.
[01.07.25 08:03:58:187]  INFO bosses.ValakasManager: ValakasManager: State of Valakas is NOTSPAWN.
[01.07.25 08:03:58:188]  INFO bosses.ValakasManager: ValakasManager: Next spawn date of Valakas is 21:00 31.12.1969.
[01.07.25 08:03:58:281]  INFO tables.PetDataTable: PetDataTable: Loaded 3870 pets.
[01.07.25 08:03:58:285]  INFO model.Manor: ManorManager: Loaded 270 seeds
[01.07.25 08:03:58:377]  INFO scriptconfig.ScriptConfig: Loaded Service: ScripsConfig
[01.07.25 08:03:58:378]  INFO community.CharacterServicesCommunity: === CharacterServicesCommunity onLoad called ===
[01.07.25 08:03:58:379]  INFO community.CharacterServicesCommunity: COMMUNITYBOARD_ENABLED: true
[01.07.25 08:03:58:379]  INFO community.CharacterServicesCommunity: EnableMergeCommunity: false
[01.07.25 08:03:58:380]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Character Services Community loaded.
[01.07.25 08:03:58:447]  INFO dailyquests.AbstractDPScript: DailyQuestHandler: Loaded DailyQuests data
[01.07.25 08:03:58:447]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Handler registered successfully.
[01.07.25 08:03:58:455]  INFO model.Player: Loaded Service: SellPcService
[01.07.25 08:03:58:458]  INFO community.CareerManager: CommunityBoard: Manage Career service loaded.
[01.07.25 08:03:58:461]  INFO community.CommunityAuctionHouse: CommunityBoard: Auction System Service loaded.
[01.07.25 08:03:58:466]  INFO community.CommunityAugment: CommunityBoard: Augmentation Manager loaded with 188 lifestone skills.
[01.07.25 08:03:58:467]  INFO community.CommunityBoard: CommunityBoard: service loaded.
[01.07.25 08:03:58:468]  INFO community.CommunityBoardAcademy: CommunityBoard: Academy service loaded.
[01.07.25 08:03:58:468]  INFO community.CommunityBosses: CommunityBoard: Bosses loaded.
[01.07.25 08:03:58:471]  INFO community.CommunityClan: CommunityBoard: Clan service loaded.
[01.07.25 08:03:58:471]  INFO community.CommunityDropCalculator: CommunityBoard: Drop Calculator service loaded.
[01.07.25 08:03:58:472]  INFO community.CommunityEvents: CommunityBoard: Community Events service loaded.
[01.07.25 08:03:58:473]  INFO community.CommunityFacebook: CommunityBoard: Facebook Rewards page loaded.
[01.07.25 08:03:58:475]  INFO community.CommunityNpcs: CommunityBoard: Npcs loaded.
[01.07.25 08:03:58:476]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[01.07.25 08:03:58:480]  INFO community.CommunityTournament: CommunityBoard: Tournament loaded.
[01.07.25 08:03:58:480]  INFO community.CommunityWarehouse: CommunityBoard: Warehouse loaded.
[01.07.25 08:03:58:481]  INFO community.Forge: CommunityBoard: Forge loaded.
[01.07.25 08:03:58:482]  INFO community.ServicesCommunity: ServicesCommunity: Services Community service loaded.
[01.07.25 08:03:58:491]  INFO gameserver.GameServer: ===============[Spawn Manager]==================
[01.07.25 08:03:58:573]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1000 npc for group: NONE
[01.07.25 08:03:58:721]  INFO instancemanager.SpawnManager: SpawnManager: spawned 5000 npc for group: NONE
[01.07.25 08:03:58:751]  INFO naia.NaiaTowerManager: Naia Tower Manager: Loaded 12 rooms
[01.07.25 08:03:58:831]  INFO instancemanager.SpawnManager: SpawnManager: spawned 7000 npc for group: NONE
[01.07.25 08:03:59:232]  INFO instancemanager.SpawnManager: SpawnManager: spawned 12000 npc for group: NONE
[01.07.25 08:03:59:319]  INFO instancemanager.SpawnManager: SpawnManager: spawned 13000 npc for group: NONE
[01.07.25 08:04:00:306]  INFO instancemanager.SpawnManager: SpawnManager: spawned 21000 npc for group: NONE
[01.07.25 08:04:00:466]  INFO instancemanager.SpawnManager: SpawnManager: spawned 22000 npc for group: NONE
[01.07.25 08:04:01:029]  INFO instancemanager.SpawnManager: SpawnManager: spawned 25000 npc for group: NONE
[01.07.25 08:04:01:231]  INFO instancemanager.SpawnManager: SpawnManager: spawned 26000 npc for group: NONE
[01.07.25 08:04:01:655]  INFO instancemanager.SpawnManager: SpawnManager: spawned 28000 npc for group: NONE
[01.07.25 08:04:02:676]  INFO instancemanager.SpawnManager: SpawnManager: spawned 32000 npc for group: NONE
[01.07.25 08:04:03:621]  INFO instancemanager.SpawnManager: SpawnManager: spawned 35000 npc for group: NONE
[01.07.25 08:04:03:940]  INFO instancemanager.SpawnManager: SpawnManager: spawned 36000 npc for group: NONE
[01.07.25 08:04:04:252]  INFO instancemanager.SpawnManager: SpawnManager: spawned 37000 npc for group: NONE
[01.07.25 08:04:04:645]  INFO instancemanager.SpawnManager: SpawnManager: spawned 38373 npc; spawns: 17087; group: NONE
[01.07.25 08:04:04:645]  INFO gameserver.GameServer: --------------------------------------------------------------------=[ Boats ]
[01.07.25 08:04:04:646]  INFO data.BoatHolder: BoatHolder: loaded 2 boat(s) count.
[01.07.25 08:04:04:658]  INFO data.BoatHolder: BoatHolder: Spawning: RunePrimeval_Vehicle
[01.07.25 08:04:04:658]  INFO data.BoatHolder: BoatHolder: Spawning: AdenGracia_Airship
[01.07.25 08:04:04:659]  INFO holder.StaticObjectHolder: StaticObjectHolder: spawned: 50 static object(s).
[01.07.25 08:04:04:659]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Dimensional Rift ]
[01.07.25 08:04:04:664]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 13 room types with 112 rooms.
[01.07.25 08:04:04:664]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 300 DimensionalRift spawns, 0 errors.
[01.07.25 08:04:04:668]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Seven Signs ]
[01.07.25 08:04:04:677]  INFO instancemanager.AutoSpawnManager: AutoSpawnHandler: Loaded 50 handlers in total.
[01.07.25 08:04:04:678]  INFO gameserver.GameServer: ===================================================================
[01.07.25 08:04:04:678]  INFO gameserver.GameServer: ===================[Loading Olympiad System]=======================
[01.07.25 08:04:04:681]  INFO olympiad.Olympiad: Olympiad System: Loading Olympiad System....
[01.07.25 08:04:04:681]  INFO olympiad.Olympiad: Olympiad System: Currently in Olympiad Period
[01.07.25 08:04:04:681]  INFO olympiad.Olympiad: Olympiad System: Period Ends....
[01.07.25 08:04:04:682]  INFO olympiad.Olympiad: Olympiad System: In 3 days, 15 hours and 57 mins.
[01.07.25 08:04:04:682]  INFO olympiad.Olympiad: Olympiad System: Next Weekly Change is in....
[01.07.25 08:04:04:683]  INFO olympiad.Olympiad: Olympiad System: In 4 days, 4 hours and 32 mins.
[01.07.25 08:04:04:683]  INFO olympiad.Olympiad: Olympiad System: Loaded 0 Noblesses
[01.07.25 08:04:04:683]  INFO olympiad.Olympiad: Olympiad System: Competition Period Starts in 0 days, 9 hours and 56 mins.
[01.07.25 08:04:04:684]  INFO olympiad.Olympiad: Olympiad System: Event starts/started: Tue Jul 01 18:00:04 BRT 2025
[01.07.25 08:04:04:686]  INFO entity.Hero: Hero System: Loaded 0 Heroes.
[01.07.25 08:04:04:686]  INFO entity.Hero: Hero System: Loaded 0 all time Heroes.
[01.07.25 08:04:04:686]  INFO gameserver.GameServer: ===================[Olympiad System Loaded]=======================
[01.07.25 08:04:04:687]  INFO gameserver.GameServer: ===================================================================
[01.07.25 08:04:04:687]  INFO instancemanager.PetitionManager: Initializing PetitionManager
[01.07.25 08:04:04:692]  INFO instancemanager.CursedWeaponsManager: CursedWeaponsManager: Loaded 2 cursed weapon(s).
[01.07.25 08:04:04:692]  INFO gameserver.GameServer: ------------------------------------------=[ Loaded Small Achievement System ]
[01.07.25 08:04:04:702]  INFO gameserver.GameServer: --------------------------------------------------------------=[ ItemHandler ]
[01.07.25 08:04:04:703]  INFO items.ItemHandler: ItemHandler: loaded 0 item handler(s) count.
[01.07.25 08:04:04:703]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Admin Commands ]
[01.07.25 08:04:04:703]  INFO admincommands.AdminCommandHandler: AdminCommandHandler: loaded 475 admin command handler(s) count.
[01.07.25 08:04:04:703]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Players Commands ]
[01.07.25 08:04:04:707]  INFO usercommands.UserCommandHandler: UserCommandHandler: loaded 17 user command handler(s) count.
[01.07.25 08:04:04:707]  INFO voicecommands.VoicedCommandHandler: VoicedCommandHandler: loaded 102 voiced command handler(s) count.
[01.07.25 08:04:04:712]  INFO gameserver.GameServer: ======================[Tournament By Kara`]==========================
[01.07.25 08:04:04:714]  INFO tournament.TournamentHolder: [TournamentHolder] Initialized 5 tournament holders:
[01.07.25 08:04:04:714]  INFO tournament.TournamentHolder: 1 vs 1
[01.07.25 08:04:04:714]  INFO tournament.TournamentHolder: 2 vs 2
[01.07.25 08:04:04:714]  INFO tournament.TournamentHolder: 4 vs 4
[01.07.25 08:04:04:714]  INFO tournament.TournamentHolder: 5 vs 5
[01.07.25 08:04:04:714]  INFO tournament.TournamentHolder: 6 vs 6
[01.07.25 08:04:04:715]  INFO gameserver.GameServer: ======================[Loading Castels & Clan Halls]==========================
[01.07.25 08:04:04:745]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Gludio Castle time - 16:00 06.07.2025
[01.07.25 08:04:04:749]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Dion Castle time - 16:00 06.07.2025
[01.07.25 08:04:04:752]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Giran Castle time - 20:00 06.07.2025
[01.07.25 08:04:04:756]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Oren Castle time - 20:00 06.07.2025
[01.07.25 08:04:04:760]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Aden Castle time - 16:00 06.07.2025
[01.07.25 08:04:04:765]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Innadril Castle time - 20:00 06.07.2025
[01.07.25 08:04:04:769]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Goddard Castle time - 20:00 06.07.2025
[01.07.25 08:04:04:772]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Rune Castle time - 16:00 06.07.2025
[01.07.25 08:04:04:775]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Schuttgart Castle time - 16:00 06.07.2025
[01.07.25 08:04:04:777]  INFO impl.ClanHallNpcSiegeEvent: ClanHallNpcSiegeEvent: Fortress Of Resistance time - 18:00 07.07.2025
[01.07.25 08:04:04:779]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[01.07.25 08:04:04:780]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[01.07.25 08:04:04:781]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Topaz Hall time - 15:00 02.07.2025
[01.07.25 08:04:04:782]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Ruby Hall time - 15:00 02.07.2025
[01.07.25 08:04:04:784]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Crystal Hall time - 15:00 02.07.2025
[01.07.25 08:04:04:785]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[01.07.25 08:04:04:786]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[01.07.25 08:04:04:787]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[01.07.25 08:04:04:788]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[01.07.25 08:04:04:789]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Atramental Barracks time - 15:00 02.07.2025
[01.07.25 08:04:04:790]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Scarlet Barracks time - 15:00 02.07.2025
[01.07.25 08:04:04:791]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Viridian Barracks time - 15:00 02.07.2025
[01.07.25 08:04:04:791]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Devastated Castle time - 18:00 05.07.2025
[01.07.25 08:04:04:792]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Bandit Stronghold time - 22:00 05.07.2025
[01.07.25 08:04:04:793]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[01.07.25 08:04:04:794]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[01.07.25 08:04:04:796]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[01.07.25 08:04:04:797]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[01.07.25 08:04:04:798]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Gold Manor time - 15:00 02.07.2025
[01.07.25 08:04:04:799]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[01.07.25 08:04:04:801]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[01.07.25 08:04:04:802]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[01.07.25 08:04:04:803]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[01.07.25 08:04:04:804]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[01.07.25 08:04:04:805]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[01.07.25 08:04:04:806]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[01.07.25 08:04:04:807]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[01.07.25 08:04:04:808]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[01.07.25 08:04:04:809]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[01.07.25 08:04:04:810]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Mont Chamber time - 15:00 02.07.2025
[01.07.25 08:04:04:811]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Astaire Chamber time - 15:00 02.07.2025
[01.07.25 08:04:04:812]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Aria Chamber time - 15:00 02.07.2025
[01.07.25 08:04:04:813]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Yiana Chamber time - 15:00 02.07.2025
[01.07.25 08:04:04:814]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Roien Chamber time - 15:00 02.07.2025
[01.07.25 08:04:04:815]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Luna Chamber time - 15:00 02.07.2025
[01.07.25 08:04:04:816]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Traban Chamber time - 15:00 02.07.2025
[01.07.25 08:04:04:818]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Eisen Hall time - 15:00 02.07.2025
[01.07.25 08:04:04:819]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Heavy Metal Hall time - 15:00 02.07.2025
[01.07.25 08:04:04:820]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Molten Ore Hall time - 15:00 02.07.2025
[01.07.25 08:04:04:821]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Titan Hall time - 15:00 02.07.2025
[01.07.25 08:04:04:822]  INFO impl.ClanHallMiniGameEvent: ClanHallMiniGameEvent: Rainbow Springs time - 22:00 05.07.2025
[01.07.25 08:04:04:823]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Wild Beast Reserve time - 21:00 05.07.2025
[01.07.25 08:04:04:823]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Fortress of Dead time - 18:00 05.07.2025
[01.07.25 08:04:04:824]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Gludio Dominion time - undefined
[01.07.25 08:04:04:825]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Dion Dominion time - undefined
[01.07.25 08:04:04:825]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Giran Dominion time - undefined
[01.07.25 08:04:04:825]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Oren Dominion time - undefined
[01.07.25 08:04:04:825]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Aden Dominion time - undefined
[01.07.25 08:04:04:826]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Innadril Dominion time - undefined
[01.07.25 08:04:04:826]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Goddard Dominion time - undefined
[01.07.25 08:04:04:827]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Rune Dominion time - undefined
[01.07.25 08:04:04:827]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Schuttgart Dominion time - undefined
[01.07.25 08:04:04:830]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Shanty Fortress time - undefined
[01.07.25 08:04:04:833]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Southern Fortress time - undefined
[01.07.25 08:04:04:836]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hive Fortress time - undefined
[01.07.25 08:04:04:839]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Valley Fortress time - undefined
[01.07.25 08:04:04:841]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Ivory Fortress time - undefined
[01.07.25 08:04:04:844]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Narsell Fortress time - undefined
[01.07.25 08:04:04:846]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Bayou Fortress time - undefined
[01.07.25 08:04:04:849]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: White Sands Fortress time - undefined
[01.07.25 08:04:04:852]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Borderland Fortress time - undefined
[01.07.25 08:04:04:855]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Swamp Fortress time - undefined
[01.07.25 08:04:04:858]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Archaic Fortress time - undefined
[01.07.25 08:04:04:861]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Floran Fortress time - undefined
[01.07.25 08:04:04:863]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Cloud Mountain Fortress time - undefined
[01.07.25 08:04:04:866]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Tanor Fortress time - undefined
[01.07.25 08:04:04:869]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Dragonspine Fortress time - undefined
[01.07.25 08:04:04:873]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Antharas Fortress time - undefined
[01.07.25 08:04:04:876]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Western Fortress time - undefined
[01.07.25 08:04:04:879]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hunter's Fortress time - undefined
[01.07.25 08:04:04:882]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Aaru Fortress time - undefined
[01.07.25 08:04:04:885]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Demon Fortress time - undefined
[01.07.25 08:04:04:888]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Monastic Fortress time - undefined
[01.07.25 08:04:04:889]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 70-75 Level time - 08:30 01.07.2025
[01.07.25 08:04:04:889]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 76-79 Level time - 08:30 01.07.2025
[01.07.25 08:04:04:889]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 80-85 Level time - 08:30 01.07.2025
[01.07.25 08:04:04:890]  INFO fightclub.LastManStandingEvent: LastManStandingEvent: Last Hero time - undefined
[01.07.25 08:04:04:891]  INFO fightclub.TeamVSTeamEvent: TeamVSTeamEvent: Team VS Team time - undefined
[01.07.25 08:04:04:891]  INFO fightclub.LuckyCreaturesEvent: LuckyCreaturesEvent: Lucky Creatures time - undefined
[01.07.25 08:04:04:891]  INFO fightclub.CaptureTheFlagEvent: CaptureTheFlagEvent: Capture The Flag time - undefined
[01.07.25 08:04:04:891]  INFO fightclub.FFATreasureHuntEvent: FFATreasureHuntEvent: Treasure Hunt time - undefined
[01.07.25 08:04:04:891]  INFO fightclub.KoreanStyleEvent: KoreanStyleEvent: Korean Style time - undefined
[01.07.25 08:04:04:891]  INFO fightclub.ProtectTheKingEvent: ProtectTheKingEvent: Protect The King time - undefined
[01.07.25 08:04:04:892]  INFO fightclub.FightForThroneEvent: FightForThroneEvent: Fight For Throne time - undefined
[01.07.25 08:04:04:892]  INFO fightclub.DeathMatchEvent: DeathMatchEvent: Death Match time - undefined
[01.07.25 08:04:04:892]  INFO fightclub.RussianRouletteEvent: RussianRouletteEvent: Russian Roulette time - undefined
[01.07.25 08:04:04:893]  INFO instancemanager.CastleManorManager: Manor System: Initializing...
[01.07.25 08:04:04:894]  INFO gameserver.GameServer: ==============================================================================
[01.07.25 08:04:04:894]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Auto Cleaner ]
[01.07.25 08:04:04:896]  INFO gameserver.GameServer: IdFactory: Free ObjectID's remaining: 1879004841
[01.07.25 08:04:04:896]  INFO gameserver.GameServer: ==============================================================================
[01.07.25 08:04:04:897]  INFO instancemanager.CoupleManager: Initializing CoupleManager
[01.07.25 08:04:04:898]  INFO instancemanager.CoupleManager: Loaded: 0 couples(s)
[01.07.25 08:04:04:899]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Hellbound ]
[01.07.25 08:04:04:906]  INFO instancemanager.HellboundManager: HellboundManager: Loaded 335 spawn entries.
[01.07.25 08:04:05:034]  INFO instancemanager.HellboundManager: HellboundManager: Spawned 623 mobs and NPCs according to the current Hellbound stage
[01.07.25 08:04:05:035]  INFO instancemanager.HellboundManager: Hellbound Manager: Loaded
[01.07.25 08:04:05:035]  INFO naia.NaiaTowerManager: Naia Core Manager: Loaded
[01.07.25 08:04:05:036]  INFO gameserver.GameServer: ==============================================================================
[01.07.25 08:04:05:036]  INFO instancemanager.SoDManager: Seed of Destruction Manager: Loaded
[01.07.25 08:04:05:037]  INFO instancemanager.SoIManager: Seed of Infinity Manager: Loaded. Current stage is: 1
[01.07.25 08:04:05:038]  INFO instancemanager.SpawnManager: SpawnManager: spawned 4 npc; spawns: 4; group: soi_world_mouths
[01.07.25 08:04:05:039]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: soi_world_abyssgaze2
[01.07.25 08:04:05:039]  INFO instancemanager.BloodAltarManager: Blood Altar Manager: Initializing...
[01.07.25 08:04:05:054]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_alive_npc
[01.07.25 08:04:05:056]  INFO gameserver.GameServer: Preparing Drop Calculator
[01.07.25 08:04:05:115]  INFO gameserver.GameServer: ----------------------------------------------------------=[ Offline Buffers ]
[01.07.25 08:04:05:116]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loading offline buffers...
[01.07.25 08:04:05:119]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loaded: 0 offline buffer(s)
[01.07.25 08:04:05:140]  INFO achievements.Achievements: Achievement System: Loaded 4 achievement categories and 39 achievements.
[01.07.25 08:04:05:141]  INFO gameserver.GameServer: Emotions Loaded....
[01.07.25 08:04:05:142]  INFO gameserver.GameServer: --------------------------------------------------=[ DataBase Cleaner Loaded ]
[01.07.25 08:04:05:142]  INFO dao.CharacterDAO: Found 0 characters to delete!
[01.07.25 08:04:05:215]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Initializing
[01.07.25 08:04:05:216]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Load OK
[01.07.25 08:04:05:216]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Loaded 0 announce.
[01.07.25 08:04:05:218]  INFO gameserver.GameServer: ---------------------------------------------------=[ Loaded Facebook System ]
[01.07.25 08:04:05:220]  INFO gameserver.GameServer: -----------------------------------------------------=[ Loaded Stream System ]
[01.07.25 08:04:05:223]  INFO holder.ProxiesHolder: ProxiesHolder: loaded 20 proxies(s) count.
[01.07.25 08:04:05:223]  INFO gameserver.GameServer: ------------------------------------------------------=[ Loaded Proxy System ]
[01.07.25 08:04:07:764]  INFO donation.DonationReader: Donation Reader service started
[01.07.25 08:04:07:764]  INFO gameserver.GameServer: --------------------------------------------=[ Loaded AUTO - Donation System ]
[01.07.25 08:04:07:767]  INFO taskmanager.BackupTaskManager: Next Auto Backup in 22 H., 35 Min.
[01.07.25 08:04:07:776]  INFO votingengine.VotingSettings: VotingSettings: Loaded 3 messages, 4 drops!
[01.07.25 08:04:07:778]  INFO votingengine.VotingRewardAPI: VoteReward: Has been successfully loaded!
[01.07.25 08:04:07:781]  INFO model.PhantomPlayers: Loading phantom players...
[01.07.25 08:04:07:789]  INFO model.PhantomPlayers: Loaded 32002 possible phantom names.
[01.07.25 08:04:07:789]  INFO model.PhantomPlayers: Loaded 7172 possible phantom titles.
[01.07.25 08:04:07:790]  INFO model.PhantomPlayers: PhantomPlayers: Cached 0 players.
[01.07.25 08:04:07:790]  INFO model.PhantomPlayers: PhantomPlayers: Free names 32002.
[01.07.25 08:04:07:791]  INFO model.PhantomPlayers: Loaded 0 phantom players from database with a maximum of 3000 phantoms.
[01.07.25 08:04:07:791]  INFO model.PhantomPlayers: Scheduled spawner with 1 seconds delay.
[01.07.25 08:04:07:931]  INFO instancemanager.SpawnManager: SpawnManager: spawned 443 npc; spawns: 121; group: DAY
[01.07.25 08:04:07:993]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dawn_spawn
[01.07.25 08:04:08:223]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dusk_spawn
[01.07.25 08:04:08:223]  INFO gameserver.GameServer: Telnet server is currently disabled.
[01.07.25 08:04:08:225]  INFO gameserver.Shutdown: Scheduled server restart in 21h 40m 52s.
[01.07.25 08:04:08:226]  INFO gameserver.GameServer: ==============================================================================
[01.07.25 08:04:08:226]  INFO gameserver.GameServer: >>>>>>>>>>>>>>> GameServer Started <<<<<<<<<<<<<<
[01.07.25 08:04:08:226]  INFO gameserver.GameServer: Maximum Numbers of Connected Players: 3000
[01.07.25 08:04:08:226]  INFO gameserver.GameServer: ====================================================================
[01.07.25 08:04:08:248]  INFO gameserver.GameServer: AllowedMemory: ........... 6291456 KB
[01.07.25 08:04:08:248]  INFO gameserver.GameServer: 
Allocated: ............... 4194304 KB (66.6667%)
[01.07.25 08:04:08:248]  INFO gameserver.GameServer: 
Non-Allocated: ........... 2097152 KB (33.3333%)
[01.07.25 08:04:08:248]  INFO gameserver.GameServer: 
AllocatedMemory: ......... 4194304 KB
[01.07.25 08:04:08:249]  INFO gameserver.GameServer: Used: .................... 1148637 KB (18.2571%)
[01.07.25 08:04:08:249]  INFO gameserver.GameServer: 
Unused (cached): ......... 3045666 KB (48.4096%)
[01.07.25 08:04:08:249]  INFO gameserver.GameServer: 
UseableMemory: ........... 5142818 KB (81.7429%)
[01.07.25 08:04:08:249]  INFO gameserver.GameServer: 

[01.07.25 08:04:08:249]  INFO gameserver.GameServer: ====================================================================
[01.07.25 08:04:08:251]  INFO loginservercon.AuthServerCommunication: Connecting to authserver on 127.0.0.1:9014
[01.07.25 08:04:08:269]  INFO lspackets.AuthResponse: Registered on authserver as 1 [Bartz]
[01.07.25 08:05:45:218]  INFO tasks.RestoreOfflineTraders: Restored 0 offline traders
[01.07.25 08:50:58:191]  INFO threading.RunnableImpl: FourSepulchersManager: Entry time: Tue Jul 01 08:55:58 BRT 2025
[01.07.25 09:03:58:492]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[01.07.25 09:04:05:109]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_dead_npc
[01.07.25 09:04:05:114]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_aden
[01.07.25 09:04:05:115]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: bloodaltar_boss_darkelf
[01.07.25 09:04:05:117]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_dion
[01.07.25 09:04:05:119]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_dwarw
[01.07.25 09:04:05:122]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_giran
[01.07.25 09:04:05:123]  INFO instancemanager.SpawnManager: SpawnManager: spawned 3 npc; spawns: 3; group: bloodaltar_boss_gludin
[01.07.25 09:04:05:124]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_gludio
[01.07.25 09:04:05:125]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_goddart
[01.07.25 09:04:05:126]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_heine
[01.07.25 09:04:05:126]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: bloodaltar_boss_orc
[01.07.25 09:04:05:127]  INFO instancemanager.SpawnManager: SpawnManager: spawned 2 npc; spawns: 2; group: bloodaltar_boss_oren
[01.07.25 09:04:05:127]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: bloodaltar_boss_schutgart
[01.07.25 09:06:00:181]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, before:0 toRemove:0 after:0 to Change:0
[01.07.25 09:06:00:182]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, Before: 0 Final:0
[01.07.25 09:06:00:183]  INFO fightclubmanager.FightClubEventManager: Team VS Team: Removing room because it doesnt have enough players
[01.07.25 09:34:05:114]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_alive_npc
[01.07.25 09:50:58:234]  INFO threading.RunnableImpl: FourSepulchersManager: Entry time: Tue Jul 01 09:55:58 BRT 2025
[01.07.25 10:03:38:758] ERROR loginservercon.AuthServerCommunication: AuthServer I/O error
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:401)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:434)
	at l2mv.gameserver.network.loginservercon.AuthServerCommunication.read(AuthServerCommunication.java:258)
	at l2mv.gameserver.network.loginservercon.AuthServerCommunication.run(AuthServerCommunication.java:220)
[01.07.25 10:03:43:765]  INFO loginservercon.AuthServerCommunication: Connecting to authserver on 127.0.0.1:9014
[01.07.25 10:03:43:783]  INFO lspackets.AuthResponse: Registered on authserver as 1 [Bartz]
[01.07.25 10:03:58:492]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[01.07.25 10:06:00:148]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, before:0 toRemove:0 after:0 to Change:0
[01.07.25 10:06:00:148]  INFO fightclubmanager.FightClubEventManager: Equalizing FC Room, Before: 0 Final:0
[01.07.25 10:06:00:149]  INFO fightclubmanager.FightClubEventManager: Capture The Flag: Removing room because it doesnt have enough players
[01.07.25 11:13:27:192]  INFO gameserver.GameServer: ============================================================================
[01.07.25 11:13:27:193]  INFO gameserver.GameServer: Nome: .................................................................... GAME SERVER
[01.07.25 11:13:27:193]  INFO gameserver.GameServer: Project Revision: ........................................................ LII [MultVerso]
[01.07.25 11:13:27:193]  INFO gameserver.GameServer: Update: .................................................................. High Five: Part 5
[01.07.25 11:13:27:193]  INFO gameserver.GameServer: Numero da Revision:....................................................... 2.0
[01.07.25 11:13:27:194]  INFO gameserver.GameServer: Build Revision: .......................................................... MultVerso
[01.07.25 11:13:27:194]  INFO gameserver.GameServer: Build date: .............................................................. 2025.06.30 12:18
[01.07.25 11:13:27:194]  INFO gameserver.GameServer: Compiler version: ........................................................ 17.0.7+7 (Eclipse Adoptium)
[01.07.25 11:13:27:194]  INFO gameserver.GameServer: ============================================================================
[01.07.25 11:13:27:234]  INFO gameserver.Config: Loaded 6 trade words.
[01.07.25 11:13:27:244]  INFO gameserver.Config: Abuse: Loaded 15 abuse words.
[01.07.25 11:13:27:297]  INFO gameserver.Config: Loaded 313 Configs!
[01.07.25 11:13:27:303]  INFO gameserver.GameServer: IP EXTERNO =============================127.0.0.1
[01.07.25 11:13:27:303]  INFO gameserver.GameServer: IP INTERNO =============================127.0.0.1
[01.07.25 11:13:27:303]  INFO gameserver.GameServer: Maximo Online IP ====================== 3000
[01.07.25 11:13:27:305]  INFO gameserver.GameServer: Hora do Restart  =======================45 5 * * *
[01.07.25 11:13:27:447]  INFO gameserver.GameServer: -----------------------------------------=[ Loading Protection Configuration ]
[01.07.25 11:13:27:452]  INFO idfactory.IdFactory: IdFactory: Clear characters online status.
[01.07.25 11:13:27:497]  INFO idfactory.IdFactory: IdFactory: Cleaned 0 elements from database in 0sec.
[01.07.25 11:13:27:505]  INFO idfactory.IdFactory: IdFactory: Extracted 2 used id's from characters
[01.07.25 11:13:27:505]  INFO idfactory.IdFactory: IdFactory: Extracted 46 used id's from items
[01.07.25 11:13:27:506]  INFO idfactory.IdFactory: IdFactory: Extracted 1 used id's from clan_data
[01.07.25 11:13:27:506]  INFO idfactory.IdFactory: IdFactory: Extracted total 49 used id's.
[01.07.25 11:13:27:506]  INFO idfactory.BitSetIDFactory: IdFactory: 102912 id's available.
[01.07.25 11:13:27:607]  INFO gameserver.GameServer: ===============[Loading Scripts]==================
[01.07.25 11:13:27:608]  INFO scripts.Scripts: Scripts: Loading...
[01.07.25 11:13:27:608]  INFO scripts.Scripts: Loading Server Scripts
[01.07.25 11:13:27:883]  INFO scripts.Scripts: Scripts: Loaded 1288 classes.
[01.07.25 11:13:27:891]  INFO vote.VoteMain: Loaded: Vote System - Individual Reward.
[01.07.25 11:13:27:891]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Twitch Manager ]
[01.07.25 11:13:27:920]  INFO gameserver.GameServer: ----------------------------------------------------------=[ AntiFeedManager ]
[01.07.25 11:13:27:920]  INFO gameserver.GameServer: -------------------------------------------------------------=[ QuestManager ]
[01.07.25 11:13:27:921]  INFO gameserver.GameServer: QuestManager : loaded 0 quest's
[01.07.25 11:13:27:930]  INFO tables.FakePlayersTable: FakePlayersTable: Loaded 32002 Fake Players.
[01.07.25 11:13:27:938]  INFO utils.Strings: Loaded 418 translit entries.
[01.07.25 11:13:27:940]  INFO gameserver.GameTimeController: GameTimeController: initialized. Current time is 1:20 in the night.
[01.07.25 11:13:27:940]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Lineage World ]
[01.07.25 11:13:27:942]  INFO model.World: L2World: Creating regions: [256][272][32].
[01.07.25 11:13:27:942]  INFO gameserver.GameServer: ==============================================================================
[01.07.25 11:13:27:944]  INFO htm.HtmCache: HtmCache: disabled.
[01.07.25 11:13:27:951]  INFO data.StringHolder: StringHolder: load strings: 1066 for lang: ENGLISH
[01.07.25 11:13:27:951]  INFO data.StringHolder: StringHolder: load strings: 876 for lang: RUSSIAN
[01.07.25 11:13:29:173]  INFO skills.SkillsEngine: SkillsEngine: Loaded 66051 skill templates from XML files. Max id: 91000, max level: 259
[01.07.25 11:13:29:728]  INFO holder.OptionDataHolder: OptionDataHolder: loaded 24975 option data(s) count.
[01.07.25 11:13:30:293]  INFO holder.ItemHolder: ItemHolder: loaded 19240 item(s) count.
[01.07.25 11:13:30:299]  INFO holder.FakePlayerNpcsHolder: FakePlayerNpcsHolder: loaded 8 fake player npcs(s) count.
[01.07.25 11:13:31:233]  INFO holder.NpcHolder: NpcHolder: loaded 10519 npc(s) count.
[01.07.25 11:13:31:240]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[01.07.25 11:13:31:252]  INFO instancemanager.MapRegionManager: MapRegionManager: loaded 272 map region manager(s) count.
[01.07.25 11:13:31:280]  INFO holder.ExchangeItemHolder: ExchangeItemHolder: loaded 602 exchange item(s) count.
[01.07.25 11:13:31:284]  INFO holder.StaticObjectHolder: StaticObjectHolder: loaded 50 static object(s) count.
[01.07.25 11:13:31:377]  INFO holder.DoorHolder: DoorHolder: loaded 1155 door(s) count.
[01.07.25 11:13:31:421]  INFO holder.ZoneHolder: ZoneHolder: loaded 1260 zone(s) count.
[01.07.25 11:13:31:718]  INFO holder.SpawnHolder: SpawnHolder: loaded 28949 spawn(s) count.
[01.07.25 11:13:31:797]  INFO holder.InstantZoneHolder: InstantZoneHolder: loaded 94 instant zone(s) count.
[01.07.25 11:13:32:094]  INFO holder.AirshipDockHolder: AirshipDockHolder: loaded 4 airship dock(s) count.
[01.07.25 11:13:32:138]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 57215 normal learns for 103 classes.
[01.07.25 11:13:32:138]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 79 transfer learns for 3 classes.
[01.07.25 11:13:32:139]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 120 transformation learns for 6 races.
[01.07.25 11:13:32:139]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 662 fishing learns for 6 races.
[01.07.25 11:13:32:139]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 72 certification learns.
[01.07.25 11:13:32:139]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 3 collection learns.
[01.07.25 11:13:32:140]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 44 pledge learns.
[01.07.25 11:13:32:140]  INFO holder.SkillAcquireHolder: SkillAcquireHolder: load 18 sub unit learns.
[01.07.25 11:13:32:148]  INFO holder.CharTemplateHolder: CharTemplateHolder: loaded 206 char template(s) count.
[01.07.25 11:13:32:237]  INFO holder.ResidenceHolder: ResidenceHolder: total size: 83
[01.07.25 11:13:32:237]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 44 clanhall(s).
[01.07.25 11:13:32:238]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 castle(s).
[01.07.25 11:13:32:238]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 21 fortress(s).
[01.07.25 11:13:32:238]  INFO holder.ResidenceHolder: ResidenceHolder:  - load 9 dominion(s).
[01.07.25 11:13:32:306]  INFO instancemanager.RaidBossSpawnManager: RaidBossSpawnManager: Loaded 194 Statuses
[01.07.25 11:13:32:321]  INFO entity.SevenSigns: SevenSigns: Currently in the Competition (Quest Event) period!
[01.07.25 11:13:32:321]  INFO entity.SevenSigns: SevenSigns: The Seal of Avarice remains unclaimed.
[01.07.25 11:13:32:321]  INFO entity.SevenSigns: SevenSigns: The Seal of Gnosis remains unclaimed.
[01.07.25 11:13:32:321]  INFO entity.SevenSigns: SevenSigns: The Seal of Strife remains unclaimed.
[01.07.25 11:13:32:321]  INFO entity.SevenSigns: SevenSigns: The Competition this week, if the trend continue, will end with a tie.
[01.07.25 11:13:32:322]  INFO entity.SevenSigns: SevenSigns: Next period begins in 6 days, 6 hours and 46 mins.
[01.07.25 11:13:32:428]  INFO holder.EventHolder: EventHolder: loaded 105 event(s) count.
[01.07.25 11:13:32:441]  INFO holder.FightClubMapHolder: FightClubMapHolder: loaded 42 fight club map(s) count.
[01.07.25 11:13:32:446]  INFO holder.CubicHolder: CubicHolder: loaded 145 cubic(s) count.
[01.07.25 11:13:32:481]  INFO holder.BuyListHolder: TradeController: Loaded 4 file(s).
[01.07.25 11:13:32:482]  INFO holder.BuyListHolder: TradeController: Loaded 25014 Items.
[01.07.25 11:13:32:482]  INFO holder.BuyListHolder: TradeController: Loaded 726 Buylists.
[01.07.25 11:13:32:496]  INFO holder.RecipeHolder: RecipeController: Loaded 998 Recipes.
[01.07.25 11:13:32:824]  INFO holder.ProductHolder: ProductItemTable: Loaded 0 product item on sale.
[01.07.25 11:13:32:833]  INFO holder.HennaHolder: HennaHolder: loaded 180 henna(s) count.
[01.07.25 11:13:32:838]  INFO holder.EnchantItemHolder: EnchantItemHolder: load 4 enchant scroll(s).
[01.07.25 11:13:32:843]  INFO holder.SoulCrystalHolder: SoulCrystalHolder: loaded 54 soul crystal(s) count.
[01.07.25 11:13:32:847]  INFO holder.ArmorSetsHolder: ArmorSetsHolder: loaded 199 armor sets(s) count.
[01.07.25 11:13:32:854]  INFO holder.PetitionGroupHolder: PetitionGroupHolder: loaded 3 petition group(s) count.
[01.07.25 11:13:32:858]  INFO holder.DressArmorHolder: DressArmorHolder: loaded 57 dress armor(s) count.
[01.07.25 11:13:32:861]  INFO holder.DressCloakHolder: DressCloakHolder: loaded 7 dress cloak(s) count.
[01.07.25 11:13:32:863]  INFO holder.DressShieldHolder: DressShieldHolder: loaded 10 dress shield(s) count.
[01.07.25 11:13:32:868]  INFO holder.DressWeaponHolder: DressWeaponHolder: loaded 401 dress weapon(s) count.
[01.07.25 11:13:32:916]  INFO holder.AugmentationDataHolder: AugmentationDataHolder: loaded 1022 augmentation data(s) count.
[01.07.25 11:13:32:920]  INFO holder.PremiumHolder: PremiumHolder: loaded 3 premium(s) count.
[01.07.25 11:13:32:922]  INFO holder.FoundationHolder: FoundationHolder: loaded 268 foundation(s) count.
[01.07.25 11:13:32:927]  INFO holder.DonationHolder: DonationHolder: loaded 192 donation(s) count.
[01.07.25 11:13:32:930]  INFO holder.TournamentMapHolder: TournamentMapHolder: loaded 3 tournament map(s) count.
[01.07.25 11:13:32:934]  INFO holder.FacebookCommentsHolder: FacebookCommentsHolder: loaded 2 facebook comments(s) count.
[01.07.25 11:13:32:934]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Banned HWIDS ]
[01.07.25 11:13:32:935]  INFO security.HWIDBan: black list (Hwid) loaded size: 0
[01.07.25 11:13:32:935]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Clan Crests ]
[01.07.25 11:13:32:937]  INFO cache.CrestCache: CrestCache: Loaded 0 crests
[01.07.25 11:13:32:937]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Loading Images ]
[01.07.25 11:13:32:938]  INFO cache.ImagesCache: ImagesCache: Loading images...
[01.07.25 11:13:44:172]  INFO cache.ImagesCache: ImagesCache: Loaded 8734 images
[01.07.25 11:13:44:172]  INFO gameserver.GameServer: ==============================================================================
[01.07.25 11:13:44:189]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Fish Table ]
[01.07.25 11:13:44:192]  INFO tables.FishTable: FishTable: Loaded 270 fishes.
[01.07.25 11:13:44:194]  INFO tables.FishTable: FishTable: Loaded 919 fish rewards.
[01.07.25 11:13:44:194]  INFO gameserver.GameServer: -------------------------------------------------------------------=[ Skills ]
[01.07.25 11:13:44:195]  INFO tables.SkillTreeTable: SkillTreeTable: Loaded 526 enchanted skills.
[01.07.25 11:13:44:222]  INFO datatables.EnchantNamesTable: EnchantNamesTable: Loaded: 1319 skill enchant names
[01.07.25 11:13:44:222]  INFO gameserver.GameServer: --------------------------------------------------------=[ Augmentation Data ]
[01.07.25 11:13:44:224]  INFO tables.AugmentationData: Initializing AugmentationData.
[01.07.25 11:13:44:235]  INFO tables.AugmentationData: AugmentationData: 10 bad skill(s) were skipped.
[01.07.25 11:13:44:247]  INFO tables.AugmentationData: AugmentationData: Loaded: 52 augmentation stats.
[01.07.25 11:13:44:247]  INFO tables.AugmentationData: AugmentationData: Loaded: 24 accessory augmentation stats.
[01.07.25 11:13:44:249]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 0
[01.07.25 11:13:44:250]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 1
[01.07.25 11:13:44:250]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 2
[01.07.25 11:13:44:250]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 3
[01.07.25 11:13:44:250]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 4
[01.07.25 11:13:44:250]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 5
[01.07.25 11:13:44:250]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 6
[01.07.25 11:13:44:250]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 7
[01.07.25 11:13:44:251]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 8
[01.07.25 11:13:44:251]  INFO tables.AugmentationData: AugmentationData: Loaded: 17 blue, 106 purple and 54 red skills for lifeStoneLevel 9
[01.07.25 11:13:44:253]  INFO tables.EnchantHPBonusTable: EnchantHPBonusTable: Loaded bonuses for 6 grades.
[01.07.25 11:13:44:253]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Level Up Table ]
[01.07.25 11:13:44:255]  INFO tables.LevelUpTable: LevelUpData: Loaded 103 Character Level Up Templates.
[01.07.25 11:13:44:257]  INFO tables.PetSkillsTable: PetSkillsTable: Loaded 2558 skills.
[01.07.25 11:13:44:257]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Item Logs ]
[01.07.25 11:13:44:259]  INFO gameserver.GameServer: ---------------------------------------------------------------=[ Auctioneer ]
[01.07.25 11:13:44:260]  INFO itemauction.ItemAuctionManager: Initializing ItemAuctionManager
[01.07.25 11:13:44:263]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 1 auction(s) for instance 32320.
[01.07.25 11:13:44:266]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 1 on 2025.07.01 17:00:00 for instance 32320
[01.07.25 11:13:44:267]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 2 auction(s) for instance 32321.
[01.07.25 11:13:44:267]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 4 on 2025.07.03 17:00:00 for instance 32321
[01.07.25 11:13:44:268]  INFO itemauction.ItemAuctionInstance: ItemAuction: Loaded 37 item(s) and registered 2 auction(s) for instance 32322.
[01.07.25 11:13:44:268]  INFO itemauction.ItemAuctionInstance: ItemAuction: Schedule next auction 5 on 2025.07.05 17:00:00 for instance 32322
[01.07.25 11:13:44:269]  INFO itemauction.ItemAuctionManager: ItemAuctionManager: Loaded 3 instance(s).
[01.07.25 11:13:44:269]  INFO gameserver.GameServer: ------------------------------------------------------------=[ Masterio Pack ]
[01.07.25 11:13:44:303]  INFO gameserver.GameServer: ------------------------------------------------------=[ Merge System Loaded ]
[01.07.25 11:13:44:321]  INFO bosses.AntharasManager: AntharasManager: State of Antharas is NOTSPAWN.
[01.07.25 11:13:44:322]  INFO bosses.AntharasManager: AntharasManager: Next spawn date of Antharas is 21:00 31.12.1969.
[01.07.25 11:13:44:323]  INFO bosses.BaiumManager: BaiumManager: State of Baium is NOTSPAWN.
[01.07.25 11:13:44:326]  INFO bosses.BaiumManager: BaiumManager: Next spawn date: 21:00 31.12.1969
[01.07.25 11:13:44:337]  INFO bosses.BelethManager: Beleth Manager: Loaded successfully
[01.07.25 11:13:44:338]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Loaded 20 Mysterious-Box spawns.
[01.07.25 11:13:44:342]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 physical monsters spawns.
[01.07.25 11:13:44:345]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 716 magical monsters spawns.
[01.07.25 11:13:44:346]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 92 Church of duke monsters spawns.
[01.07.25 11:13:44:347]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: loaded 68 Emperor's grave NPC spawns.
[01.07.25 11:13:44:348]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Conquerors' Sepulcher Manager
[01.07.25 11:13:44:349]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Emperors' Sepulcher Manager
[01.07.25 11:13:44:349]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Great Sages' Sepulcher Manager
[01.07.25 11:13:44:350]  INFO bosses.FourSepulchersSpawn: FourSepulchersManager: Spawned Judges' Sepulcher Manager
[01.07.25 11:13:44:350]  INFO bosses.FourSepulchersManager: FourSepulchersManager: Beginning in Attack time
[01.07.25 11:13:44:352]  INFO bosses.SailrenManager: SailrenManager: State of Sailren is NOTSPAWN.
[01.07.25 11:13:44:352]  INFO bosses.SailrenManager: SailrenManager: Next spawn date of Sailren is 21:00 31.12.1969.
[01.07.25 11:13:44:353]  INFO bosses.ValakasManager: ValakasManager: State of Valakas is NOTSPAWN.
[01.07.25 11:13:44:353]  INFO threading.RunnableImpl: Tue Jul 01 11:13:44 BRT 2025 Atk announce scheduled to 15.0 minute of this hour.
[01.07.25 11:13:44:353]  INFO bosses.ValakasManager: ValakasManager: Next spawn date of Valakas is 21:00 31.12.1969.
[01.07.25 11:13:44:445]  INFO tables.PetDataTable: PetDataTable: Loaded 3870 pets.
[01.07.25 11:13:44:449]  INFO model.Manor: ManorManager: Loaded 270 seeds
[01.07.25 11:13:44:535]  INFO scriptconfig.ScriptConfig: Loaded Service: ScripsConfig
[01.07.25 11:13:44:536]  INFO community.CharacterServicesCommunity: === CharacterServicesCommunity onLoad called ===
[01.07.25 11:13:44:537]  INFO community.CharacterServicesCommunity: COMMUNITYBOARD_ENABLED: true
[01.07.25 11:13:44:537]  INFO community.CharacterServicesCommunity: EnableMergeCommunity: false
[01.07.25 11:13:44:537]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Character Services Community loaded.
[01.07.25 11:13:44:597]  INFO dailyquests.AbstractDPScript: DailyQuestHandler: Loaded DailyQuests data
[01.07.25 11:13:44:597]  INFO community.CharacterServicesCommunity: CharacterServicesCommunity: Handler registered successfully.
[01.07.25 11:13:44:605]  INFO model.Player: Loaded Service: SellPcService
[01.07.25 11:13:44:607]  INFO community.CareerManager: CommunityBoard: Manage Career service loaded.
[01.07.25 11:13:44:609]  INFO community.CommunityAuctionHouse: CommunityBoard: Auction System Service loaded.
[01.07.25 11:13:44:613]  INFO community.CommunityAugment: CommunityBoard: Augmentation Manager loaded with 188 lifestone skills.
[01.07.25 11:13:44:613]  INFO community.CommunityBoard: CommunityBoard: service loaded.
[01.07.25 11:13:44:614]  INFO community.CommunityBoardAcademy: CommunityBoard: Academy service loaded.
[01.07.25 11:13:44:615]  INFO community.CommunityBosses: CommunityBoard: Bosses loaded.
[01.07.25 11:13:44:616]  INFO community.CommunityClan: CommunityBoard: Clan service loaded.
[01.07.25 11:13:44:617]  INFO community.CommunityDropCalculator: CommunityBoard: Drop Calculator service loaded.
[01.07.25 11:13:44:617]  INFO community.CommunityEvents: CommunityBoard: Community Events service loaded.
[01.07.25 11:13:44:618]  INFO community.CommunityFacebook: CommunityBoard: Facebook Rewards page loaded.
[01.07.25 11:13:44:618]  INFO community.CommunityNpcs: CommunityBoard: Npcs loaded.
[01.07.25 11:13:44:619]  INFO community.CommunityRanking: Ranking in the community board has been updated.
[01.07.25 11:13:44:624]  INFO community.CommunityTournament: CommunityBoard: Tournament loaded.
[01.07.25 11:13:44:625]  INFO community.CommunityWarehouse: CommunityBoard: Warehouse loaded.
[01.07.25 11:13:44:625]  INFO community.Forge: CommunityBoard: Forge loaded.
[01.07.25 11:13:44:627]  INFO community.ServicesCommunity: ServicesCommunity: Services Community service loaded.
[01.07.25 11:13:44:632]  INFO gameserver.GameServer: ===============[Spawn Manager]==================
[01.07.25 11:13:44:700]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1000 npc for group: NONE
[01.07.25 11:13:44:829]  INFO instancemanager.SpawnManager: SpawnManager: spawned 5000 npc for group: NONE
[01.07.25 11:13:44:854]  INFO naia.NaiaTowerManager: Naia Tower Manager: Loaded 12 rooms
[01.07.25 11:13:44:928]  INFO instancemanager.SpawnManager: SpawnManager: spawned 7000 npc for group: NONE
[01.07.25 11:13:45:248]  INFO instancemanager.SpawnManager: SpawnManager: spawned 12000 npc for group: NONE
[01.07.25 11:13:45:327]  INFO instancemanager.SpawnManager: SpawnManager: spawned 13000 npc for group: NONE
[01.07.25 11:13:46:161]  INFO instancemanager.SpawnManager: SpawnManager: spawned 21000 npc for group: NONE
[01.07.25 11:13:46:285]  INFO instancemanager.SpawnManager: SpawnManager: spawned 22000 npc for group: NONE
[01.07.25 11:13:46:732]  INFO instancemanager.SpawnManager: SpawnManager: spawned 25000 npc for group: NONE
[01.07.25 11:13:46:902]  INFO instancemanager.SpawnManager: SpawnManager: spawned 26000 npc for group: NONE
[01.07.25 11:13:47:243]  INFO instancemanager.SpawnManager: SpawnManager: spawned 28000 npc for group: NONE
[01.07.25 11:13:48:036]  INFO instancemanager.SpawnManager: SpawnManager: spawned 32000 npc for group: NONE
[01.07.25 11:13:48:789]  INFO instancemanager.SpawnManager: SpawnManager: spawned 35000 npc for group: NONE
[01.07.25 11:13:49:072]  INFO instancemanager.SpawnManager: SpawnManager: spawned 36000 npc for group: NONE
[01.07.25 11:13:49:341]  INFO instancemanager.SpawnManager: SpawnManager: spawned 37000 npc for group: NONE
[01.07.25 11:13:49:720]  INFO instancemanager.SpawnManager: SpawnManager: spawned 38373 npc; spawns: 17087; group: NONE
[01.07.25 11:13:49:720]  INFO gameserver.GameServer: --------------------------------------------------------------------=[ Boats ]
[01.07.25 11:13:49:721]  INFO data.BoatHolder: BoatHolder: loaded 2 boat(s) count.
[01.07.25 11:13:49:723]  INFO data.BoatHolder: BoatHolder: Spawning: RunePrimeval_Vehicle
[01.07.25 11:13:49:723]  INFO data.BoatHolder: BoatHolder: Spawning: AdenGracia_Airship
[01.07.25 11:13:49:724]  INFO holder.StaticObjectHolder: StaticObjectHolder: spawned: 50 static object(s).
[01.07.25 11:13:49:724]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Dimensional Rift ]
[01.07.25 11:13:49:728]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 13 room types with 112 rooms.
[01.07.25 11:13:49:728]  INFO instancemanager.DimensionalRiftManager: DimensionalRiftManager: Loaded 300 DimensionalRift spawns, 0 errors.
[01.07.25 11:13:49:732]  INFO gameserver.GameServer: --------------------------------------------------------------=[ Seven Signs ]
[01.07.25 11:13:49:743]  INFO instancemanager.AutoSpawnManager: AutoSpawnHandler: Loaded 50 handlers in total.
[01.07.25 11:13:49:743]  INFO gameserver.GameServer: ===================================================================
[01.07.25 11:13:49:743]  INFO gameserver.GameServer: ===================[Loading Olympiad System]=======================
[01.07.25 11:13:49:748]  INFO olympiad.Olympiad: Olympiad System: Loading Olympiad System....
[01.07.25 11:13:49:748]  INFO olympiad.Olympiad: Olympiad System: Currently in Olympiad Period
[01.07.25 11:13:49:748]  INFO olympiad.Olympiad: Olympiad System: Period Ends....
[01.07.25 11:13:49:749]  INFO olympiad.Olympiad: Olympiad System: In 3 days, 12 hours and 47 mins.
[01.07.25 11:13:49:749]  INFO olympiad.Olympiad: Olympiad System: Next Weekly Change is in....
[01.07.25 11:13:49:750]  INFO olympiad.Olympiad: Olympiad System: In 4 days, 1 hours and 22 mins.
[01.07.25 11:13:49:750]  INFO olympiad.Olympiad: Olympiad System: Loaded 0 Noblesses
[01.07.25 11:13:49:750]  INFO olympiad.Olympiad: Olympiad System: Competition Period Starts in 0 days, 6 hours and 47 mins.
[01.07.25 11:13:49:751]  INFO olympiad.Olympiad: Olympiad System: Event starts/started: Tue Jul 01 18:00:49 BRT 2025
[01.07.25 11:13:49:752]  INFO entity.Hero: Hero System: Loaded 0 Heroes.
[01.07.25 11:13:49:753]  INFO entity.Hero: Hero System: Loaded 0 all time Heroes.
[01.07.25 11:13:49:753]  INFO gameserver.GameServer: ===================[Olympiad System Loaded]=======================
[01.07.25 11:13:49:753]  INFO gameserver.GameServer: ===================================================================
[01.07.25 11:13:49:754]  INFO instancemanager.PetitionManager: Initializing PetitionManager
[01.07.25 11:13:49:758]  INFO instancemanager.CursedWeaponsManager: CursedWeaponsManager: Loaded 2 cursed weapon(s).
[01.07.25 11:13:49:759]  INFO gameserver.GameServer: ------------------------------------------=[ Loaded Small Achievement System ]
[01.07.25 11:13:49:770]  INFO gameserver.GameServer: --------------------------------------------------------------=[ ItemHandler ]
[01.07.25 11:13:49:770]  INFO items.ItemHandler: ItemHandler: loaded 0 item handler(s) count.
[01.07.25 11:13:49:770]  INFO gameserver.GameServer: -----------------------------------------------------------=[ Admin Commands ]
[01.07.25 11:13:49:770]  INFO admincommands.AdminCommandHandler: AdminCommandHandler: loaded 475 admin command handler(s) count.
[01.07.25 11:13:49:770]  INFO gameserver.GameServer: ---------------------------------------------------------=[ Players Commands ]
[01.07.25 11:13:49:774]  INFO usercommands.UserCommandHandler: UserCommandHandler: loaded 17 user command handler(s) count.
[01.07.25 11:13:49:774]  INFO voicecommands.VoicedCommandHandler: VoicedCommandHandler: loaded 102 voiced command handler(s) count.
[01.07.25 11:13:49:777]  INFO gameserver.GameServer: ======================[Tournament By Kara`]==========================
[01.07.25 11:13:49:780]  INFO tournament.TournamentHolder: [TournamentHolder] Initialized 5 tournament holders:
[01.07.25 11:13:49:780]  INFO tournament.TournamentHolder: 1 vs 1
[01.07.25 11:13:49:780]  INFO tournament.TournamentHolder: 2 vs 2
[01.07.25 11:13:49:780]  INFO tournament.TournamentHolder: 4 vs 4
[01.07.25 11:13:49:780]  INFO tournament.TournamentHolder: 5 vs 5
[01.07.25 11:13:49:781]  INFO tournament.TournamentHolder: 6 vs 6
[01.07.25 11:13:49:782]  INFO gameserver.GameServer: ======================[Loading Castels & Clan Halls]==========================
[01.07.25 11:13:49:814]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Gludio Castle time - 16:00 06.07.2025
[01.07.25 11:13:49:818]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Dion Castle time - 16:00 06.07.2025
[01.07.25 11:13:49:820]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Giran Castle time - 20:00 06.07.2025
[01.07.25 11:13:49:823]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Oren Castle time - 20:00 06.07.2025
[01.07.25 11:13:49:827]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Aden Castle time - 16:00 06.07.2025
[01.07.25 11:13:49:831]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Innadril Castle time - 20:00 06.07.2025
[01.07.25 11:13:49:835]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Goddard Castle time - 20:00 06.07.2025
[01.07.25 11:13:49:838]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Rune Castle time - 16:00 06.07.2025
[01.07.25 11:13:49:842]  INFO impl.CastleSiegeEvent: CastleSiegeEvent: Schuttgart Castle time - 16:00 06.07.2025
[01.07.25 11:13:49:843]  INFO impl.ClanHallNpcSiegeEvent: ClanHallNpcSiegeEvent: Fortress Of Resistance time - 18:00 07.07.2025
[01.07.25 11:13:49:846]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[01.07.25 11:13:49:848]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[01.07.25 11:13:49:849]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Topaz Hall time - 15:00 02.07.2025
[01.07.25 11:13:49:850]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Ruby Hall time - 15:00 02.07.2025
[01.07.25 11:13:49:851]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Crystal Hall time - 15:00 02.07.2025
[01.07.25 11:13:49:852]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[01.07.25 11:13:49:853]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[01.07.25 11:13:49:855]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[01.07.25 11:13:49:857]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[01.07.25 11:13:49:858]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Atramental Barracks time - 15:00 02.07.2025
[01.07.25 11:13:49:860]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Scarlet Barracks time - 15:00 02.07.2025
[01.07.25 11:13:49:861]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Viridian Barracks time - 15:00 02.07.2025
[01.07.25 11:13:49:861]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Devastated Castle time - 18:00 05.07.2025
[01.07.25 11:13:49:861]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Bandit Stronghold time - 22:00 05.07.2025
[01.07.25 11:13:49:862]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[01.07.25 11:13:49:863]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[01.07.25 11:13:49:865]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[01.07.25 11:13:49:866]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[01.07.25 11:13:49:867]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Gold Manor time - 15:00 02.07.2025
[01.07.25 11:13:49:868]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[01.07.25 11:13:49:869]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Golden Chamber time - 15:00 02.07.2025
[01.07.25 11:13:49:871]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Silver Chamber time - 15:00 02.07.2025
[01.07.25 11:13:49:872]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Mithril Chamber time - 15:00 02.07.2025
[01.07.25 11:13:49:873]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: The Bronze Chamber time - 15:00 02.07.2025
[01.07.25 11:13:49:874]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Silver Manor time - 15:00 02.07.2025
[01.07.25 11:13:49:875]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Moonstone Hall time - 15:00 02.07.2025
[01.07.25 11:13:49:876]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Onyx Hall time - 15:00 02.07.2025
[01.07.25 11:13:49:877]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Emerald Hall time - 15:00 02.07.2025
[01.07.25 11:13:49:878]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Sapphire Hall time - 15:00 02.07.2025
[01.07.25 11:13:49:879]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Mont Chamber time - 15:00 02.07.2025
[01.07.25 11:13:49:880]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Astaire Chamber time - 15:00 02.07.2025
[01.07.25 11:13:49:881]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Aria Chamber time - 15:00 02.07.2025
[01.07.25 11:13:49:882]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Yiana Chamber time - 15:00 02.07.2025
[01.07.25 11:13:49:883]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Roien Chamber time - 15:00 02.07.2025
[01.07.25 11:13:49:884]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Luna Chamber time - 15:00 02.07.2025
[01.07.25 11:13:49:885]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Traban Chamber time - 15:00 02.07.2025
[01.07.25 11:13:49:886]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Eisen Hall time - 15:00 02.07.2025
[01.07.25 11:13:49:888]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Heavy Metal Hall time - 15:00 02.07.2025
[01.07.25 11:13:49:889]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Molten Ore Hall time - 15:00 02.07.2025
[01.07.25 11:13:49:891]  INFO impl.ClanHallAuctionEvent: ClanHallAuctionEvent: Titan Hall time - 15:00 02.07.2025
[01.07.25 11:13:49:892]  INFO impl.ClanHallMiniGameEvent: ClanHallMiniGameEvent: Rainbow Springs time - 22:00 05.07.2025
[01.07.25 11:13:49:892]  INFO impl.ClanHallTeamBattleEvent: ClanHallTeamBattleEvent: Wild Beast Reserve time - 21:00 05.07.2025
[01.07.25 11:13:49:892]  INFO impl.ClanHallSiegeEvent: ClanHallSiegeEvent: Fortress of Dead time - 18:00 05.07.2025
[01.07.25 11:13:49:893]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Gludio Dominion time - undefined
[01.07.25 11:13:49:894]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Dion Dominion time - undefined
[01.07.25 11:13:49:894]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Giran Dominion time - undefined
[01.07.25 11:13:49:894]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Oren Dominion time - undefined
[01.07.25 11:13:49:895]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Aden Dominion time - undefined
[01.07.25 11:13:49:895]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Innadril Dominion time - undefined
[01.07.25 11:13:49:895]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Goddard Dominion time - undefined
[01.07.25 11:13:49:896]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Rune Dominion time - undefined
[01.07.25 11:13:49:896]  INFO impl.DominionSiegeEvent: DominionSiegeEvent: Schuttgart Dominion time - undefined
[01.07.25 11:13:49:899]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Shanty Fortress time - undefined
[01.07.25 11:13:49:902]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Southern Fortress time - undefined
[01.07.25 11:13:49:906]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hive Fortress time - undefined
[01.07.25 11:13:49:909]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Valley Fortress time - undefined
[01.07.25 11:13:49:911]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Ivory Fortress time - undefined
[01.07.25 11:13:49:914]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Narsell Fortress time - undefined
[01.07.25 11:13:49:916]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Bayou Fortress time - undefined
[01.07.25 11:13:49:920]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: White Sands Fortress time - undefined
[01.07.25 11:13:49:922]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Borderland Fortress time - undefined
[01.07.25 11:13:49:925]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Swamp Fortress time - undefined
[01.07.25 11:13:49:927]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Archaic Fortress time - undefined
[01.07.25 11:13:49:930]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Floran Fortress time - undefined
[01.07.25 11:13:49:933]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Cloud Mountain Fortress time - undefined
[01.07.25 11:13:49:935]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Tanor Fortress time - undefined
[01.07.25 11:13:49:938]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Dragonspine Fortress time - undefined
[01.07.25 11:13:49:940]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Antharas Fortress time - undefined
[01.07.25 11:13:49:943]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Western Fortress time - undefined
[01.07.25 11:13:49:945]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Hunter's Fortress time - undefined
[01.07.25 11:13:49:948]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Aaru Fortress time - undefined
[01.07.25 11:13:49:950]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Demon Fortress time - undefined
[01.07.25 11:13:49:952]  INFO impl.FortressSiegeEvent: FortressSiegeEvent: Monastic Fortress time - undefined
[01.07.25 11:13:49:953]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 70-75 Level time - 11:30 01.07.2025
[01.07.25 11:13:49:953]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 76-79 Level time - 11:30 01.07.2025
[01.07.25 11:13:49:953]  INFO impl.KrateisCubeEvent: KrateisCubeEvent: Krateis Cube 80-85 Level time - 11:30 01.07.2025
[01.07.25 11:13:49:953]  INFO fightclub.LastManStandingEvent: LastManStandingEvent: Last Hero time - undefined
[01.07.25 11:13:49:954]  INFO fightclub.TeamVSTeamEvent: TeamVSTeamEvent: Team VS Team time - undefined
[01.07.25 11:13:49:954]  INFO fightclub.LuckyCreaturesEvent: LuckyCreaturesEvent: Lucky Creatures time - undefined
[01.07.25 11:13:49:954]  INFO fightclub.CaptureTheFlagEvent: CaptureTheFlagEvent: Capture The Flag time - undefined
[01.07.25 11:13:49:955]  INFO fightclub.FFATreasureHuntEvent: FFATreasureHuntEvent: Treasure Hunt time - undefined
[01.07.25 11:13:49:955]  INFO fightclub.KoreanStyleEvent: KoreanStyleEvent: Korean Style time - undefined
[01.07.25 11:13:49:955]  INFO fightclub.ProtectTheKingEvent: ProtectTheKingEvent: Protect The King time - undefined
[01.07.25 11:13:49:955]  INFO fightclub.FightForThroneEvent: FightForThroneEvent: Fight For Throne time - undefined
[01.07.25 11:13:49:955]  INFO fightclub.DeathMatchEvent: DeathMatchEvent: Death Match time - undefined
[01.07.25 11:13:49:955]  INFO fightclub.RussianRouletteEvent: RussianRouletteEvent: Russian Roulette time - undefined
[01.07.25 11:13:49:956]  INFO instancemanager.CastleManorManager: Manor System: Initializing...
[01.07.25 11:13:49:958]  INFO gameserver.GameServer: ==============================================================================
[01.07.25 11:13:49:958]  INFO gameserver.GameServer: -------------------------------------------------------------=[ Auto Cleaner ]
[01.07.25 11:13:49:958]  INFO gameserver.GameServer: IdFactory: Free ObjectID's remaining: 1879004828
[01.07.25 11:13:49:958]  INFO gameserver.GameServer: ==============================================================================
[01.07.25 11:13:49:959]  INFO instancemanager.CoupleManager: Initializing CoupleManager
[01.07.25 11:13:49:959]  INFO instancemanager.CoupleManager: Loaded: 0 couples(s)
[01.07.25 11:13:49:961]  INFO gameserver.GameServer: ----------------------------------------------------------------=[ Hellbound ]
[01.07.25 11:13:49:968]  INFO instancemanager.HellboundManager: HellboundManager: Loaded 335 spawn entries.
[01.07.25 11:13:50:092]  INFO instancemanager.HellboundManager: HellboundManager: Spawned 623 mobs and NPCs according to the current Hellbound stage
[01.07.25 11:13:50:092]  INFO instancemanager.HellboundManager: Hellbound Manager: Loaded
[01.07.25 11:13:50:093]  INFO naia.NaiaTowerManager: Naia Core Manager: Loaded
[01.07.25 11:13:50:094]  INFO gameserver.GameServer: ==============================================================================
[01.07.25 11:13:50:094]  INFO instancemanager.SoDManager: Seed of Destruction Manager: Loaded
[01.07.25 11:13:50:095]  INFO instancemanager.SoIManager: Seed of Infinity Manager: Loaded. Current stage is: 1
[01.07.25 11:13:50:096]  INFO instancemanager.SpawnManager: SpawnManager: spawned 4 npc; spawns: 4; group: soi_world_mouths
[01.07.25 11:13:50:096]  INFO instancemanager.SpawnManager: SpawnManager: spawned 1 npc; spawns: 1; group: soi_world_abyssgaze2
[01.07.25 11:13:50:097]  INFO instancemanager.BloodAltarManager: Blood Altar Manager: Initializing...
[01.07.25 11:13:50:112]  INFO instancemanager.SpawnManager: SpawnManager: spawned 51 npc; spawns: 51; group: bloodaltar_alive_npc
[01.07.25 11:13:50:114]  INFO gameserver.GameServer: Preparing Drop Calculator
[01.07.25 11:13:50:173]  INFO gameserver.GameServer: ----------------------------------------------------------=[ Offline Buffers ]
[01.07.25 11:13:50:174]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loading offline buffers...
[01.07.25 11:13:50:174]  INFO datatables.OfflineBuffersTable: OfflineBuffersTable: Loaded: 0 offline buffer(s)
[01.07.25 11:13:50:186]  INFO achievements.Achievements: Achievement System: Loaded 4 achievement categories and 39 achievements.
[01.07.25 11:13:50:189]  INFO gameserver.GameServer: Emotions Loaded....
[01.07.25 11:13:50:189]  INFO gameserver.GameServer: --------------------------------------------------=[ DataBase Cleaner Loaded ]
[01.07.25 11:13:50:191]  INFO dao.CharacterDAO: Found 0 characters to delete!
[01.07.25 11:13:50:256]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Initializing
[01.07.25 11:13:50:257]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Load OK
[01.07.25 11:13:50:257]  INFO instancemanager.AutoAnnounce: AutoAnnounce: Loaded 0 announce.
[01.07.25 11:13:50:259]  INFO gameserver.GameServer: ---------------------------------------------------=[ Loaded Facebook System ]
[01.07.25 11:13:50:260]  INFO gameserver.GameServer: -----------------------------------------------------=[ Loaded Stream System ]
[01.07.25 11:13:50:264]  INFO holder.ProxiesHolder: ProxiesHolder: loaded 20 proxies(s) count.
[01.07.25 11:13:50:264]  INFO gameserver.GameServer: ------------------------------------------------------=[ Loaded Proxy System ]
[01.07.25 11:13:53:063]  INFO donation.DonationReader: Donation Reader service started
[01.07.25 11:13:53:064]  INFO gameserver.GameServer: --------------------------------------------=[ Loaded AUTO - Donation System ]
[01.07.25 11:13:53:066]  INFO taskmanager.BackupTaskManager: Next Auto Backup in 19 H., 26 Min.
[01.07.25 11:13:53:075]  INFO votingengine.VotingSettings: VotingSettings: Loaded 3 messages, 4 drops!
[01.07.25 11:13:53:078]  INFO votingengine.VotingRewardAPI: VoteReward: Has been successfully loaded!
[01.07.25 11:13:53:080]  INFO model.PhantomPlayers: Loading phantom players...
[01.07.25 11:13:53:087]  INFO model.PhantomPlayers: Loaded 32002 possible phantom names.
[01.07.25 11:13:53:087]  INFO model.PhantomPlayers: Loaded 7172 possible phantom titles.
[01.07.25 11:13:53:088]  INFO model.PhantomPlayers: PhantomPlayers: Cached 0 players.
[01.07.25 11:13:53:088]  INFO model.PhantomPlayers: PhantomPlayers: Free names 32002.
[01.07.25 11:13:53:089]  INFO model.PhantomPlayers: Loaded 0 phantom players from database with a maximum of 3000 phantoms.
[01.07.25 11:13:53:089]  INFO model.PhantomPlayers: Scheduled spawner with 1 seconds delay.
[01.07.25 11:13:53:254]  INFO instancemanager.SpawnManager: SpawnManager: spawned 444 npc; spawns: 122; group: NIGHT
[01.07.25 11:13:53:304]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dawn_spawn
[01.07.25 11:13:53:505]  INFO instancemanager.SpawnManager: SpawnManager: spawned 653 npc; spawns: 429; group: dusk_spawn
[01.07.25 11:13:53:505]  INFO gameserver.GameServer: Telnet server is currently disabled.
[01.07.25 11:13:53:508]  INFO gameserver.Shutdown: Scheduled server restart in 18h 31m 7s.
[01.07.25 11:13:53:508]  INFO gameserver.GameServer: ==============================================================================
[01.07.25 11:13:53:508]  INFO gameserver.GameServer: >>>>>>>>>>>>>>> GameServer Started <<<<<<<<<<<<<<
[01.07.25 11:13:53:508]  INFO gameserver.GameServer: Maximum Numbers of Connected Players: 3000
[01.07.25 11:13:53:508]  INFO gameserver.GameServer: ====================================================================
[01.07.25 11:13:53:530]  INFO gameserver.GameServer: AllowedMemory: ........... 6291456 KB
[01.07.25 11:13:53:530]  INFO gameserver.GameServer: 
Allocated: ............... 4194304 KB (66.6667%)
[01.07.25 11:13:53:530]  INFO gameserver.GameServer: 
Non-Allocated: ........... 2097152 KB (33.3333%)
[01.07.25 11:13:53:530]  INFO gameserver.GameServer: 
AllocatedMemory: ......... 4194304 KB
[01.07.25 11:13:53:531]  INFO gameserver.GameServer: Used: .................... 1141844 KB (18.1491%)
[01.07.25 11:13:53:531]  INFO gameserver.GameServer: 
Unused (cached): ......... 3052459 KB (48.5175%)
[01.07.25 11:13:53:531]  INFO gameserver.GameServer: 
UseableMemory: ........... 5149611 KB (81.8509%)
[01.07.25 11:13:53:531]  INFO gameserver.GameServer: 

[01.07.25 11:13:53:531]  INFO gameserver.GameServer: ====================================================================
[01.07.25 11:13:53:533]  INFO loginservercon.AuthServerCommunication: Connecting to authserver on 127.0.0.1:9014
[01.07.25 11:13:53:543]  INFO lspackets.AuthResponse: Registered on authserver as 1 [Bartz]
[01.07.25 11:15:30:257]  INFO tasks.RestoreOfflineTraders: Restored 0 offline traders
