# Permission for battles for experience in the arena
ArenaExp = True

# Did the PvP points for killing in the arena (Coliseum, etc.).
# As in these areas, no concepts of "PvP" and "PC", even if the player
# No resistance, will pvp points. Can lead to cheat pvp.
# Default: false
SiegePvpCount = false

# Did the PvP points for killing a siege zone.
# As in these areas, no concepts of "PvP" and "PC", even if the player
# No resistance, will pvp points. Can lead to cheat pvp.
# Default: false
ZonePvpCount = false

# AutoLoot loot. No geodata recommended that otherwise might sagging loot for textures.
AutoLoot = True

# AutoLoot only adena.
AutoLootOnlyAdena = False

# AutoLoot Herb with mobs.
AutoLootHerbs = False

# If the option is enabled AutoLootIndividual AutoLoot / AutoLootHerbs each player can adjust for themselves
AutoLootIndividual = True

# AutoLoot bosses at AutoLoot = True, does not apply to Herb
AutoLootFromRaids = False

# Configure the chance factor groups and Augment insert options. (Syntax: group, option)
AugmentChance = 1.0,1.0

# AutoLoot loot for players with karma. AutoLoot must be enabled.
# And identify the type of loot items from the PC - on the ground or avtolut.
AutoLootPK = False

# Player with karma can use NPC shops.
AltKarmaPlayerCanShop = False

# Deleveling death.
Delevel = True

# Low Level PKing protection
# If attackerLevel - defenderLevel > X:
# You are unable to attack. 
# It works only in Normal Zone and if defender isn't in PvP Mode
# Default: 100
LevelDifferenceProtection = 30

# Give it a fine for wearing equip higher level?
# True giving, False not to
# Default: True
EpicExpertisePenalty = True

# Minimum Level to send Mail.
MinLevelToSendMail = 61

# Vitamin Pets Food Item ID
VitaminPetsFoodID = 5169
VitaminDeselotFoodID = 20768
VitaminSuperPetID = 14818

# Pets Options
allowPetAttackMaster = false
TeleportPetToMaster = false

# Give it a fine for wearing items above level?
# True giving, False not to
# Default: True
ExpertisePenalty = True

# All Scrolls on click to teleport characters in Town of Giran only!
# Default = False
AllScrollsSoEToGiran = False

# Turn off the books for learning skills (for PvP servers)
AltDisableSpellbooks = True

# Store the cubes, Herb, song and dance at logout, use the power
AltSaveUnsaveable = False

# The effects of the remaining time to less than 5 seconds are lost.
AltSaveEffectsRemainingTime = 5

# Send a message when you try to use a skill unprepared
AltShowSkillReuseMessage = True

# Remove buffs SA and inlaid at removal is appropriate weapons (offlike = False)
AltDeleteSABuffs = False

# Auto-learning skills for the players (only for testing!)
AutoLearnSkills = True

# Auto-learning skills from Forgotten Scrolls (valid only when AutoLearnSkills = True)
AutoLearnForgottenSkills = False

# Set the title when creating a char, default False.
CharTitle = True
CharAddTitle = MultVerso

# Fitting. Does not work.
WearTestEnabled = False

# Buffs don't use SpS, manaheal spells heals more with SpS if True
SavingSpS = False
ManahealSpSBonus = False

# Chance craft masterwork items (default is 3%)
CraftMasterworkChance = 3.
# Chance dual craft (default 3%)
CraftDoubleCraftChance = 3.

# Remove one-time recipes.
# True - from the recipe book, False - from inventory.
AltUnregisterRecipe = False

# Allow or Shift + click to all players
AllowShiftClick = True
# Show players drop list when shift + click on mobs.
AltShowDroplist = True
# Show extended details page, not so beautiful but more comfortable and informative
AltFullStatsPage = True

# Add a subclass without quest. Disabled by default.
AltAllowSubClassWithoutQuest = True
# You can replace up to 30 Baium rags
AltAllowSubClassWithoutBaium = True
# Start level on Subclass
AltStartLevelToSubclass = 40
# Minimum level to add subclass.
AltLevelToGetSubclass = 75
# How to add a subclass to the existing 3rd
AltSubAdd = 0
# Books certificates to take the coach to ne or all at once
# The default is one (False)
AltSubBook = True

# The maximum level that you can take
AltMaxLevel = 85
AltMaxSubLevel = 84

# Alternative extension cookbook, in addition in 50 basic.
AltAddRecipes = 0

# Maximum number of clans in the alliance is set to 3
AltMaxAllySize = 3

# Require Clan or Alliance Castle
AltRequireClanCastle = False
# Require Castle for join Dawn in Seven Signs
AltRequireCastleDawn = False
AltAllowAdenaDawn = True

# Retail Seven Signs - Teleport out from cata/necro
# If true ( when a player hit in mobs and is not registered, will auto teleport out from catacombs or necropols)
# Official Default True
Retail_SevenSigns = False

# Option allows you to give the right to take things from the clan warehouse not only Cloud
AltAllowOthersWithdrawFromClanWarehouse = False
# Option allows you to use the command. Clan (assignment of privileges to the taking
# Of KWH) only clan leader
AltAllowClanCommandOnlyForClanLeader = True

# If you create a clan, is set next level
ClanLevelCreate = 0

# The number of reputation points you need to raise the level of the clan.
ClanLevel6Cost = 5000
ClanLevel7Cost = 10000
ClanLevel8Cost = 20000
ClanLevel9Cost = 40000
ClanLevel10Cost = 40000
ClanLevel11Cost = 75000

# The number of people in the clan you need to raise the level of the clan.
ClanLevel6Requirement = 25
ClanLevel7Requirement = 40
ClanLevel8Requirement = 60
ClanLevel9Requirement = 80
ClanLevel10Requirement = 120
ClanLevel11Requirement = 140

# The number of sworn blood necessary for taking 9th lvl clan
# Default: 150
BloodOaths = 150

# The amount of blood needed for an alliance taking 10th lvl clan
# Default: 5
BloodPledges = 5

# The minimum number of points to end the Clan Academy
# Default: 190
MinAcademPoint = 190

# The maximum number of points to end the Clan Academy
# Default: 650
MaxAcademPoint = 550

# Notification of players on the current period 7-stamps every n minutes.
# By default, n = 30, to disable the n = 0.
SSAnnouncePeriod = 0

# Allow any augmentation of things
AugmentAll = False

# An alternative to the loss of a fine expo death.
# Portraits are taken into account in the exp counter and PK.
# This is not a "Death Penalty" from the update S5.5!
EnableAltDeathPenalty = False
# Consider counter PK for settlement alternative penalty to loss exp.
AltPKDeathRate = 0.

# Get Death Penalty for death. Introduced in S5.5
EnableDeathPenaltyC5 = True
# Chance of getting a Death Penalty.
DeathPenaltyC5Chance = 10
# If use scrolls of recovery for your PC.
ChaoticCanUseScrollOfRecovery = True

# Rtg the loss expo with active Death Penalty. 0 to disable.
# Calculated as bonus dpLevel / 2.
# If it will become <* 1 it will be set to one to take no effect at calculations.
DeathPenaltyC5RateExpPenalty = 1
# Rate for karma modifier
# Death penalty is calculated as:
# Random (100) <= (DeathPenaltyC5Chance + player's karma/DeathPenaltyC5RateKarma)
DeathPenaltyC5RateKarma = 500

# Add Pushkin opportunities and forge merchant mammon
PushkinSignsOptions = False

# Teleport to the catacombs for Aden
TeleToCatacombs = False

# The Smiths is a function of crystallization
BSCrystallize = False

# The maximum grade for an upgrade, the default low B (figure - the maximum the reference price)
# D - 1387000, C - 4713500, B - 10091400, A - 26968400, S - 2147483647
MammonUpgrade = 6680500

# The maximum grade for the equivalent exchange, the default top B (figure - the maximum the reference price)
# D - 1387000, C - 4713500, B - 10091400, A - 26968400, S - 2147483647
MammonExchange = 10091400

# If False, all Common in shops will be removed
AllowSellCommon = True

# If False, all shady things for beginners and shops will be removed
AllowShadowWeapons = True

# Tattoos sale in Luxor (multisell 6500)
AllowTattoo = False

# You can turn off some multiselly
# DisabledMultisells = 170

# You can prohibit selling in retail stores things more certain price, for each slot
# ShopPriceLimits =

# You can set items selling ban
# ShopUnallowedItems =

# Upon reaching level 45 or more, the player will automatically start _350_EnhanceYourWeapon quest.
# This is done in order to help players level-up soul crystals without needing to take the quest.
AutoSoulCrystalQuest = false

# Potions that can be used for pet
AllowedPetPotions = 735, 1060, 1061, 1062, 1374, 1375, 1539, 1540, 6035, 6036

# Base limit buffs.
# Default: 20
BuffLimit = 24
# Base limit Dance / Song
# Default: 12
MusicLimit = 12
# Number Debuff
# Default: 8
DebuffLimit = 8
# Number TriggerBuff
# Default: 12
TriggerLimit = 12

# Alternative time update buff
# Template: IdSkill, mulDuration
# Where is IdSkill Id skill and mulDuration is how we want to multiply it update
# Example: 1075.5, 276.67
# Default: disabled

AltTimeModeSkillDuration = False

EnableSkillDuration = false
SkillDurationList = 1085,6

# The delay allows only the owner to pick up loot stamped within n seconds. Normal monsters
NonOwnerItemPickupDelay = 15
# Delay for only raids in seconds. Default: 300
NonOwnerItemPickupDelayRaids = 60

# Minimum group size to enter the Festival of Darkness.
FestivalMinPartySize = 5
# Multiplier on the price of participation in the festival.
FestivalRatePrice = 1.0

# -----------------------------------
# Configuration Dimension Rift
#
# Some information:
# - Player in the Dimension Rift can not leave the group or be
# Invited to another group, outside Dimension Rift.
# - Solo can not enter the Dimension Rift.
# -----------------------------------
# Minimum group size to enter the Dimension Rift. Min = 2, Max = 9.
# If during participation in the Dimension Rift, a group will be less than the minimum, the
# All members of the group will be teleported to the waiting room.
RiftMinPartySize = 2
# How to spawn mobs
RiftSpawnDelay = 10000
# Maximum number of "hops" between the rooms.
# At the end of the group will be teleported to the waiting room.
MaxRiftJumps = 4
# Delay between "jumps."
# In minutes.
AutoJumpsDelay = 8
# In milliseconds.
AutoJumpsDelayRandom = 120000
# Cost dimension fragments to enter the Dimension Rift.
# This amount will be taken from each player.
RecruitFC = 18
SoldierFC = 21
OfficerFC = 24
CaptainFC = 27
CommanderFC = 30
HeroFC = 33

# Clan Skills
AllowClanSkills = True

# Permitted to study the transformation of skills and sub classes without completing quests
# CERTIFICATE QUEST  = FALSE (Retail)
AllowLearnTransSkillsWOQuest = true

# Only the Party Leader can invite new members to the group
PartyLeaderOnlyCanInvite = True

# Can I start a conversation with an NPC, and open the window craft sitting. Offe is impossible, but it is very inconvenient.
AllowTalkWhileSitting = False

# Nobless teleporters are available to all
AllowNobleTPToAll = False

# Timeout to use social action (False off-like)
AltSocialActionReuse = False

# Koefitsient respawn time raids
AltRaidRespawnMultiplier = 1.0

# Inlaid things can pass, throw and sell
AlowDropAugmented = False

# Time modifier buffs clan hall (ID skills 4342 .. 4360)
ClanHallBuffTimeModifier = 6.0

# Modifier time singing and dancing
SongDanceTimeModifier = 1.0

# Weight Penalty 
MaxLoadModifier = 10.0

# Modifier value teleportation
GkCostMultiplier = 1.0

# Chance that the monster will be the champion (10h/50x hp, exp / sp, drop, actually skill 4407), percent
AltChampionChance1 = 4.
AltChampionChance2 = 0.
# Can be aggressive champion
AltChampionAggro = False
# Can be a champion of social
AltChampionSocial = False
# Top level champions (above this level mobs can not be champions)
AltChampionTopLevel = 75
# Lower level champions (below this level mobs can not be champions)
AltChampionMinLevel = 20

AltChampionDropHerbs = False

# Quests are given to all participants, not just dobivshey party, use the power (off-like False)
NoLasthitOnRaid = False

# Newby Buffer buff only the players or even summons / pets, use the power (off-like False)
BuffSummon = True

# Use Limitation Improved pets (off-like False):
# - Mages can not call a Baby Buffalo Improved
# - No war can cause Improved Baby Kookaburra
ImprovedPetsLimitedUse = False

# Official: baby pets and treat rechargeable only in combat
PetsHealOnlyInBattle = True

# Radius of where things are distributed within the party and Exp, defaults to 1500
AltPartyDistributionRange = 1500

#Table bonus to experiment for the party
AltPartyBonus = 2.00; 2.20; 2.40; 2.60; 2.80; 3.00; 3.00; 3.00; 3.00

# Lowering leveled at 5 less than required for the skill, skill is removed
AltRemoveSkillsOnDelevel = True

# Use the base rate modifier rate of fire, turn off is not recommended (big freebie)
AltUseBowReuseModifier = True

# Tearing doors KX by double-clicking
AllowChDoorOpenOnClick = True

# Button to cast all buffs in CH
AltChAllBuffs = False

# The ability to buy watch buffs instead 20and minute for a fee
AltChAllowHourBuff = False

# Hide village with MP
AltChSimpleDialog = False

# Allow castle doors / gates to be operated by defenders under siege
SiegeOperateDoors = False

# Only Castle Lord can operate doors under siege if it allowed
SiegeOperateDoorsLordOnly = True

# The level to which (inclusive) gatekeepers free (Official = 40)
GkFree = 40

# Restriction on entry to Cruma on character level 56 + lvl in Cruma TP prohibited (OFF Like).
GkCruma = 56

# All physical skills have overhear
AltAllPhysSkillsOverhit = True

# Use the Vitality at charge Exp, SP
AltVitalityEnabled = True
# Rtg bonus vitality, default: 1.00
#Bonus # Vitality is calculated as follows: <Vitality level> / 2 * AltVitalityRate
AltVitalityRate = 15.00
# Restore Vitality for killing a raid boss, default: 2000
AltVitalityRaidBonus = 2000
# Multiplier vitality consumption, default: 1.00. The higher the value, the more consumption.
AltVitalityConsumption = 5.00

# Solo kamaloka only for premium
KamalokaNightmaresPremiumOnly = False

# Tickets for re-entry in instances
SellReenterNightmaresTicket = False
SellReenterAbyssTicket = False
SellReenterLabyrinthTicket = False

# Limit inventory for pets (default 12)
AltPetInventoryLimit = 12

# Augmentations
AugmentationNGSkillChance = 15
AugmentationNGGlowChance = 0
AugmentationMidSkillChance = 30
AugmentationMidGlowChance = 40
AugmentationHighSkillChance = 45
AugmentationHighGlowChance = 70
AugmentationTopSkillChance = 60
AugmentationTopGlowChance = 100
AugmentationBaseStatChance = 1
AugmentationAccSkillChance = 10

# Distance to Follow
FollowRange = 100

# Opens cell Cloak
OpenCloakSlot = False

# Allow delete song/dance with alt+click
AltDispelDanceSong = True

# --------------------------------------------------------------
# 		Auction items (off)
# --------------------------------------------------------------
# Is the auction items
AltItemAuctionEnabled = True
# Whether to allow players to put more than one betting on the same auction
# The default is False
AltItemAuctionCanRebid = False
# Announced a start of the auction
# The default is True
AltItemAuctionAnnounce = True
# Eid auction an item
# The default is 57 Adena
AltItemAuctionBidItemId = 57
# The maximum rate
# Max: 99900000000
# Retail: 2100000000
AltItemAuctionMaxBid = 2100000000
# Time after which the player does not pick up rate is removed and no refund
# Offlayk (default) 604 800 000 = 1 week
AltItemAuctionMaxCancelTimeInMillis = 604800000
# If true then all private stores created will also appear automatically on Auction House, and removed when stopped
AuctionPrivateStoreAutoAdded = True
# --------------------------------------------------------------
# 		Community Board Auction System
# --------------------------------------------------------------
# Should Auction system be enabled?
EnableAuctionSystem = true

# Adena fee for making new auction
AuctionFee = 1000000

# Time between adding new auction, in seconds
AuctionAddDelay = 15

# How many days, player has to be unactive to delete auction
# and give him items back
AuctionInactivityDaysToDelete = 7

# Allow winning, deleting and adding auction while being outside town?
AuctionOutsideTown = false

# -------------------------------------------------------------
# 		Fishing Championship
# -------------------------------------------------------------
# Whether fishing tournament
AltFishChampionshipEnabled = True
# Eid things as a reward for winning (default 57)
AltFishChampionshipRewardItemId = 57
# Award for first place (default 800000)
AltFishChampionshipReward1 = 800000
# Award for the second place (default 500000)
AltFishChampionshipReward2 = 500000
# Award for the third place (default 300000)
AltFishChampionshipReward3 = 300000
# Award for the fourth place (default 200000)
AltFishChampionshipReward4 = 200000
# Award for the fifth place (default 100000)
AltFishChampionshipReward5 = 100000

# -------------------------------------------------------------
# 		Handy Block Checker
# -------------------------------------------------------------
# Whether Handy Block Checker
EnableBlockCheckerEvent = True
# Minimum number of people per team (default 2)
BlockCheckerMinTeamMembers = 2
# Rtg for rewards
BlockCheckerRateCoinReward = 1.
# Enable the player to select a command (default is True)
HBCEFairPlay = True

# -------------------------------------------------------------
# 			System petitions
# -------------------------------------------------------------
# Is the system of petitions
PetitioningAllowed = True
# Maximum number of petitions that can create 1 player (default 5)
MaxPetitionsPerPlayer = 5
# Maximum number of petitions that are in process (default is 25)
MaxPetitionsPending = 25

# -------------------------------------------------------------
# 			Pc Bang Points
# -------------------------------------------------------------
AltPcBangPointsEnabled = True
# Number of points accrued
AltPcBangPointsBonus = 15
# Time in minutes between the finance charges
AltPcBangPointsDelay = 30
# Minimum character level for the calculation of
AltPcBangPointsMinLvl = 20
# Chance to get double points
AltPcBangPointsDoubleChance = 15.

# ============================
# Stuck Subclass - Settings  #
# ============================
# All the skills learned in the sub-classes can be used by
AltEnableMultiProfa = false

#Show level monster
AltShowMonstersLvL = False

# Show monster type in monster title ( * ) = aggressive
AltShowMonstersAgression = False

# Confines the use of dance and songs at the effect of Shield Slam
# Default NO!
ShieldSlamBlockIsMusic = false

# Default: 0, values ​​in the range of 0 to 11
HellboundLevel = 11

# ================
#  Clan Penalty	 #
# ================
# Penalty for clan leave
# Default value in hours = 24
ClanLeavePenalty = 0
# Penalty for ally leave
# Default value in hours = 24
AllyLeavePenalty = 24
# Dissolve ally penalty
# Default value in hours = 24
DissolveAllyPenalty = 24
# Disolve clan penalty
# Default: 24 hours
DissolvedClanPenalty = 24
# Disband clan time
# Default: 48 hours
DisbanClanTime = 48

# ***************************************************************************
# Field of silence zones config
# ***************************************************************************
# ---------------------------------------------------------------------------
# Drop chance for ancient herbs (id 14824, 14825, 14826, 14827)
# Can appear only in water. 
# ---------------------------------------------------------------------------
# Spawn location for Ancient Herbs as  x,y,z;x1,y1,z1;...;xN,yN,zN
AncientHerbSpawnPoints = 90891,171809,-3765;91038,173332,-3765;84825,175500,-3765;84604,180407,-3760;83144,181263,-3765;79535,181557,-3760;79700,183108,-3765;82782,185375,-3760;82450,186523,-3765;79497,187199,-3760;80337,188781,-3765;83265,189267,-3765;86443,186673,-3765;86104,185645,-3760;87907,183527,-3760;87158,186115,-3760;89498,187474,-3760;91829,188645,-3760;93125,191117,-3765;90760,192797,-3765;87554,191466,-3765;86224,192764,-3765;83764,193413,-3768;80968,192765,-3764;79371,191145,-3760;81721,191475,-3760;88809,204734,-3760;89814,203520,-3760;91576,204594,-3760;91232,205447,-3765;93641,208728,-3765;94620,206690,-3765;94351,211663,-3765;90628,211426,-3760;89913,212538,-3765;91453,214318,-3760;92696,216789,-3760;91934,219138,-3765;92528,220140,-3760;92756,223352,-3765;91200,223511,-3765;91049,224798,-3760;87657,225484,-3760;86296,226629,-3760;84900,226958,-3765;83944,224498,-3760;86631,223720,-3760;87988,222801,-3760;82490,221379,-3760;80914,221359,-3765;80731,218907,-3760;77147,217591,-3760;77193,214720,-3765;76947,213449,-3760;80313,210834,-3760;78887,210534,-3765;82902,209212,-3765;84569,206135,-3760;85326,215156,-3765;89050,218827,-3765;88405,217174,-3760;87750,210575,-3765
# Radius for herb spawns. The centre is spawn point
AncientHerbSpawnRadius = 850
# Chance for herb to appear near every spawn point
AncientHerbSpawnChance = 100
# Maximum count of herbs next to every spawn point:
AncientHerbSpawnCount = 3
# Time, after new herbs will be spawned (do not reduce)
AncientHerbRespawnTime = 60
# Time after old herbs will be deleted (do not reduce)
AncientHerbDespawnTime = 60

EnablePoll = False
AnnounceToVoteInMin = 10