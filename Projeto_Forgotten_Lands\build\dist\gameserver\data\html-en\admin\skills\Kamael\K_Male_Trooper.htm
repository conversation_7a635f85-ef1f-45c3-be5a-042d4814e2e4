<html><body><table width=260>
<tr><td width=40>
<button value="Main" action="bypass -h admin_admin" width=40 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df">
</td><td width=180>
<center><PERSON><PERSON><PERSON> Trooper</center>
</td><td width=40>
<button value="Back" action="bypass -h admin_show_html skills/Kamael/kamael.htm" width=40 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df">
</td></tr></table>
<img src="sek.cbui355" width=260 height=2><br>
<center><table width=260>
<tr><td>Level:</td><td><edit var="level" width=110 height=15></td></tr>
</table><br>
<font color="b5a6c6"><table width=260>
<tr><td>Abyssal Blaze (max 10)</td><td><button value="Add" action="bypass -h admin_add_skill 1433 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Ancient Sword Mastery (max 45)</td><td><button value="Add" action="bypass -h admin_add_skill 472 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Change Weapon (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 1473 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Dark Strike (max 15)</td><td><button value="Add" action="bypass -h admin_add_skill 476 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Double Thrust (max 15)</td><td><button value="Add" action="bypass -h admin_add_skill 478 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Light Armor Mastery (max 50)</td><td><button value="Add" action="bypass -h admin_add_skill 465 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Magic Immunity (max 55)</td><td><button value="Add" action="bypass -h admin_add_skill 466 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Hard March (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 479 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Rapier Mastery (max 45)</td><td><button value="Add" action="bypass -h admin_add_skill 474 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Soul Mastery (max 23)</td><td><button value="Add" action="bypass -h admin_add_skill 467 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Dark Explosion (max 4)</td><td><button value="Add" action="bypass -h admin_add_skill 1434 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Increase Power (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 1432 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Surrender to Unholy (max 18)</td><td><button value="Add" action="bypass -h admin_add_skill 1445 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Death Mark (max 10)</td><td><button value="Add" action="bypass -h admin_add_skill 1435 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Erase Mark (max 3)</td><td><button value="Add" action="bypass -h admin_add_skill 1475 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Furious Soul (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 482 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Dark Armor (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 481 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Rush (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 484 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Courage (max 3)</td><td><button value="Add" action="bypass -h admin_add_skill 499 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Dark Blade (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 480 $level" width=55 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Disarm (max 7)</td><td><button value="Add" action="bypass -h admin_add_skill 485 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Sword Shield (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 483 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
</table></center></font>
</body></html>