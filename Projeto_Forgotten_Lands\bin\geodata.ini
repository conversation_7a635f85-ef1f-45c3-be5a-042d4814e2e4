# Dimension maps
GeoFirstX = 11
GeoFirstY = 10
GeoLastX = 26
GeoLastY = 26

# Template file geodata
GeoFilesPattern = (\\d{2}_\\d{2})\\.l2j

# If True GEODATA
AllowGeodata = False

# Allow the player to fall from the horizontal wall if he clicks on the wall itself
AllowFallFromWalls = True

#Motion keyboard (arrows)
AllowMoveWithKeyboard = True

# Increase to Z for packages
ClientZShift = 16

# Optimization of geodata in memory, saves about 150MB memory
# Optimization should first need to generate a map of matches
CompactGeoData = False

# The minimum difference between the layers
MinLayerHeight = 64

# The maximum height of the step for geodata
MaxZDiff = 64

# ÐÐ°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½Ð°Ñ Ð²ÑÑÐ¾ÑÐ° ÑÑÑÐ¿ÐµÐ½ÑÐºÐ¸ Ð´Ð»Ñ Ð³ÐµÐ¾Ð´Ð°ÑÑ Ð½Ð° ÑÑÑÐºÐµ Ð³ÐµÐ¾-ÑÐµÐ³Ð¸Ð¾Ð½Ð¾Ð²
RegionEdgeMaxZDiff = 64

# Diagonal Search
# It should be disabled only for debugging
PathFindDiagonal = True

#Smooths the way #
# It should be disabled only for debugging
PathClean = True

# Strongly accelerates the search, the effect on the quality of unknown
# 0 - off, 1 - is just the beginning, 2 - the beginning and the end of the road
PathFindBoost = 2

# The maximum height of the step to find a way
PathFindMaxZDiff = 32

# ÐÐ½Ð¾Ð¶Ð¸ÑÐµÐ»Ñ Ð´Ð»Ñ Ð¿Ð»Ð¾ÑÐ°Ð´Ð¸ Ð¿Ð¾Ð¸ÑÐºÐ° Ð¿ÑÑÐ¸
PathFindMapMul = 2

# The maximum time allowed to spend on the search path (in nanoseconds)
PathFindMaxTime = 100000000

# Configure the buffers to find a way, the number of x size of the map (in cages geodata)
# Minimum 96, maximum - 512
PathFindBuffers = 8x96;8x128;8x160;8x192;8x256;4x288;4x320;2x384;1x512

# Interval(Millis) between check if Skill Cast Criteria(Geodata Wall Check) are met
# Set 0 to disable
# Default: 200
GeodataSkillCheckTaskInterval = 150
