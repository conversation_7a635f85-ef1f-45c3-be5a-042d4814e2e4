<title>Choose skill to add</title>
<center>
[admin_add_skill 3 1|Power Strike 1 (Lv 5)]%%
[admin_add_skill 3 2|Power Strike 2 (Lv 5)]%%
[admin_add_skill 3 3|Power Strike 3 (Lv 5)]%%
[admin_add_skill 3 4|Power Strike 4 (Lv 10)]%%
[admin_add_skill 3 5|Power Strike 5 (Lv 10)]%%
[admin_add_skill 3 6|Power Strike 6 (Lv 10)]%%
[admin_add_skill 3 7|Power Strike 7 (Lv 15)]%%
[admin_add_skill 3 8|Power Strike 8 (Lv 15)]%%
[admin_add_skill 3 9|Power Strike 9 (Lv 15)]%%
[admin_add_skill 16 1|Mortal Blow 1 (Lv 5)]%%
[admin_add_skill 16 2|Mortal Blow 2 (Lv 5)]%%
[admin_add_skill 16 3|Mortal Blow 3 (Lv 5)]%%
[admin_add_skill 16 4|Mortal Blow 4 (Lv 10)]%%
[admin_add_skill 16 5|Mortal Blow 5 (Lv 10)]%%
[admin_add_skill 16 6|Mortal Blow 6 (Lv 10)]%%
[admin_add_skill 16 7|Mortal Blow 7 (Lv 15)]%%
[admin_add_skill 16 8|Mortal Blow 8 (Lv 15)]%%
[admin_add_skill 16 9|Mortal Blow 9 (Lv 15)]%%
[admin_add_skill 56 1|Power Shot 1 (Lv 5)]%%
[admin_add_skill 56 2|Power Shot 2 (Lv 5)]%%
[admin_add_skill 56 3|Power Shot 3 (Lv 5)]%%
[admin_add_skill 56 4|Power Shot 4 (Lv 10)]%%
[admin_add_skill 56 5|Power Shot 5 (Lv 10)]%%
[admin_add_skill 56 6|Power Shot 6 (Lv 10)]%%
[admin_add_skill 56 7|Power Shot 7 (Lv 15)]%%
[admin_add_skill 56 8|Power Shot 8 (Lv 15)]%%
[admin_add_skill 56 9|Power Shot 9 (Lv 15)]%%
[admin_add_skill 58 1|Elemental Heal 1 (Lv 15)]%%
[admin_add_skill 58 2|Elemental Heal 2 (Lv 15)]%%
[admin_add_skill 58 3|Elemental Heal 3 (Lv 15)]%%
[admin_add_skill 77 1|Attack Aura 1 (Lv 10)]%%
[admin_add_skill 91 1|Defense Aura 1 (Lv 5)]%%
[admin_add_skill 141 1|Weapon Mastery 1 (Lv 5)]%%
[admin_add_skill 141 2|Weapon Mastery 2 (Lv 10)]%%
[admin_add_skill 141 3|Weapon Mastery 3 (Lv 15)]%%
[admin_add_skill 142 1|Armor Mastery 1 (Lv 5)]%%
[admin_add_skill 142 2|Armor Mastery 2 (Lv 10)]%%
[admin_add_skill 142 3|Armor Mastery 3 (Lv 10)]%%
[admin_add_skill 142 4|Armor Mastery 4 (Lv 15)]%%
[admin_add_skill 142 5|Armor Mastery 5 (Lv 15)]%%
[admin_add_skill 194 1|Lucky 1 (Lv 1)]%%
</center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
