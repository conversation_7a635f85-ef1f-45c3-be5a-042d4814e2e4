<title>Choose skill to add</title>
<center>
[admin_add_skill 15 1|Charm 1 (Lv 20)]%%
[admin_add_skill 15 2|Charm 2 (Lv 20)]%%
[admin_add_skill 15 3|Charm 3 (Lv 20)]%%
[admin_add_skill 15 4|Charm 4 (Lv 24)]%%
[admin_add_skill 15 5|Charm 5 (Lv 24)]%%
[admin_add_skill 15 6|Charm 6 (Lv 24)]%%
[admin_add_skill 15 7|Charm 7 (Lv 28)]%%
[admin_add_skill 15 8|Charm 8 (Lv 28)]%%
[admin_add_skill 15 9|Charm 9 (Lv 28)]%%
[admin_add_skill 15 10|Charm 10 (Lv 32)]%%
[admin_add_skill 15 11|Charm 11 (Lv 32)]%%
[admin_add_skill 15 12|Charm 12 (Lv 32)]%%
[admin_add_skill 15 13|Charm 13 (Lv 36)]%%
[admin_add_skill 15 14|Charm 14 (Lv 36)]%%
[admin_add_skill 15 15|Charm 15 (Lv 36)]%%
[admin_add_skill 16 10|Mortal Blow 10 (Lv 20)]%%
[admin_add_skill 16 11|Mortal Blow 11 (Lv 20)]%%
[admin_add_skill 16 12|Mortal Blow 12 (Lv 20)]%%
[admin_add_skill 16 13|Mortal Blow 13 (Lv 24)]%%
[admin_add_skill 16 14|Mortal Blow 14 (Lv 24)]%%
[admin_add_skill 16 15|Mortal Blow 15 (Lv 24)]%%
[admin_add_skill 16 16|Mortal Blow 16 (Lv 28)]%%
[admin_add_skill 16 17|Mortal Blow 17 (Lv 28)]%%
[admin_add_skill 16 18|Mortal Blow 18 (Lv 28)]%%
[admin_add_skill 16 19|Mortal Blow 19 (Lv 32)]%%
[admin_add_skill 16 20|Mortal Blow 20 (Lv 32)]%%
[admin_add_skill 16 21|Mortal Blow 21 (Lv 32)]%%
[admin_add_skill 16 22|Mortal Blow 22 (Lv 36)]%%
[admin_add_skill 16 23|Mortal Blow 23 (Lv 36)]%%
[admin_add_skill 16 24|Mortal Blow 24 (Lv 36)]%%
[admin_add_skill 21 1|Poison Recovery 1 (Lv 24)]%%
[admin_add_skill 27 1|Unlock 1 (Lv 20)]%%
[admin_add_skill 27 2|Unlock 2 (Lv 24)]%%
[admin_add_skill 27 3|Unlock 3 (Lv 28)]%%
[admin_add_skill 27 4|Unlock 4 (Lv 32)]%%
[admin_add_skill 27 5|Unlock 5 (Lv 36)]%%
[admin_add_skill 56 10|Power Shot 10 (Lv 20)]%%
[admin_add_skill 56 11|Power Shot 11 (Lv 20)]%%
[admin_add_skill 56 12|Power Shot 12 (Lv 20)]%%
[admin_add_skill 56 13|Power Shot 13 (Lv 24)]%%
[admin_add_skill 56 14|Power Shot 14 (Lv 24)]%%
[admin_add_skill 56 15|Power Shot 15 (Lv 24)]%%
[admin_add_skill 56 16|Power Shot 16 (Lv 28)]%%
[admin_add_skill 56 17|Power Shot 17 (Lv 28)]%%
[admin_add_skill 56 18|Power Shot 18 (Lv 28)]%%
[admin_add_skill 56 19|Power Shot 19 (Lv 32)]%%
[admin_add_skill 56 20|Power Shot 20 (Lv 32)]%%
[admin_add_skill 56 21|Power Shot 21 (Lv 32)]%%
[admin_add_skill 56 22|Power Shot 22 (Lv 36)]%%
[admin_add_skill 56 23|Power Shot 23 (Lv 36)]%%
[admin_add_skill 56 24|Power Shot 24 (Lv 36)]%%
[admin_add_skill 58 4|Elemental Heal 4 (Lv 20)]%%
[admin_add_skill 58 5|Elemental Heal 5 (Lv 20)]%%
[admin_add_skill 58 6|Elemental Heal 6 (Lv 20)]%%
[admin_add_skill 58 7|Elemental Heal 7 (Lv 24)]%%
[admin_add_skill 58 8|Elemental Heal 8 (Lv 24)]%%
[admin_add_skill 58 9|Elemental Heal 9 (Lv 24)]%%
[admin_add_skill 58 10|Elemental Heal 10 (Lv 28)]%%
[admin_add_skill 58 11|Elemental Heal 11 (Lv 28)]%%
[admin_add_skill 58 12|Elemental Heal 12 (Lv 28)]%%
[admin_add_skill 58 13|Elemental Heal 13 (Lv 32)]%%
[admin_add_skill 58 14|Elemental Heal 14 (Lv 32)]%%
[admin_add_skill 58 15|Elemental Heal 15 (Lv 32)]%%
[admin_add_skill 58 16|Elemental Heal 16 (Lv 36)]%%
[admin_add_skill 58 17|Elemental Heal 17 (Lv 36)]%%
[admin_add_skill 58 18|Elemental Heal 18 (Lv 36)]%%
<br>
<button value="Page2" action="bypass -h admin_skill_index Elf/E_Scout2" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
