# ========================================================== 
# 			Configs for the coins rewards when donating	     #
# ========================================================== 

# Item given as reward for donations
DonationRewardItemId = 37000

# How many coins should be given per each euro
DonationMultiplierPerEuro = 10

# Donation bonuses for rewards after x amount donated
# The bonuses are in %
# Format: amount of donation >= x,bonus %;amount of donation >= x,bonus
# Meaning that the first one is if the player donated more than x money, then he will get the y bonus
# Remember to always put the 0 in the last part
# This starts always with the max amount available
# Default: 300,35;200,25;100,20;25,15;10,10;0,1
DonationBonusRewards = 300,35;200,25;100,20;25,15;10,10;0,1

