<title>Choose skill to add</title>
<center>
[admin_add_skill 146 5|Anti Magic 5 (Lv 20)]%%
[admin_add_skill 146 6|Anti Magic 6 (Lv 20)]%%
[admin_add_skill 146 7|Anti Magic 7 (Lv 25)]%%
[admin_add_skill 146 8|Anti Magic 8 (Lv 25)]%%
[admin_add_skill 146 9|Anti Magic 9 (Lv 30)]%%
[admin_add_skill 146 10|Anti Magic 10 (Lv 30)]%%
[admin_add_skill 146 11|Anti Magic 11 (Lv 35)]%%
[admin_add_skill 146 12|Anti Magic 12 (Lv 35)]%%
[admin_add_skill 164 1|Quick Recovery 1 (Lv 20)]%%
[admin_add_skill 164 2|Quick Recovery 2 (Lv 30)]%%
[admin_add_skill 212 1|Fast HP Recovery 1 (Lv 35)]%%
[admin_add_skill 213 1|Boost Mana 1 (Lv 20)]%%
[admin_add_skill 213 2|Boost Mana 2 (Lv 30)]%%
[admin_add_skill 228 1|Fast Spell Casting 1 (Lv 25)]%%
[admin_add_skill 229 1|Fast Mana Recovery 1 (Lv 25)]%%
[admin_add_skill 229 2|Fast Mana Recovery 2 (Lv 35)]%%
[admin_add_skill 234 1|Robe Mastery 1 (Lv 20)]%%
[admin_add_skill 234 2|Robe Mastery 2 (Lv 20)]%%
[admin_add_skill 234 3|Robe Mastery 3 (Lv 25)]%%
[admin_add_skill 234 4|Robe Mastery 4 (Lv 25)]%%
[admin_add_skill 234 5|Robe Mastery 5 (Lv 30)]%%
[admin_add_skill 234 6|Robe Mastery 6 (Lv 30)]%%
[admin_add_skill 234 7|Robe Mastery 7 (Lv 35)]%%
[admin_add_skill 234 8|Robe Mastery 8 (Lv 35)]%%
[admin_add_skill 239 1|Expertise D 1 (Lv 20)]%%
[admin_add_skill 249 3|Weapon Mastery 3 (Lv 20)]%%
[admin_add_skill 249 4|Weapon Mastery 4 (Lv 25)]%%
[admin_add_skill 249 5|Weapon Mastery 5 (Lv 25)]%%
[admin_add_skill 249 6|Weapon Mastery 6 (Lv 30)]%%
[admin_add_skill 249 7|Weapon Mastery 7 (Lv 30)]%%
[admin_add_skill 249 8|Weapon Mastery 8 (Lv 35)]%%
[admin_add_skill 249 9|Weapon Mastery 9 (Lv 35)]%%
[admin_add_skill 285 1|Higher Mana Gain 1 (Lv 20)]%%
[admin_add_skill 285 2|Higher Mana Gain 2 (Lv 20)]%%
[admin_add_skill 285 3|Higher Mana Gain 3 (Lv 25)]%%
[admin_add_skill 285 4|Higher Mana Gain 4 (Lv 25)]%%
[admin_add_skill 285 5|Higher Mana Gain 5 (Lv 30)]%%
[admin_add_skill 285 6|Higher Mana Gain 6 (Lv 30)]%%
[admin_add_skill 285 7|Higher Mana Gain 7 (Lv 35)]%%
[admin_add_skill 285 8|Higher Mana Gain 8 (Lv 35)]%%
[admin_add_skill 1069 1|Sleep 1 (Lv 25)]%%
[admin_add_skill 1069 2|Sleep 2 (Lv 25)]%%
[admin_add_skill 1069 3|Sleep 3 (Lv 25)]%%
[admin_add_skill 1069 4|Sleep 4 (Lv 30)]%%
[admin_add_skill 1069 5|Sleep 5 (Lv 30)]%%
[admin_add_skill 1069 6|Sleep 6 (Lv 30)]%%
[admin_add_skill 1069 7|Sleep 7 (Lv 35)]%%
[admin_add_skill 1069 8|Sleep 8 (Lv 35)]%%
[admin_add_skill 1069 9|Sleep 9 (Lv 35)]%%
[admin_add_skill 1078 1|Concentration 1 (Lv 20)]%%
[admin_add_skill 1078 2|Concentration 2 (Lv 30)]%%
[admin_add_skill 1126 1|Servitor Recharge 1 (Lv 25)]%%
[admin_add_skill 1126 2|Servitor Recharge 2 (Lv 25)]%%
[admin_add_skill 1126 3|Servitor Recharge 3 (Lv 30)]%%
[admin_add_skill 1126 4|Servitor Recharge 4 (Lv 30)]%%
[admin_add_skill 1126 5|Servitor Recharge 5 (Lv 35)]%%
[admin_add_skill 1126 6|Servitor Recharge 6 (Lv 35)]%%
<br>
<button value="Page2" action="bypass -h admin_skill_index DarkElf/DE_DarkWizard2" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table><br>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
