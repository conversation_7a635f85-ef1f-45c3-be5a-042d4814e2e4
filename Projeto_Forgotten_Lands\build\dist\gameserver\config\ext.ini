# The new petitions
# Should be included in the client
# l2.ini
# [URL]
# UseNewPetition = true
# Configure a data / petition_group.xml
NewPetitionSystem = false

# A different version of the mini-games
# Should be included in the client
# l2.ini
# [Localize]
# UseJapanMinigame1 = true
# The main difference is the saving account + show high class accounts
JapanMinigame = true

# The system is simplified search help
# Initial players have flazhek, <PERSON><PERSON><PERSON><PERSON> all (if / mark on), turn flazhek (/ initial on)
# After 6 months, the initial players can help others will shake icon (/ evangelist on)
# l2.ini
# [Localize]
# UseLectureMark = true
LectureMark = false

# Simulation Off Kernels
RemoteWhoLog = True
RemoteWhoSendTrash = True
RemoteWhoMaxOnline = 2000
RemoteOnlineIncrement = 50
RemoteWhoForceInc = 50
RemotePrivStoreFactor = 12

# Enable system Boat - Report on client-server system, with auto ban and zaniseniem the table
# Works: through social action games by pressing the button (Bot-Report), or on command / autohuntingreport
EnableAutoHuntingReport = False
