# =========================================================== #
# 					  Properties 							  #
#					 Formula Configuration     				  #
# DO NOT TOUCH THIS CONFIG- ELSE YOU UNBALANCE YOUR SERVER	  #
# =========================================================== #
# Multiplier chance of passing skills (default is 11)
SkillsChanceMod = 11

# The extent to which the calculation is being built matk chance (default 0.5)
SkillsChancePow = 0.5

# Lower limit chances of passing skills
SkillsChanceMin = 10

# Chance of passing skills mobs
SkillsMobChance = 0.5

# Upper limit chances of passing skills
SkillsChanceCap = 90

# Minimum Cast
SkillsCastTimeMin = 333

# Modifier efficiency absorb damage (default is 1)
AbsorbDamageModifier = 1.0

# Limits settings
LimitPatk = 200000
LimitMAtk = 250000
LimitPDef = 150000
LimitMDef = 150000
LimitPatkSpd = 1500
LimitMatkSpd = 1999
LimitCriticalDamage = 2000
LimitCritical = 500
LimitMCritical = 20
LimitAccuracy = 200
LimitEvasion = 250
LimitMove = 250
GmLimitMove = 1500
LimitFame = 100000

# The factors for NPC
NpcPAtkModifier = 1.0
NpcMAtkModifier = 1.0
NpcMaxHpModifier = 1.0
NpcMaxMpModifier = 1.0
NpcPDefModifier = 1.0
NpcMDefModifier = 1.0

# Damage Multiplier spear at several targets, the damage is reduced for each goal by: (Done by previous target) x (multiplier).
# Default: 1.0 - not to reduce damage
PoleDamageModifier = 1.0

# Delta Mod
# Don't Change!
SkillsDeltaModMult = 0.06

# Attacker Weapon Modifier
# Don't Change!
SkillsAttackerWeaponMod = 1.95

# M Atk Modifier Max
# Don't Change!
SkillsMAtkModMax = 1.3

# M Atk Modifier Min
# Don't Change!
SkillsMAtkModMin = 0.7

# Don't Change!
SkillsElementModMult = 0.2

# Don't Change!
SkillsElementModMax = 1.2

# Don't Change!
SkillsElementModMin = 0.8