-- =====================================================
-- Fix for Email Verification System
-- Projeto Forgotten Lands
-- =====================================================

-- Expand the 'var' column to accommodate longer variable names
ALTER TABLE `account_variables` MODIFY COLUMN `var` VARCHAR(50) NOT NULL DEFAULT '';

-- Optional: Clean up any existing test data
DELETE FROM `account_variables` WHERE `var` IN ('email_pending', 'email_code', 'email_expiry', 'email_verified');

-- Show current structure
DESCRIBE `account_variables`;

-- Show sample data
SELECT * FROM `account_variables` WHERE `var` LIKE 'email%' LIMIT 10;
