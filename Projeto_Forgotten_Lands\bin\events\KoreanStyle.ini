# ================================================= 
# Settings Event item Korean Style				  #
# =================================================
# Reward for member of winning team in the format itemId,count;itemId,count
Korean_Winner_Reward = 6673,5
# Reward for kill in the format itemId,count;itemId,count
Korean_Kill_Reward = 6673;5
# Time to start
Korean_time = 1
# Number of participants for each team
Korean_PlayersInTeam = 50
# Reflections used for event:
Korean_Reflections = 603;604;605;606
# Min level for registering
Korean_MinLevel = 83
# Max level for registering
Korean_MaxLevel = 85
# Should we keep old buffs when player is teleporting?
Korean_AllowBuffs = false
# Check for windows by IP or HWid?
Korean_CheckWindowMethod = HWid
# Buff Players after teleporting?
Korean_BuffPlayers = true
# List buff for Fighter
Korean_FighterBuffs = 1204,2;1086,2;1068,3;1045,6;1048,6;1040,3;1062,2;1036,2
# List buff for Wizards
Korean_MageBuffs = 1204,2;1085,3;1059,3;1045,6;1048,6;1040,3;1062,2;1036,2
# Seconds of no activity(No dmg decreased), until killing one of the players
# So if something fucks up, event won't stuck at all
Korean_Sec_Until_Kill = 15
# Reset the skill's reuses of players after each round?
Korean_ResetReuse = true
# Disable skills during Event
# Currently they are the same as TvT skills
#Skills 2222,1;
Korean_DisallowedSkills = 