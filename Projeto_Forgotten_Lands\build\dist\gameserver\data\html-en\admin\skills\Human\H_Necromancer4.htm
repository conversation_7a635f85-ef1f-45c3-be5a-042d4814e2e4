<title>Choose skill to add</title>
<center>
[admin_add_skill 1157 4|Body To Mind 4 (Lv 58)]%%
[admin_add_skill 1157 5|Body To Mind 5 (Lv 66)]%%
[admin_add_skill 1159 1|Curse Death Link 1 (Lv 52)]%%
[admin_add_skill 1159 2|Curse Death Link 2 (Lv 52)]%%
[admin_add_skill 1159 3|Curse Death Link 3 (Lv 56)]%%
[admin_add_skill 1159 4|Curse Death Link 4 (Lv 56)]%%
[admin_add_skill 1159 5|Curse Death Link 5 (Lv 58)]%%
[admin_add_skill 1159 6|Curse Death Link 6 (Lv 58)]%%
[admin_add_skill 1159 7|Curse Death Link 7 (Lv 60)]%%
[admin_add_skill 1159 8|Curse Death Link 8 (Lv 60)]%%
[admin_add_skill 1159 9|Curse Death Link 9 (Lv 62)]%%
[admin_add_skill 1159 10|Curse Death Link 10 (Lv 62)]%%
[admin_add_skill 1159 11|Curse Death Link 11 (Lv 64)]%%
[admin_add_skill 1159 12|Curse Death Link 12 (Lv 64)]%%
[admin_add_skill 1159 13|Curse Death Link 13 (Lv 66)]%%
[admin_add_skill 1159 14|Curse Death Link 14 (Lv 66)]%%
[admin_add_skill 1159 15|Curse Death Link 15 (Lv 68)]%%
[admin_add_skill 1159 16|Curse Death Link 16 (Lv 68)]%%
[admin_add_skill 1159 17|Curse Death Link 17 (Lv 70)]%%
[admin_add_skill 1159 18|Curse Death Link 18 (Lv 70)]%%
[admin_add_skill 1159 19|Curse Death Link 19 (Lv 72)]%%
[admin_add_skill 1159 20|Curse Death Link 20 (Lv 72)]%%
[admin_add_skill 1159 21|Curse Death Link 21 (Lv 74)]%%
[admin_add_skill 1159 22|Curse Death Link 22 (Lv 74)]%%
[admin_add_skill 1163 1|Curse Discord 1 (Lv 40)]%%
[admin_add_skill 1163 2|Curse Discord 2 (Lv 44)]%%
[admin_add_skill 1163 3|Curse Discord 3 (Lv 48)]%%
[admin_add_skill 1163 4|Curse Discord 4 (Lv 52)]%%
[admin_add_skill 1163 5|Curse Discord 5 (Lv 56)]%%
[admin_add_skill 1163 6|Curse Discord 6 (Lv 58)]%%
[admin_add_skill 1163 7|Curse Discord 7 (Lv 60)]%%
[admin_add_skill 1163 8|Curse Discord 8 (Lv 62)]%%
[admin_add_skill 1163 9|Curse Discord 9 (Lv 64)]%%
[admin_add_skill 1163 10|Curse Discord 10 (Lv 66)]%%
[admin_add_skill 1163 11|Curse Discord 11 (Lv 68)]%%
[admin_add_skill 1163 12|Curse Discord 12 (Lv 70)]%%
[admin_add_skill 1163 13|Curse Discord 13 (Lv 72)]%%
[admin_add_skill 1163 14|Curse Discord 14 (Lv 74)]%%
[admin_add_skill 1164 6|Curse: Weakness 6 (Lv 40)]%%
[admin_add_skill 1164 7|Curse: Weakness 7 (Lv 44)]%%
[admin_add_skill 1164 8|Curse: Weakness 8 (Lv 48)]%%
[admin_add_skill 1164 9|Curse: Weakness 9 (Lv 52)]%%
[admin_add_skill 1164 10|Curse: Weakness 10 (Lv 56)]%%
[admin_add_skill 1164 11|Curse: Weakness 11 (Lv 58)]%%
[admin_add_skill 1164 12|Curse: Weakness 12 (Lv 60)]%%
[admin_add_skill 1164 13|Curse: Weakness 13 (Lv 62)]%%
[admin_add_skill 1164 14|Curse: Weakness 14 (Lv 64)]%%
[admin_add_skill 1164 15|Curse: Weakness 15 (Lv 66)]%%
[admin_add_skill 1164 16|Curse: Weakness 16 (Lv 68)]%%
[admin_add_skill 1164 17|Curse: Weakness 17 (Lv 70)]%%
[admin_add_skill 1164 18|Curse: Weakness 18 (Lv 72)]%%
[admin_add_skill 1164 19|Curse: Weakness 19 (Lv 74)]%%
[admin_add_skill 1167 3|Poisonous Cloud 3 (Lv 48)]%%
[admin_add_skill 1167 4|Poisonous Cloud 4 (Lv 56)]%%
[admin_add_skill 1167 5|Poisonous Cloud 5 (Lv 64)]%%
[admin_add_skill 1167 6|Poisonous Cloud 6 (Lv 74)]%%
[admin_add_skill 1168 4|Curse:Poison 4 (Lv 44)]%%
[admin_add_skill 1168 5|Curse:Poison 5 (Lv 52)]%%
[admin_add_skill 1168 6|Curse:Poison 6 (Lv 62)]%%
[admin_add_skill 1168 7|Curse:Poison 7 (Lv 72)]%%
[admin_add_skill 1169 1|Curse Fear 1 (Lv 40)]%%
[admin_add_skill 1169 2|Curse Fear 2 (Lv 44)]%%
[admin_add_skill 1169 3|Curse Fear 3 (Lv 48)]%%
[admin_add_skill 1169 4|Curse Fear 4 (Lv 52)]%%
[admin_add_skill 1169 5|Curse Fear 5 (Lv 56)]%%
[admin_add_skill 1169 6|Curse Fear 6 (Lv 58)]%%
[admin_add_skill 1169 7|Curse Fear 7 (Lv 60)]%%
[admin_add_skill 1169 8|Curse Fear 8 (Lv 62)]%%
[admin_add_skill 1169 9|Curse Fear 9 (Lv 64)]%%
[admin_add_skill 1169 10|Curse Fear 10 (Lv 66)]%%
[admin_add_skill 1169 11|Curse Fear 11 (Lv 68)]%%
[admin_add_skill 1169 12|Curse Fear 12 (Lv 70)]%%
[admin_add_skill 1169 13|Curse Fear 13 (Lv 72)]%%
[admin_add_skill 1169 14|Curse Fear 14 (Lv 74)]%%
[admin_add_skill 1170 1|Anchor 1 (Lv 44)]%%
[admin_add_skill 1170 2|Anchor 2 (Lv 48)]%%
[admin_add_skill 1170 3|Anchor 3 (Lv 52)]%%
[admin_add_skill 1170 4|Anchor 4 (Lv 56)]%%
[admin_add_skill 1170 5|Anchor 5 (Lv 58)]%%
[admin_add_skill 1170 6|Anchor 6 (Lv 60)]%%
[admin_add_skill 1170 7|Anchor 7 (Lv 62)]%%
[admin_add_skill 1170 8|Anchor 8 (Lv 64)]%%
[admin_add_skill 1170 9|Anchor 9 (Lv 66)]%%
[admin_add_skill 1170 10|Anchor 10 (Lv 68)]%%
[admin_add_skill 1170 11|Anchor 11 (Lv 70)]%%
<br>
<button value="Page1" action="bypass -h admin_skill_index Human/H_Necromancer" width=40 height=15><br>
<button value="Page2" action="bypass -h admin_skill_index Human/H_Necromancer2" width=40 height=15><br>
<button value="Page3" action="bypass -h admin_skill_index Human/H_Necromancer3" width=40 height=15><br>
<button value="Page5" action="bypass -h admin_skill_index Human/H_Necromancer5" width=40 height=15></center>
Add a custom skill :
<center><table><tr><td>Id:<edit var="id" width=40></td><td>Level:<edit var="level" width=40></td><td>&nbsp;<button value="Add" action="bypass -h admin_add_skill $id $level" width=40 height=15 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td></tr></table>
<button value="Back" action="bypass -h admin_show_skills" width=40 height=15></center>
