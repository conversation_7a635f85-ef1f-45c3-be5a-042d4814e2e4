# ======================================
# Fight Club Mnager Configurations |
# ======================================

# Enables the Event?
# True - enabled, False - off
FightClubEnabled = True

# The minimum level for participation in the Event
MinimumLevel = 1

# The maximum level for participation in the Event
MaximumLevel = 85

# Maximum difference in level between the participants
MaximumLevelDifference = 10

# ID's items that can be selected as a bet. Listed separated by commas.
# By default, 57, 4037 (<PERSON><PERSON>, Coin of Luck)
AllowedItems = 57, 4037

# Number of bets on a single page
RatesOnPage = 10

# Delay to teleport into the arena, in seconds
ArenaTeleportDelay = 5

# Clear buffs to teleport into the arena?
CancelBuffs = True

# Remove pets to teleport players to the arena?
UnsummonPets = True

# Remove summons players to teleport into the arena?
# If True, the pupils will also be removed, regardless of the previous setting
UnsummonSummons = False

# True if the summons will lose their buffs when they die in the fight club. False if you want them to keep it for the next re-summon
SummonLoseBuffsOnDeath = False

# Remove clan skills at the time of the battle
RemoveClanSkills = False

# Remove heroic skills at the time of the battle
RemoveHeroSkills = False

# The time that is given to the players to prepare for battle in seconds
TimeToPreparation = 10

# Duration of fight in seconds before will be declared a draw
TimeToDraw = 300

# Allow a draw?
# If True, stakes will be refunded in the event that both players are alive
# If False, wins the one who left more HP
AllowDraw = True

# Time in seconds after which the players will be teleported to the end of the battle
TimeToBack = 10

# Making the announcement, while adding new rates
AnnounceRate = False

# Should we announce TOP Killer to the whole world?
FightClubAnnounceTopKiller = True

AnnounceStartBatleToAllScreen = True
AnnounceRateToAllScreen = True
