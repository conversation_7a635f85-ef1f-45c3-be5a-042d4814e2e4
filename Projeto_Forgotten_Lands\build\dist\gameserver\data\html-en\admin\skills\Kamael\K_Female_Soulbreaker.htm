<html><body><table width=260>
<tr><td width=40>
<button value="Main" action="bypass -h admin_admin" width=40 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df">
</td><td width=180>
<center><PERSON><PERSON><PERSON> Female Soul Breaker</center>
</td><td width=40>
<button value="Back" action="bypass -h admin_show_html skills/Kamael/kamael.htm" width=40 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df">
</td></tr></table>
<img src="sek.cbui355" width=260 height=2><br>
<center><table width=260>
<tr><td>Level:</td><td><edit var="level" width=110 height=15></td></tr>
</table><br>
<font color="b5a6c6"><table width=260>
<tr><td>Death Mark (max 10)</td><td><button value="Add" action="bypass -h admin_add_skill 1435 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Life to Soul (max 5)</td><td><button value="Add" action="bypass -h admin_add_skill 502 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Light Armor Mastery (max 50)</td><td><button value="Add" action="bypass -h admin_add_skill 465 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Magic Immunity (max 55)</td><td><button value="Add" action="bypass -h admin_add_skill 466 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Rapier Mastery (max 45)</td><td><button value="Add" action="bypass -h admin_add_skill 474 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Soul Mastery (max 23)</td><td><button value="Add" action="bypass -h admin_add_skill 467 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Soul of Pain (max 30)</td><td><button value="Add" action="bypass -h admin_add_skill 1436 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Surrender to Unholy (max 18)</td><td><button value="Add" action="bypass -h admin_add_skill 1445 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Triple Thrust (max 37)</td><td><button value="Add" action="bypass -h admin_add_skill 504 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Abyssal Power (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 1474 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Dark Armor (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 481 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Soul of Empower (max 2)</td><td><button value="Add" action="bypass -h admin_add_skill 1441 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Vicious Stance (max 20)</td><td><button value="Add" action="bypass -h admin_add_skill 312 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Dark Flame (max 26)</td><td><button value="Add" action="bypass -h admin_add_skill 1437 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Erase Mark (max 3)</td><td><button value="Add" action="bypass -h admin_add_skill 1475 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Pride of Kamael (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 1444 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Shining Edge (max 28)</td><td><button value="Add" action="bypass -h admin_add_skill 505 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Checkmate (max 4)</td><td><button value="Add" action="bypass -h admin_add_skill 506 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Shadow Bind (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 1446 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Spread Wing (max 25)</td><td><button value="Add" action="bypass -h admin_add_skill 492 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Steal Divinity (max 5)</td><td><button value="Add" action="bypass -h admin_add_skill 1440 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Annihilation Circle (max 9)</td><td><button value="Add" action="bypass -h admin_add_skill 1438 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Protection from Darkness (max 3)</td><td><button value="Add" action="bypass -h admin_add_skill 1442 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Voice Bind (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 1447 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Blink (max 1)</td><td><button value="Add" action="bypass -h admin_add_skill 1448 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
<tr><td>Curse of Divinity (max 5)</td><td><button value="Add" action="bypass -h admin_add_skill 1439 $level" width=55 height=15 back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"></td></tr>
</table></center></font>
</body></html>
